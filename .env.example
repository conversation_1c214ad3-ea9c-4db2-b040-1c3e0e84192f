
NEXT_PUBLIC_DEBUG=off
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Skip Clerk during build
SKIP_CLERK_INIT=true

#  vercel secret key and cron timeout
CRON_SECRET=dummy-cron-secret
TIMEOUT_SECONDS=790

# Neon or Supabase
DATABASE_URL=postgresql://dummy:dummy@localhost:5432/dummy

# Clerk
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_dummydummydummydummydummydummydummydummydummydummy
CLERK_SECRET_KEY=sk_test_0000000000000000000000000000000000000000
CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/draw
CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/draw

# dulupay Payment switcher
NEXT_PUBLIC_WXPAY_DISABLED=on
NEXT_PUBLIC_ALIPAY_DISABLED=on

# dulupay Payment config
# 查看地址 https://www.dulupay.com/user/userinfo.php?mod=api
PAY_API_URL=https://api.dulupay.com
# 商户 ID
PAY_PID=dummy
# 平台公钥
PAY_PUBLIC_KEY=dummy
# 商户公钥
PAY_MERCHANT_PRIVATE_KEY=dummy

# STRIPE
NEXT_PUBLIC_STRIPE_DISABLED=on
STRIPE_SECRET_KEY=sk_test_dummy
STRIPE_WEBHOOK_SECRET=sk_test_dummy

# Tuzi API Key - default 分组
TUZI_API_URL=https://api.tu-zi.com/v1
TUZI_API_KEY=sk_dummy
TUZI_MODEL_IMAGE=gpt-4o-image
TUZI_MODEL_IMAGE_VIP=gpt-4o-image-vip
TUZI_MODEL_IMAGE_SMALL=gpt-4o-image
TUZI_MODEL_IMAGE=gpt-4o-image-vip
TUZI_MODEL_IMAGE_VIP=gpt-4o-image-vip

# Tuzi Openai Key - openai 分组
TUZI_OPENAI_API_URL=https://api.tu-zi.com/v1
TUZI_OPENAI_API_KEY=sk-
TUZI_OPENAI_MODEL_IMAGE=gpt-image-1

# XAI API Key
XAI_API_URL=https://api.xai.com/v1
XAI_API_KEY=xai_dummy
XAI_API_MODEL_IMAGE=grok-2-image-latest

# Get your OpenAI API Key here for chat models: https://platform.openai.com/account/api-keys
OPENAI_API_URL=https://api.openai.com/v1
OPENAI_API_KEY=sk_dummy
OPENAI_MODEL_IMAGE=gpt-image-1
OPENAI_MODEL_IMAGE_SMALL=dall-e-3

# DeepSeek API Key
# https://www.volcengine.com/experience/ark?utm_term=202502dsinvite&ac=DSASUQY5&rc=S5JCRGKP
# 需要在开通管理中，开通所有模型后使用，也可以使用其他供应商的模型和 url，比如官方模型

OPENAI_COMPLETIONS_BASE_URL=https://ark.cn-beijing.volces.com/api/v3

# DeepSeek API Key
OPENAI_COMPLETIONS_API_KEY=dummy

# DeepSeek Model
OPENAI_COMPLETIONS_MODEL_SMALL=doubao-seed-1-6-flash-250615
OPENAI_COMPLETIONS_MODEL_LARGE=deepseek-v3-250324
OPENAI_COMPLETIONS_MODEL_REASONING=deepseek-r1-250120
OPENAI_COMPLETIONS_MODEL_FUNCTION=doubao-seed-1-6-250615

# Cloudflare R2 Storage
R2_ACCOUNT_ID=dummy
R2_ACCESS_KEY_ID=dummy
R2_SECRET_ACCESS_KEY=dummy
R2_BUCKET_NAME=dummy
R2_PUBLIC_URL_PREFIX=https://cdn-dev.images.zhaikr.com

# Build-specific environment variable
BUILD_STANDALONE=true
