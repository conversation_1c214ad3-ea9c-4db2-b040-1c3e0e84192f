export interface Share {
  id: string;
  shareId: string;
  historyId: string;
  userId: string;
  isPublic: boolean;
  allowFork: boolean;
  imageUrl: string;
  customPrompt: string;
  model: string;
  styleId: string;
  viewCount: number;
  likeCount: number;
  forkCount: number;
  forkEarnings: number;
  originalImages?: string[];
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    avatar: string | null;
  };
  history?: {
    id: string;
    status: boolean;
    resultUrl: string | null;
    prompt: string;
    pointsUsed: number;
    createdAt: string;
    parameters: any;
    extra: any;
  } | null;
}

export interface ShareStats {
  viewCount: number;
  likeCount: number;
  forkCount: number;
  forkEarnings: number;
}

export interface ShareSettings {
  isPublic: boolean;
  allowFork: boolean;
}
