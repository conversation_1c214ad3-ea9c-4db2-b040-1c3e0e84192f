import Image from "next/image";
import Link from "next/link";

export function Contact() {
  return (
    <div className="py-4 px-2 md:px-4 relative">
      <div className="p-8 rounded-xl bg-white shadow-[0_5px_15px_rgba(0,0,0,0.08),0_15px_35px_-5px_rgba(25,28,33,0.2)] ring-1 ring-gray-950/5">
        <div className="grid grid-cols-1">
          <div className="flex flex-col items-center gap-6">
            <h2 className="text-[1.0625rem] font-semibold">添加个人微信</h2>
            <div className="px-2.5 py-6 bg-[#FAFAFB] rounded-lg w-full flex flex-col items-center">
              <Image
                src="/images/wechat-public.jpg"
                alt="Personal WeChat QR Code"
                height={280}
                width={280}
                className="rounded-lg max-h-[280px] max-w-full w-auto h-auto"
              />
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <Link
            href="https://interjc.net/about"
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm text-[#7D7D7E] hover:text-primary transition-colors"
          >
            可备注 Image AI 进群，或查看更多联系方式
          </Link>
        </div>
      </div>
    </div>
  );
}
