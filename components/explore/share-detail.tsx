"use client";

import { useEffect, useState, useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Share } from "@/types/share";
import { getShareUrl } from "@/lib/sharing/utils";
import { useCopy } from "@/lib/hooks/use-copy";
import { notFound } from "next/navigation";
import Link from "next/link";
import { drawModels } from "@/constants/draw/models";
import { DRAW_STYLES } from "@/constants/draw";
import { Button } from "@/components/ui/button";
import { Copy, Wand2 } from "lucide-react";
import { QRCodeSVG } from "qrcode.react";
import { ChevronDown, ChevronUp } from "lucide-react";
import { ShowMore, type ShowMoreRef, type ShowMoreToggleLinesFn } from "@re-dev/react-truncate";

interface ShareDetailProps {
  id: string;
}

interface ApiResponse {
  data: Share | null;
  message: string;
}

export function ShareDetail({ id }: ShareDetailProps) {
  const [apiResponse, setApiResponse] = useState<ApiResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);
  const { copyToClipboard } = useCopy();
  const showMoreRef = useRef<ShowMoreRef>(null);

  useEffect(() => {
    fetchShare();
  }, [id]);

  const fetchShare = async () => {
    try {
      const response = await fetch(`/api/public/shares/${id}`);
      const data: ApiResponse = await response.json();

      if (!response.ok) {
        setApiResponse(data);
        if (response.status === 404) {
          notFound();
        }
        return;
      }

      setApiResponse(data);
    } catch (error) {
      console.error("Error fetching share:", error);
      setApiResponse({ data: null, message: `获取分享详情失败` });
    } finally {
      setLoading(false);
    }
  };

  // 复制分享链接
  const copyShareLink = () => {
    if (!apiResponse?.data) return;
    const shareUrl = getShareUrl(apiResponse.data.shareId);
    copyToClipboard(shareUrl, "链接已复制到剪贴板", "复制链接失败，请手动复制");
  };

  const toggleLines: ShowMoreToggleLinesFn = (e) => {
    showMoreRef.current?.toggleLines(e);
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto space-y-8">
        <Skeleton className="h-8 w-1/4" />
        <Card>
          <CardHeader className="p-0">
            <Skeleton className="w-full aspect-square" />
          </CardHeader>
          <CardContent className="p-6 space-y-4">
            <div className="space-y-2">
              <Skeleton className="h-6 w-1/4" />
              <Skeleton className="h-4 w-3/4" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-6 w-1/4" />
              <div className="grid grid-cols-2 gap-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-1/3" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!apiResponse) {
    return null;
  }

  if (!apiResponse.data) {
    return (
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-xl font-medium text-muted-foreground">
                {apiResponse.message}
              </p>
              <p className="mt-4 text-sm text-blue-500">
                <Link href="/explore">返回探索页面</Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const authorQueryLink = `/explore?userId=${apiResponse.data.userId}`;
  const modelQueryLink = `/explore?model=${apiResponse.data.model}`;
  const styleQueryLink = `/explore?style=${apiResponse.data.styleId}`;

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      <Card>
        <div className="flex flex-col md:flex-row h-full">
          <div className="w-full md:w-1/2">
            <div
              className="flex items-center justify-center h-full relative overflow-hidden"
              style={{
                backgroundImage: `url(${apiResponse.data?.imageUrl})`,
                backgroundSize: "cover",
                backgroundPosition: "center",
              }}
            >
              <div className="absolute inset-0 backdrop-blur-sm bg-white/80" />
              <img
                src={apiResponse.data?.imageUrl}
                alt={apiResponse.data?.customPrompt}
                className="w-full h-full object-cover rounded-t md:rounded-l md:rounded-tr-none relative z-10 md:sticky md:top-4"
              />
            </div>
          </div>
          <div className="w-full md:w-1/2 flex flex-col">
            <CardContent className="p-6 space-y-6 flex-1">
              <div className="flex items-center gap-3">
                <img
                  src={
                    apiResponse.data?.author.avatar ||
                    "/images/default-avatar.png"
                  }
                  alt={apiResponse.data?.author.name}
                  className="w-10 h-10 rounded-full object-cover"
                />
                <div>
                  <Link
                    href={authorQueryLink}
                    className="font-medium text-blue-500 hover:underline"
                  >
                    {apiResponse.data?.author.name}
                  </Link>
                  <p className="text-sm text-muted-foreground">作者</p>
                </div>
              </div>
              <div className="space-y-2">
                <h2 className="text-xl font-semibold">详情</h2>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">模型</p>
                    <Link
                      href={modelQueryLink}
                      className="text-blue-500 hover:underline"
                    >
                      {drawModels.find((m) => m.id === apiResponse.data?.model)
                        ?.name || String(apiResponse.data?.model)}
                    </Link>
                  </div>
                  <div>
                    <p className="text-muted-foreground">风格</p>
                    <Link
                      href={styleQueryLink}
                      className="text-blue-500 hover:underline"
                    >
                      {apiResponse.data?.styleId
                        ? DRAW_STYLES[
                            apiResponse.data.styleId as keyof typeof DRAW_STYLES
                          ]?.name
                        : apiResponse.data?.styleId}
                    </Link>
                  </div>
                  <div>
                    <p className="text-muted-foreground">图片数量</p>
                    <p>
                      {apiResponse.data?.allowFork
                        ? Array.isArray(apiResponse.data?.originalImages)
                          ? apiResponse.data.originalImages.length
                          : 0
                        : "已隐藏"}
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">一键复制</p>
                    <p>{apiResponse.data?.allowFork ? "允许" : "禁止"}</p>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <h2 className="text-xl font-semibold flex items-center justify-between">
                  <span>提示词</span>
                  {apiResponse.data?.customPrompt && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-muted-foreground"
                      onClick={() =>
                        copyToClipboard(
                          apiResponse.data?.customPrompt || "",
                          "提示词已复制到剪贴板",
                          "复制提示词失败，请重试"
                        )
                      }
                    >
                      <Copy className="w-4 h-4 mr-2" />
                      复制
                    </Button>
                  )}
                </h2>
                <div className="space-y-2">
                  <ShowMore
                    ref={showMoreRef}
                    lines={5}
                    more={
                      <Button variant="ghost" size="sm" className="text-muted-foreground hover:bg-transparent" onClick={toggleLines}>
                        <span className="flex items-center gap-2">
                          展开
                          <ChevronDown className="w-4 h-4" />
                        </span>
                      </Button>
                    }
                    less={
                      <Button variant="ghost" size="sm" className="text-muted-foreground hover:bg-transparent" onClick={toggleLines}>
                        <span className="flex items-center gap-2">
                          收起
                          <ChevronUp className="w-4 h-4" />
                        </span>
                      </Button>
                    }
                  >
                    {apiResponse.data?.allowFork
                      ? apiResponse.data?.customPrompt || "无自定义提示词"
                      : "已隐藏"}
                  </ShowMore>
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t p-4">
              <div className="w-full flex justify-between gap-6">
                <div className="gap-2">
                  {apiResponse.data?.allowFork ? (
                    <Link
                      href={`/draw?shareId=${apiResponse.data.shareId}`}
                      className="w-full inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3"
                    >
                      <Wand2 className="w-4 h-4 mr-2" />
                      立即制作
                    </Link>
                  ) : (
                    <Button disabled={true} className="w-full">
                      <Wand2 className="w-4 h-4 mr-2" />
                      立即制作
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full flex items-center justify-center gap-2 mt-2"
                    onClick={copyShareLink}
                  >
                    <Copy className="w-4 h-4" />
                    复制链接
                  </Button>
                </div>
                <div className="gap-2 cursor-pointer" onClick={copyShareLink}>
                  {apiResponse.data && (
                    <QRCodeSVG
                      value={getShareUrl(apiResponse.data.shareId)}
                      size={80}
                      level="H"
                    />
                  )}
                </div>
              </div>
            </CardFooter>
          </div>
        </div>
      </Card>
      <div className="flex justify-center">
        <Link
          href={`/explore`}
          className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3"
        >
          返回探索页面
        </Link>
      </div>
    </div>
  );
}
