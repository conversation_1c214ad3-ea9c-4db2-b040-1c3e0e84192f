"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { DRAW_STYLES } from "@/constants/draw";
import { drawModels } from "@/constants/draw/models";
import { Search, X } from "lucide-react";
import { DatePickerWithRange } from "@/components/ui/date-picker-with-range";
import { DateRange } from "react-day-picker";
import { Label } from "@/components/ui/label";

interface FiltersProps {
  dateRange?: DateRange;
  onDateRangeChange: (dateRange: DateRange | undefined) => void;
}

export function Filters({ dateRange, onDateRangeChange }: FiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleFilterChange = (key: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value && value !== "all") {
      params.set(key, value);
    } else {
      params.delete(key);
    }
    router.push(`/explore?${params.toString()}`);
  };

  const handleClear = () => {
    router.push("/explore");
    onDateRangeChange(undefined);
  };

  const handleSearch = () => {
    router.refresh();
  };

  return (
    <div className="space-y-6 pb-4">
      <div className="flex flex-wrap gap-6">
        <div className="space-y-2 min-w-[240px]">
          <Label>时间范围</Label>
          <DatePickerWithRange
            date={dateRange}
            onSelect={onDateRangeChange}
            className="w-full"
          />
        </div>

        <div className="space-y-2 min-w-[160px]">
          <Label>状态</Label>
          <Select
            value={searchParams.get("model") || "all"}
            onValueChange={(value) => handleFilterChange("model", value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="选择模型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部模型</SelectItem>
              {drawModels.map((model) => (
                <SelectItem key={model.id} value={model.id}>
                  {model.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2 min-w-[160px]">
          <Label>风格</Label>
          <Select
            value={searchParams.get("style") || "all"}
            onValueChange={(value) => handleFilterChange("style", value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="选择风格" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部风格</SelectItem>
              {Object.entries(DRAW_STYLES).map(([id, style]) => (
                <SelectItem key={id} value={id}>
                  {style.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2 min-w-[160px]">
          <Label>用户</Label>
          <Input
            type="text"
            placeholder="用户 ID"
            className="w-full"
            value={searchParams.get("userId") || ""}
            onChange={(e) => handleFilterChange("userId", e.target.value)}
          />
        </div>

        <div className="flex items-end gap-2">
          <Button
            variant="default"
            size="icon"
            onClick={handleSearch}
            title="搜索"
          >
            <Search className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={handleClear}
            title="清空条件"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
