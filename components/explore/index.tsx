"use client";

import { useEffect, useState, useRef, useCallback } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Loader2, Wand2 } from "lucide-react";
import { DRAW_STYLES } from "@/constants/draw";
import { Filters } from "./filters";
import { DateRange } from "react-day-picker";
import { startOfDay, endOfDay } from "date-fns";
import { useToast } from "@/lib/hooks/use-toast";

interface Share {
  id: string;
  shareId: string;
  imageUrl: string;
  model: string;
  styleId: string;
  customPrompt: string;
  viewCount: number;
  likeCount: number;
  forkCount: number;
  userId: string;
  allowFork?: boolean;
}

export function Explore() {
  const [shares, setShares] = useState<Share[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const searchParams = useSearchParams();
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const { toast } = useToast();

  const ITEMS_PER_PAGE = 9;

  const fetchShares = useCallback(async (pageNum: number, append: boolean = false) => {
    try {
      const params = new URLSearchParams(searchParams.toString());

      // Add date range parameters if they exist
      if (dateRange?.from) {
        params.append("startDate", startOfDay(dateRange.from).toISOString());
      }
      if (dateRange?.to) {
        params.append("endDate", endOfDay(dateRange.to).toISOString());
      }

      // Add pagination parameters
      params.append("page", pageNum.toString());
      params.append("limit", ITEMS_PER_PAGE.toString());

      const response = await fetch(`/api/public/shares?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch shares: ${response.status}`);
      }

      const data = await response.json();
      console.log("[EXPLORE] Fetched shares:", data.length);

      // If we received fewer items than requested, there are no more items to load
      if (data.length < ITEMS_PER_PAGE) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }

      if (append) {
        setShares(prev => [...prev, ...data]);
      } else {
        setShares(data);
        setHasMore(data.length >= ITEMS_PER_PAGE);
      }
    } catch (error) {
      console.error("[EXPLORE] Error fetching shares:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch shares");
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  }, [searchParams, dateRange, ITEMS_PER_PAGE]);

  // Initial load
  useEffect(() => {
    setIsLoading(true);
    setPage(1);
    fetchShares(1, false);
  }, [searchParams, dateRange, fetchShares]);

  // Set up intersection observer for infinite scrolling
  useEffect(() => {
    if (isLoading || !hasMore) return;

    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && !isLoadingMore && hasMore) {
        loadMore();
      }
    }, { threshold: 0.1 });

    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [isLoading, isLoadingMore, hasMore]);

  // Function to load more items
  const loadMore = () => {
    if (isLoadingMore || !hasMore) return;

    setIsLoadingMore(true);
    const nextPage = page + 1;
    setPage(nextPage);
    fetchShares(nextPage, true);
  };

  if (isLoading) {
    return (
      <div className="p-8 rounded-xl bg-white shadow-[0_5px_15px_rgba(0,0,0,0.08),0_15px_35px_-5px_rgba(25,28,33,0.2)] ring-1 ring-gray-950/5 w-full">
        <h1 className="text-3xl font-bold mb-6">探索公开创作</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <CardHeader className="p-0">
                <Skeleton className="w-full aspect-square" />
              </CardHeader>
              <CardContent className="p-4">
                <Skeleton className="h-4 w-1/3 mb-2" />
                <Skeleton className="h-4 w-full" />
              </CardContent>
              <CardFooter className="flex justify-between items-center p-4 pt-0">
                <div className="flex gap-4">
                  <Skeleton className="h-4 w-8" />
                  <Skeleton className="h-4 w-8" />
                  <Skeleton className="h-4 w-8" />
                </div>
                <Skeleton className="h-8 w-24" />
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 rounded-xl bg-white shadow-[0_5px_15px_rgba(0,0,0,0.08),0_15px_35px_-5px_rgba(25,28,33,0.2)] ring-1 ring-gray-950/5 w-full">
        <h1 className="text-3xl font-bold mb-6">探索公开创作</h1>
        <div className="text-center text-destructive">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 rounded-xl bg-white shadow-[0_5px_15px_rgba(0,0,0,0.08),0_15px_35px_-5px_rgba(25,28,33,0.2)] ring-1 ring-gray-950/5 w-full">
      <h1 className="text-md font-bold mb-6">探索公开创作</h1>
      <Filters dateRange={dateRange} onDateRangeChange={setDateRange} />
      {shares.length === 0 && !isLoading ? (
        <div className="text-center text-muted-foreground">
          暂无公开分享
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {shares.map((share) => (
              <Card key={share.shareId} className="overflow-hidden">
                <CardHeader className="p-0">
                  <Link href={`/explore/${share.shareId}`}>
                    <img
                      src={share.imageUrl}
                      alt={share.customPrompt}
                      className="w-full aspect-square object-cover"
                    />
                  </Link>
                </CardHeader>
                <CardContent className="p-2">
                </CardContent>
                <CardFooter className="flex justify-between items-center p-4 pt-0">
                  <div className="flex gap-4 text-sm text-muted-foreground">
                    <span>{DRAW_STYLES[share.styleId as keyof typeof DRAW_STYLES]?.name || share.styleId}</span>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="default"
                      size="sm"
                      className="inline-flex items-center justify-center"
                      onClick={() => {
                        if (share.allowFork === false) {
                          toast({
                            variant: "destructive",
                            description: "该分享不允许复刻",
                          });
                          return;
                        }
                        router.push(`/draw?shareId=${share.shareId}`);
                      }}
                      disabled={share.allowFork === false}
                    >
                      <Wand2 className="w-4 h-4 mr-1" />
                      复刻
                    </Button>
                    <Link
                      href={`/explore/${share.shareId}`}
                      className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3"
                    >
                      查看详情
                    </Link>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>

          {/* Load More Button and Observer Element */}
          <div ref={loadMoreRef} className="mt-8 flex justify-center">
            {hasMore && (
              <Button
                onClick={loadMore}
                disabled={isLoadingMore}
                className="px-8"
              >
                {isLoadingMore ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    加载中...
                  </>
                ) : (
                  "加载更多"
                )}
              </Button>
            )}
          </div>
        </>
      )}
    </div>
  );
}
