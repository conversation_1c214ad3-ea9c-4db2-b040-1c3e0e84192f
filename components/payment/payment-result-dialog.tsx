'use client';

import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { CheckCircle2, XCircle } from "lucide-react";
import Link from "next/link";

import { Button } from "@/components/ui/button";

interface PaymentResultDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  status: 'success' | 'failed';
  points?: number;
}

export function PaymentResultDialog({ isOpen, onOpenChange, status, points }: PaymentResultDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center">
            {status === "success" ? "充值成功" : "充值失败"}
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center py-6 space-y-4">
          {status === "success" ? (
            <>
              <CheckCircle2 className="w-16 h-16 text-green-500" />
              <p className="text-center text-lg">
                恭喜您充值成功！
                <br />
                {points}积分已经到账
              </p>
            </>
          ) : (
            <>
              <XCircle className="w-16 h-16 text-destructive" />
              <p className="text-center text-lg">
                充值失败
                <br />
                请稍后重试或联系客服
              </p>
            </>
          )}
        </div>
        <DialogFooter>
          <div className="w-full flex justify-center items-center">
            <Link href="/draw">
              <Button variant="outline">
                开始创作
              </Button>
            </Link>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
