"use client";

import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { XCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export function PaymentErrorCard() {
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());
  const errorMessage = getErrorMessage(params.get("error") || "unknown_error");

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <div className="flex justify-center mb-4">
          <XCircle className="h-16 w-16 text-red-500" />
        </div>
        <CardTitle className="text-xl text-center">
          支付失败
        </CardTitle>
        <CardDescription className="text-center">
          我们遇到了处理您的付款时出现的问题
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Alert className="bg-red-50 border-red-200">
          <AlertTitle>错误信息</AlertTitle>
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      </CardContent>
      <CardFooter className="flex justify-center gap-4">
        <Button asChild variant="outline">
          <Link href="/">返回首页</Link>
        </Button>
        <Button asChild>
          <Link href="/pricing">重试</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

function getErrorMessage(errorCode: string): string {
  const errorMessages: Record<string, string> = {
    "Invalid signature":
      "支付验证失败。签名无效。",
    "Order not found": "系统中找不到该订单。",
    "Payment amount mismatch":
      "支付金额与订单金额不匹配。",
    "Order already processed": "该订单已经处理过了。",
    system_error: "处理您的支付时发生系统错误。",
    unknown_error: "处理您的支付时发生未知错误。",
  };

  return errorMessages[errorCode] || errorMessages.unknown_error;
}
