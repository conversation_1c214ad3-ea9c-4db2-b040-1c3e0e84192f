'use client';

import { useState, useCallback, useEffect } from "react";
import { QRCodeSVG } from "qrcode.react";
import { Loader2, CheckCircle2, XCircle, AlertCircle } from "lucide-react";

import type { PricingTier } from "@/constants/draw/pricing";
import { wxpayEnabled, alipayEnabled, stripeEnabled } from "@/constants/payment";

import { cn } from "@/lib/utils";
import { useUser } from "@/lib/hooks/use-user";
import { useOrderPolling } from "@/lib/hooks/use-order-polling";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";


import { PaymentResultDialog } from "./payment-result-dialog";

interface PaymentDialogProps {
  tier: PricingTier;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PaymentDialog({ tier, isOpen, onOpenChange }: PaymentDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<"alipay" | "wechat" | "stripe" | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [orderId, setOrderId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [resultStatus, setResultStatus] = useState<'success' | 'failed'>('success');
  const { fetchUser } = useUser();

  // Reset states when dialog is closed
  useEffect(() => {
    if (!isOpen) {
      setPaymentMethod(null);
      setQrCodeUrl(null);
      setOrderId(null);
      setError(null);
      setIsLoading(false);
      setShowResult(false);
    }
  }, [isOpen]);

  const handleSuccess = useCallback(async () => {
    try {
      await fetchUser();
      setResultStatus('success');
      setShowResult(true);
    } catch (error) {
      console.error("Failed to update user data:", error);
      setError("更新用户数据失败");
    }
  }, [fetchUser]);

  const handleFailed = useCallback(() => {
    setResultStatus('failed');
    setShowResult(true);
  }, []);

  const handleResultClose = useCallback((open: boolean) => {
    if (!open) {
      setShowResult(false);
      onOpenChange(false);
    }
  }, [onOpenChange]);

  const { status, error: pollingError, isPolling } = useOrderPolling({
    orderId,
    onSuccess: handleSuccess,
    onFailed: handleFailed,
  });

  const handlePayment = async (method: 'wechat' | 'alipay' | 'stripe') => {
    try {
      setIsLoading(true);
      setError(null);
      setPaymentMethod(method);

      const response = await fetch('/api/purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tierId: tier.id,
          paymentMethod: method,
        }),
      });

      if (!response.ok) {
        throw new Error("创建订单失败");
      }

      const data = await response.json();

      // 检查 API 响应格式
      console.log('API Response:', data);

      if (!data.orderId || !data.qrCodeUrl) {
        throw new Error("无效的支付信息");
      }

      setOrderId(data.orderId);
      setQrCodeUrl(data.qrCodeUrl);

      // 如果是 Stripe 支付，直接跳转到支付页面
      // if (method === 'stripe') {
      //   window.location.href = data.qrCodeUrl;
      // }
    } catch (error) {
      console.error("Payment error:", error);
      setError(error instanceof Error ? error.message : "创建订单失败");
    } finally {
      setIsLoading(false);
    }
  };

  const renderStatus = () => {
    if (error || pollingError) {
      return (
        <div className="flex items-center justify-center gap-2 text-sm text-destructive">
          <XCircle className="h-4 w-4" />
          <span>{error || pollingError}</span>
        </div>
      );
    }

    if (!status) return null;

    switch (status) {
      case "SUCCESS":
        return (
          <div className="flex items-center justify-center gap-2 text-sm text-green-500">
            <CheckCircle2 className="h-4 w-4" />
            <span>支付成功！</span>
          </div>
        );
      case "FAILED":
        return (
          <div className="flex items-center justify-center gap-2 text-sm text-destructive">
            <XCircle className="h-4 w-4" />
            <span>支付失败，请重新尝试</span>
          </div>
        );
      case "REFUND":
        return (
          <div className="flex items-center justify-center gap-2 text-sm text-yellow-500">
            <AlertCircle className="h-4 w-4" />
            <span>订单已退款</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>等待支付完成... (二维码有效期5分钟)</span>
          </div>
        );
  }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[640px]">
          <DialogHeader>
            <DialogTitle>
              {tier.name} - {tier.price}元 ({tier.points}积分)
            </DialogTitle>
          </DialogHeader>
          {!qrCodeUrl ? (
            <div className="flex justify-center items-center space-x-4 py-4">
              { wxpayEnabled && (
                <Button
                  onClick={() => handlePayment("wechat")}
                  disabled={isLoading}
                  className={cn(
                    "w-full h-24 flex flex-col items-center justify-center gap-2",
                    "border-2 border-zinc-900 dark:border-white",
                    "transition-all duration-300",
                    "shadow-[4px_4px_0px_0px] shadow-zinc-900 dark:shadow-white",
                    "hover:shadow-[6px_6px_0px_0px]",
                    "hover:translate-x-[-2px] hover:translate-y-[-2px]",
                    "bg-zinc-50 dark:bg-zinc-800",
                    "text-zinc-900 dark:text-white",
                    "hover:bg-white dark:hover:bg-zinc-700",
                    "active:bg-zinc-50 dark:active:bg-zinc-800"
                  )}
                >
                  <span className="text-2xl">微信支付</span>
                  <span className="text-sm">WeChat Pay</span>
                </Button>
              )}
              { alipayEnabled && (
                <Button
                  onClick={() => handlePayment("alipay")}
                  disabled={isLoading}
                  className={cn(
                    "w-full h-24 flex flex-col items-center justify-center gap-2",
                    "border-2 border-zinc-900 dark:border-white",
                    "transition-all duration-300",
                    "shadow-[4px_4px_0px_0px] shadow-zinc-900 dark:shadow-white",
                    "hover:shadow-[6px_6px_0px_0px]",
                    "hover:translate-x-[-2px] hover:translate-y-[-2px]",
                    "bg-zinc-50 dark:bg-zinc-800",
                    "text-zinc-900 dark:text-white",
                    "hover:bg-white dark:hover:bg-zinc-700",
                    "active:bg-zinc-50 dark:active:bg-zinc-800"
                  )}
                >
                  <span className="text-2xl">支付宝</span>
                  <span className="text-sm">Alipay</span>
                </Button>
              )}
              {stripeEnabled && (
                <Button
                  onClick={() => handlePayment("stripe")}
                  disabled={isLoading}
                  className={cn(
                    "w-full h-24 flex flex-col items-center justify-center gap-2",
                    "border-2 border-zinc-900 dark:border-white",
                    "transition-all duration-300",
                    "shadow-[4px_4px_0px_0px] shadow-zinc-900 dark:shadow-white",
                    "hover:shadow-[6px_6px_0px_0px]",
                    "hover:translate-x-[-2px] hover:translate-y-[-2px]",
                    "bg-zinc-50 dark:bg-zinc-800",
                    "text-zinc-900 dark:text-white",
                    "hover:bg-white dark:hover:bg-zinc-700",
                    "active:bg-zinc-50 dark:active:bg-zinc-800"
                  )}
                >
                  <span className="text-2xl">海外/信用卡</span>
                  <span className="text-sm">Stripe</span>
                </Button>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center space-y-4 py-4">
              {paymentMethod !== "stripe" && (
                <>
                  <QRCodeSVG value={qrCodeUrl} size={200} />
                  <p className="text-sm text-muted-foreground">
                    请使用{paymentMethod === "alipay" ? "支付宝" : "微信"}
                    扫码支付（
                    <a
                      className="text-blue-500"
                      href={qrCodeUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      点击打开支付链接
                    </a>
                    ）
                  </p>
                </>
              )}
              {paymentMethod === "stripe" && (
                <a
                  className="text-blue-500"
                  href={qrCodeUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <div className="text-sm text-muted-foreground">
                    <QRCodeSVG value={qrCodeUrl} size={200} />
                    请扫码支付（或点击打开支付链接）
                  </div>
                </a>
              )}
              {renderStatus()}
            </div>
          )}
        </DialogContent>
      </Dialog>
      <PaymentResultDialog
        isOpen={showResult}
        onOpenChange={handleResultClose}
        status={resultStatus}
        points={tier.points}
      />
    </>
  );
}
