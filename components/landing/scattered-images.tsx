"use client"

import { cn } from "@/lib/utils"
import { GridMotion } from "@/components/ui/grid-motion"
import { SAMPLE_IMAGES } from "@/constants/landing/images"
import { useState, useEffect, useCallback } from "react"

interface ScatteredImagesProps {
  className?: string
}

function shuffleArray<T>(array: T[]): T[] {
  const newArray = [...array]
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]]
  }
  return newArray
}

export function ScatteredImages({ className }: ScatteredImagesProps) {
  const [items, setItems] = useState<any[]>([])
  const [isHovering, setIsHovering] = useState(false)

  const generateItems = useCallback(() => {
    // Convert sample images to the correct format
    const formattedImages = SAMPLE_IMAGES.map(image => ({
      type: 'image' as const,
      src: image.src,
      alt: image.alt,
      width: image.width,
      height: image.height
    }))

    // 确保有足够的图片用于三行，每行7张
    const requiredImages = 21 // 3 rows × 7 columns
    const repeatedImages = [...formattedImages]
    while (repeatedImages.length < requiredImages) {
      repeatedImages.push(...formattedImages)
    }

    // 将图片分成三组，每组7张不重复的图片
    const shuffledImages = shuffleArray(repeatedImages)
    const result: any[] = []

    for (let row = 0; row < 3; row++) {
      const rowImages = new Set<string>() // 用于检查重复
      const rowStart = row * 7

      for (let col = 0; col < 7; col++) {
        let imageIndex = rowStart + col
        let attempts = 0
        const maxAttempts = 100 // 防止无限循环

        // 尝试找到一个不重复的图片
        while (attempts < maxAttempts) {
          const image = shuffledImages[imageIndex % shuffledImages.length]
          if (!rowImages.has(image.src)) {
            result.push(image)
            rowImages.add(image.src)
            break
          }
          imageIndex++
          attempts++
        }

        // 如果实在找不到不重复的图片，就使用当前图片
        if (attempts >= maxAttempts) {
          const image = shuffledImages[imageIndex % shuffledImages.length]
          result.push(image)
        }
      }
    }

    return result
  }, [])

  useEffect(() => {
    setItems(generateItems())
  }, [generateItems])

  // Set up automatic shuffling every 30 seconds when not hovering
  useEffect(() => {
    if (isHovering) return

    const interval = setInterval(() => {
      setItems(generateItems())
    }, 30000)

    return () => clearInterval(interval)
  }, [isHovering, generateItems])

  return (
    <div
      className={cn("w-full relative overflow-hidden pb-8", className)}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <GridMotion
        items={items}
        gradientColor="hsl(var(--brand))"
        isHovering={isHovering}
      />
    </div>
  )
}
