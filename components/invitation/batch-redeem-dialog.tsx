"use client";

import { useState } from "react";
import { useInvitationStore } from "@/store/invitation";
import { useUserStore } from "@/components/global/user-initializer";
import { useToast } from "@/lib/hooks/use-toast";
import { formatNumber } from "@/lib/utils";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";

interface BatchRedeemDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
  isAdmin?: boolean;
  redeemAll?: boolean;
}

export function BatchRedeemDialog({
  open,
  onOpenChange,
  onSuccess,
  isAdmin = false,
  redeemAll = false,
}: BatchRedeemDialogProps) {
  const { toast } = useToast();
  const refreshUserCredits = useUserStore(state => state.refreshUserInfo);
  const {
    usages,
    selectedUsageIds,
    batchRedeemLoading,
    redeemBatchPoints,
    redeemBatchCash,
  } = useInvitationStore();

  const [redeemType, setRedeemType] = useState<"points" | "cash">("points");
  const [note, setNote] = useState("");

  // 计算选中记录的总积分和总现金
  const selectedUsages = redeemAll
    ? usages.filter(u => u.status === "ready")
    : usages.filter(u => selectedUsageIds.includes(u.id));

  const totalPoints = selectedUsages.reduce(
    (sum, usage) => sum + (usage.pointsAwarded || 0),
    0
  );

  const totalCash = selectedUsages.reduce(
    (sum, usage) => sum + (usage.cashAwarded || 0),
    0
  );

  // 处理兑换
  const handleRedeem = async () => {
    try {
      // 获取要兑换的记录ID
      const usageIds = redeemAll
        ? usages.filter(u => u.status === "ready").map(u => u.id)
        : selectedUsageIds;

      if (usageIds.length === 0) {
        toast({
          title: "错误",
          description: "没有选择要兑换的记录",
          variant: "destructive",
        });
        return;
      }

      // 根据兑换类型调用不同的方法
      let result;
      if (redeemType === "points") {
        result = await redeemBatchPoints(usageIds);
      } else if (isAdmin && redeemType === "cash") {
        result = await redeemBatchCash(usageIds, note);
      } else {
        toast({
          title: "错误",
          description: "无效的兑换类型",
          variant: "destructive",
        });
        return;
      }

      // 显示成功消息
      const successCount = result.results?.successful?.length || 0;
      const failedCount = result.results?.failed?.length || 0;

      toast({
        title: "兑换成功",
        description: `成功兑换 ${successCount} 条记录${failedCount > 0 ? `，${failedCount} 条记录失败` : ''}`,
        variant: "default",
      });

      // 关闭对话框
      onOpenChange(false);

      // 刷新用户信息（更新钱包余额等）
      // 在对话框关闭后进行，避免 UI 闪烁
      await refreshUserCredits();

      // 调用成功回调
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error redeeming batch:", error);
      toast({
        title: "兑换失败",
        description: error instanceof Error ? error.message : "批量兑换失败，请重试",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>批量兑换奖励</DialogTitle>
          <DialogDescription>
            {redeemAll
              ? "将一次性兑换所有待兑换的记录"
              : "将兑换选中的记录"}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* 兑换类型选择（仅管理员可选择） */}
          {isAdmin ? (
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="redeem-type" className="text-right">
                兑换类型
              </label>
              <Select
                value={redeemType}
                onValueChange={(value) => setRedeemType(value as "points" | "cash")}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择兑换类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="points">积分</SelectItem>
                  <SelectItem value="cash">现金</SelectItem>
                </SelectContent>
              </Select>
            </div>
          ) : null}

          {/* 兑换数量显示 */}
          <div className="grid grid-cols-4 items-center gap-4">
            <span className="text-right">兑换数量</span>
            <div className="col-span-3">
              <span className="font-medium">
                {selectedUsages.length} 条记录
              </span>
            </div>
          </div>

          {/* 积分总额显示 */}
          {redeemType === "points" && (
            <div className="grid grid-cols-4 items-center gap-4">
              <span className="text-right">积分总额</span>
              <div className="col-span-3">
                <span className="font-medium text-blue-600">
                  {formatNumber(totalPoints)} 积分
                </span>
              </div>
            </div>
          )}

          {/* 现金总额显示 */}
          {redeemType === "cash" && (
            <div className="grid grid-cols-4 items-center gap-4">
              <span className="text-right">现金总额</span>
              <div className="col-span-3">
                <span className="font-medium text-green-600">
                  ¥{(totalCash / 100).toFixed(2)}
                </span>
              </div>
            </div>
          )}

          {/* 备注（仅管理员兑换现金时显示） */}
          {isAdmin && redeemType === "cash" && (
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="note" className="text-right">
                备注
              </label>
              <Textarea
                id="note"
                value={note}
                onChange={(e) => setNote(e.target.value)}
                placeholder="可选备注信息"
                className="col-span-3"
              />
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={batchRedeemLoading}
          >
            取消
          </Button>
          <Button
            onClick={handleRedeem}
            disabled={selectedUsages.length === 0 || batchRedeemLoading}
          >
            {batchRedeemLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                处理中...
              </>
            ) : (
              `确认兑换${redeemType === "points" ? "积分" : "现金"}`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
