import { CreativePricing } from "./creative-pricing"
import type { PricingTier } from "@/constants/draw/pricing"
import { Gift, Star, Crown, Rocket } from "lucide-react";
import { PRICING_TIERS } from "@/constants/draw/pricing";

const pricingTiers: PricingTier[] = [
  {
    id: PRICING_TIERS[0].id,
    name: PRICING_TIERS[0].name,
    icon: <Gift className="w-6 h-6" />,
    price: PRICING_TIERS[0].price,
    points: PRICING_TIERS[0].points,
    description: PRICING_TIERS[0].description,
    color: PRICING_TIERS[0].color,
    features: PRICING_TIERS[0].features,
  },
  {
    id: PRICING_TIERS[1].id,
    name: PRICING_TIERS[1].name,
    icon: <Star className="w-6 h-6" />,
    price: PRICING_TIERS[1].price,
    points: PRICING_TIERS[1].points,
    description: PRICING_TIERS[1].description,
    color: PRICING_TIERS[1].color,
    features: PRICING_TIERS[1].features,
    popular: PRICING_TIERS[1].popular,
  },
  {
    id: PRICING_TIERS[2].id,
    name: PRICING_TIERS[2].name,
    icon: <Rocket className="w-6 h-6" />,
    price: PRICING_TIERS[2].price,
    points: PRICING_TIERS[2].points,
    description: PRICING_TIERS[2].description,
    color: PRICING_TIERS[2].color,
    features: PRICING_TIERS[2].features,
    popular: PRICING_TIERS[2].popular,
  },
  {
    id: PRICING_TIERS[3].id,
    name: PRICING_TIERS[3].name,
    icon: <Crown className="w-6 h-6" />,
    price: PRICING_TIERS[3].price,
    points: PRICING_TIERS[3].points,
    description: PRICING_TIERS[3].description,
    color: PRICING_TIERS[3].color,
    features: PRICING_TIERS[3].features,
    popular: PRICING_TIERS[3].popular,
  },
];

export function PricingContent() {
  return <CreativePricing tiers={pricingTiers} />;
}
