'use client';

import Link from "next/link";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import type { PricingTier } from "@/constants/draw/pricing";

interface PricingButtonProps {
    tier: PricingTier;
    onOpenPaymentDialog?: (tier: PricingTier) => void;
}

export function PricingButton({ tier, onOpenPaymentDialog }: PricingButtonProps) {

    if (tier.price === 0) {
        return (
            <Link href="/draw">
                <Button
                    className={cn(
                        "w-full h-12 font-handwritten text-lg relative",
                        "border-2 border-zinc-900 dark:border-white",
                        "transition-all duration-300",
                        "shadow-[4px_4px_0px_0px] shadow-zinc-900 dark:shadow-white",
                        "hover:shadow-[6px_6px_0px_0px]",
                        "hover:translate-x-[-2px] hover:translate-y-[-2px]",
                        "bg-zinc-50 dark:bg-zinc-800",
                        "text-zinc-900 dark:text-white",
                        "hover:bg-white dark:hover:bg-zinc-700",
                        "active:bg-zinc-50 dark:active:bg-zinc-800"
                    )}
                >
                    立即开始
                </Button>
            </Link>
        );
    }

    return (
        <Button
            onClick={() => onOpenPaymentDialog?.(tier)}
            className={cn(
                "w-full h-12 font-handwritten text-lg relative",
                "border-2 border-zinc-900 dark:border-white",
                "transition-all duration-300",
                "shadow-[4px_4px_0px_0px] shadow-zinc-900 dark:shadow-white",
                "hover:shadow-[6px_6px_0px_0px]",
                "hover:translate-x-[-2px] hover:translate-y-[-2px]",
                tier.popular
                ? [
                    "bg-amber-400 text-zinc-900",
                    "hover:bg-amber-300",
                    "active:bg-amber-400",
                    "dark:hover:bg-amber-300",
                    "dark:active:bg-amber-400",
                    ]
                : [
                    "bg-zinc-50 dark:bg-zinc-800",
                    "text-zinc-900 dark:text-white",
                    "hover:bg-white dark:hover:bg-zinc-700",
                    "active:bg-zinc-50 dark:active:bg-zinc-800",
                    ]
            )}
        >
            立即充值
        </Button>
    );
}
