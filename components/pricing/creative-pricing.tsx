"use client";

import Link from "next/link";
import { useState } from "react";
import { Check, Lock, Unlock } from "lucide-react";

import { cn } from "@/lib/utils";
import {
  getModelList, PRICING_TIERS,
  type PricingTier,
} from "@/constants/draw/pricing";
import { useProfileStore } from "@/store/profile";

import { Badge } from "@/components/ui/badge";
import { useUserStore } from "@/components/global/user-initializer";

import { PricingButton } from "./pricing-button";
import { PaymentDialog } from "../payment/payment-dialog";

function CreativePricing({
  tag = "按次付费",
  title = "高质量绘图，无需办理 PLUS 会员",
  tiers = PRICING_TIERS,
}: {
  tag?: string;
  title?: string;
  tiers?: PricingTier[];
}) {
  const freePackage = tiers.find((tier) => tier.id === "package-free");
  const nonFreePackages = tiers.filter((tier) => tier.id !== "package-free");
  const modelList = getModelList();

  // 获取用户付费状态
  const { profile } = useProfileStore();
  const { currentUser } = useUserStore();
  const isPaid = profile?.isPaid || (currentUser as any)?.isPaid || false;

  // 支付弹窗状态
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [selectedTier, setSelectedTier] = useState<PricingTier | null>(null);

  // 处理付费模型点击
  const handlePaidModelClick = () => {
    const proPackage = tiers.find((tier) => tier.id === "package-b");
    if (proPackage) {
      setSelectedTier(proPackage);
      setIsPaymentDialogOpen(true);
    }
  };

  // 处理打开支付弹窗
  const handleOpenPaymentDialog = (tier: PricingTier) => {
    setSelectedTier(tier);
    setIsPaymentDialogOpen(true);
  };

  return (
    <>
      <div className="w-full max-w-6xl mx-auto px-4 pt-8">
        <div className="text-center space-y-6 mb-16">
        <div className="relative">
          <h2 className="text-4xl md:text-5xl font-bold font-handwritten text-zinc-900 dark:text-white rotate-[-1deg]">
            {title}
            <div className="absolute -right-12 top-0 text-amber-500 rotate-12">
              ✨
            </div>
            <div className="absolute -left-8 bottom-0 text-blue-500 -rotate-12">
              ⭐️
            </div>
          </h2>
          <div
            className="absolute -bottom-4 left-1/2 -translate-x-1/2 w-44 h-3 bg-blue-500/20
                      rotate-[-1deg] rounded-full blur-sm"
          />
        </div>
        <p className="font-handwritten text-xl text-zinc-600 dark:text-zinc-400 rotate-[-1deg]">
          <div></div>
          <Badge
            variant="outline"
            className="font-handwritten text-md text-indigo-500 mr-2"
          >
            {tag}
          </Badge>
          免费注册即送 {freePackage?.points} 积分，立即
          <Link href="/draw" className="text-blue-500 hover:text-blue-600 ml-2">
            开始创作
          </Link>
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 lg:gap-8">
        {nonFreePackages.map((tier, index) => (
          <div
            key={tier.name}
            className={cn(
              "relative group mb-8",
              "transition-all duration-300",
              index === 1 && "rotate-[-1deg]",
              index === 2 && "rotate-[1deg]",
              index === 3 && "rotate-[-2deg]"
            )}
          >
            <div
              className={cn(
                "absolute inset-0 bg-white dark:bg-zinc-900",
                "border-2 border-zinc-900 dark:border-white",
                "rounded-lg shadow-[4px_4px_0px_0px] shadow-zinc-900 dark:shadow-white",
                "transition-all duration-300",
                "group-hover:shadow-[8px_8px_0px_0px]",
                "group-hover:translate-x-[-4px]",
                "group-hover:translate-y-[-4px]"
              )}
            />

            <div className="relative p-6">
              {tier.popular && (
                <div
                  className="absolute -top-2 -right-2 bg-amber-400 text-zinc-900
                  font-handwritten px-3 py-1 rounded-full rotate-12 text-sm border-2 border-zinc-900"
                >
                  最受欢迎
                </div>
              )}

              <div className="mb-6">
                <div
                  className={cn(
                    "w-12 h-12 rounded-full mb-4",
                    "flex items-center justify-center",
                    "border-2 border-zinc-900 dark:border-white",
                    `text-${tier.color}-500`
                  )}
                >
                  {tier.icon}
                </div>
                <h3 className="font-handwritten text-2xl text-zinc-900 dark:text-white">
                  {tier.name}
                </h3>
                <p className="font-handwritten text-zinc-600 dark:text-zinc-400">
                  {tier.description}
                </p>
              </div>

              {/* Price */}
              <div className="mb-6 font-handwritten">
                <span className="text-4xl font-bold text-zinc-900 dark:text-white">
                  {tier.price === 0 ? "FREE" : `¥${tier.price}`}
                </span>
              </div>

              <div className="space-y-3 mb-6">
                {tier.features.map((feature) => (
                  <div key={feature} className="flex items-center gap-3">
                    <div
                      className="w-5 h-5 rounded-full border-2 border-zinc-900
                      dark:border-white flex items-center justify-center"
                    >
                      <Check className="w-3 h-3" />
                    </div>
                    <span className="font-handwritten text-lg text-zinc-900 dark:text-white">
                      {feature}
                    </span>
                  </div>
                ))}
              </div>

              <PricingButton
                tier={tier}
                onOpenPaymentDialog={handleOpenPaymentDialog}
              />
            </div>
          </div>
        ))}
      </div>

      <div className="relative group my-10 w-full">
        <div
          className={cn(
            "absolute inset-0 bg-white dark:bg-zinc-900",
            "border-2 border-zinc-900 dark:border-white",
            "rounded-lg shadow-[4px_4px_0px_0px] shadow-zinc-900 dark:shadow-white",
            "transition-all duration-300",
            "group-hover:shadow-[8px_8px_0px_0px]",
            "group-hover:translate-x-[-4px]",
            "group-hover:translate-y-[-4px]"
          )}
        />
        <div className="relative p-6">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
            {modelList.map((model) => (
              <div
                key={model.id}
                className={cn(
                  "relative bg-white dark:bg-zinc-800 rounded-md border px-2.5 py-1.5 transition-all",
                  model.paidOnly && !isPaid
                    ? "border-amber-200 dark:border-amber-800 opacity-75 cursor-pointer"
                    : "border-zinc-200 dark:border-zinc-700 hover:shadow-md"
                )}
                onClick={() => {
                  if (model.paidOnly && !isPaid) {
                    // 如果是付费模型且用户未付费，触发支付弹窗
                    handlePaidModelClick();
                  }
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span
                      className={cn(
                        "font-handwritten text-sm font-semibold truncate mr-1.5",
                        model.paidOnly && !isPaid
                          ? "text-zinc-500 dark:text-zinc-400"
                          : "text-zinc-900 dark:text-white"
                      )}
                    >
                      {model.name}
                    </span>
                    {model.paidOnly &&
                      (isPaid ? (
                        <Unlock className="h-3 w-3 text-green-500" />
                      ) : (
                        <Lock className="h-3 w-3 text-amber-500" />
                      ))}
                  </div>
                  <span
                    className={cn(
                      "text-xs font-medium whitespace-nowrap mr-1",
                      model.paidOnly && !isPaid
                        ? "text-zinc-400"
                        : "text-blue-500"
                    )}
                  >
                    {model.points}分/次
                  </span>
                </div>
                {model.type === "image" && (
                  <span className="absolute -top-1 -right-1 text-xs text-amber-500">
                    ✨
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="absolute -z-10 inset-0 overflow-hidden">
        <div className="absolute top-40 left-20 text-4xl rotate-12">✎</div>
        <div className="absolute bottom-40 right-20 text-4xl -rotate-12">
          ✏️
        </div>
      </div>
    </div>

    {/* 支付弹窗 */}
    {selectedTier && (
      <PaymentDialog
        tier={selectedTier}
        isOpen={isPaymentDialogOpen}
        onOpenChange={setIsPaymentDialogOpen}
      />
    )}
    </>
  );
}

export { CreativePricing }
