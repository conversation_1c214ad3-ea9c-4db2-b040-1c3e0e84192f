"use client";

import { FC, useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { format, formatDistanceToNow } from "date-fns";
import { zhCN } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import { List, Grid, Settings, Share2, ExternalLink, Trash } from "lucide-react";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { drawModels } from "@/constants/draw/models";
import { DRAW_STYLES } from "@/constants/draw";
import { HistoryItemDetailsModal } from "./history-item-details-modal";
import { HistoryStatusBadge } from "./history-status-badge";
import { useRouter } from "next/navigation";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/lib/hooks/use-toast";

export interface HistoryItem {
  id: string;
  userId: string;
  status: boolean;
  resultUrl: string | null;
  prompt: string;
  description: string | null;
  pointsUsed: number;
  parameters: {
    model: string;
    [key: string]: any;
  };
  extra?: {
    model: string;
    style?: string;
    [key: string]: any;
  };
  createdAt: string;
  drawStatus?: "PENDING" | "PROCESSING" | "SUCCESS" | "FAILED";
  drawResult?: string;
  share?: {
    id: string;
    shareId: string;
    isPublic: boolean;
    allowFork: boolean;
  };
}

interface ImagePreviewProps {
  url: string | null;
  id?: string;
  size?: 'sm' | 'lg';
  drawStatus?: "PENDING" | "PROCESSING" | "SUCCESS" | "FAILED";
  item?: HistoryItem;
  onItemClick?: (item: HistoryItem) => void;
}

const ImagePreview: FC<ImagePreviewProps> = ({ url, size = 'sm', drawStatus, item, onItemClick }) => {
  // 处理中状态显示骨架屏
  const isProcessing = drawStatus === "PROCESSING";

  if (isProcessing) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-gray-50 rounded",
        size === 'sm' ? "w-16 h-16" : "w-full h-48"
      )}>
        <div className="animate-pulse">
          <div className={cn(
            "bg-gray-200 rounded",
            size === 'sm' ? "w-14 h-14" : "w-32 h-32"
          )}></div>
        </div>
      </div>
    );
  }

  if (!url) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-gray-50 rounded",
        size === 'sm' ? "w-16 h-16" : "w-full h-48"
      )}>
        <span className={cn(
          "text-red-500",
          size === 'sm' ? "text-2xl" : "text-4xl"
        )}>❌</span>
      </div>
    );
  }

  const imageClasses = cn(
    "object-contain rounded cursor-pointer",
    size === 'sm' ? "h-16 w-auto" : "h-48 w-auto"
  );

  return (
    <div
      className="relative flex items-center justify-center cursor-pointer"
      onClick={() => onItemClick && item && onItemClick(item)}
    >
      <img
        src={url}
        alt="Preview"
        className={imageClasses}
      />
    </div>
  );
};

export const getModelName = (modelId: string) => {
  const model = drawModels.find(m => m.id === modelId);
  return model ? model.name : modelId;
};

export const getStyleName = (styleId: string) => {
  const style = DRAW_STYLES[styleId as keyof typeof DRAW_STYLES];
  return style ? style.name : styleId;
};

export const History: FC = () => {
  const { toast } = useToast();
  const [histories, setHistories] = useState<HistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [displayMode, setDisplayMode] = useState<"list" | "gallery">("list");
  const [selectedItem, setSelectedItem] = useState<HistoryItem | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const [sharingItem, setSharingItem] = useState<HistoryItem | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingItem, setDeletingItem] = useState<string | null>(null);
  const router = useRouter();
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    totalCount: 0,
    totalPages: 1,
    hasNextPage: false,
    hasPrevPage: false,
  });
  const [filters, setFilters] = useState<{
    status: string;
    drawStatus: string;
    model: string;
    style: string;
  }>({
    status: "true",
    drawStatus: "SUCCESS",
    model: "all",
    style: "all",
  });

  const fetchHistories = async (page = pagination.page) => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (filters.status !== "all") params.append("status", filters.status);
      if (filters.drawStatus !== "all") params.append("drawStatus", filters.drawStatus);
      if (filters.model !== "all") params.append("model", filters.model);
      if (filters.style !== "all") params.append("style", filters.style);

      params.append("includeShare", "true");
      params.append("page", page.toString());
      params.append("limit", pagination.limit.toString());

      const response = await fetch(`/api/history?${params.toString()}`);
      if (!response.ok) throw new Error("Failed to fetch histories");
      const data = await response.json();
      console.log('Histories response:', data);
      setHistories(data.histories || []);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching histories:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateShare = async () => {
    if (!sharingItem) return;

    try {
      setIsLoading(true);
      const response = await fetch("/api/shares", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          historyId: sharingItem.id,
          isPublic: true,
          allowFork: true,
        }),
      });

      if (!response.ok) throw new Error("Failed to create share");

      const data = await response.json();
      setIsShareDialogOpen(false);
      router.push(`/settings/sharing/${data.id}`);
    } catch (error) {
      console.error("Error creating share:", error);
      toast({
        title: "错误",
        description: "创建分享失败",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteConfirm = (id: string) => {
    setDeletingItem(id);
    setIsDeleteDialogOpen(true);
  };

  const handleDelete = async () => {
    if (!deletingItem) return;

    try {
      const response = await fetch(`/api/history/${deletingItem}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete history");

      toast({
        title: "成功",
        description: "历史记录已删除",
      });

      // 重新获取历史记录
      fetchHistories();
    } catch (error) {
      console.error("Error deleting history:", error);
      toast({
        title: "错误",
        description: "删除历史记录失败",
        variant: "destructive",
      });
    } finally {
      setIsDeleteDialogOpen(false);
      setDeletingItem(null);
    }
  };

  useEffect(() => {
    fetchHistories();
  }, [filters]);

  return (
    <div className="py-4 px-2 md:px-4 relative">
      <div className="p-4 sm:p-8 rounded-xl bg-white shadow-[0_5px_15px_rgba(0,0,0,0.08),0_15px_35px_-5px_rgba(25,28,33,0.2)] ring-1 ring-gray-950/5 w-full">
        <div className="flex justify-between items-center mb-6">
          <div className="items-center gap-2">
            <h2 className="text-[0.9375rem] font-semibold">我的画廊</h2>
            <p className="text-xs text-blue-500 font-medium">
              请及时存储图片到本地
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant={displayMode === "list" ? "default" : "outline"}
              size="icon"
              onClick={() => setDisplayMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={displayMode === "gallery" ? "default" : "outline"}
              size="icon"
              onClick={() => setDisplayMode("gallery")}
            >
              <Grid className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex flex-row flex-wrap gap-4 mb-6">
          <div className="w-[120px] space-y-2">
            <Label>处理状态</Label>
            <Select
              value={filters.drawStatus}
              onValueChange={(value) =>
                setFilters({ ...filters, drawStatus: value })
              }
            >
              <SelectTrigger className="md:w-[120px]">
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="PENDING">待处理</SelectItem>
                <SelectItem value="PROCESSING">处理中</SelectItem>
                <SelectItem value="SUCCESS">已完成</SelectItem>
                <SelectItem value="FAILED">处理失败</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="w-[120px] space-y-2">
            <Label>
              结果{" "}
              <small className="text-xs text-gray-500">(失败不扣积分)</small>
            </Label>
            <Select
              value={filters.status}
              onValueChange={(value) =>
                setFilters({ ...filters, status: value })
              }
            >
              <SelectTrigger className="md:w-[120px]">
                <SelectValue placeholder="选择结果" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="true">成功</SelectItem>
                <SelectItem value="false">失败</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="w-[120px] space-y-2">
            <Label>模型</Label>
            <Select
              value={filters.model}
              onValueChange={(value) =>
                setFilters({ ...filters, model: value })
              }
            >
              <SelectTrigger className="md:w-[120px]">
                <SelectValue placeholder="选择模型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                {drawModels.map((model) => (
                  <SelectItem key={model.id} value={model.id}>
                    {model.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="w-[120px] space-y-2">
            <Label>风格</Label>
            <Select
              value={filters.style}
              onValueChange={(value) =>
                setFilters({ ...filters, style: value })
              }
            >
              <SelectTrigger className="md:w-[120px]">
                <SelectValue placeholder="选择风格" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                {Object.entries(DRAW_STYLES).map(([key, style]) => (
                  <SelectItem key={key} value={key}>
                    {style.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {displayMode === "list" ? (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[100px]">预览</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>模型</TableHead>
                  <TableHead>风格</TableHead>
                  <TableHead>积分</TableHead>
                  <TableHead>时间</TableHead>
                  <TableHead className="w-[100px]">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center">
                      加载中...
                    </TableCell>
                  </TableRow>
                ) : histories.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center">
                      暂无数据
                    </TableCell>
                  </TableRow>
                ) : (
                  histories.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <ImagePreview
                          url={item.resultUrl}
                          size="sm"
                          drawStatus={item.drawStatus}
                          item={item}
                          onItemClick={(item) => {
                            setSelectedItem(item);
                            setIsDetailsModalOpen(true);
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <HistoryStatusBadge
                          status={item.status}
                          drawStatus={item.drawStatus}
                          variant="span"
                        />
                      </TableCell>
                      <TableCell>
                        {getModelName(item.extra?.model || "")}
                      </TableCell>
                      <TableCell>
                        {getStyleName(item.extra?.style || "")}
                      </TableCell>
                      <TableCell>
                        {item.drawStatus === "PROCESSING" ? (
                          <div className="animate-pulse">
                            <div className="h-4 w-8 bg-gray-200 rounded"></div>
                          </div>
                        ) : (
                          <span
                            className={cn(
                              "text-sm",
                              item.status && !item?.extra?.isWebp
                                ? "text-green-500"
                                : "text-red-500 line-through"
                            )}
                          >
                            {item.pointsUsed}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="cursor-help">
                                {formatDistanceToNow(new Date(item.createdAt), {
                                  addSuffix: true,
                                  locale: zhCN
                                })}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent>
                              {format(new Date(item.createdAt), "yyyy-MM-dd HH:mm:ss")}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                      <TableCell>
                        <div className="flex justify-end">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setSelectedItem(item);
                              setIsDetailsModalOpen(true);
                            }}
                          >
                            <Settings className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() =>
                              router.push(`/settings/history/${item.id}`)
                            }
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                          {item.status &&
                            item.resultUrl &&
                            !item?.extra?.isWebp && (
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => {
                                  if (item.share) {
                                    router.push(
                                      `/settings/sharing/${item.share.id}`
                                    );
                                  } else {
                                    setSharingItem(item);
                                    setIsShareDialogOpen(true);
                                  }
                                }}
                              >
                                <Share2 className="h-4 w-4" />
                              </Button>
                            )}
                          {item.drawStatus !== "PROCESSING" && (
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteConfirm(item.id)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {loading ? (
              <div className="col-span-full text-center">加载中...</div>
            ) : histories.length === 0 ? (
              <div className="col-span-full text-center">暂无数据</div>
            ) : (
              histories.map((item) => (
                <Card key={item.id}>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <ImagePreview
                        url={item.resultUrl}
                        size="lg"
                        drawStatus={item.drawStatus}
                        item={item}
                        onItemClick={(item) => {
                          setSelectedItem(item);
                          setIsDetailsModalOpen(true);
                        }}
                      />
                      <div className="text-sm text-muted-foreground space-y-1">
                        <div>
                          模型: {getModelName(item.extra?.model || "-")}
                        </div>
                        <div>
                          风格: {getStyleName(item.extra?.style || "-")}
                        </div>
                        <div>
                          积分:
                          {item.drawStatus === "PROCESSING" ? (
                            <div className="inline-block animate-pulse ml-1">
                              <div className="h-4 w-8 bg-gray-200 rounded"></div>
                            </div>
                          ) : (
                            <span
                              className={cn(
                                "text-sm ml-1",
                                item.status && !item?.extra?.isWebp
                                  ? "text-green-500"
                                  : "text-red-500 line-through"
                              )}
                            >
                              {item.pointsUsed}
                            </span>
                          )}
                        </div>
                        <div>
                          时间:{" "}
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="cursor-help">
                                  {formatDistanceToNow(new Date(item.createdAt), {
                                    addSuffix: true,
                                    locale: zhCN
                                  })}
                                </span>
                              </TooltipTrigger>
                              <TooltipContent>
                                {format(new Date(item.createdAt), "yyyy-MM-dd HH:mm:ss")}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </div>
                      <div className="flex items-center justify-between pt-2 border-t">
                        <HistoryStatusBadge
                          status={item.status}
                          drawStatus={item.drawStatus}
                          variant="span"
                        />
                        <div className="flex">
                          {item.drawStatus !== "PROCESSING" && (
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteConfirm(item.id)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          )}
                          {item.status &&
                            item.resultUrl &&
                            !item?.extra?.isWebp && (
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => {
                                  if (item.share) {
                                    router.push(
                                      `/settings/sharing/${item.share.id}`
                                    );
                                  } else {
                                    setSharingItem(item);
                                    setIsShareDialogOpen(true);
                                  }
                                }}
                              >
                                <Share2 className="h-4 w-4" />
                              </Button>
                            )}
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setSelectedItem(item);
                              setIsDetailsModalOpen(true);
                            }}
                          >
                            <Settings className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() =>
                              router.push(`/settings/history/${item.id}`)
                            }
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )}

        {/* Pagination Controls */}
        {!loading && pagination.totalPages > 1 && (
          <div className="flex justify-between items-center mt-6">
            <div className="text-sm text-muted-foreground">
              共 {pagination.totalCount} 条记录，第 {pagination.page} /{" "}
              {pagination.totalPages} 页
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchHistories(pagination.page - 1)}
                disabled={!pagination.hasPrevPage}
              >
                上一页
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchHistories(pagination.page + 1)}
                disabled={!pagination.hasNextPage}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </div>

      <HistoryItemDetailsModal
        item={selectedItem}
        isOpen={isDetailsModalOpen}
        onOpenChange={setIsDetailsModalOpen}
        onItemUpdate={(updatedItem) => {
          // Update the item in the histories array
          setHistories(
            histories.map((h) => (h.id === updatedItem.id ? updatedItem : h))
          );
          // Update the selected item
          setSelectedItem(updatedItem);
        }}
      />

      <AlertDialog open={isShareDialogOpen} onOpenChange={setIsShareDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>创建分享</AlertDialogTitle>
            <AlertDialogDescription>
              确定要创建分享吗？创建后可以在分享管理中查看和修改分享设置。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleCreateShare} disabled={isLoading}>
              {isLoading ? "创建中..." : "确定"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除这条历史记录吗？此操作无法撤销，相关的图片和分享将被删除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
