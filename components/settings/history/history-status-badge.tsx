"use client";

import { FC } from "react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { DrawStatus } from "@/lib/db/schema";

interface HistoryStatusBadgeProps {
  status: boolean;
  drawStatus?: DrawStatus;
  className?: string;
  variant?: "badge" | "span";
}

export const HistoryStatusBadge: FC<HistoryStatusBadgeProps> = ({
  status,
  drawStatus = "SUCCESS",
  className,
  variant = "badge",
}) => {
  // 只有 status 为 true 和 drawStatus 为 SUCCESS 才是成功
  const isSuccess = status && drawStatus === "SUCCESS";

  // 如果 status 为 false，则根据 drawStatus 显示不同状态
  const getStatusText = () => {
    if (!status) {
      // 失败状态下，根据 drawStatus 显示不同的失败原因
      switch (drawStatus) {
        case "PENDING":
          return "等待中";
        case "PROCESSING":
          return "处理中";
        case "FAILED":
          return "处理失败";
        default:
          return "生成失败";
      }
    }

    // 成功状态
    return "成功";
  };

  // 获取状态对应的颜色样式
  const getStatusColorClass = () => {
    if (isSuccess) {
      return "bg-green-100 text-green-800";
    }

    if (!status && drawStatus === "PROCESSING") {
      return "bg-blue-100 text-blue-800";
    }

    return "bg-red-100 text-red-800";
  };

  if (variant === "span") {
    return (
      <span
        className={cn(
          "px-2 py-1 rounded-full text-xs",
          getStatusColorClass(),
          className
        )}
      >
        {getStatusText()}
      </span>
    );
  }

  // 获取Badge的variant
  const getBadgeVariant = () => {
    if (isSuccess) {
      return "default";
    }

    if (!status && drawStatus === "PROCESSING") {
      return "outline";
    }

    return "destructive";
  };

  // 获取Badge的额外样式
  const getBadgeClass = () => {
    if (isSuccess) {
      return "bg-green-500 hover:bg-green-600";
    }

    if (!status && drawStatus === "PROCESSING") {
      return "bg-blue-500 text-white hover:bg-blue-600";
    }

    return "";
  };

  return (
    <Badge
      variant={getBadgeVariant()}
      className={cn(
        getBadgeClass(),
        className
      )}
    >
      {getStatusText()}
    </Badge>
  );
};
