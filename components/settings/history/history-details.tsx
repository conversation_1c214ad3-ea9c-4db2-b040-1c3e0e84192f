"use client";

import { FC, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { marked } from 'marked';
import { ChevronLeft, Image, Calendar, CreditCard, Share2, Trash2 } from "lucide-react";
import { format } from "date-fns";

import { useToast } from "@/lib/hooks/use-toast";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { TransactionsTable, Transaction } from "@/components/shared/transactions-table";
import { HistoryStatusBadge } from "./history-status-badge";

interface HistoryExtra {
  model?: string;
  style?: string;
  originalImages?: string[];
  transactions?: Transaction[];
  [key: string]: any;
}

interface HistoryDetail {
  id: string;
  userId: string;
  status: boolean;
  resultUrl: string | null;
  prompt: string;
  description: string | null;
  pointsUsed: number;
  parameters: {
    model: string;
    [key: string]: any;
  };
  extra: HistoryExtra;
  createdAt: string;
  updatedAt: string;
  drawStatus?: "PENDING" | "PROCESSING" | "SUCCESS" | "FAILED";
  drawResult?: string;
  share?: {
    id: string;
    shareId: string;
    isPublic: boolean;
    allowFork: boolean;
    viewCount: number;
    likeCount: number;
    forkCount: number;
  };
}

interface HistoryDetailsProps {
  id: string;
}

export const HistoryDetails: FC<HistoryDetailsProps> = ({ id }) => {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [history, setHistory] = useState<HistoryDetail | null>(null);
  const [isCreatingShare, setIsCreatingShare] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  useEffect(() => {
    const fetchHistoryDetails = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/history/${id}`);

        if (!response.ok) {
          throw new Error("Failed to fetch history details");
        }

        const data = await response.json();
        setHistory(data);
      } catch (error) {
        console.error("Error fetching history details:", error);
        toast({
          title: "错误",
          description: "获取历史记录详情失败，请重试",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchHistoryDetails();
  }, [id, toast]);

  const handleCreateShare = async () => {
    if (!history || !history.status) return;

    try {
      setIsCreatingShare(true);
      const response = await fetch("/api/shares", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          historyId: history.id,
          isPublic: true,
          allowFork: true,
        }),
      });

      if (!response.ok) throw new Error("Failed to create share");

      const data = await response.json();
      router.push(`/settings/sharing/${data.id}`);
    } catch (error) {
      console.error("Error creating share:", error);
      toast({
        variant: "destructive",
        description: "创建分享失败",
      });
    } finally {
      setIsCreatingShare(false);
    }
  };

  const handleCopyPrompt = () => {
    if (!history?.prompt) return;

    navigator.clipboard.writeText(history.prompt);
    toast({
      description: "提示词已复制到剪贴板",
    });
  };

  const handleDelete = async () => {
    if (!history) return;

    try {
      setIsDeleting(true);
      const response = await fetch(`/api/history/${history.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete history");
      }

      toast({
        title: "成功",
        description: "历史记录已删除",
      });

      // 返回到历史记录列表页
      router.push("/settings/history");
    } catch (error) {
      console.error("Error deleting history:", error);
      toast({
        variant: "destructive",
        title: "错误",
        description: "删除历史记录失败，请重试",
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  const getModelName = (model: string) => {
    const modelMap: Record<string, string> = {
      "sd15": "Stable Diffusion 1.5",
      "sd21": "Stable Diffusion 2.1",
      "sdxl": "Stable Diffusion XL",
      "sdxl-lightning": "SDXL Lightning",
      "dalle3": "DALL-E 3",
      "midjourney": "Midjourney",
    };
    return modelMap[model] || model;
  };

  const getStyleName = (style: string) => {
    const styleMap: Record<string, string> = {
      "realistic": "写实风格",
      "anime": "动漫风格",
      "3d": "3D 渲染",
      "pixel": "像素艺术",
      "painting": "绘画风格",
      "sketch": "素描风格",
    };
    return styleMap[style] || style;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-40 mb-2" />
            <Skeleton className="h-4 w-60" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!history) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <h1 className="text-2xl font-bold">历史记录不存在</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center py-8 text-muted-foreground">未找到该历史记录信息</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { extra } = history;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={() => router.back()}>
          <ChevronLeft className="h-4 w-4 mr-2" />
          返回
        </Button>
        <h1 className="text-2xl font-bold">历史记录详情</h1>
        <div className="ml-auto">
          <div className="flex justify-end gap-2">
            {/* 删除按钮 - 处理中状态下隐藏 */}
            {history.drawStatus !== "PROCESSING" && (
              <AlertDialog
                open={showDeleteDialog}
                onOpenChange={setShowDeleteDialog}
              >
                <AlertDialogTrigger asChild>
                  <Button
                    variant="destructive"
                    className="flex items-center gap-2"
                    disabled={isDeleting}
                  >
                    <Trash2 className="h-4 w-4" />
                    {isDeleting ? "删除中..." : "删除"}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>确认删除</AlertDialogTitle>
                    <AlertDialogDescription>
                      您确定要删除这条历史记录吗？此操作无法撤销，相关的图片和相关分享将被删除。
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>取消</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDelete}
                      className="bg-red-500 hover:bg-red-600"
                    >
                      确认删除
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}

            {/* 分享按钮 */}
            {history.status && history.resultUrl && !history?.extra?.isWebp && (
              <div className="flex justify-end">
                {history.share ? (
                  <a
                    href={`${process.env.NEXT_PUBLIC_APP_URL}/explore/${history.share?.shareId}`}
                    target="_blank"
                  >
                    <Button className="flex items-center gap-2">
                      <Share2 className="h-4 w-4" />
                      查看分享
                    </Button>
                  </a>
                ) : (
                  <Button
                    className="flex items-center gap-2"
                    onClick={handleCreateShare}
                    disabled={isCreatingShare}
                  >
                    <Share2 className="h-4 w-4" />
                    {isCreatingShare ? "创建中..." : "创建分享"}
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 图片预览 */}
      <Card>
        <CardHeader>
          <CardTitle>
            <div className="flex items-center justify-between gap-2">
              <span>生成结果</span>
              <CardDescription>
                <HistoryStatusBadge
                  status={history.status}
                  drawStatus={history.drawStatus}
                />
              </CardDescription>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative h-[300px] flex items-center justify-center bg-gray-50 rounded-lg">
            {history.drawStatus === "PROCESSING" ? (
              <div className="animate-pulse">
                <div className="h-48 w-48 bg-gray-200 rounded"></div>
              </div>
            ) : history.resultUrl ? (
              <a
                href={history.resultUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="w-full h-full flex items-center justify-center"
              >
                <img
                  src={history.resultUrl}
                  alt="Preview"
                  className="w-full h-full object-contain cursor-pointer"
                />
              </a>
            ) : history.drawResult ? (
              <div
                className="markdown-content mt-4 h-[300px] overflow-y-auto"
                dangerouslySetInnerHTML={{
                  __html: marked.parse(history.drawResult),
                }}
              ></div>
            ) : (
              <span className="text-4xl text-red-500">❌</span>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-2">
        {/* 基本信息 */}
        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
            <CardDescription>历史记录基本信息概览</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <CreditCard className="h-8 w-8 mb-2 text-primary" />
                {history.drawStatus === "PROCESSING" ? (
                  <div className="animate-pulse">
                    <div className="h-8 w-16 bg-gray-200 rounded mx-auto"></div>
                  </div>
                ) : (
                  <span
                    className={cn(
                      "text-2xl font-bold",
                      history.status && !history?.extra?.isWebp
                        ? "text-green-600"
                        : "text-red-600 line-through"
                    )}
                  >
                    {history.pointsUsed}
                  </span>
                )}
                <span className="text-sm text-muted-foreground">积分</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <Image className="h-8 w-8 mb-2 text-primary" />
                <span className="text-lg font-medium">
                  {getModelName(extra?.model || "-")}
                </span>
                <span className="text-sm text-muted-foreground">模型</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <Calendar className="h-8 w-8 mb-2 text-primary" />
                <span className="text-lg font-medium">
                  {format(new Date(history.createdAt), "yyyy-MM-dd")}
                </span>
                <span className="text-sm text-muted-foreground">创建日期</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <span className="text-lg font-medium">
                  {getStyleName(extra?.style || "-")}
                </span>
                <span className="text-sm text-muted-foreground">风格</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 详细信息 */}
        <Card>
          <CardHeader>
            <CardTitle>详细信息</CardTitle>
            <CardDescription>历史记录详细信息</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">ID:</span>
                <span className="text-muted-foreground font-mono text-sm">
                  {history.id}
                </span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">状态:</span>
                <HistoryStatusBadge
                  status={history.status}
                  drawStatus={history.drawStatus}
                />
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">模型:</span>
                <span>{getModelName(extra?.model || "-")}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">风格:</span>
                <span>{getStyleName(extra?.style || "-")}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">积分:</span>
                {history.drawStatus === "PROCESSING" ? (
                  <div className="animate-pulse">
                    <div className="h-4 w-10 bg-gray-200 rounded"></div>
                  </div>
                ) : (
                  <span
                    className={cn(
                      history.status && !history?.extra?.isWebp
                        ? "text-green-600"
                        : "text-red-600 line-through"
                    )}
                  >
                    {history.pointsUsed}
                  </span>
                )}
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">创建时间:</span>
                <span>
                  {format(new Date(history.createdAt), "yyyy-MM-dd HH:mm:ss")}
                </span>
              </div>
              <div className="flex justify-between py-2">
                <span className="font-medium">更新时间:</span>
                <span>
                  {format(new Date(history.updatedAt), "yyyy-MM-dd HH:mm:ss")}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 提示词 */}
      <Card>
        <CardHeader>
          <CardTitle>提示词</CardTitle>
          <CardDescription>生成图片使用的提示词</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-muted p-4 rounded-md whitespace-pre-wrap">
              {history.prompt || "未设置提示词"}
            </div>
            {history.prompt && (
              <div className="flex justify-end">
                <Button variant="outline" size="sm" onClick={handleCopyPrompt}>
                  复制提示词
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 描述 */}
      {history.description && (
        <Card>
          <CardHeader>
            <CardTitle>描述</CardTitle>
            <CardDescription>生成图片的描述</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-muted p-4 rounded-md whitespace-pre-wrap">
              {history.description}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 参数 */}
      {history.parameters && Object.keys(history.parameters).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>参数</CardTitle>
            <CardDescription>生成图片使用的参数</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(history.parameters).map(([key, value]) => (
                <div key={key} className="flex justify-between py-2 border-b">
                  <span className="font-medium">{key}:</span>
                  <span className="text-muted-foreground">
                    {typeof value === "object"
                      ? JSON.stringify(value)
                      : String(value)}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 交易记录 */}
      <Card>
        <CardHeader>
          <CardTitle>关联交易记录</CardTitle>
          <CardDescription>与此历史记录相关的交易记录</CardDescription>
        </CardHeader>
        <CardContent>
          <TransactionsTable transactions={extra.transactions || []} />
        </CardContent>
      </Card>
    </div>
  );
};
