'use client';

import Link from "next/link";
import { marked } from 'marked';
import { format } from "date-fns";
import { useState } from "react";
import { useRouter } from "next/navigation";

import { cn } from "@/lib/utils";
import { useToast } from "@/lib/hooks/use-toast";

import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { useUserStore } from "@/components/global/user-initializer";

import { HistoryItem } from "./history";
import { getModelName } from "./history";
import { getStyleName } from "./history";

interface HistoryItemDetailsModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  item: HistoryItem | null;
  onItemUpdate?: (updatedItem: HistoryItem) => void;
}

export function HistoryItemDetailsModal({ isOpen, onOpenChange, item, onItemUpdate }: HistoryItemDetailsModalProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const currentUser = useUserStore(state => state.currentUser);

  if (!item) {
    return null;
  }

  const handleCreateShare = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/shares", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          historyId: item.id,
          isPublic: true,
          allowFork: true,
        }),
      });

      if (!response.ok) throw new Error("Failed to create share");

      const data = await response.json();

      // Update the item with the new share data
      if (item && onItemUpdate) {
        const updatedItem = {
          ...item,
          share: {
            id: data.id,
            shareId: data.shareId,
            isPublic: data.isPublic,
            allowFork: data.allowFork,
            viewCount: data.viewCount || 0,
            likeCount: data.likeCount || 0,
            forkCount: data.forkCount || 0,
          }
        };
        onItemUpdate(updatedItem);
      }

      // Show success message
      toast({
        variant: "default",
        title: "创建成功",
        description: "分享已创建",
        className: "bg-green-500 text-white border-green-600",
      });
    } catch (error) {
      console.error("Error creating share:", error);
      toast({
        variant: "destructive",
        description: "创建分享失败",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyPrompt = async () => {
    if (!item.prompt) return;

    try {
      await navigator.clipboard.writeText(item.prompt);
      toast({
        variant: "default",
        title: "复制成功",
        description: "提示词已复制到剪贴板",
        className: "bg-green-500 text-white border-green-600",
      });
    } catch (err) {
      toast({
        variant: "destructive",
        description: "复制失败，请重试",
      });
    }
  };

  const handleReplicate = () => {
    // 检查是否是自己的历史记录
    if (currentUser?.id !== item.userId) {
      toast({
        variant: "destructive",
        description: "只能复刻自己的历史记录",
      });
      return;
    }

    // 关闭当前模态框
    onOpenChange(false);
    // 跳转到绘图页面，带上historyId参数
    router.push(`/draw?historyId=${item.id}`);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            <span>历史记录详情</span>
            <span className="mx-1">-</span>
            <Link
              href={`/settings/history/${item.id}`}
              className="text-blue-500 hover:underline"
            >
              查看
            </Link>
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          <div className="relative h-[300px] flex items-center justify-center bg-gray-50 rounded-lg">
            {item.drawStatus === "PROCESSING" ? (
              <div className="animate-pulse">
                <div className="h-48 w-48 bg-gray-200 rounded"></div>
              </div>
            ) : item.resultUrl ? (
              <a
                href={item.resultUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="w-full h-full flex items-center justify-center"
              >
                <img
                  src={item.resultUrl}
                  alt="Preview"
                  className="w-full h-full object-contain cursor-pointer"
                />
              </a>
            ) : item.drawResult ? (
              <div
                className="markdown-content mt-4 h-[300px] overflow-y-auto"
                dangerouslySetInnerHTML={{
                  __html: marked.parse(item.drawResult),
                }}
              ></div>
            ) : (
              <span className="text-4xl text-red-500">❌</span>
            )}

          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="font-medium">模型</h3>
                <p className="text-sm text-muted-foreground">
                  {getModelName(item.extra?.model || "-")}
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="font-medium">风格</h3>
                <p className="text-sm text-muted-foreground">
                  {getStyleName(item.extra?.style || "-")}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="font-medium">创建时间</h3>
                <p className="text-sm text-muted-foreground">
                  {format(new Date(item.createdAt), "yyyy-MM-dd HH:mm")}
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="font-medium">消费积分</h3>
                <p
                  className={cn(
                    "text-sm",
                    item.status && !item?.extra?.isWebp
                      ? "text-green-500"
                      : "text-red-500 line-through"
                  )}
                >
                  {item.pointsUsed}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="font-medium">提示词</h3>
                <div className="flex items-center gap-2">
                  {item.prompt ? (
                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <p className="text-sm text-muted-foreground cursor-help">
                          已设置
                        </p>
                      </HoverCardTrigger>
                      <HoverCardContent className="w-80">
                        <p className="text-sm">{item.prompt}</p>
                      </HoverCardContent>
                    </HoverCard>
                  ) : (
                    <p className="text-sm text-muted-foreground">未设置</p>
                  )}
                  {item.prompt && (
                    <button
                      onClick={handleCopyPrompt}
                      className="text-sm text-blue-500 hover:text-blue-600"
                    >
                      复制
                    </button>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="font-medium">上传图片</h3>
                <Badge variant="outline" className="text-sm">
                  {Array.isArray(item.extra?.originalImages)
                    ? item.extra.originalImages.length
                    : 0}{" "}
                  张
                </Badge>
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            {item.status && (
              <div className="flex gap-2">
                {item.status &&
                  item.resultUrl &&
                  !item?.extra?.isWebp &&
                  (!item.share ? (
                    <Button
                      className="flex-1"
                      variant="outline"
                      onClick={handleCreateShare}
                      disabled={isLoading}
                    >
                      {isLoading ? "创建中..." : "创建分享"}
                    </Button>
                  ) : (
                    <a
                      href={`${process.env.NEXT_PUBLIC_APP_URL}/explore/${item.share?.shareId}`}
                      target="_blank"
                      className="flex-1"
                    >
                      <Button className="w-full" variant="outline">
                        查看分享
                      </Button>
                    </a>
                  ))
                }
                <Button className="flex-1" onClick={handleReplicate}>
                  一键复刻
                </Button>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
