"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/lib/hooks/use-toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatNumber } from "@/lib/utils";

interface InvitationUsage {
  id: string;
  invitationId: string;
  invitation: {
    inviteCode: string;
    inviteType: "points" | "cash" | "both";
    refRatio: number;
  };
  referee: {
    clerkId: string;
    username: string;
    email: string;
    avatarUrl?: string;
  };
  registeredAt: string;
  firstRechargeAt?: string;
  rechargeAmount?: number;
  pointsAwarded?: number;
  cashAwarded?: number;
  status: "pending" | "ready" | "completed" | "void";
  redeemedAt?: string;
  extra?: {
    redeemType?: "points" | "cash";
    [key: string]: any;
  };
}

interface InvitationUsageListProps {
  refreshTrigger?: number;
  onRedeemSuccess?: () => void;
}

export function InvitationUsageList({ refreshTrigger = 0, onRedeemSuccess }: InvitationUsageListProps) {
  // const { toast } = useToast();
  const [usages, setUsages] = useState<InvitationUsage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  // const [redeemingId, setRedeemingId] = useState<string | null>(null);
  // const [redeemLoading, setRedeemLoading] = useState(false);

  useEffect(() => {
    async function fetchUsages() {
      try {
        setLoading(true);
        setError(null);

        const url = statusFilter === "all"
          ? "/api/invitation_usages"
          : `/api/invitation_usages?status=${statusFilter}`;

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error("Failed to fetch invitation usages");
        }

        const data = await response.json();
        setUsages(data.usages || []);
      } catch (error) {
        console.error("Error fetching invitation usages:", error);
        setError(error instanceof Error ? error.message : "Failed to load invitation usages");
      } finally {
        setLoading(false);
      }
    }

    fetchUsages();
  }, [statusFilter, refreshTrigger]);

  // 处理积分兑换
  // const handleRedeemPoints = async (usageId: string) => {
  //   try {
  //     // 防止重复点击
  //     if (redeemLoading) return;

  //     setRedeemingId(usageId);
  //     setRedeemLoading(true);

  //     const response = await fetch(`/api/invitation_usages/${usageId}/redeem`, {
  //       method: "POST",
  //       headers: {
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify({
  //         redeem_type: "points",
  //       }),
  //     });

  //     if (!response.ok) {
  //       const errorData = await response.json();
  //       throw new Error(errorData.error || "兑换积分失败");
  //     }

  //     // 更新本地状态 - 标记为已完成并记录兑换类型
  //     setUsages(prevUsages =>
  //       prevUsages.map(usage =>
  //         usage.id === usageId
  //           ? {
  //               ...usage,
  //               status: "completed",
  //               redeemedAt: new Date().toISOString(),
  //               extra: { ...usage.extra, redeemType: "points" }
  //             }
  //           : usage
  //       )
  //     );

  //     // 调用成功回调
  //     if (onRedeemSuccess) {
  //       onRedeemSuccess();
  //     }

  //     toast({
  //       title: "兑换成功",
  //       description: "积分奖励已成功兑换",
  //       className: "bg-green-500 text-white border-green-600",
  //     });
  //   } catch (error) {
  //     console.error("兑换积分失败:", error);
  //     toast({
  //       variant: "destructive",
  //       title: "兑换失败",
  //       description: error instanceof Error ? error.message : "兑换积分时出错",
  //     });
  //   } finally {
  //     setRedeemingId(null);
  //     setRedeemLoading(false);
  //   }
  // };



  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="bg-gray-100">待充值</Badge>;
      case "ready":
        return <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300">待兑换</Badge>;
      case "completed":
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">已兑换</Badge>;
      case "void":
        return <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">已作废</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return <Skeleton className="h-64 w-full" />;
  }

  if (error) {
    return (
      <div className="p-4 border border-red-200 rounded-md bg-red-50">
        <h3 className="text-lg font-medium text-red-800 mb-2">加载失败</h3>
        <p className="text-red-600">{error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => onRedeemSuccess ? onRedeemSuccess() : null}
        >
          重试
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {usages.length === 0 ? (
        <div className="p-8 text-center border rounded-md bg-gray-50">
          <p className="text-muted-foreground">暂无邀请记录</p>
        </div>
      ) : (
        <div className="rounded-md border">
          <div className="flex justify-between items-center p-2 border-b">
            <div></div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => (onRedeemSuccess ? onRedeemSuccess() : null)}
              >
                刷新
              </Button>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="筛选状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="pending">待充值</SelectItem>
                  <SelectItem value="ready">待兑换</SelectItem>
                  <SelectItem value="completed">已兑换</SelectItem>
                  <SelectItem value="void">已作废</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>被邀请人</TableHead>
                <TableHead>邀请码</TableHead>
                <TableHead>注册时间</TableHead>
                <TableHead>充值情况</TableHead>
                <TableHead>奖励</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>操作记录</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {usages.map((usage) => (
                <TableRow key={usage.id}>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={usage.referee.avatarUrl} />
                        <AvatarFallback>{usage.referee.username}</AvatarFallback>
                      </Avatar>
                      <div className="text-sm text-muted-foreground">
                        {usage.referee.email}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{usage.invitation.inviteCode}</TableCell>
                  <TableCell>
                    {format(new Date(usage.registeredAt), "yyyy-MM-dd")}
                  </TableCell>
                  <TableCell>
                    {usage.firstRechargeAt ? (
                      <div>
                        <div>
                          {format(
                            new Date(usage.firstRechargeAt),
                            "yyyy-MM-dd"
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          ¥{((usage.rechargeAmount || 0) / 100).toFixed(2)}
                        </div>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">未充值</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {/* 根据状态和兑换类型显示奖励 */}
                    {usage.status === "ready" ? (
                      // 待兑换状态显示所有可能的奖励
                      <>
                        {usage.pointsAwarded ? (
                          <div className="text-blue-600">
                            {formatNumber(usage.pointsAwarded)} 积分
                          </div>
                        ) : null}
                        {usage.cashAwarded ? (
                          <div className="text-green-600">
                            ¥{((usage.cashAwarded || 0) / 100).toFixed(2)}
                          </div>
                        ) : null}
                      </>
                    ) : usage.status === "completed" ? (
                      // 已兑换状态只显示已兑换的奖励类型
                      <>
                        {usage.extra?.redeemType === "points" &&
                        usage.pointsAwarded ? (
                          <div className="text-blue-600">
                            {formatNumber(usage.pointsAwarded)} 积分
                            <div className="text-xs text-muted-foreground">
                              已兑换
                            </div>
                          </div>
                        ) : usage.extra?.redeemType === "cash" &&
                          usage.cashAwarded ? (
                          <div className="text-green-600">
                            ¥{((usage.cashAwarded || 0) / 100).toFixed(2)}
                            <div className="text-xs text-muted-foreground">
                              已兑换
                            </div>
                          </div>
                        ) : (
                          // 兼容旧数据，没有redeemType字段的情况
                          <>
                            {usage.pointsAwarded ? (
                              <div className="text-blue-600">
                                {formatNumber(usage.pointsAwarded)} 积分
                                <div className="text-xs text-muted-foreground">
                                  已兑换
                                </div>
                              </div>
                            ) : null}
                            {usage.cashAwarded ? (
                              <div className="text-green-600">
                                ¥{((usage.cashAwarded || 0) / 100).toFixed(2)}
                                <div className="text-xs text-muted-foreground">
                                  已兑换
                                </div>
                              </div>
                            ) : null}
                          </>
                        )}
                      </>
                    ) : (
                      // 其他状态（待充值、作废等）
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>{getStatusBadge(usage.status)}</TableCell>
                  <TableCell>
                    {usage.status === "completed" && usage.redeemedAt ? (
                      <span className="text-sm text-muted-foreground">
                        {format(new Date(usage.redeemedAt), "yyyy-MM-dd")}
                      </span>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}
