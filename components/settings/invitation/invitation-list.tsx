"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { <PERSON><PERSON>, ExternalLink, Eye } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/lib/hooks/use-toast";
import { formatNumber } from "@/lib/utils";
import { generateInviteLink } from "@/constants/invitation";

interface Invitation {
  id: string;
  inviteCode: string;
  inviteType: "points" | "cash" | "both";
  refRatio: number;
  channel?: string;
  maxUses: number;
  expiresAt?: string;
  createdAt: string;
  usageCount: number;
  readyCount: number;
  completedCount: number;
}

interface InvitationListProps {
  refreshTrigger?: number;
  onViewUsages?: () => void;
}

export function InvitationList({ refreshTrigger = 0, onViewUsages }: InvitationListProps) {
  const { toast } = useToast();
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchInvitations() {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch("/api/invitations");

        if (!response.ok) {
          throw new Error("Failed to fetch invitations");
        }

        const data = await response.json();
        setInvitations(data.invitations || []);
      } catch (error) {
        console.error("Error fetching invitations:", error);
        setError(error instanceof Error ? error.message : "Failed to load invitations");
      } finally {
        setLoading(false);
      }
    }

    fetchInvitations();
  }, [refreshTrigger]);

  const copyInviteLink = (inviteCode: string) => {
    const link = generateInviteLink(inviteCode);
    navigator.clipboard.writeText(link);
    toast({
      title: "已复制",
      description: "邀请链接已复制到剪贴板",
      className: "bg-green-500 text-white border-green-600",
    });
  };

  const getInviteTypeText = (type: "points" | "cash" | "both") => {
    switch (type) {
      case "points": return "积分奖励";
      case "cash": return "现金奖励";
      case "both": return "积分或现金";
      default: return type;
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardHeader>
          <CardTitle className="text-red-600">加载失败</CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error}</p>
        </CardContent>
        <CardFooter>
          <Button variant="outline" onClick={() => window.location.reload()}>
            重试
          </Button>
        </CardFooter>
      </Card>
    );
  }

  if (invitations.length === 0) {
    return (
      <Card className="border-gray-200 bg-gray-50">
        <CardHeader>
          <CardTitle>暂无邀请码</CardTitle>
          <CardDescription>
            创建您的第一个邀请码，开始邀请好友吧
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">我的邀请码</h3>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>邀请码</TableHead>
              <TableHead>奖励类型</TableHead>
              <TableHead>奖励比例</TableHead>
              <TableHead>使用情况</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {invitations.map((invitation) => (
              <TableRow key={invitation.id}>
                <TableCell className="font-medium">
                  {invitation.inviteCode}
                  {invitation.channel && (
                    <Badge variant="outline" className="ml-2">
                      {invitation.channel}
                    </Badge>
                  )}
                </TableCell>
                <TableCell>{getInviteTypeText(invitation.inviteType)}</TableCell>
                <TableCell>{(invitation.refRatio * 100).toFixed(0)}%</TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    <span>
                      已使用: {formatNumber(invitation.usageCount)}
                      {invitation.maxUses > 0 && `/${formatNumber(invitation.maxUses)}`}
                    </span>
                    {invitation.readyCount > 0 && (
                      <span className="text-amber-500 text-sm">
                        待兑换: {formatNumber(invitation.readyCount)}
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    <span>{format(new Date(invitation.createdAt), "yyyy-MM-dd")}</span>
                    {invitation.expiresAt && (
                      <span className="text-sm text-muted-foreground">
                        过期: {format(new Date(invitation.expiresAt), "yyyy-MM-dd")}
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => copyInviteLink(invitation.inviteCode)}
                      title="复制邀请链接"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <a
                      href={generateInviteLink(invitation.inviteCode)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center justify-center h-8 w-8 rounded-md text-sm font-medium ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground"
                      title="打开邀请链接"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </a>
                    {onViewUsages && (
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={onViewUsages}
                        title="查看使用记录"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
