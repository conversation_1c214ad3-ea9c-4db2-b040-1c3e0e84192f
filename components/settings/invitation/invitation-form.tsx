"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { nanoid } from "nanoid";
import { CalendarIcon, RefreshCcw } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { useToast } from "@/lib/hooks/use-toast";

const formSchema = z.object({
  invite_code: z.string().min(3).max(20).optional(),
  invite_type: z.enum(["points", "cash", "both"]),
  ref_ratio: z.coerce.number().min(0.01).max(1),
  channel: z.string().optional(),
  max_uses: z.coerce.number().int().min(0).optional(),
  expires_at: z.date().optional(),
});

interface InvitationFormProps {
  onSuccess?: () => void;
}

export function InvitationForm({ onSuccess }: InvitationFormProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      invite_code: "",
      invite_type: "points",
      ref_ratio: 0.1,
      channel: "",
      max_uses: 0,
    },
  });

  const generateRandomCode = () => {
    form.setValue("invite_code", nanoid(8));
  };

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setLoading(true);

      // 准备请求数据
      const requestData = {
        ...values,
        expires_at: values.expires_at ? values.expires_at.toISOString() : undefined,
      };

      // 发送请求
      const response = await fetch("/api/invitations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "创建邀请码失败");
      }

      // 重置表单
      form.reset({
        invite_code: "",
        invite_type: "points",
        ref_ratio: 0.1,
        channel: "",
        max_uses: 0,
        expires_at: undefined,
      });

      // 调用成功回调
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("创建邀请码失败:", error);
      toast({
        variant: "destructive",
        title: "创建失败",
        description: error instanceof Error ? error.message : "创建邀请码时出错",
      });
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="bg-gray-50 p-4 rounded-lg border">
      <h3 className="text-lg font-medium mb-4">创建邀请码</h3>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="invite_code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>邀请码</FormLabel>
                  <div className="flex gap-2">
                    <FormControl>
                      <Input
                        placeholder="留空自动生成"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={generateRandomCode}
                    >
                      <RefreshCcw className="h-4 w-4" />
                    </Button>
                  </div>
                  <FormDescription>
                    自定义邀请码或留空自动生成
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="invite_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>奖励类型</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择奖励类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="points">积分奖励</SelectItem>
                      <SelectItem value="cash">现金奖励</SelectItem>
                      <SelectItem value="both">积分或现金</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    被邀请人充值后您将获得的奖励类型
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="ref_ratio"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>奖励比例</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0.01"
                      max="1"
                      placeholder="0.1"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    充值金额的奖励比例，如0.1表示10%
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="channel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>渠道标记</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="可选"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormDescription>
                    可选，用于标记邀请来源
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="max_uses"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>最大使用次数</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      placeholder="0表示不限制"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    0表示不限制使用次数
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="expires_at"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>过期时间</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "yyyy-MM-dd")
                          ) : (
                            <span>不设置则永不过期</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date < new Date(new Date().setHours(0, 0, 0, 0))
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormDescription>
                    不设置则永不过期
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? "创建中..." : "创建邀请码"}
          </Button>
        </form>
      </Form>
    </div>
  );
}
