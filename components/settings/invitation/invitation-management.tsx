"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { InvitationActivation } from "./invitation-activation";
import { InvitationUsageList } from "./invitation-usage-list";
import { PendingRedemptionList } from "./invitation-pending-redemption-list";
import { InvitationStats } from "./invitation-stats";
import { useToast } from "@/lib/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export function InvitationManagement() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("pending");
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [hasInvitation, setHasInvitation] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  // 检查用户是否有邀请码
  const checkInvitation = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/invitations?limit=1");

      if (!response.ok) {
        throw new Error("Failed to fetch invitation");
      }

      const data = await response.json();
      setHasInvitation(data.invitations && data.invitations.length > 0);
    } catch (error) {
      console.error("Error checking invitation:", error);
      setHasInvitation(false);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    checkInvitation();
  }, [refreshTrigger, checkInvitation]);

  // 刷新数据的函数
  const refreshData = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // 激活邀请码成功后的回调
  const handleActivateSuccess = () => {
    toast({
      title: "激活成功",
      description: "邀请码已成功激活",
      className: "bg-green-500 text-white border-green-600",
    });
    refreshData();
  };

  // 更新邀请码成功后的回调
  const handleUpdateSuccess = () => {
    toast({
      title: "更新成功",
      description: "邀请码已成功更新",
      className: "bg-green-500 text-white border-green-600",
    });
    refreshData();
  };

  // 兑换奖励成功后的回调
  const handleRedeemSuccess = () => {
    toast({
      title: "兑换成功",
      description: "奖励已成功兑换",
      className: "bg-green-500 text-white border-green-600",
    });
    refreshData();
  };

  // 加载中状态
  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  // 如果用户没有邀请码，只显示激活按钮
  if (hasInvitation === false) {
    return (
      <div className="space-y-6">
        <InvitationActivation
          refreshTrigger={refreshTrigger}
          onActivateSuccess={handleActivateSuccess}
          onUpdateSuccess={handleUpdateSuccess}
        />
      </div>
    );
  }

  // 如果用户有邀请码，显示完整界面
  return (
    <div className="space-y-6">
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>邀请状态</CardTitle>
            <CardDescription>
              <div className="flex items-center justify-end space-x-2">
                <span>积分或奖金二选一领取</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => (checkInvitation ? checkInvitation() : null)}
                >
                  刷新
                </Button>
              </div>
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 邀请统计信息 */}
          <InvitationStats refreshTrigger={refreshTrigger} />

          {/* 邀请码信息 */}
          <InvitationActivation
            refreshTrigger={refreshTrigger}
            onActivateSuccess={handleActivateSuccess}
            onUpdateSuccess={handleUpdateSuccess}
          />
        </CardContent>
      </Card>

      <Tabs
        defaultValue="pending"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="pending">待兑换</TabsTrigger>
          <TabsTrigger value="all">全部邀请</TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="mt-4">
          <PendingRedemptionList
            refreshTrigger={refreshTrigger}
            onRedeemSuccess={handleRedeemSuccess}
            checkInvitation={checkInvitation}
          />
        </TabsContent>

        <TabsContent value="all" className="mt-4">
          <InvitationUsageList
            refreshTrigger={refreshTrigger}
            onRedeemSuccess={checkInvitation}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
