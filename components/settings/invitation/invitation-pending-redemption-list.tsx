"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";

import { useToast } from "@/lib/hooks/use-toast";
import { formatNumber } from "@/lib/utils";
import { useInvitationStore } from "@/store/invitation";

import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { BatchRedeemDialog } from "@/components/invitation/batch-redeem-dialog";

interface InvitationUsage {
  id: string;
  invitationId: string;
  invitation: {
    inviteCode: string;
    inviteType: "points" | "cash" | "both";
    refRatio: number;
  };
  referee: {
    clerkId: string;
    username: string;
    email: string;
    avatarUrl?: string;
  };
  registeredAt: string;
  firstRechargeAt?: string;
  rechargeAmount?: number;
  pointsAwarded?: number;
  cashAwarded?: number;
  status: "pending" | "ready" | "completed" | "void";
  redeemedAt?: string;
}

interface PendingRedemptionListProps {
  refreshTrigger?: number;
  onRedeemSuccess?: () => void;
  checkInvitation?: () => void;
}

export function PendingRedemptionList({
  refreshTrigger = 0,
  onRedeemSuccess,
  checkInvitation
}: PendingRedemptionListProps) {
  const { toast } = useToast();
  const [localUsages, setLocalUsages] = useState<InvitationUsage[]>([]);
  const [localLoading, setLocalLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // const [redeemingId, setRedeemingId] = useState<string | null>(null);
  // const [redeemLoading, setRedeemLoading] = useState(false);
  const [batchRedeemDialogOpen, setBatchRedeemDialogOpen] = useState(false);

  // 使用store
  const {
    usages: storeUsages,
    loadingUsages: storeLoading,
    selectedUsageIds,
    fetchInvitationUsages,
    toggleUsageSelection,
    setSelectedUsageIds,
    clearSelectedUsages
  } = useInvitationStore();

  // 使用store的数据
  const usages = storeUsages.length > 0 ? storeUsages : localUsages;
  const loading = storeUsages.length > 0 ? storeLoading : localLoading;

  useEffect(() => {
    async function fetchPendingUsages() {
      try {
        // 尝试使用store加载数据
        try {
          await fetchInvitationUsages({
            status: "ready",
            limit: 50
          });
          return; // 如果store加载成功，就不需要再加载本地数据
        } catch (storeError) {
          console.error("Error fetching from store, falling back to local:", storeError);
        }

        // 如果store加载失败，则使用本地加载
        setLocalLoading(true);
        setError(null);

        // 只获取待兑换的记录
        const url = "/api/invitation_usages?status=ready";
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error("Failed to fetch pending redemptions");
        }

        const data = await response.json();
        setLocalUsages(data.usages || []);
      } catch (error) {
        console.error("Error fetching pending redemptions:", error);
        setError(error instanceof Error ? error.message : "Failed to load pending redemptions");
      } finally {
        setLocalLoading(false);
      }
    }

    fetchPendingUsages();
  }, [refreshTrigger, fetchInvitationUsages]);

  // 处理批量兑换成功
  const handleBatchRedeemSuccess = () => {
    toast({
      title: "兑换成功",
      description: "批量兑换操作已完成",
      className: "bg-green-500 text-white border-green-600",
    });

    // 清除选中的记录
    clearSelectedUsages();

    // 调用成功回调
    if (onRedeemSuccess) {
      onRedeemSuccess();
    }

    // 调用检查邀请函数刷新数据
    if (checkInvitation) {
      checkInvitation();
    }
  };

  // 处理积分兑换
  // const handleRedeemPoints = async (usageId: string) => {
  //   try {
  //     // 防止重复点击
  //     if (redeemLoading) return;

  //     setRedeemingId(usageId);
  //     setRedeemLoading(true);

  //     const response = await fetch(`/api/invitation_usages/${usageId}/redeem`, {
  //       method: "POST",
  //       headers: {
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify({
  //         redeem_type: "points",
  //       }),
  //     });

  //     if (!response.ok) {
  //       const errorData = await response.json();
  //       throw new Error(errorData.error || "兑换积分失败");
  //     }

  //     // 更新本地状态
  //     if (storeUsages.length > 0) {
  //       // 如果使用的是store，则重新加载数据
  //       await fetchInvitationUsages({
  //         status: "ready",
  //         limit: 50
  //       });
  //     } else {
  //       // 如果使用的是本地数据，则直接更新
  //       setLocalUsages(prevUsages => prevUsages.filter(usage => usage.id !== usageId));
  //     }

  //     // 调用成功回调
  //     if (onRedeemSuccess) {
  //       onRedeemSuccess();
  //     }

  //     // 调用检查邀请函数刷新数据
  //     if (checkInvitation) {
  //       checkInvitation();
  //     }

  //     toast({
  //       title: "兑换成功",
  //       description: "积分奖励已成功兑换",
  //       className: "bg-green-500 text-white border-green-600",
  //     });
  //   } catch (error) {
  //     console.error("兑换积分失败:", error);
  //     toast({
  //       variant: "destructive",
  //       title: "兑换失败",
  //       description: error instanceof Error ? error.message : "兑换积分时出错",
  //     });
  //   } finally {
  //     setRedeemingId(null);
  //     setRedeemLoading(false);
  //   }
  // };



  if (loading) {
    return <Skeleton className="h-32 w-full" />;
  }

  if (error) {
    return (
      <div className="p-4 border border-red-200 rounded-md bg-red-50">
        <h3 className="text-lg font-medium text-red-800 mb-2">加载失败</h3>
        <p className="text-red-600">{error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => checkInvitation ? checkInvitation() : null}
        >
          重试
        </Button>
      </div>
    );
  }

  if (usages.length === 0) {
    return (
      <div className="p-6 text-center border rounded-md bg-gray-50">
        <p className="text-muted-foreground">暂无待兑换奖励</p>
      </div>
    );
  }

  // 判断当前页所有记录是否全部选中
  const isAllSelected = () => {
    if (usages.length === 0) return false;
    return usages.every(usage => selectedUsageIds.includes(usage.id));
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // 选中所有记录
      setSelectedUsageIds(usages.map(usage => usage.id));
    } else {
      // 取消选中所有记录
      clearSelectedUsages();
    }
  };

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <div className="flex justify-end items-center p-2 border-b">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              disabled={selectedUsageIds.length <= 0}
              onClick={() => setBatchRedeemDialogOpen(true)}
            >
              批量兑换 ({selectedUsageIds.length})
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => checkInvitation ? checkInvitation() : null}
            >
              刷新
            </Button>
          </div>
        </div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox
                  checked={isAllSelected()}
                  onCheckedChange={handleSelectAll}
                  aria-label="Select all"
                />
              </TableHead>
              <TableHead>被邀请人</TableHead>
              <TableHead>充值金额</TableHead>
              <TableHead>奖励</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {usages.map((usage) => (
              <TableRow key={usage.id}>
                <TableCell className="w-[50px]">
                  <Checkbox
                    checked={selectedUsageIds.includes(usage.id)}
                    onCheckedChange={() => toggleUsageSelection(usage.id)}
                    aria-label={`Select ${usage.referee.username}`}
                  />
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={usage.referee.avatarUrl} />
                      <AvatarFallback>{usage.referee.username}</AvatarFallback>
                    </Avatar>
                    <div className="text-sm text-muted-foreground">
                      {usage.referee.email}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  {usage.rechargeAmount ? (
                    <>
                      <div className="text-sm text-muted-foreground">
                        {format(
                          new Date(usage.firstRechargeAt || usage.registeredAt),
                          "yyyy-MM-dd"
                        )}
                      </div>
                      <div className="font-medium">
                        ¥{((usage.rechargeAmount || 0) / 100).toFixed(2)}
                      </div>
                    </>
                  ) : (
                    <span className="text-muted-foreground">-</span>
                  )}
                </TableCell>
                <TableCell>
                  {usage.pointsAwarded && usage.pointsAwarded > 0 ? (
                    <div className="text-blue-600">
                      {formatNumber(usage.pointsAwarded)} 积分
                    </div>
                  ) : null}
                  {usage.cashAwarded && usage.cashAwarded > 0 ? (
                    <div className="text-green-600">
                      ¥{((usage.cashAwarded || 0) / 100).toFixed(2)}
                    </div>
                  ) : null}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* 批量兑换对话框 */}
      <BatchRedeemDialog
        open={batchRedeemDialogOpen}
        onOpenChange={setBatchRedeemDialogOpen}
        onSuccess={handleBatchRedeemSuccess}
      />
    </div>
  );
}
