"use client";

import { useState, useEffect } from "react";
import Link from "next/link";

import { formatNumber } from "@/lib/utils";

import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Coins } from "lucide-react";
import { useInvitationStore } from "@/store/invitation";
import { BatchRedeemDialog } from "@/components/invitation/batch-redeem-dialog";
import { useToast } from "@/lib/hooks/use-toast";

interface InvitationStatsProps {
  refreshTrigger?: number;
}

interface StatsData {
  totalInvitations: number;
  pendingCount: number;
  readyCount: number;
  completedCount: number;
  totalPointsAwarded: number;
  totalCashAwarded: number;
  pendingPointsAwarded: number;
  pendingCashAwarded: number;
}

export function InvitationStats({ refreshTrigger = 0 }: InvitationStatsProps) {
  const { toast } = useToast();
  const [batchRedeemDialogOpen, setBatchRedeemDialogOpen] = useState(false);

  // 使用store
  const {
    stats,
    loadingStats,
    fetchInvitationStats,
    fetchInvitationUsages
  } = useInvitationStore();

  // 加载数据
  useEffect(() => {
    async function loadData() {
      try {
        await fetchInvitationStats(true);
      } catch (error) {
        console.error("Error fetching invitation stats:", error);
      }
    }

    loadData();
  }, [fetchInvitationStats, refreshTrigger]);

  // 处理批量兑换按钮点击
  const handleBatchRedeemClick = async () => {
    try {
      // 加载所有待兑换的记录
      await fetchInvitationUsages({
        status: "ready",
        limit: 50 // 最多加载50条记录
      });

      // 打开对话框
      setBatchRedeemDialogOpen(true);
    } catch (error) {
      console.error("Error loading invitation usages:", error);
      toast({
        title: "错误",
        description: "加载邀请记录失败，请重试。",
        variant: "destructive",
      });
    }
  };

  // 处理批量兑换成功
  const handleBatchRedeemSuccess = async () => {
    toast({
      title: "兑换成功",
      description: "批量兑换操作已完成",
      className: "bg-green-500 text-white border-green-600",
    });

    // 刷新统计数据
    await fetchInvitationStats(true);
  };

  return (
    <>
      <div className="grid gap-4 grid-cols-2 md:grid-cols-4">
        <div className="p-3 bg-gray-50 rounded-md">
          <div className="text-sm font-medium mb-1">累计邀请</div>
          {loadingStats ? (
            <Skeleton className="h-7 w-1/2" />
          ) : (
            <div className="text-xl font-bold">
              {formatNumber(stats?.totalInvitations || 0)}
              <span className="text-xs text-muted-foreground ml-1">人</span>
            </div>
          )}
          <p className="text-xs text-muted-foreground mt-1">
            已完成:{" "}
            {loadingStats ? (
              <Skeleton className="h-3 w-8 inline-block" />
            ) : (
              formatNumber(stats?.completedCount || 0)
            )}
          </p>
        </div>

        <div className="p-3 bg-gray-50 rounded-md">
          <div className="text-sm font-medium mb-1">等待兑换</div>
          {loadingStats ? (
            <Skeleton className="h-7 w-1/2" />
          ) : (
            <div className="text-xl font-bold text-amber-500">
              {formatNumber(stats?.readyCount || 0)}
              <span className="text-xs text-muted-foreground ml-1">人</span>
            </div>
          )}
          <p className="text-xs text-muted-foreground mt-1">
            已兑换:{" "}
            {loadingStats ? (
              <Skeleton className="h-3 w-8 inline-block" />
            ) : (
              formatNumber(stats?.completedCount || 0)
            )}
          </p>
        </div>

        <div className="p-3 bg-gray-50 rounded-md">
          <div className="text-sm font-medium mb-1 flex justify-between items-center">
            <span>积分奖励</span>
            {!loadingStats && (stats?.pendingPointsAwarded || 0) > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                onClick={handleBatchRedeemClick}
              >
                <Coins className="h-3 w-3 mr-1" />
                一键兑换
              </Button>
            )}
          </div>
          {loadingStats ? (
            <Skeleton className="h-7 w-1/2" />
          ) : (
            <div className="text-xl font-bold text-blue-500">
              {formatNumber(stats?.pendingPointsAwarded || 0)}
            </div>
          )}
          <p className="text-xs text-muted-foreground mt-1">
            累计领取:{" "}
            {loadingStats ? (
              <Skeleton className="h-3 w-8 inline-block" />
            ) : (
              formatNumber(stats?.totalPointsAwarded || 0)
            )}{" "}
            分
          </p>
        </div>

        <div className="p-3 bg-gray-50 rounded-md">
          <div className="text-sm font-medium mb-1">
            <span>现金奖励</span>
            {!loadingStats && (stats?.pendingCashAwarded || 0) > 1000 ? (
              <Link
                href="/contact"
                className="text-xs text-muted-foreground ml-1"
              >
                (联系管理员)
              </Link>
            ) : (
              <span className="text-xs text-muted-foreground ml-1">
                (10 元起领)
              </span>
            )}
          </div>
          {loadingStats ? (
            <Skeleton className="h-7 w-1/2" />
          ) : (
            <div className="text-xl font-bold text-green-500">
              ¥{((stats?.pendingCashAwarded || 0) / 100).toFixed(2)}
            </div>
          )}
          <p className="text-xs text-muted-foreground mt-1">
            累计领取: ¥{((stats?.totalCashAwarded || 0) / 100).toFixed(2)} 元
          </p>
        </div>
      </div>

      {/* 批量兑换对话框 */}
      <BatchRedeemDialog
        open={batchRedeemDialogOpen}
        onOpenChange={setBatchRedeemDialogOpen}
        onSuccess={handleBatchRedeemSuccess}
        redeemAll={true}
      />
    </>
  );
}
