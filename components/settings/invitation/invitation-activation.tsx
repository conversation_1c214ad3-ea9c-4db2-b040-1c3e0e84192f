"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { Copy, ExternalLink, RefreshCw, Edit2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";

import { useToast } from "@/lib/hooks/use-toast";

// 不再需要 nanoid，因为我们使用自定义的随机码生成函数
import { INVITE_CODE_LENGTH, INVITE_CODE_REGEX, INVITE_CODE_ERROR_MESSAGE, generateInviteLink } from "@/constants/invitation";

interface Invitation {
  id: string;
  inviteCode: string;
  inviteType: string;
  refRatio: number;
  channel?: string;
  maxUses: number;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface InvitationActivationProps {
  refreshTrigger?: number;
  onActivateSuccess?: () => void;
  onUpdateSuccess?: () => void;
}

export function InvitationActivation({
  refreshTrigger = 0,
  onActivateSuccess,
  onUpdateSuccess,

}: InvitationActivationProps) {
  const { toast } = useToast();
  const [invitation, setInvitation] = useState<Invitation | null>(null);
  const [loading, setLoading] = useState(true);
  const [activating, setActivating] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [inviteCode, setInviteCode] = useState("");
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取用户邀请码
  useEffect(() => {
    async function fetchInvitation() {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch("/api/invitations?limit=1");

        if (!response.ok) {
          throw new Error("Failed to fetch invitation");
        }

        const data = await response.json();
        if (data.invitations && data.invitations.length > 0) {
          setInvitation(data.invitations[0]);
          setInviteCode(data.invitations[0].inviteCode);
        } else {
          setInvitation(null);
        }
      } catch (error) {
        console.error("Error fetching invitation:", error);
        setError(error instanceof Error ? error.message : "Failed to load invitation");
      } finally {
        setLoading(false);
      }
    }

    fetchInvitation();
  }, [refreshTrigger]);

  // 激活邀请码
  const handleActivate = async () => {
    try {
      setActivating(true);
      setError(null);

      // 自动生成邀请码，不使用用户输入
      const response = await fetch("/api/invitations/activate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({}),  // 空对象，系统自动生成邀请码
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "激活邀请码失败");
      }

      const data = await response.json();
      setInvitation(data);
      setInviteCode(data.inviteCode);

      if (onActivateSuccess) {
        onActivateSuccess();
      }
    } catch (error) {
      console.error("激活邀请码失败:", error);
      setError(error instanceof Error ? error.message : "激活邀请码时出错");
      toast({
        variant: "destructive",
        title: "激活失败",
        description: error instanceof Error ? error.message : "激活邀请码时出错",
      });
    } finally {
      setActivating(false);
    }
  };

  // 更新邀请码
  const handleUpdate = async () => {
    if (!invitation) return;

    // 验证邀请码格式
    if (!INVITE_CODE_REGEX.test(inviteCode)) {
      toast({
        variant: "destructive",
        title: "验证失败",
        description: INVITE_CODE_ERROR_MESSAGE,
      });
      return;
    }

    try {
      setUpdating(true);
      setError(null);

      const response = await fetch(`/api/invitations/${invitation.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          invite_code: inviteCode,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "更新邀请码失败");
      }

      const data = await response.json();
      setInvitation(data);
      setInviteCode(data.inviteCode);

      if (onUpdateSuccess) {
        onUpdateSuccess();
      }
    } catch (error) {
      console.error("更新邀请码失败:", error);
      setError(error instanceof Error ? error.message : "更新邀请码时出错");
      toast({
        variant: "destructive",
        title: "更新失败",
        description: error instanceof Error ? error.message : "更新邀请码时出错",
      });
    } finally {
      setUpdating(false);
    }
  };

  // 生成随机邀请码
  const generateRandomCode = () => {
    // 生成符合规则的随机邀请码（只包含小写字母和数字）
    const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < INVITE_CODE_LENGTH; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    setInviteCode(result);
  };

  // 复制邀请链接
  const copyInviteLink = () => {
    if (!invitation) return;

    const link = generateInviteLink(invitation.inviteCode);
    navigator.clipboard.writeText(link);
    toast({
      title: "已复制",
      description: "邀请链接已复制到剪贴板",
      className: "bg-green-500 text-white border-green-600",
    });
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (error && !invitation) {
    return (
      <div className="p-4 border border-red-200 rounded-md bg-red-50">
        <h3 className="text-lg font-medium text-red-600 mb-2">加载失败</h3>
        <p className="text-red-600">{error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          重试
        </Button>
      </div>
    );
  }

  // 未激活状态
  if (!invitation) {
    return (
      <div className="space-y-4">
        <div className="py-24 px-8 bg-gray-50 rounded-md text-center space-y-4">
          <Button
            onClick={handleActivate}
            disabled={activating}
            className="w-full md:w-auto"
          >
            {activating ? "激活中..." : "激活您的专属邀请码"}
          </Button>
          <p className="text-muted-foreground text-sm">
            邀请好友加入并获得奖励，激活邀请码即表示您已同意
            <Link
              target="_blank"
              href="/terms/invitation"
              className="text-blue-500 hover:underline ml-1"
            >
              邀请奖励计划条款与细则
            </Link>
          </p>
        </div>
      </div>
    );
  }

  // 已激活状态
  return (
    <>
      <div className="space-y-4">
        <div className="flex flex-col space-y-2">
          <label className="text-sm font-medium">邀请链接</label>
          <div className="p-3 bg-gray-50 rounded-md flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm truncate">
                {generateInviteLink(invitation.inviteCode)}
              </span>
              <Button
                variant="link"
                size="icon"
                onClick={copyInviteLink}
                title="复制邀请链接"
                className="text-blue-500"
              >
                <Copy className="h-4 w-4" />
              </Button>
              <a
                href={generateInviteLink(invitation.inviteCode)}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center h-8 w-8 rounded-md text-blue-500 text-sm font-medium ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
                title="打开邀请链接"
              >
                <ExternalLink className="h-4 w-4" />
              </a>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setEditDialogOpen(true)}
                title="编辑邀请码"
              >
                <Edit2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        <div className="flex flex-col space-y-2">
          <label className="text-sm font-medium">奖励信息</label>
          <div className="p-3 bg-gray-50 rounded-md">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">奖励类型</p>
                <p className="font-medium">
                  {invitation.inviteType === "points"
                    ? "积分奖励"
                    : invitation.inviteType === "cash"
                    ? "现金奖励"
                    : "积分或现金"}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">奖励比例</p>
                <p className="font-medium">
                  {(invitation.refRatio * 100).toFixed(0)}%
                </p>
              </div>
            </div>
          </div>
          <p className="text-sm text-muted-foreground">
            <span>被邀请人首次充值后，您将获得相应比例的奖励</span>
            <Link href="/terms/invitation" className="ml-1 text-blue-500 hover:underline">了解详情</Link>
          </p>
        </div>
      </div>

      {/* 编辑邀请码对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>编辑邀请码</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="flex flex-col space-y-2">
              <label className="text-sm font-medium">邀请码</label>
              <div className="flex gap-2">
                <Input
                  value={inviteCode}
                  onChange={(e) => setInviteCode(e.target.value)}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={generateRandomCode}
                  title="生成随机邀请码"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <Button
              onClick={() => {
                handleUpdate();
                setEditDialogOpen(false);
              }}
              disabled={updating || inviteCode === invitation.inviteCode}
              className="w-full"
            >
              {updating ? "更新中..." : "更新邀请码"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
