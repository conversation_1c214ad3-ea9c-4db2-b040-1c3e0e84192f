"use client";

import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { AdminNavItem } from "@/components/global/admin-nav-item";

const tabs = [
  {
    id: "profile",
    label: "个人信息",
    href: "/settings",
  },
  {
    id: "history",
    label: "我的画廊",
    href: "/settings/history",
  },
  {
    id: "sharing",
    label: "分享管理",
    href: "/settings/sharing",
  },
  {
    id: "invitation",
    label: "邀请管理",
    href: "/settings/invitation",
  },
  {
    id: "orders",
    label: "充值历史",
    href: "/settings/orders",
  },
];

export function SettingsNav() {
  const pathname = usePathname();

  const isActiveTab = (href: string) => {
    if (href === "/settings" && pathname === "/settings") {
      return true;
    }
    return href !== "/settings" && pathname.startsWith(href);
  };

  return (
    <div className="w-full md:w-32 flex-shrink-0">
      <div className="md:space-y-2 md:sticky md:top-24 pl-2 md:pl-0">
        <div className="flex flex-wrap justify-center md:justify-start md:flex-col overflow-x-auto md:overflow-x-visible gap-2 pb-2 md:pb-0">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              variant={isActiveTab(tab.href) ? "default" : "ghost"}
              className={cn(
                "whitespace-nowrap justify-start text-left",
                isActiveTab(tab.href)
                  ? "bg-accent text-accent-foreground hover:text-white"
                  : "hover:bg-accent hover:text-accent-foreground"
              )}
              asChild
            >
              <Link href={tab.href}>{tab.label}</Link>
            </Button>
          ))}
          <Button
            variant="ghost"
            className="whitespace-nowrap justify-start text-left"
          >
            <AdminNavItem />
          </Button>
        </div>
      </div>
    </div>
  );
}
