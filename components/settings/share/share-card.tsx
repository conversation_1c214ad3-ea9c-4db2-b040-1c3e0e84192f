import Link from "next/link";
import { useState } from "react";
import { Icon } from "@iconify/react";

import { Card, CardContent } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { useToast } from "@/lib/hooks/use-toast";
import { cn } from "@/lib/utils";

import { useShareStore } from "@/store/sharing";
import { Share } from "@/types/share";

import { ShareStatusModal } from "./share-status-modal";

interface ShareCardProps {
  share: Share;
  className?: string;
}

export function ShareCard({ share, className }: ShareCardProps) {
  const [isVisibilityModalOpen, setIsVisibilityModalOpen] = useState(false);
  const [isForkModalOpen, setIsForkModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const fetchShares = useShareStore(state => state.fetchShares);
  const deleteShare = useShareStore(state => state.deleteShare);
  const { toast } = useToast();

  return (
    <>
      <Card className={cn("hover:shadow-md transition-shadow", className)}>
        <CardContent className="p-4">
          <div className="space-y-4">
            <div className="w-full h-48 bg-gray-50 rounded-md flex items-center justify-center">
              <img
                src={share.imageUrl}
                alt="Share preview"
                className="max-w-full max-h-48 object-contain"
              />
            </div>
            <div className="px-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <div className="text-muted-foreground flex items-center gap-1">
                    <Icon icon="mdi:earth" className="w-4 h-4" />
                    分享
                  </div>
                  <div
                    className={cn(
                      "font-medium cursor-pointer hover:text-foreground transition-colors",
                      share.isPublic
                        ? "text-green-500"
                        : "text-muted-foreground"
                    )}
                    onClick={() => setIsVisibilityModalOpen(true)}
                  >
                    {share.isPublic ? "公开" : "私密"}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-muted-foreground flex items-center gap-1">
                    <Icon icon="mdi:source-fork" className="w-4 h-4" />
                    复刻
                  </div>
                  <div
                    className={cn(
                      "font-medium cursor-pointer hover:text-foreground transition-colors",
                      share.allowFork
                        ? "text-green-500"
                        : "text-muted-foreground"
                    )}
                    onClick={() => setIsForkModalOpen(true)}
                  >
                    {share.allowFork ? "允许" : "禁止"}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div
                    className="flex items-center gap-2 text-red-500 hover:text-red-600 transition-colors cursor-pointer"
                    onClick={() => setIsDeleteModalOpen(true)}
                  >
                    <div className="flex items-center gap-1">
                      <Icon icon="mdi:delete-outline" className="w-4 h-4" />
                      操作
                    </div>
                    <div className="font-medium">删除</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Link
                    href={`/settings/sharing/${share.id}`}
                    className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <div className="flex items-center gap-1">
                      <Icon icon="mdi:eye-outline" className="w-4 h-4" />
                      详情
                    </div>
                    <div className="font-medium">查看</div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <ShareStatusModal
        share={share}
        isOpen={isVisibilityModalOpen}
        onClose={() => setIsVisibilityModalOpen(false)}
        type="visibility"
        onAfterUpdate={fetchShares}
      />
      <ShareStatusModal
        share={share}
        isOpen={isForkModalOpen}
        onClose={() => setIsForkModalOpen(false)}
        type="fork"
        onAfterUpdate={fetchShares}
      />

      <AlertDialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除这个分享吗？此操作无法撤销，删除后将无法恢复。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={async () => {
                try {
                  setIsDeleting(true);
                  await deleteShare(share.id);
                  toast({
                    title: "删除成功",
                    description: "分享已成功删除",
                    variant: "default",
                    className: "bg-green-500 text-white border-green-600",
                  });
                  // Refresh the shares list
                  fetchShares();
                } catch (error) {
                  console.error("Error deleting share:", error);
                  toast({
                    title: "删除失败",
                    description: "无法删除分享，请重试",
                    variant: "destructive",
                  });
                } finally {
                  setIsDeleting(false);
                  setIsDeleteModalOpen(false);
                }
              }}
              className="bg-red-500 hover:bg-red-600"
              disabled={isDeleting}
            >
              {isDeleting ? "删除中..." : "删除"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
