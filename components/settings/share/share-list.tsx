import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useShareStore } from "@/store/sharing";
import { useEffect, useState } from "react";
import { ShareCard } from "./share-card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { List, Grid, Eye, Share2, Trash2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";
import { zhCN } from "date-fns/locale";
import { useToast } from "@/lib/hooks/use-toast";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export function ShareList() {
  const { shares, loading, fetchShares, filter = { isPublic: null, allowFork: null }, setFilter, deleteShare } = useShareStore();
  const [displayMode, setDisplayMode] = useState<"list" | "gallery">("gallery");
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deletingItem, setDeletingItem] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    totalCount: 0,
    totalPages: 1,
    hasNextPage: false,
    hasPrevPage: false,
  });

  useEffect(() => {
    fetchShares();
  }, [fetchShares]);

  const filteredShares = shares.filter((share) => {
    if (filter?.isPublic !== null && share.isPublic !== filter.isPublic) {
      return false;
    }
    if (filter?.allowFork !== null && share.allowFork !== filter.allowFork) {
      return false;
    }
    return true;
  });

  // Calculate pagination values without using useEffect
  const totalCount = filteredShares.length;
  const totalPages = Math.ceil(totalCount / pagination.limit);
  const hasNextPage = pagination.page < totalPages;
  const hasPrevPage = pagination.page > 1;

  // Update pagination only when page changes
  const handlePageChange = (newPage: number) => {
    setPagination({
      ...pagination,
      page: newPage,
      totalCount,
      totalPages,
      hasNextPage: newPage < totalPages,
      hasPrevPage: newPage > 1,
    });
  };

  // Get current page items
  const getCurrentPageItems = () => {
    const startIndex = (pagination.page - 1) * pagination.limit;
    const endIndex = startIndex + pagination.limit;
    return filteredShares.slice(startIndex, endIndex);
  };

  const currentShares = getCurrentPageItems();

  // Handle delete confirmation
  const handleDeleteConfirm = (id: string) => {
    setDeletingItem(id);
    setIsDeleteModalOpen(true);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!deletingItem) return;

    try {
      setIsDeleting(true);
      await deleteShare(deletingItem);
      toast({
        title: "删除成功",
        description: "分享已成功删除",
        variant: "default",
        className: "bg-green-500 text-white border-green-600",
      });
    } catch (error) {
      console.error("Error deleting share:", error);
      toast({
        title: "删除失败",
        description: "无法删除分享，请重试",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
      setDeletingItem(null);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <Skeleton className="h-24 w-24 rounded-md" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-4 w-1/3" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-6">
        <div className="flex gap-4">
          <Select
            value={filter?.isPublic === null ? "all" : filter.isPublic ? "public" : "private"}
            onValueChange={(value) => {
              setFilter({
                ...filter,
                isPublic: value === "all" ? null : value === "public",
              });
            }}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="分享状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部</SelectItem>
              <SelectItem value="public">公开</SelectItem>
              <SelectItem value="private">私密</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filter?.allowFork === null ? "all" : filter.allowFork ? "allowed" : "disallowed"}
            onValueChange={(value) => {
              setFilter({
                ...filter,
                allowFork: value === "all" ? null : value === "allowed",
              });
            }}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="复刻状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部</SelectItem>
              <SelectItem value="allowed">允许复刻</SelectItem>
              <SelectItem value="disallowed">禁止复刻</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2">
          <Button
            variant={displayMode === "list" ? "default" : "outline"}
            size="icon"
            onClick={() => setDisplayMode("list")}
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant={displayMode === "gallery" ? "default" : "outline"}
            size="icon"
            onClick={() => setDisplayMode("gallery")}
          >
            <Grid className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {filteredShares.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center text-muted-foreground">
            暂无分享内容
          </CardContent>
        </Card>
      ) : displayMode === "gallery" ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
          {currentShares.map((share) => (
            <ShareCard key={share.id} share={share} />
          ))}
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">预览</TableHead>
                <TableHead>分享状态</TableHead>
                <TableHead>复刻状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead className="w-[180px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentShares.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center">
                    暂无数据
                  </TableCell>
                </TableRow>
              ) : (
                currentShares.map((share) => (
                  <TableRow key={share.id}>
                    <TableCell>
                      <div className="w-16 h-16 flex items-center justify-center">
                        <a
                          href={`${process.env.NEXT_PUBLIC_APP_URL}/explore/${share.shareId}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="cursor-pointer"
                        >
                          <img
                            src={share.imageUrl}
                            alt="Share preview"
                            className="max-w-full max-h-16 object-contain rounded hover:opacity-80 transition-opacity"
                          />
                        </a>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div
                        className={cn(
                          "font-medium cursor-pointer",
                          share.isPublic
                            ? "text-green-500"
                            : "text-muted-foreground"
                        )}
                        onClick={() => {
                          // This would open the visibility modal in a real implementation
                          // For now, we'll just show a toast
                          toast({
                            title: "功能提示",
                            description: "点击卡片视图中的相应按钮可以修改分享状态",
                            variant: "default",
                          });
                        }}
                      >
                        {share.isPublic ? "公开" : "私密"}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div
                        className={cn(
                          "font-medium cursor-pointer",
                          share.allowFork
                            ? "text-green-500"
                            : "text-muted-foreground"
                        )}
                        onClick={() => {
                          // This would open the fork modal in a real implementation
                          // For now, we'll just show a toast
                          toast({
                            title: "功能提示",
                            description: "点击卡片视图中的相应按钮可以修改复刻状态",
                            variant: "default",
                          });
                        }}
                      >
                        {share.allowFork ? "允许" : "禁止"}
                      </div>
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="cursor-help">
                              {formatDistanceToNow(new Date(share.createdAt), {
                                addSuffix: true,
                                locale: zhCN
                              })}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            {new Date(share.createdAt).toLocaleString('zh-CN')}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          asChild
                        >
                          <a href={`/settings/sharing/${share.id}`}>
                            <Eye className="h-4 w-4" />
                          </a>
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          asChild
                        >
                          <a
                            href={`${process.env.NEXT_PUBLIC_APP_URL}/explore/${share.shareId}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Share2 className="h-4 w-4" />
                          </a>
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteConfirm(share.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Pagination Controls */}
      {!loading && totalPages > 1 && (
        <div className="flex justify-between items-center mt-6">
          <div className="text-sm text-muted-foreground">
            共 {totalCount} 条记录，第 {pagination.page} /{" "}
            {totalPages} 页
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={!hasPrevPage}
            >
              上一页
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={!hasNextPage}
            >
              下一页
            </Button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除这个分享吗？此操作无法撤销，删除后将无法恢复。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-500 hover:bg-red-600"
              disabled={isDeleting}
            >
              {isDeleting ? "删除中..." : "删除"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
