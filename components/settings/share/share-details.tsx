"use client";

import { useEffect, useState } from "react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useRouter } from "next/navigation";
import { useToast } from "@/lib/hooks/use-toast";
import { Share } from "@/types/share";
import { useShareStore } from "@/store/sharing";
import { Icon } from "@iconify/react";
import { useCopy } from "@/lib/hooks/use-copy";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { ShareStatusModal } from "@/components/settings/share/share-status-modal";
import { drawModels } from "@/constants/draw";
import { DRAW_STYLES } from "@/constants/draw";
import { QRCodeSVG } from "qrcode.react";

interface ShareDetailsProps {
  id: string;
}

export function ShareDetails({ id }: ShareDetailsProps) {
  const { toast } = useToast();
  const [share, setShare] = useState<Share | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const router = useRouter();
  const { updateShare } = useShareStore();
  const [isVisibilityModalOpen, setIsVisibilityModalOpen] = useState(false);
  const [isForkModalOpen, setIsForkModalOpen] = useState(false);
  const { copyToClipboard } = useCopy();

  useEffect(() => {
    fetchShare();
  }, [id]);

  const fetchShare = async () => {
    try {
      const response = await fetch(`/api/shares/${id}`);
      if (!response.ok) throw new Error("Failed to fetch share");
      const data = await response.json();
      setShare(data);
    } catch (error) {
      console.error("Error fetching share:", error);
      toast({
        title: "错误",
        description: "获取分享详情失败",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSettingsChange = async (settings: { isPublic: boolean; allowFork: boolean }) => {
    if (!share) return;

    setSaving(true);
    try {
      await updateShare(share.id, settings);
      setShare((prev) => prev ? { ...prev, ...settings } : null);
      toast({
        title: "成功",
        description: "更新成功",
      });
    } catch (error) {
      console.error("Error updating share:", error);
      toast({
        title: "错误",
        description: "更新失败",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="py-4 px-2 md:px-4 relative">
        <div className="p-4 sm:p-8 rounded-xl bg-white shadow-[0_5px_15px_rgba(0,0,0,0.08),0_15px_35px_-5px_rgba(25,28,33,0.2)] ring-1 ring-gray-950/5 w-full">
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <Skeleton className="h-8 w-1/4" />
                <Skeleton className="h-24 w-24 rounded-md" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-4 w-1/3" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!share) {
    return (
      <div className="py-4 px-2 md:px-4 relative">
        <div className="p-4 sm:p-8 rounded-xl bg-white shadow-[0_5px_15px_rgba(0,0,0,0.08),0_15px_35px_-5px_rgba(25,28,33,0.2)] ring-1 ring-gray-950/5 w-full">
          <Card>
            <CardContent className="p-6 text-center text-muted-foreground">
              分享不存在或已被删除
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="py-4 px-2 md:px-4 relative">
      <div className="w-full flex flex-col gap-8">
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <img
                  src={share.imageUrl}
                  alt="Share preview"
                  className="w-full h-auto rounded-lg object-cover"
                />
              </div>
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">模型</p>
                      <p className="font-medium">
                        {drawModels.find((m) => m.id === share.model)?.name ||
                          share.model}
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">风格</p>
                      <p className="font-medium">
                        {share.styleId
                          ? DRAW_STYLES[
                              share.styleId as keyof typeof DRAW_STYLES
                            ]?.name
                          : "默认"}
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">图片数量</p>
                      <p className="font-medium">
                        {share.allowFork
                          ? Array.isArray(share.originalImages)
                            ? share.originalImages.length
                            : 0
                          : "已隐藏"}
                      </p>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center justify-between">
                      <p className="text-muted-foreground">提示词</p>
                      {share.allowFork && share.customPrompt && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-muted-foreground"
                          onClick={() =>
                            copyToClipboard(
                              share.customPrompt,
                              "提示词已复制到剪贴板",
                              "复制提示词失败，请重试"
                            )
                          }
                        >
                          <Icon
                            icon="mdi:content-copy"
                            className="w-4 h-4 mr-2"
                          />
                          复制
                        </Button>
                      )}
                    </div>
                    <p className="font-medium mt-1">
                      {share.allowFork
                        ? share.customPrompt || "无自定义提示词"
                        : "已隐藏"}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between w-full border-t pt-4">
              <div className="flex items-center min-w-0 flex-1">
                <QRCodeSVG
                  value={`${process.env.NEXT_PUBLIC_APP_URL}/explore/${share.shareId}`}
                  size={64}
                  className="rounded-md mr-2"
                />
                <Link
                  href={`${process.env.NEXT_PUBLIC_APP_URL}/explore/${share.shareId}`}
                  title={`${process.env.NEXT_PUBLIC_APP_URL}/explore/${share.shareId}`}
                  className="text-blue-500 hover:text-blue-600 hover:underline truncate"
                  target="_blank"
                >
                  分享地址
                </Link>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() =>
                    copyToClipboard(
                      `${process.env.NEXT_PUBLIC_APP_URL}/explore/${share.shareId}`
                    )
                  }
                >
                  <Icon icon="mdi:content-copy" className="w-4 h-4" />
                </Button>
              </div>
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <div className="text-muted-foreground flex items-center gap-1">
                    <Icon icon="mdi:earth" className="w-4 h-4" />
                    分享
                  </div>
                  <div
                    className={cn(
                      "font-medium cursor-pointer hover:text-foreground transition-colors",
                      share.isPublic
                        ? "text-green-500"
                        : "text-muted-foreground"
                    )}
                    onClick={() => setIsVisibilityModalOpen(true)}
                  >
                    {share.isPublic ? "公开" : "私密"}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-muted-foreground flex items-center gap-1">
                    <Icon icon="mdi:source-fork" className="w-4 h-4" />
                    复刻
                  </div>
                  <div
                    className={cn(
                      "font-medium cursor-pointer hover:text-foreground transition-colors",
                      share.allowFork
                        ? "text-green-500"
                        : "text-muted-foreground"
                    )}
                    onClick={() => setIsForkModalOpen(true)}
                  >
                    {share.allowFork ? "允许" : "禁止"}
                  </div>
                </div>
              </div>
            </div>
          </CardFooter>
        </Card>
        <ShareStatusModal
          share={share}
          isOpen={isVisibilityModalOpen}
          onClose={() => setIsVisibilityModalOpen(false)}
          type="visibility"
          onAfterUpdate={fetchShare}
        />
        <ShareStatusModal
          share={share}
          isOpen={isForkModalOpen}
          onClose={() => setIsForkModalOpen(false)}
          type="fork"
          onAfterUpdate={fetchShare}
        />
      </div>
      <div className="flex justify-center mt-4">
        <Button
          variant="outline"
          onClick={() => router.push("/settings/sharing")}
        >
          返回列表
        </Button>
      </div>
    </div>
  );
}
