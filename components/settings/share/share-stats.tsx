import { ShareStats } from "@/types/share";
import { Icon } from "@iconify/react";
import { formatDistanceToNow } from "date-fns";
import { zhCN } from "date-fns/locale";

interface ShareStatsProps {
  stats: ShareStats;
  createdAt: string;
  updatedAt: string;
  className?: string;
}

export function ShareStatsDisplay({
  stats,
  createdAt,
  updatedAt,
  className,
}: ShareStatsProps) {
  return (
    <div className={className}>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h3 className="font-medium">统计数据</h3>
          <div className="mt-2 space-y-1 text-muted-foreground">
            <div className="flex items-center gap-1">
              <Icon icon="mdi:eye" className="w-4 h-4" />
              浏览: {stats.viewCount}
            </div>
            <div className="flex items-center gap-1">
              <Icon icon="mdi:heart" className="w-4 h-4" />
              点赞: {stats.likeCount}
            </div>
            <div className="flex items-center gap-1">
              <Icon icon="mdi:source-fork" className="w-4 h-4" />
              复刻: {stats.forkCount}
            </div>
            <div className="flex items-center gap-1">
              <Icon icon="mdi:currency-usd" className="w-4 h-4" />
              复刻收益: {stats.forkEarnings} 积分
            </div>
          </div>
        </div>
        <div>
          <h3 className="font-medium">时间信息</h3>
          <div className="mt-2 space-y-1 text-muted-foreground">
            <div>
              创建于{" "}
              {formatDistanceToNow(new Date(createdAt), {
                addSuffix: true,
                locale: zhCN,
              })}
            </div>
            <div>
              更新于{" "}
              {formatDistanceToNow(new Date(updatedAt), {
                addSuffix: true,
                locale: zhCN,
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
