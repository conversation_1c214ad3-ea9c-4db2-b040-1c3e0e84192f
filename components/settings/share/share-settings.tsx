import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { ShareSettings } from "@/types/share";
import { cn } from "@/lib/utils";

interface ShareSettingsProps {
  settings: ShareSettings;
  onSettingsChange: (settings: ShareSettings) => void;
  disabled?: boolean;
  className?: string;
}

export function ShareSettingsControls({
  settings,
  onSettingsChange,
  disabled = false,
  className,
}: ShareSettingsProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center gap-2">
        <Switch
          checked={settings.isPublic}
          disabled={disabled}
          onCheckedChange={(checked) =>
            onSettingsChange({ ...settings, isPublic: checked })
          }
          className="data-[state=checked]:bg-green-500"
        />
        <Label>公开分享</Label>
      </div>
      <div className="flex items-center gap-2">
        <Switch
          checked={settings.allowFork}
          disabled={disabled}
          onCheckedChange={(checked) =>
            onSettingsChange({ ...settings, allowFork: checked })
          }
          className="data-[state=checked]:bg-green-500"
        />
        <Label>允许复刻</Label>
      </div>
    </div>
  );
}
