import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Share } from "@/types/share";
import { useShareStore } from "@/store/sharing";
import { useState } from "react";

interface ShareStatusModalProps {
  share: Share;
  isOpen: boolean;
  onClose: () => void;
  type: "visibility" | "fork";
  onAfterUpdate?: () => Promise<void>; // 新增回调，用于刷新数据
}

export function ShareStatusModal({ share, isOpen, onClose, type, onAfterUpdate }: ShareStatusModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const updateShare = useShareStore((state) => state.updateShare);
  const fetchShares = useShareStore((state) => state.fetchShares);

  const handleUpdate = async (value: boolean) => {
    try {
      setIsLoading(true);
      await updateShare(share.id, {
        ...share,
        isPublic: type === "visibility" ? value : share.isPublic,
        allowFork: type === "fork" ? value : share.allowFork,
      });

      // 更新后刷新数据
      if (onAfterUpdate) {
        // 如果提供了自定义回调，使用自定义回调刷新
        await onAfterUpdate();
      } else {
        // 否则默认刷新列表
        await fetchShares();
      }
      onClose();
    } catch (error) {
      console.error("Error updating share:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const title = type === "visibility" ? "修改可见性" : "修改复刻权限";
  const currentValue = type === "visibility" ? share.isPublic : share.allowFork;
  const description = type === "visibility"
    ? "公开分享可以让其他用户看到这个分享内容，私密分享则只有您自己可以看到。"
    : "允许复刻后，其他用户可以看到您的自定义提示词，并且可以一键复刻您的分享。禁止复刻则其他用户只能查看，无法复制您的提示词。";

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-sm">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
        <DialogFooter>
          <div className="flex gap-2">
            <Button
              variant={currentValue ? "default" : "outline"}
              onClick={() => handleUpdate(true)}
              disabled={isLoading}
            >
              {type === "visibility" ? "公开" : "允许复刻"}
            </Button>
            <Button
              variant={!currentValue ? "default" : "outline"}
              onClick={() => handleUpdate(false)}
              disabled={isLoading}
            >
              {type === "visibility" ? "私密" : "禁止复刻"}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
