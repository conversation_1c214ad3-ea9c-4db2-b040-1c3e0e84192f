"use client";

import { FC } from "react";
import { useUser, OrganizationSwitcher } from "@clerk/nextjs";
import { useProfile } from "@/lib/hooks/use-profile";
import { Transaction } from "@/store/profile";

interface RowProps {
  desc: string;
  value: string;
}

const Row: FC<RowProps> = ({ desc, value }) => {
  return (
    <div className="h-[2.125rem] grid grid-cols-2 items-center relative">
      <span className="text-xs font-semibold block flex-shrink-0">{desc}</span>
      <span className="text-xs text-[#7D7D7E] font-mono block relative">
        <span className="block truncate w-full">{value}</span>
      </span>
    </div>
  );
}

function formatDate(date: Date) {
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "numeric",
    day: "numeric",
  });
}

function formatDateWithNumbers(date: Date): string {
  return date.toLocaleString("en-US", {
    month: "numeric",
    day: "numeric",
    year: "numeric",
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });
}

export const UserInfo: FC = () => {
  const { user } = useUser();
  const { wallet, transactions, profile } = useProfile();

  if (!user) return null;

  // 计算本月消费
  const currentMonth = new Date().getMonth();
  const monthlySpent = transactions
    ?.filter((tx: Transaction) => {
      const txDate = new Date(tx.createdAt);
      return txDate.getMonth() === currentMonth && tx.type === 'debit';
    })
    .reduce((acc: number, tx: Transaction) => acc + tx.amount, 0) || 0;

  return (
    <div className="px-2 md:px-4 relative">
      <div className="p-8 rounded-xl bg-white shadow-[0_5px_15px_rgba(0,0,0,0.08),0_15px_35px_-5px_rgba(25,28,33,0.2)] ring-1 ring-gray-950/5 w-full">
        <div className="flex flex-col items-center gap-2 mb-6">
          <div className="w-full relative flex justify-center">
            <img src={user.imageUrl} className="size-20 rounded-full" />
          </div>
          {user.firstName && user.lastName ? (
            <h1 className="text-[1.0625rem] font-semibold text-center">
              {user.firstName} {user.lastName}
            </h1>
          ) : (
            <div className="h-4" />
          )}
          <OrganizationSwitcher />
        </div>

        <div className="px-2.5 bg-[#FAFAFB] rounded-lg divide-y divide-[#EEEEF0]">
          <Row desc="邮箱" value={user.emailAddresses[0].emailAddress} />
          <Row desc="最近登录" value={formatDate(user.lastSignInAt!)} />
          <Row desc="注册时间" value={formatDate(user.createdAt!)} />
          <Row desc="用户 ID" value={user.id} />
        </div>

        <h2 className="mt-6 mb-4 text-[0.9375rem] font-semibold">积分信息</h2>
        <div className="px-2.5 bg-[#FAFAFB] rounded-lg divide-y divide-[#EEEEF0]">
          <div className="h-[2.125rem] grid grid-cols-2 items-center relative">
            <span className="text-xs font-semibold block flex-shrink-0">
              可用积分
            </span>
            <div className="flex items-center gap-2">
              <span className="text-xs text-[#7D7D7E] font-mono block relative">
                <span className="block truncate w-full">
                  {String(wallet?.permanentPoints || 0)}
                </span>
              </span>
              <a
                href="/pricing"
                className="text-xs text-blue-500 hover:text-blue-600 hover:underline"
              >
                充值
              </a>
            </div>
          </div>
          <div className="h-[2.125rem] grid grid-cols-2 items-center relative">
            <span className="text-xs font-semibold block flex-shrink-0">
              消费记录
            </span>
            <a
              href="/settings/history"
              className="text-xs text-blue-500 hover:text-blue-600 hover:underline"
            >
              查看详情
            </a>
          </div>
          <div className="h-[2.125rem] grid grid-cols-2 items-center relative">
            <span className="text-xs font-semibold block flex-shrink-0">
              充值历史
            </span>
            <a
              href="/settings/orders"
              className="text-xs text-blue-500 hover:text-blue-600 hover:underline"
            >
              查看详情
            </a>
          </div>
          <Row
            desc="账户类型"
            value={profile?.isPaid ? "付费会员" : "普通会员"}
          />
        </div>
      </div>
    </div>
  );
}
