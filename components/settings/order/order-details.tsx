"use client";

import { FC, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/lib/hooks/use-toast";
import { format } from "date-fns";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronLeft, CreditCard, Calendar, User, ExternalLink, Info } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { TransactionsTable, Transaction } from "@/components/shared/transactions-table";
import { getStripeDashboardUrl } from "@/lib/stripe/utils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface OrderExtra {
  points?: number;
  price?: number;
  package?: {
    id: string;
    name: string;
    points: number;
    description: string;
  };
  exchangeType?: string;
  historyId?: string;
  relatedOrderId?: string;
  note?: string;
  transactions?: Transaction[];
}

interface OrderDetail {
  id: string;
  type: string;
  amount: number;
  buyerId: string;
  userId: string;
  status: string;
  paymentMethod?: string;
  outTradeNo?: string;
  tradeNo?: string;
  qrCodeUrl?: string;
  paidAt?: string;
  refundedAt?: string;
  description: string;
  extra: OrderExtra;
  createdAt: string;
  updatedAt: string;
}

interface OrderDetailsProps {
  id: string;
}

interface StripeSessionDetails {
  session?: any;
  paymentIntent?: any;
  dashboardUrl?: string;
}

export const OrderDetails: FC<OrderDetailsProps> = ({ id }) => {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [order, setOrder] = useState<OrderDetail | null>(null);
  const [stripeDetailsOpen, setStripeDetailsOpen] = useState(false);
  const [stripeDetails, setStripeDetails] = useState<StripeSessionDetails | null>(null);
  const [loadingStripeDetails, setLoadingStripeDetails] = useState(false);

  // 获取订单详情
  useEffect(() => {
    const fetchOrderDetails = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/orders/${id}`);

        if (!response.ok) {
          throw new Error("Failed to fetch order details");
        }

        const data = await response.json();
        setOrder(data);
      } catch (error) {
        console.error("Error fetching order details:", error);
        toast({
          title: "错误",
          description: "获取订单详情失败，请重试",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [id, toast]);

  // 获取 Stripe 会话详情
  const fetchStripeSessionDetails = async (sessionId: string) => {
    try {
      setLoadingStripeDetails(true);
      setStripeDetailsOpen(true);

      const response = await fetch(`/api/stripe/sessions/${sessionId}`);

      if (!response.ok) {
        throw new Error("获取 Stripe 交易详情失败");
      }

      const data = await response.json();
      setStripeDetails(data);
    } catch (error) {
      console.error("Error fetching Stripe session details:", error);
      toast({
        title: "错误",
        description: "获取 Stripe 交易详情失败，请重试",
        variant: "destructive",
      });
      setStripeDetailsOpen(false);
    } finally {
      setLoadingStripeDetails(false);
    }
  };

  // 状态标签
  const renderStatusBadge = (status: string) => (
    <span className={cn(
      "px-2 py-1 rounded-full text-xs",
      {
        "bg-green-100 text-green-700": status === "SUCCESS",
        "bg-yellow-100 text-yellow-700": status === "PENDING",
        "bg-red-100 text-red-700": status === "FAILED",
        "bg-gray-100 text-gray-700": status === "REFUND",
      }
    )}>
      {{
        SUCCESS: "成功",
        PENDING: "处理中",
        FAILED: "失败",
        REFUND: "已退款"
      }[status] || status}
    </span>
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-40 mb-2" />
            <Skeleton className="h-4 w-60" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <h1 className="text-2xl font-bold">订单不存在</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center py-8 text-muted-foreground">未找到该订单信息</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { extra } = order;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={() => router.back()}>
          <ChevronLeft className="h-4 w-4 mr-2" />
          返回
        </Button>
        <h1 className="text-2xl font-bold">订单详情</h1>
        <div className="ml-auto">{renderStatusBadge(order.status)}</div>
      </div>

      {/* Stripe 交易详情对话框 */}
      <Dialog open={stripeDetailsOpen} onOpenChange={setStripeDetailsOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Stripe 交易详情</DialogTitle>
            <DialogDescription>交易 ID: {order?.tradeNo}</DialogDescription>
          </DialogHeader>

          {loadingStripeDetails ? (
            <div className="py-8 flex justify-center">
              <Skeleton className="h-32 w-full" />
            </div>
          ) : stripeDetails ? (
            <div className="space-y-4">
              {/* 支付会话信息 */}
              {stripeDetails.session && (
                <Card>
                  <CardHeader>
                    <CardTitle>支付会话信息</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">会话 ID:</span>
                        <span className="font-mono text-sm">
                          {stripeDetails.session.id}
                        </span>
                      </div>
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">支付状态:</span>
                        <span>{stripeDetails.session.payment_status}</span>
                      </div>
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">金额:</span>
                        <span>
                          {(stripeDetails.session.amount_total / 100).toFixed(
                            2
                          )}{" "}
                          {stripeDetails.session.currency?.toUpperCase()}
                        </span>
                      </div>
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">客户邮箱:</span>
                        <span>
                          {stripeDetails.session.customer_details?.email ||
                            "未提供"}
                        </span>
                      </div>
                      <div className="flex justify-between py-2">
                        <span className="font-medium">创建时间:</span>
                        <span>
                          {new Date(
                            stripeDetails.session.created * 1000
                          ).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 支付意向信息 */}
              {stripeDetails.paymentIntent && (
                <Card>
                  <CardHeader>
                    <CardTitle>支付意向信息</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">支付意向 ID:</span>
                        <span className="font-mono text-sm">
                          {stripeDetails.paymentIntent.id}
                        </span>
                      </div>
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">状态:</span>
                        <span>{stripeDetails.paymentIntent.status}</span>
                      </div>
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">金额:</span>
                        <span>
                          {(stripeDetails.paymentIntent.amount / 100).toFixed(
                            2
                          )}{" "}
                          {stripeDetails.paymentIntent.currency?.toUpperCase()}
                        </span>
                      </div>
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">支付方式:</span>
                        <span>
                          {stripeDetails.paymentIntent.payment_method_types?.join(
                            ", "
                          ) || "未知"}
                        </span>
                      </div>
                      <div className="flex justify-between py-2">
                        <span className="font-medium">创建时间:</span>
                        <span>
                          {new Date(
                            stripeDetails.paymentIntent.created * 1000
                          ).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              <div className="flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => setStripeDetailsOpen(false)}
                >
                  关闭
                </Button>
              </div>
            </div>
          ) : (
            <div className="py-8 text-center text-muted-foreground">
              无法获取交易详情
            </div>
          )}
        </DialogContent>
      </Dialog>

      <div className="grid gap-6 md:grid-cols-2">
        {/* 订单摘要 */}
        <Card>
          <CardHeader>
            <CardTitle>订单摘要</CardTitle>
            <CardDescription>订单基本信息概览</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <CreditCard className="h-8 w-8 mb-2 text-primary" />
                <span className="text-2xl font-bold">{order.amount}</span>
                <span className="text-sm text-muted-foreground">积分</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <User className="h-8 w-8 mb-2 text-primary" />
                <span className="text-2xl font-bold">
                  {order.buyerId === "system" ? "系统" : "自己"}
                </span>
                <span className="text-sm text-muted-foreground">来源</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <Calendar className="h-8 w-8 mb-2 text-primary" />
                <span className="text-lg font-medium">
                  {format(new Date(order.createdAt), "yyyy-MM-dd")}
                </span>
                <span className="text-sm text-muted-foreground">创建日期</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                {extra.price ? (
                  <>
                    <span className="text-2xl font-bold">
                      ¥{extra.price.toFixed(2)}
                    </span>
                    <span className="text-sm text-muted-foreground">金额</span>
                  </>
                ) : (
                  <>
                    <span className="text-2xl font-bold">-</span>
                    <span className="text-sm text-muted-foreground">金额</span>
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 订单详情 */}
        <Card>
          <CardHeader>
            <CardTitle>订单详情</CardTitle>
            <CardDescription>详细订单信息</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">订单ID:</span>
                <span className="text-muted-foreground font-mono text-sm">
                  {order.id}
                </span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">类型:</span>
                <span>{order.type === "credit" ? "充值" : "消费"}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">描述:</span>
                <span>{order.description}</span>
              </div>
              {order.paymentMethod && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">支付方式:</span>
                  <span>
                    {order.paymentMethod === "alipay"
                      ? "支付宝"
                      : order.paymentMethod === "wxpay"
                      ? "微信"
                      : order.paymentMethod === "stripe"
                      ? "Stripe"
                      : order.paymentMethod}
                  </span>
                </div>
              )}
              {order.outTradeNo && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">商户订单号:</span>
                  <span className="font-mono text-sm">{order.outTradeNo}</span>
                </div>
              )}
              {order.tradeNo && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium w-28">交易单号:</span>
                  <div className="flex items-center gap-2">
                    {order.paymentMethod === "stripe" ? (
                      <>
                        <button
                          onClick={() =>
                            order.tradeNo &&
                            fetchStripeSessionDetails(order.tradeNo)
                          }
                          className="text-blue-600 hover:text-blue-800"
                          title={order.tradeNo}
                        >
                          <div className="flex items-center gap-1">
                            <span>查看详情</span>
                            <Info className="h-4 w-4" />
                          </div>
                        </button>
                      </>
                    ) : (
                      <span className="font-mono text-sm break-all">
                        {order.tradeNo}
                      </span>
                    )}
                  </div>
                </div>
              )}
              {order.paidAt && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">支付时间:</span>
                  <span>
                    {format(new Date(order.paidAt), "yyyy-MM-dd HH:mm:ss")}
                  </span>
                </div>
              )}
              {order.refundedAt && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">退款时间:</span>
                  <span>
                    {format(new Date(order.refundedAt), "yyyy-MM-dd HH:mm:ss")}
                  </span>
                </div>
              )}
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">创建时间:</span>
                <span>
                  {format(new Date(order.createdAt), "yyyy-MM-dd HH:mm:ss")}
                </span>
              </div>
              <div className="flex justify-between py-2">
                <span className="font-medium">更新时间:</span>
                <span>
                  {format(new Date(order.updatedAt), "yyyy-MM-dd HH:mm:ss")}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 关联信息 */}
      {(extra.historyId || extra.relatedOrderId || extra.exchangeType) && (
        <Card>
          <CardHeader>
            <CardTitle>关联信息</CardTitle>
            <CardDescription>与此订单相关的其他信息</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {extra.exchangeType && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">交易类型:</span>
                  <span>
                    {{
                      refund: "退款",
                      gift: "赠送",
                      award: "奖励",
                      affiliate: "推广",
                      other: "其他",
                    }[extra.exchangeType] || extra.exchangeType}
                  </span>
                </div>
              )}
              {extra.historyId && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">关联历史记录:</span>
                  <Link
                    href={`/settings/history/${extra.historyId}`}
                    className="text-blue-600 hover:underline font-mono text-sm"
                  >
                    {extra.historyId}
                  </Link>
                </div>
              )}
              {extra.relatedOrderId && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">关联订单:</span>
                  <Link
                    href={`/settings/orders/${extra.relatedOrderId}`}
                    className="text-blue-600 hover:underline font-mono text-sm"
                  >
                    {extra.relatedOrderId}
                  </Link>
                </div>
              )}
              {extra.note && (
                <div className="flex justify-between py-2">
                  <span className="font-medium">备注:</span>
                  <span>{extra.note}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 套餐信息 */}
      {extra.package && (
        <Card>
          <CardHeader>
            <CardTitle>套餐信息</CardTitle>
            <CardDescription>购买的积分套餐详情</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">套餐名称:</span>
                <span>{extra.package.name}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">套餐ID:</span>
                <span className="font-mono text-sm">{extra.package.id}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">积分数量:</span>
                <span>{extra.package.points}</span>
              </div>
              <div className="flex justify-between py-2">
                <span className="font-medium">套餐描述:</span>
                <span>{extra.package.description}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 交易记录 */}
      <Card>
        <CardHeader>
          <CardTitle>关联交易记录</CardTitle>
          <CardDescription>与此订单相关的交易记录</CardDescription>
        </CardHeader>
        <CardContent>
          <TransactionsTable transactions={extra.transactions || []} />
        </CardContent>
      </Card>
    </div>
  );
};
