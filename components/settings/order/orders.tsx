"use client";

import { FC, useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Label } from "@/components/ui/label";
import { format, subDays, startOfDay, endOfDay } from "date-fns";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useToast } from "@/lib/hooks/use-toast";
import { DatePickerWithRange } from "@/components/ui/date-picker-with-range";
import { DateRange } from "react-day-picker";
import { Button } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface Transaction {
  id: string;
  type: string;
  amount: number;
  description: string;
  exchangeType?: string;
  timestamp: string;
  userId: string;
  note?: string;
}

interface OrderItem {
  id: string;
  type: string;
  amount: number;
  buyerId: string;
  userId: string;
  status: string;
  paymentMethod: "alipay" | "wxpay" | "stripe";
  outTradeNo: string;
  tradeNo: string | null;
  extra: {
    points?: number;
    price?: number;
    package?: {
      id: string;
      name: string;
      points: number;
      description: string;
    };
    exchangeType?: string;
    historyId?: string;
    relatedOrderId?: string;
    note?: string;
    transactions?: Transaction[];
  };
  createdAt: string;
  updatedAt: string;
}

export const Orders: FC = () => {
  const { toast } = useToast();
  const router = useRouter();
  const [orders, setOrders] = useState<OrderItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    buyerType: "all",
    status: "all",
    dateRange: {
      from: startOfDay(subDays(new Date(), 3)),
      to: endOfDay(new Date()),
    },
    paymentMethod: "all" as "all" | "alipay" | "wxpay" | "stripe",
  });

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (filters.buyerType !== "all") params.append("buyerType", filters.buyerType);
      if (filters.status !== "all") params.append("status", filters.status);
      if (filters.paymentMethod !== "all")
        params.append("paymentMethod", filters.paymentMethod);
      if (filters.dateRange.from) {
        params.append("startDate", startOfDay(filters.dateRange.from).toISOString());
      }
      if (filters.dateRange.to) {
        params.append("endDate", endOfDay(filters.dateRange.to).toISOString());
      }

      const response = await fetch(`/api/orders?${params.toString()}`);
      if (!response.ok) throw new Error("Failed to fetch orders");
      const data = await response.json();
      setOrders(data);
    } catch (error) {
      console.error("Error fetching orders:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, [filters]);

  const handleDateRangeChange = (range: DateRange | undefined) => {
    if (range?.from && range?.to) {
      setFilters({
        ...filters,
        dateRange: {
          from: startOfDay(range.from),
          to: endOfDay(range.to),
        },
      });
    }
  };

  // 订单详情提示内容
  const renderTooltipContent = (item: OrderItem) => (
    <div className="space-y-2">
      <div className="font-medium">订单详情</div>
      <div className="text-sm space-y-1.5">
        <div className="grid grid-cols-[80px_1fr] gap-1">
          <span className="text-foreground/70">订单ID:</span>
          <code className="text-xs bg-muted/50 px-1 py-0.5 rounded text-foreground break-all">{item.id}</code>
        </div>
        <div className="grid grid-cols-[80px_1fr] gap-1">
          <span className="text-foreground/70">支付金额:</span>
          <span className="text-foreground">{item.extra.price ? `¥${item.extra.price.toFixed(2)}` : "-"}</span>
        </div>
        <div className="grid grid-cols-[80px_1fr] gap-1">
          <span className="text-foreground/70">包含积分:</span>
          <span className="text-foreground">{item.extra.points || item.amount}</span>
        </div>
        {item.extra.package && (
          <>
            <div className="grid grid-cols-[80px_1fr] gap-1">
              <span className="text-foreground/70">套餐ID:</span>
              <code className="text-xs bg-muted/50 px-1 py-0.5 rounded text-foreground break-all">{item.extra.package.id}</code>
            </div>
            <div className="grid grid-cols-[80px_1fr] gap-1">
              <span className="text-foreground/70">套餐说明:</span>
              <span className="text-foreground break-words">{item.extra.package.description}</span>
            </div>
          </>
        )}
        {item.extra.exchangeType && (
          <div className="grid grid-cols-[80px_1fr] gap-1">
            <span className="text-foreground/70">交易类型:</span>
            <span className="text-foreground">
              {{
                refund: "退款",
                gift: "赠送",
                award: "奖励",
                affiliate: "推广",
                other: "其他"
              }[item.extra.exchangeType] || item.extra.exchangeType}
            </span>
          </div>
        )}
        {item.extra.historyId && (
          <div className="grid grid-cols-[80px_1fr] gap-1">
            <span className="text-foreground/70">关联历史:</span>
            <Link
              href={`/settings/history/${item.extra.historyId}`}
              className="text-blue-600 hover:underline font-mono text-xs"
              onClick={(e) => e.stopPropagation()}
            >
              {item.extra.historyId}
            </Link>
          </div>
        )}
        {item.extra.relatedOrderId && (
          <div className="grid grid-cols-[80px_1fr] gap-1">
            <span className="text-foreground/70">关联订单:</span>
            <Link
              href={`/settings/orders/${item.extra.relatedOrderId}`}
              className="text-blue-600 hover:underline font-mono text-xs"
              onClick={(e) => e.stopPropagation()}
            >
              {item.extra.relatedOrderId}
            </Link>
          </div>
        )}
        {item.extra.transactions && item.extra.transactions.length > 0 && (
          <div className="grid grid-cols-[80px_1fr] gap-1">
            <span className="text-foreground/70">关联交易:</span>
            <span className="text-foreground">{item.extra.transactions.length}条交易记录</span>
          </div>
        )}
        <div className="grid grid-cols-[80px_1fr] gap-1">
          <span className="text-foreground/70">购买人:</span>
          <span className="text-foreground">
            {item.userId === item.buyerId ? "自己" : "系统"}
          </span>
        </div>
        <div className="grid grid-cols-[80px_1fr] gap-1">
          <span className="text-foreground/70">创建时间:</span>
          <span className="text-foreground">{format(new Date(item.createdAt), "yyyy-MM-dd HH:mm:ss")}</span>
        </div>
        <div className="grid grid-cols-[80px_1fr] gap-1">
          <span className="text-foreground/70">更新时间:</span>
          <span className="text-foreground">{format(new Date(item.updatedAt), "yyyy-MM-dd HH:mm:ss")}</span>
        </div>
        <div className="flex justify-between items-center mt-2 pt-2 border-t">
          <span className="text-xs text-foreground/70">点击可复制订单ID</span>
          <Link
            href={`/settings/orders/${item.id}`}
            className="text-xs text-blue-600 hover:underline flex items-center gap-1"
            onClick={(e) => {
              e.stopPropagation();
              router.push(`/settings/orders/${item.id}`);
            }}
          >
            查看详情 <ExternalLink size={12} />
          </Link>
        </div>
      </div>
    </div>
  );

  // 状态标签
  const renderStatusBadge = (status: string) => (
    <span className={cn(
      "px-2 py-1 rounded-full text-xs",
      {
        "bg-green-100 text-green-700": status === "SUCCESS",
        "bg-yellow-100 text-yellow-700": status === "PENDING",
        "bg-red-100 text-red-700": status === "FAILED",
        "bg-gray-100 text-gray-700": status === "REFUND",
      }
    )}>
      {{
        SUCCESS: "成功",
        PENDING: "处理中",
        FAILED: "失败",
        REFUND: "已退款"
      }[status] || status}
    </span>
  );

  const handleCopyOrderId = (id: string) => {
    navigator.clipboard.writeText(id);
    toast({
      title: "成功",
      description: "订单ID已复制到剪贴板",
    });
  };

  return (
    <div className="py-4 px-2 md:px-4 relative">
      <TooltipProvider>
        <div className="p-8 rounded-xl bg-white shadow-[0_5px_15px_rgba(0,0,0,0.08),0_15px_35px_-5px_rgba(25,28,33,0.2)] ring-1 ring-gray-950/5 w-full">
          <h2 className="text-[0.9375rem] font-semibold mb-6">订单历史</h2>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="space-y-2">
              <Label>充值来源</Label>
              <Select
                value={filters.buyerType}
                onValueChange={(value) =>
                  setFilters({ ...filters, buyerType: value })
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="选择充值来源" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="system">系统赠送</SelectItem>
                  <SelectItem value="self">自己充值</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>订单状态</Label>
              <Select
                value={filters.status}
                onValueChange={(value) =>
                  setFilters({ ...filters, status: value })
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="选择订单状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="SUCCESS">成功</SelectItem>
                  <SelectItem value="PENDING">处理中</SelectItem>
                  <SelectItem value="FAILED">失败</SelectItem>
                  <SelectItem value="REFUND">已退款</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>充值方式</Label>
              <Select
                value={filters.paymentMethod}
                onValueChange={(value: "all" | "alipay" | "wxpay" | "stripe") =>
                  setFilters({ ...filters, paymentMethod: value })
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="选择充值方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="alipay">支付宝</SelectItem>
                  <SelectItem value="wxpay">微信</SelectItem>
                  <SelectItem value="stripe">Stripe</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>时间范围</Label>
              <DatePickerWithRange
                date={filters.dateRange}
                onSelect={handleDateRangeChange}
              />
            </div>
          </div>

          {/* 移动端订单列表 */}
          <div className="sm:hidden space-y-4 rounded-md border p-1">
            {loading ? (
              <div className="text-center py-6">加载中...</div>
            ) : orders.length === 0 ? (
              <div className="text-center py-6">暂无数据</div>
            ) : (
              orders.map((item) => (
                <div
                  key={`mobile-${item.id}`}
                  className="rounded-lg border overflow-hidden cursor-pointer"
                  onClick={() => handleCopyOrderId(item.id)}
                >
                  <div className="bg-slate-100 p-3 flex justify-between items-center">
                    <span
                      className={
                        item.type === "credit"
                          ? "text-green-500 font-medium"
                          : "text-red-500 font-medium"
                      }
                    >
                      {item.type === "credit" ? "充值" : "消费"}
                    </span>
                    {renderStatusBadge(item.status)}
                  </div>
                  <div className="p-3 space-y-2">
                    <div className="flex justify-between py-1 border-b">
                      <span className="font-medium">来源:</span>
                      <span>
                        {item.buyerId === "system" ? "系统赠送" : "自己充值"}
                      </span>
                    </div>
                    <div className="flex justify-between py-1 border-b">
                      <span className="font-medium">积分:</span>
                      <span>{item.amount}</span>
                    </div>
                    <div className="flex justify-between py-1 border-b">
                      <span className="font-medium">金额:</span>
                      <span>
                        {item.extra.price
                          ? `¥${item.extra.price.toFixed(2)}`
                          : "-"}
                      </span>
                    </div>
                    <div className="flex justify-between py-1 border-b">
                      <span className="font-medium">充值方式:</span>
                      <span>
                        {item.paymentMethod === "alipay"
                          ? "支付宝"
                          : item.paymentMethod === "wxpay"
                          ? "微信"
                          : item.paymentMethod === "stripe"
                          ? "Stripe"
                          : item.buyerId === "system"
                          ? "-"
                          : "未知"}
                      </span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span className="font-medium">时间:</span>
                      <span>
                        {format(new Date(item.createdAt), "yyyy-MM-dd HH:mm")}
                      </span>
                    </div>
                    <div className="flex justify-end mt-2 pt-2 border-t">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/settings/orders/${item.id}`);
                        }}
                      >
                        查看详情 <ExternalLink size={14} />
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* 桌面端订单表格 */}
          <div className="hidden sm:block rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="whitespace-nowrap">类型</TableHead>
                  <TableHead className="whitespace-nowrap">积分</TableHead>
                  <TableHead className="whitespace-nowrap">金额</TableHead>
                  <TableHead className="whitespace-nowrap">充值方式</TableHead>
                  <TableHead className="whitespace-nowrap">状态</TableHead>
                  <TableHead className="whitespace-nowrap">时间</TableHead>
                  <TableHead className="whitespace-nowrap">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center">
                      加载中...
                    </TableCell>
                  </TableRow>
                ) : orders.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center">
                      暂无数据
                    </TableCell>
                  </TableRow>
                ) : (
                  orders.map((item) => (
                    <TableRow
                      key={`desktop-${item.id}`}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleCopyOrderId(item.id)}
                    >
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TableCell>
                            <span
                              className={
                                item.type === "credit"
                                  ? "text-green-500"
                                  : "text-red-500"
                              }
                            >
                              {item.type === "credit" ? "充值" : "消费"}
                            </span>
                          </TableCell>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-[300px] space-y-2 p-4 bg-white/80 backdrop-blur-sm border text-foreground">
                          {renderTooltipContent(item)}
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TableCell>{item.amount}</TableCell>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-[300px] space-y-2 p-4 bg-white/80 backdrop-blur-sm border text-foreground">
                          {renderTooltipContent(item)}
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TableCell>
                            {item.extra.price
                              ? `¥${item.extra.price.toFixed(2)}`
                              : "-"}
                          </TableCell>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-[300px] space-y-2 p-4 bg-white/80 backdrop-blur-sm border text-foreground">
                          {renderTooltipContent(item)}
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TableCell>
                            {item.paymentMethod === "alipay"
                              ? "支付宝"
                              : item.paymentMethod === "wxpay"
                              ? "微信"
                              : item.paymentMethod === "stripe"
                              ? "Stripe"
                              : item.buyerId === "system"
                              ? "-"
                              : "未知"}
                          </TableCell>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-[300px] space-y-2 p-4 bg-white/80 backdrop-blur-sm border text-foreground">
                          {renderTooltipContent(item)}
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TableCell>
                            {renderStatusBadge(item.status)}
                          </TableCell>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-[300px] space-y-2 p-4 bg-white/80 backdrop-blur-sm border text-foreground">
                          {renderTooltipContent(item)}
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TableCell>
                            {format(
                              new Date(item.createdAt),
                              "yyyy-MM-dd HH:mm"
                            )}
                          </TableCell>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-[300px] space-y-2 p-4 bg-white/80 backdrop-blur-sm border text-foreground">
                          {renderTooltipContent(item)}
                        </TooltipContent>
                      </Tooltip>

                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/settings/orders/${item.id}`);
                          }}
                        >
                          查看详情 <ExternalLink size={14} />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </TooltipProvider>
    </div>
  );
};
