'use client';

import React from 'react';
import { DRAW_STYLES } from '@/constants/draw';
import { drawModels } from '@/constants/draw/models';

interface ModelStyleDisplayProps {
  modelId?: string;
  styleId?: string;
  className?: string;
  showModelOnly?: boolean;
  showStyleOnly?: boolean;
}

export function getModelName(modelId: string | undefined): string {
  if (!modelId) return '-';
  const model = drawModels.find(m => m.id === modelId);
  return model ? model.name : modelId;
}

export function getStyleName(styleId: string | undefined): string {
  if (!styleId) return '-';

  // Check if styleId is a key in DRAW_STYLES
  if (styleId in DRAW_STYLES) {
    return DRAW_STYLES[styleId as keyof typeof DRAW_STYLES].name;
  }

  return styleId;
}

export function ModelStyleDisplay({
  modelId,
  styleId,
  className = '',
  showModelOnly = false,
  showStyleOnly = false
}: ModelStyleDisplayProps) {
  // If both showModelOnly and showStyleOnly are false, show both in a grid
  if (!showModelOnly && !showStyleOnly) {
    return (
      <div className={`grid grid-cols-2 gap-4 ${className}`}>
        <div className="space-y-2">
          <h3 className="font-medium">模型</h3>
          <p className="text-sm text-muted-foreground">
            {getModelName(modelId)}
          </p>
        </div>
        <div className="space-y-2">
          <h3 className="font-medium">风格</h3>
          <p className="text-sm text-muted-foreground">
            {getStyleName(styleId)}
          </p>
        </div>
      </div>
    );
  }

  // If showModelOnly is true, only show the model
  if (showModelOnly) {
    return getModelName(modelId);
  }

  // If showStyleOnly is true, only show the style
  if (showStyleOnly) {
    return getStyleName(styleId);
  }

  // Fallback
  return null;
}
