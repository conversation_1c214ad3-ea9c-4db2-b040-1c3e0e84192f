"use client";

import { useEffect } from "react";
import { useUser } from "@/lib/hooks/use-user";
import { create } from "zustand";
import { useProfileStore } from "@/store/profile";

// 定义用户状态存储
interface UserStore {
  credits: number;
  currentUser: any;
  setCredits: (credits: number) => void;

  // 单次刷新用户积分
  refreshUserInfo: () => Promise<void>;
}

export const useUserStore = create<UserStore>((set) => ({
  credits: 0,
  currentUser: null,

  setCredits: (credits) => set({ credits }),

  // 使用 ProfileStore 的 refreshUserInfo 方法
  refreshUserInfo: async () => {
    try {
      // 调用 ProfileStore 的 refreshUserInfo 方法
      const data = await useProfileStore.getState().refreshUserInfo();

      // 更新 UserStore 中的数据
      if (data.wallet) {
        set({ credits: data.wallet.permanentPoints });
      }

      // 设置当前用户
      const user = data.user || null;
      if (user) {
        user.isNewUser = data.isNewUser;
        // 确保付费状态正确设置
        console.log('[USER_REFRESH] User paid status:', user.isPaid);
      }
      set({ currentUser: user });

      return data; // 返回数据以保持与原实现一致
    } catch (error) {
      console.error('[USER_REFRESH_ERROR]', error);
      throw error; // 让调用者处理错误
    }
  },
}));

export function UserInitializer() {
  const { wallet, isLoading, user } = useUser();
  const setCredits = useUserStore((state) => state.setCredits);
  const refreshUserInfo = useUserStore((state) => state.refreshUserInfo);
  // We'll use the profile store for refreshing, but still rely on useUser for initial data

  // 定义一个简单的函数来设置当前用户
  const updateCurrentUser = (userData: any) => {
    useUserStore.setState({ currentUser: userData });
  };

  // 初始化用户信息和积分 - 使用 useUser hook 提供的数据
  useEffect(() => {
    if (!isLoading) {
      if (wallet) {
        setCredits(wallet.permanentPoints);
      } else {
        setCredits(0);
      }
      updateCurrentUser(user);
    }
  }, [isLoading, wallet, user, setCredits]);

  // 初始化时也从 API 获取最新数据
  useEffect(() => {
    // 只在客户端运行时执行一次刷新
    if (typeof window !== 'undefined') {
      refreshUserInfo().catch(error => {
        console.error('Failed to refresh user info during initialization:', error);
      });
    }
  }, []);

  // 这个组件不渲染任何可见内容
  return null;
}
