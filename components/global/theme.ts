export default {
  'code[class*="language-"]': {
    color: '#c5c8c6',
    fontFamily: 'var(--font-geist-mono)',
    direction: 'ltr',
    textAlign: 'left',
    whiteSpace: 'pre',
    wordSpacing: 'normal',
    wordBreak: 'normal',
    lineHeight: '1.5',
    MozTabSize: '4',
    OTabSize: '4',
    tabSize: '4',
    WebkitHyphens: 'none',
    MozHyphens: 'none',
    msHyphens: 'none',
    hyphens: 'none',
    fontSize: 12,
  },
  'pre[class*="language-"]': {
    color: '#c5c8c6',
    fontFamily: 'var(--font-geist-mono)',
    direction: 'ltr',
    textAlign: 'left',
    whiteSpace: 'pre',
    wordSpacing: 'normal',
    wordBreak: 'normal',
    lineHeight: '1.5',
    MozTabSize: '4',
    OTabSize: '4',
    tabSize: '4',
    WebkitHyphens: 'none',
    MozHyphens: 'none',
    msHyphens: 'none',
    hyphens: 'none',
    padding: '16px 0px 32px',
    margin: '.5em 0',
    overflow: 'auto',
    borderRadius: '0.3em',
    background: 'white',
    fontSize: 12,
    height: '100%',
  },
  ':not(pre) > code[class*="language-"]': {
    background: 'white',
    padding: '.1em',
    borderRadius: '.3em',
  },
  comment: {
    color: '#7C7C7C',
  },
  prolog: {
    color: '#7C7C7C',
  },
  doctype: {
    color: '#7C7C7C',
  },
  cdata: {
    color: '#7C7C7C',
  },
  punctuation: {
    color: '#c5c8c6',
  },
  '.namespace': {
    Opacity: '.7',
  },
  property: {
    color: '#676767',
  },
  keyword: {
    color: '#676767',
  },
  tag: {
    color: '#676767',
  },
  'class-name': {
    color: '#FFFFB6',
    textDecoration: 'underline',
  },
  boolean: {
    color: '#DB3FB9',
  },
  constant: {
    color: '#DB3FB9',
  },
  symbol: {
    color: '#f92672',
  },
  deleted: {
    color: '#f92672',
  },
  number: {
    color: '#FF73FD',
  },
  selector: {
    color: '#4248DA',
  },
  'attr-name': {
    color: '#4248DA',
  },
  string: {
    color: '#4248DA',
  },
  char: {
    color: '#4248DA',
  },
  builtin: {
    color: '#4248DA',
  },
  inserted: {
    color: '#4248DA',
  },
  variable: {
    color: '#C6C5FE',
  },
  operator: {
    color: '#EDEDED',
  },
  entity: {
    color: '#FFFFB6',
    cursor: 'help',
  },
  url: {
    color: '#676767',
  },
  '.language-css .token.string': {
    color: '#C1C1C1',
  },
  '.style .token.string': {
    color: '#C1C1C1',
  },
  atrule: {
    color: '#F9EE98',
  },
  'attr-value': {
    color: '#F9EE98',
  },
  function: {
    color: '#DAD085',
  },
  regex: {
    color: '#E9C062',
  },
  important: {
    color: '#fd971f',
    fontWeight: 'bold',
  },
  bold: {
    fontWeight: 'bold',
  },
  italic: {
    fontStyle: 'italic',
  },
};
