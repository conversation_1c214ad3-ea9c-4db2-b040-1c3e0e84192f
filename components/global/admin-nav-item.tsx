"use client";

import Link from "next/link";
import { useOrganization } from "@clerk/nextjs";

export function AdminNavItem() {
  const { isLoaded, organization } = useOrganization();

  // Check if user belongs to an organization with slug "root"
  const isAdminUser = isLoaded && organization?.slug === "root";

  if (!isLoaded || !isAdminUser) {
    return null;
  }

  return (
    <Link
      href="/admin"
    >管理后台
    </Link>
  );
}
