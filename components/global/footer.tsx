'use client'

import Link from "next/link";
import { SignedIn } from "@clerk/nextjs";
import { Icon } from '@iconify/react';

import { useUserStore } from "@/components/global/user-initializer";
import { ModelStatus } from "@/components/global/model-status";
import {
  FOOTER_BRAND,
  FOOTER_LINKS,
  FOOTER_EXTERNAL_LINKS
} from "@/constants";

export function Footer() {
  const credits = useUserStore((state) => state.credits);
  const currentYear = new Date().getFullYear();

  return (
    <footer className="max-w-[75rem] bg-white w-full mx-auto pt-4 pb-2 border-t border-[#EEEEF0] flex flex-col lg:flex-row justify-between gap-4 px-4">
      <div className="flex items-center gap-4 justify-center lg:justify-start">
        <a
          href={FOOTER_BRAND.href}
          target="_blank"
          className="flex gap-2 font-medium text-[0.8125rem] items-center"
        >
          <img
            src={FOOTER_BRAND.logo}
            alt={FOOTER_BRAND.name}
            className="w-4 h-4"
          />
          {FOOTER_BRAND.name}
          <span className="text-[#5E5F6E]">
            {FOOTER_BRAND.copyright(currentYear)}
          </span>
        </a>
        <a
          href={FOOTER_LINKS.terms.href}
          className="font-medium text-[0.8125rem] text-[#5E5F6E] hover:text-black transition-colors flex items-center"
        >
          {FOOTER_LINKS.terms.label}
        </a>
      </div>
      <div className="flex items-center gap-4 justify-center">
        <ModelStatus />
      </div>
      <ul className="flex flex-wrap gap-1 m:gap-2 justify-center m:justify-end">
        <SignedIn>
          <li>
            <Link
              href={FOOTER_LINKS.pricing.href}
              className="flex items-center gap-2 font-medium text-[0.8125rem] rounded-full px-3 py-2 hover:bg-gray-100"
            >
              <span>
                {FOOTER_LINKS.pricing.label.replace(
                  "{credits}",
                  String(credits)
                )}
              </span>
            </Link>
          </li>
          <li>
            <Link
              href={FOOTER_LINKS.invitation.href}
              className="flex items-center gap-2 font-medium text-[0.8125rem] rounded-full px-3 py-2 hover:bg-gray-100"
            >
              <span>{FOOTER_LINKS.invitation.label}</span>
            </Link>
          </li>
        </SignedIn>
        {FOOTER_EXTERNAL_LINKS.map((link, index) => (
          <li key={index}>
            <a
              href={link.href}
              target="_blank"
              className="flex items-center gap-1 font-medium text-[0.8125rem] rounded-full px-3 py-2 hover:bg-gray-100"
            >
              {link.icon && link.position === "prifix" && <Icon icon={link.icon} width="16" height="16" />}
              {link.label}{" "}
              {link.icon && link.position === "suffix" && <Icon icon={link.icon} width="16" height="16" />}
            </a>
          </li>
        ))}
      </ul>
    </footer>
  );
}
