import { User<PERSON>utton, SignedOut, SignUpButton } from "@clerk/nextjs";
import { SiteLogo } from "@/components/global/site-logo";
import { SiteTitle } from "@/components/global/site-title";
import { MainNav } from "@/components/global/main-nav";
import { NAVBAR } from "@/constants";

export function Navbar() {
  return (
    <header className="sm:fixed top-0 left-0 right-0 z-50 flex flex-wrap items-center justify-between w-full min-h-[3.5rem] sm:min-h-[4rem] px-2 sm:px-4 py-2 sm:py-0 rounded-xl bg-white/30 backdrop-blur-md border border-white/20">
      <div className="flex gap-4 items-center hidden md:flex">
        <SiteLogo />
        <div aria-hidden className="hidden md:block w-px h-6 bg-[#C7C7C8]" />
        <SiteTitle />
      </div>

      <div className="flex-1 min-w-0 flex justify-center">
        <MainNav />
      </div>

      <div className="flex items-center gap-2 flex-shrink-0">
        <SignedOut>
          <SignUpButton>
            <button className="px-3 sm:px-4 py-1.5 sm:py-2 rounded-full bg-[#131316] text-white text-sm font-semibold whitespace-nowrap">
              {NAVBAR.signUpButton}
            </button>
          </SignUpButton>
        </SignedOut>

        <UserButton
          showName={false}
          appearance={{
            elements: {
              userButtonAvatarBox: "size-6",
            },
          }}
        />
      </div>
    </header>
  );
}
