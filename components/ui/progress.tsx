"use client"

import * as React from "react"

import { cn } from "@/lib/utils"

// 成功率颜色方案 - 与 ModelStatus 组件中的方案保持一致
const getSuccessRateColorScheme = (rate: number) => {
  if (rate >= 90) return "bg-gradient-to-r from-green-700 to-green-600"; // 深绿
  if (rate >= 80) return "bg-gradient-to-r from-green-500 to-green-400"; // 浅绿
  if (rate >= 70) return "bg-gradient-to-r from-yellow-500 to-yellow-400"; // 黄色
  if (rate >= 60) return "bg-gradient-to-r from-red-400 to-red-300"; // 浅红
  return "bg-gradient-to-r from-red-600 to-red-500"; // 深红
};

const Progress = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    value?: number
    max?: number
    colorScheme?: string // 自定义颜色方案
  }
>(({ className, value = 0, max = 100, colorScheme, ...props }, ref) => {
  const percentage = Math.min(Math.max(0, (value / max) * 100), 100)

  // 如果提供了自定义颜色方案，则使用它，否则根据百分比计算颜色
  const barClass = colorScheme || getSuccessRateColorScheme(percentage);

  return (
    <div
      ref={ref}
      className={cn(
        "relative h-2 w-full overflow-hidden rounded-full bg-gray-100",
        className
      )}
      {...props}
    >
      <div
        className={cn("h-full transition-all", barClass)}
        style={{ width: `${percentage}%` }}
      />
    </div>
  )
})
Progress.displayName = "Progress"

export { Progress }
