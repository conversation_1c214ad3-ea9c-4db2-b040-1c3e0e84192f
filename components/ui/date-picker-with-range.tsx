"use client"

import * as React from "react"
import { useEffect, useRef } from "react"
import { CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import AirDatepicker from "air-datepicker"
import "air-datepicker/air-datepicker.css"
import "@/styles/air-datepicker-custom.css"
import localeZh from "air-datepicker/locale/zh"
import { DateRange } from "react-day-picker"

export function DatePickerWithRange({
  className,
  date,
  onSelect,
}: {
  className?: string
  date?: DateRange
  onSelect?: (date: DateRange | undefined) => void
}) {
  const inputRef = useRef<HTMLInputElement>(null)
  const dpRef = useRef<any>(null)

  useEffect(() => {
    if (inputRef.current) {
      dpRef.current = new AirDatepicker(inputRef.current, {
        locale: localeZh,
        range: true,
        multipleDatesSeparator: " - ",
        dateFormat: "yy-MM-dd",
        selectedDates: date?.from && date?.to ? [date.from, date.to] : [],
        onSelect: ({ date, formattedDate, datepicker }) => {
          const dates = datepicker.selectedDates
          if (dates.length === 2) {
            // 阻止事件冒泡和默认行为
            setTimeout(() => {
              onSelect?.({
                from: dates[0],
                to: dates[1]
              })
              datepicker.hide()
            }, 0)
            return false
          }
        },
        buttons: [{
          content: '清除',
          onClick: (dp) => {
            dp.clear();
            onSelect?.(undefined);
            return false; // 防止事件冒泡
          }
        }],
        classes: "air-datepicker-custom",
        isMobile: false, // 禁用移动模式，避免遮罩层问题
        position: "bottom right",
        minDate: new Date("2000-01-01"),
        maxDate: new Date("2030-12-31"),
        autoClose: false,
        onShow: () => {
          if (dpRef.current) {
            dpRef.current.$el.classList.add('air-datepicker-custom')

            // 移除遮罩层
            const overlay = document.querySelector('.air-datepicker-overlay')
            if (overlay) {
              overlay.remove()
            }
          }
        },
        onHide: () => {
          if (dpRef.current) {
            dpRef.current.$el.classList.remove('air-datepicker-custom')
          }
        }
      })
    }

    return () => {
      if (dpRef.current) {
        dpRef.current.destroy()
      }
    }
  }, [date, onSelect])

  return (
    <div className={cn("grid gap-2", className)}>
      <div className="relative">
        <Button
          type="button"
          variant={"outline"}
          className={cn(
            "w-full justify-start text-left font-normal",
            !date && "text-muted-foreground"
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          <input
            ref={inputRef}
            type="text"
            readOnly
            className="bg-transparent border-none focus:outline-none w-full cursor-pointer"
            placeholder="选择日期范围"
          />
        </Button>
      </div>
    </div>
  )
}
