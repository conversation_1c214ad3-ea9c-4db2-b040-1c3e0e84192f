"use client"

import { useEffect, useRef, ReactNode, isValidElement, useState } from 'react'
import { gsap } from 'gsap'
import { cn } from "@/lib/utils"
import Image from "next/image"

interface ImageItem {
  type: 'image'
  src: string
  alt: string
  width: number
  height: number
}

type GridItem = string | ReactNode | ImageItem

interface GridMotionProps {
  /**
   * Array of items to display in the grid
   */
  items?: GridItem[]
  /**
   * Color for the radial gradient background
   */
  gradientColor?: string
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * Whether the grid is being hovered
   */
  isHovering?: boolean
}

export function GridMotion({
  items = [],
  gradientColor = 'black',
  className,
  isHovering = false
}: GridMotionProps) {
  const gridRef = useRef<HTMLDivElement>(null)
  const rowRefs = useRef<(HTMLDivElement | null)[]>([])
  const mouseXRef = useRef(0)
  const isHoveringRef = useRef(false)
  const autoAnimateRef = useRef<gsap.core.Tween[]>([])
  const [mounted, setMounted] = useState(false)

  const totalItems = 21 // 3 rows × 7 columns
  const defaultItems = Array.from({ length: totalItems }, (_, index) => `Item ${index + 1}`)
  const combinedItems = items.length > 0 ? items : defaultItems

  // 处理自动动画
  useEffect(() => {
    if (!mounted) return

    const startAutoAnimation = () => {
      // Clear existing animations
      autoAnimateRef.current.forEach(tween => tween.kill())
      autoAnimateRef.current = []

      // Create new animations for each row
      rowRefs.current.forEach((row, index) => {
        if (row) {
          const direction = index % 2 === 0 ? 1 : -1
          const speeds = isHovering ? [50, 40, 60] : [25, 20, 30] // 上中下行的速度（秒）
          const distances = [200, 300, 150] // 上中下行的移动距离

          const tween = gsap.to(row, {
            x: direction * distances[index],
            duration: speeds[index],
            ease: 'none',
            repeat: -1,
            yoyo: true,
            yoyoEase: true,
            delay: index * 0.5
          })

          autoAnimateRef.current.push(tween)
        }
      })
    }

    startAutoAnimation()

    return () => {
      autoAnimateRef.current.forEach(tween => tween.kill())
    }
  }, [mounted, isHovering])

  // 处理鼠标交互
  useEffect(() => {
    if (!mounted) return

    mouseXRef.current = window.innerWidth / 2
    gsap.ticker.lagSmoothing(0)

    const handleMouseMove = (e: MouseEvent) => {
      if (isHoveringRef.current) {
        mouseXRef.current = e.clientX
      }
    }

    const handleMouseEnter = () => {
      isHoveringRef.current = true
      autoAnimateRef.current.forEach(tween => tween.kill())
    }

    const handleMouseLeave = () => {
      isHoveringRef.current = false
      // 重新启动自动动画
      const startAutoAnimation = () => {
        autoAnimateRef.current.forEach(tween => tween.kill())
        autoAnimateRef.current = []

        rowRefs.current.forEach((row, index) => {
          if (row) {
            const direction = index % 2 === 0 ? 1 : -1
            const speeds = [25, 20, 30]
            const distances = [200, 300, 150]

            const tween = gsap.to(row, {
              x: direction * distances[index],
              duration: speeds[index],
              ease: 'none',
              repeat: -1,
              yoyo: true,
              yoyoEase: true,
              delay: index * 0.5
            })

            autoAnimateRef.current.push(tween)
          }
        })
      }
      startAutoAnimation()
    }

    const updateMotion = () => {
      if (!isHoveringRef.current) return

      const maxMoveAmount = 300
      const baseDuration = 0.8
      const inertiaFactors = [0.4, 0.6, 0.4]

      rowRefs.current.forEach((row, index) => {
        if (row) {
          const direction = index % 2 === 0 ? 1 : -1
          const moveAmount = ((mouseXRef.current / window.innerWidth) * maxMoveAmount - maxMoveAmount / 2) * direction

          gsap.to(row, {
            x: moveAmount,
            duration: baseDuration + inertiaFactors[index],
            ease: 'power3.out',
            overwrite: 'auto',
          })
        }
      })
    }

    const removeAnimationLoop = gsap.ticker.add(updateMotion)
    window.addEventListener('mousemove', handleMouseMove)
    gridRef.current?.addEventListener('mouseenter', handleMouseEnter)
    gridRef.current?.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
      gridRef.current?.removeEventListener('mouseenter', handleMouseEnter)
      gridRef.current?.removeEventListener('mouseleave', handleMouseLeave)
      removeAnimationLoop()
    }
  }, [mounted])

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className={cn("h-full w-full overflow-hidden", className)}>
        <section
          className="relative flex w-full items-center justify-center overflow-hidden"
          style={{
            height: 'calc(100vh - 300px)',
            background: `radial-gradient(circle, ${gradientColor} 0%, transparent 100%)`,
          }}
        />
      </div>
    )
  }

  const renderItem = (item: GridItem): ReactNode => {
    if (typeof item === 'string') {
      if (item.startsWith('http')) {
        return (
          <div
            className="absolute inset-0 bg-cover bg-center"
            style={{
              backgroundImage: `url(${item})`,
            }}
          />
        )
      }
      return <div className="p-4 text-center z-1">{item}</div>
    }

    if (typeof item === 'object' && item !== null && 'type' in item && item.type === 'image') {
      const imageItem = item as ImageItem
      return (
        <Image
          src={imageItem.src}
          alt={imageItem.alt}
          width={imageItem.width}
          height={imageItem.height}
          className="object-cover w-full h-full"
          priority
        />
      )
    }

    if (isValidElement(item)) {
      return item
    }

    return null
  }

  return (
    <div
      className={cn("h-full w-full overflow-hidden cursor-grab active:cursor-grabbing", className)}
      ref={gridRef}
    >
      <section
        className="relative flex w-full items-center justify-center overflow-hidden"
        style={{
          height: 'calc(100vh - 300px)',
          background: `radial-gradient(circle, ${gradientColor} 0%, transparent 100%)`,
        }}
      >
        <div
          className="relative z-2 flex-none grid gap-6 grid-rows-[0.8fr,1.2fr,0.8fr] grid-cols-[100%] origin-center"
          style={{
            height: 'calc(100vh - 100px)',
            width: '150vw',
            transform: 'rotate(-25deg) scale(1.2)',
          }}
        >
          {[...Array(3)].map((_, rowIndex) => (
            <div
              key={rowIndex}
              className={cn(
                "grid gap-6 grid-cols-[repeat(7,1fr)] will-change-transform will-change-filter",
                rowIndex === 1 ? "scale-110" : "scale-90"
              )}
              ref={(el) => {
                rowRefs.current[rowIndex] = el;
              }}
            >
              {[...Array(7)].map((_, itemIndex) => {
                const content = combinedItems[rowIndex * 7 + itemIndex]
                return (
                  <div key={itemIndex} className="relative aspect-square transform hover:scale-105 transition-transform duration-300">
                    <div className="relative h-full w-full overflow-hidden rounded-xl bg-muted flex items-center justify-center text-foreground text-xl shadow-lg">
                      {renderItem(content)}
                    </div>
                  </div>
                )
              })}
            </div>
          ))}
        </div>
      </section>
    </div>
  )
}
