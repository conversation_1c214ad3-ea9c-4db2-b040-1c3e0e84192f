"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { BackupForm } from "./backup-form";
import { BackupExecutionsTable } from "./backup-executions-table";
import { BackupExecutionDetail } from "./backup-execution-detail";

export function AdminBackup() {
  const [activeTab, setActiveTab] = useState("create");
  const [selectedExecutionId, setSelectedExecutionId] = useState<string | null>(null);

  const handleExecutionCreated = (executionId: string) => {
    setSelectedExecutionId(executionId);
    setActiveTab("detail");
  };

  const handleBackToList = () => {
    setSelectedExecutionId(null);
    setActiveTab("history");
  };

  return (
    <div className="relative">
      <div className="p-4 sm:p-8 rounded-xl bg-white shadow-[0_5px_5px_rg<PERSON>(0,0,0,0.08),0_5px_12px_-1px_rgba(25,28,33,0.2)] w-full">
        <div className="flex justify-between items-center mb-6">
          <div className="items-center gap-2">
            <h2 className="text-[0.9375rem] font-semibold">图片备份</h2>
            <p className="text-xs text-blue-500 font-medium">
              将历史记录中的图片备份到 Cloudflare R2 存储
            </p>
          </div>
        </div>

        {selectedExecutionId ? (
          <BackupExecutionDetail 
            executionId={selectedExecutionId} 
            onBack={handleBackToList} 
          />
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="create">创建备份任务</TabsTrigger>
              <TabsTrigger value="history">备份历史记录</TabsTrigger>
            </TabsList>

            <TabsContent value="create" className="mt-4">
              <BackupForm onSuccess={handleExecutionCreated} />
            </TabsContent>

            <TabsContent value="history" className="mt-4">
              <BackupExecutionsTable />
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  );
}
