"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import { DateRange } from "react-day-picker";

import { TIMEOUT_SECONDS } from "@/constants/system";
import { useToast } from "@/lib/hooks/use-toast";

import { DatePickerWithRange } from "@/components/ui/date-picker-with-range";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface BackupFormProps {
  onSuccess?: (executionId: string) => void;
}

export function BackupForm({ onSuccess }: BackupFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().setDate(new Date().getDate() - 7)),
    to: new Date(),
  });
  const [isLoading, setIsLoading] = useState(false);
  const [dryRun, setDryRun] = useState(false);
  const [skipBackupCheck, setSkipBackupCheck] = useState(false);
  const [batchSize, setBatchSize] = useState(10);
  const [timeoutSeconds, setTimeoutSeconds] = useState(TIMEOUT_SECONDS);

  const handleBackup = async () => {
    if (!dateRange?.from || !dateRange?.to) {
      toast({
        title: "日期范围错误",
        description: "请选择有效的日期范围",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      const response = await fetch("/api/admin/backup/image", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          startDate: dateRange.from.toISOString(),
          endDate: dateRange.to.toISOString(),
          dryRun,
          batchSize,
          skipBackupCheck,
          timeoutSeconds,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "创建备份任务失败");
      }

      const data = await response.json();

      toast({
        title: "备份任务已创建",
        description: `备份任务 ID: ${data.executionId}，正在后台处理中`,
        variant: "default",
      });

      if (onSuccess) {
        onSuccess(data.executionId);
      } else {
        // Navigate to execution detail page
        router.push(`/admin/backup/${data.executionId}`);
      }
    } catch (error) {
      toast({
        title: "创建备份任务失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>备份设置</CardTitle>
        <CardDescription>
          选择要备份的日期范围和备份选项
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label>日期范围</Label>
          <DatePickerWithRange
            date={dateRange}
            onSelect={setDateRange}
          />
        </div>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="dryRun"
              checked={dryRun}
              onCheckedChange={(checked) =>
                setDryRun(checked as boolean)
              }
            />
            <Label htmlFor="dryRun">
              仅模拟运行（勾选此项将不会实际修改数据）
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="skipBackupCheck"
              checked={skipBackupCheck}
              onCheckedChange={(checked) =>
                setSkipBackupCheck(checked as boolean)
              }
            />
            <Label htmlFor="skipBackupCheck">
              忽略备份状态检查（勾选此项将重新备份所有图片，包括已备份的）
            </Label>
          </div>

          <div className="space-y-2">
            <Label htmlFor="batchSize">
              批处理大小（每批并发处理的图片数量）
            </Label>
            <div className="flex items-center space-x-2">
              <input
                id="batchSize"
                type="number"
                min="1"
                max="50"
                value={batchSize}
                onChange={(e) =>
                  setBatchSize(parseInt(e.target.value) || 10)
                }
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="timeoutSeconds">
              最大运行时间（秒）（设置为 0 表示无限制）
            </Label>
            <div className="flex items-center space-x-2">
              <input
                id="timeoutSeconds"
                type="number"
                min="0"
                max="3600"
                value={timeoutSeconds}
                onChange={(e) =>
                  setTimeoutSeconds(parseInt(e.target.value) || TIMEOUT_SECONDS)
                }
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
          </div>
        </div>
        <Button
          onClick={handleBackup}
          disabled={isLoading || !dateRange?.from || !dateRange?.to}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              创建备份任务中...
            </>
          ) : dryRun ? (
            "创建模拟备份任务"
          ) : (
            "创建实际备份任务"
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
