"use client";

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { AlertCircle } from "lucide-react";
import Link from "next/link";
import { Execution } from "./backup-types";

interface BackupExecutionDetailsProps {
  execution: Execution;
}

export function BackupExecutionDetails({ execution }: BackupExecutionDetailsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>详细信息</CardTitle>
        <CardDescription>
          备份任务处理的详细信息
        </CardDescription>
      </CardHeader>
      <CardContent>
        {(!execution.summary || !execution.summary.processed || execution.summary.processed.length === 0) ? (
          <div className="flex flex-col items-center justify-center py-8">
            <AlertCircle className="h-10 w-10 text-yellow-500 mb-2" />
            <p className="text-gray-500">暂无详细信息</p>
          </div>
        ) : (
          <ScrollArea className="h-[500px]">
            <div className="space-y-4">
              {execution.summary.processed.map((item, index) => (
                <div
                  key={index}
                  className="p-4 border rounded-lg space-y-2"
                >
                  <div className="flex justify-between">
                    <span className="font-medium">
                      历史记录 ID:
                      <Link
                        href={`/admin/histories/${item.historyId}`}
                        className="text-blue-600 hover:underline ml-1"
                      >
                        {item.historyId}
                      </Link>
                    </span>
                    <Badge variant="outline">
                      {item.shareId ? "有分享" : "无分享"}
                    </Badge>
                  </div>
                  <Separator />

                  {/* 图片预览 */}
                  {item.backupStatus === "SUCCESS" && (
                    <div className="grid grid-cols-2 gap-4 my-2">
                      <div className="flex flex-col items-center">
                        <span className="text-xs text-gray-500 mb-1">原始图片</span>
                        <div className="relative w-full h-40 border rounded overflow-hidden">
                          <img
                            src={item.oldUrl}
                            alt="原始图片"
                            className="object-contain w-full h-full"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/placeholder-image.jpg';
                              target.alt = '图片加载失败';
                            }}
                          />
                        </div>
                      </div>
                      <div className="flex flex-col items-center">
                        <span className="text-xs text-gray-500 mb-1">备份图片</span>
                        <div className="relative w-full h-40 border rounded overflow-hidden">
                          <img
                            src={item.newUrl.startsWith('[DRY RUN]') ? item.oldUrl : item.newUrl}
                            alt="备份图片"
                            className="object-contain w-full h-full"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/placeholder-image.jpg';
                              target.alt = '图片加载失败';
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-1 gap-2 text-sm">
                    <div className="flex flex-col">
                      <span className="text-gray-500">原始 URL:</span>
                      <span className="text-xs break-all">
                        {item.oldUrl}
                      </span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-gray-500">新 URL:</span>
                      <span className="text-xs break-all">
                        {item.newUrl}
                      </span>
                    </div>
                    {item.shareId && (
                      <div className="flex flex-col">
                        <span className="text-gray-500">
                          分享 ID:
                        </span>
                        <span>{item.shareId}</span>
                      </div>
                    )}
                    {item.backupStatus && (
                      <div className="flex flex-col">
                        <span className="text-gray-500">
                          备份状态:
                        </span>
                        <span>
                          {item.backupStatus === "SUCCESS" && (
                            <span className="text-green-600">成功</span>
                          )}
                          {item.backupStatus === "FAILED" && (
                            <span className="text-red-600">失败</span>
                          )}
                          {item.backupStatus === "PENDING" && (
                            <span className="text-yellow-600">待处理</span>
                          )}
                          {item.backupStatus === "SKIPPED" && (
                            <span className="text-gray-600">已跳过</span>
                          )}
                        </span>
                      </div>
                    )}
                    {item.lastBackupAt && (
                      <div className="flex flex-col">
                        <span className="text-gray-500">
                          最后备份时间:
                        </span>
                        <span>{new Date(item.lastBackupAt).toLocaleString()}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
