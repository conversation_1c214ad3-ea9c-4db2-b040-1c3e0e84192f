"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Clock, Loader2, FileText } from "lucide-react";
import { Execution } from "./backup-types";

interface BackupExecutionSummaryProps {
  execution: Execution;
}

export function BackupExecutionSummary({ execution }: BackupExecutionSummaryProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>备份摘要</CardTitle>
        <CardDescription>
          备份任务执行结果摘要
        </CardDescription>
      </CardHeader>
      <CardContent>
        {execution.status === "PENDING" && (
          <div className="flex flex-col items-center justify-center py-8">
            <Clock className="h-10 w-10 text-yellow-500 mb-2" />
            <p className="text-gray-500">任务等待中...</p>
          </div>
        )}

        {execution.status === "RUNNING" && (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-10 w-10 text-blue-500 mb-2 animate-spin" />
            <p className="text-gray-500">任务执行中...</p>
            {execution.summary && execution.summary.totalProcessed > 0 && (
              <div className="mt-4 text-center">
                <p className="text-sm">已处理: {execution.summary.totalProcessed}</p>
                <p className="text-sm text-green-600">成功: {execution.summary.succeeded}</p>
                <p className="text-sm text-red-600">失败: {execution.summary.failed}</p>
                <p className="text-sm text-yellow-600">跳过: {execution.summary.skipped}</p>
              </div>
            )}
          </div>
        )}

        {(execution.status === "SUCCESS" || execution.status === "FAILED") && execution.summary && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                <span className="text-sm text-gray-500">总计</span>
                <span className="text-2xl font-bold">
                  {execution.summary.totalProcessed}
                </span>
              </div>
              <div className="flex flex-col items-center justify-center p-4 bg-green-50 rounded-lg">
                <span className="text-sm text-green-500">成功</span>
                <span className="text-2xl font-bold text-green-600">
                  {execution.summary.succeeded}
                </span>
              </div>
              <div className="flex flex-col items-center justify-center p-4 bg-red-50 rounded-lg">
                <span className="text-sm text-red-500">失败</span>
                <span className="text-2xl font-bold text-red-600">
                  {execution.summary.failed}
                </span>
              </div>
              <div className="flex flex-col items-center justify-center p-4 bg-yellow-50 rounded-lg">
                <span className="text-sm text-yellow-500">跳过</span>
                <span className="text-2xl font-bold text-yellow-600">
                  {execution.summary.skipped}
                </span>
              </div>
            </div>

            {execution.startedAt && execution.completedAt && (
              <div className="grid grid-cols-2 gap-4 mt-4">
                <div className="flex flex-col items-center justify-center p-4 bg-blue-50 rounded-lg">
                  <span className="text-sm text-blue-500">总用时</span>
                  <span className="text-2xl font-bold text-blue-600">
                    {(() => {
                      const startTime = new Date(execution.startedAt);
                      const endTime = new Date(execution.completedAt);
                      const durationMs = endTime.getTime() - startTime.getTime();
                      const seconds = Math.floor(durationMs / 1000);
                      const minutes = Math.floor(seconds / 60);
                      const remainingSeconds = seconds % 60;
                      return `${minutes}分${remainingSeconds}秒`;
                    })()}
                  </span>
                </div>
                {execution.summary.succeeded > 0 && (
                  <div className="flex flex-col items-center justify-center p-4 bg-blue-50 rounded-lg">
                    <span className="text-sm text-blue-500">平均每张用时</span>
                    <span className="text-2xl font-bold text-blue-600">
                      {(() => {
                        const startTime = new Date(execution.startedAt);
                        const endTime = new Date(execution.completedAt);
                        const durationMs = endTime.getTime() - startTime.getTime();
                        const avgTimePerImageMs = durationMs / execution.summary.succeeded;
                        const avgTimePerImageSec = Math.floor(avgTimePerImageMs / 1000);
                        if (avgTimePerImageSec < 1) {
                          return `${Math.floor(avgTimePerImageMs)}毫秒`;
                        } else if (avgTimePerImageSec < 60) {
                          return `${avgTimePerImageSec}秒`;
                        } else {
                          const minutes = Math.floor(avgTimePerImageSec / 60);
                          const seconds = avgTimePerImageSec % 60;
                          return `${minutes}分${seconds}秒`;
                        }
                      })()}
                    </span>
                  </div>
                )}
              </div>
            )}

            {execution.summary.logUrl && (
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-blue-500 mr-2" />
                  <span className="text-sm text-blue-700">
                    日志文件
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(execution.summary.logUrl, "_blank")}
                >
                  查看日志
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
