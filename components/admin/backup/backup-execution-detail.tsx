"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/lib/hooks/use-toast";
import { Loader2, ArrowLeft, RefreshCw, AlertCircle } from "lucide-react";
import Link from "next/link";
import { Execution } from "./backup-types";
import { BackupExecutionSummary } from "./backup-execution-summary";
import { BackupExecutionLogs } from "./backup-execution-logs";
import { BackupExecutionDetails } from "./backup-execution-details";

interface BackupExecutionDetailProps {
  executionId: string;
  onBack?: () => void;
}

export function BackupExecutionDetail({ executionId, onBack }: BackupExecutionDetailProps) {
  const { toast } = useToast();
  const [execution, setExecution] = useState<Execution | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("summary");
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  // Fetch execution details
  const fetchExecution = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/executions/${executionId}`);

      if (!response.ok) {
        if (response.status === 404) {
          toast({
            title: "备份任务不存在",
            description: `ID 为 ${executionId} 的备份任务不存在`,
            variant: "destructive",
          });
          if (onBack) {
            onBack();
          }
          return;
        }
        throw new Error("Failed to fetch execution");
      }

      const data = await response.json();
      setExecution(data.execution);
    } catch (error) {
      toast({
        title: "获取备份任务详情失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Set up polling for in-progress executions
  useEffect(() => {
    fetchExecution();

    // Clean up polling interval on unmount
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [executionId]);

  // Set up or clear polling based on execution status
  useEffect(() => {
    if (execution) {
      if (execution.status === "PENDING" || execution.status === "RUNNING") {
        // Set up polling for in-progress executions
        const interval = setInterval(fetchExecution, 5000);
        setPollingInterval(interval);
        return () => clearInterval(interval);
      } else if (pollingInterval) {
        // Clear polling for completed executions
        clearInterval(pollingInterval);
        setPollingInterval(null);
      }
    }
  }, [execution?.status]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">等待中</Badge>;
      case "RUNNING":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">运行中</Badge>;
      case "SUCCESS":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">成功</Badge>;
      case "FAILED":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">失败</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleString();
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-16">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (!execution) {
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <AlertCircle className="h-10 w-10 text-yellow-500 mb-2" />
        <p className="text-gray-500">备份任务不存在或加载失败</p>
        <Button variant="outline" className="mt-4" onClick={onBack} asChild={!onBack}>
          {onBack ? (
            <span>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回备份页面
            </span>
          ) : (
            <Link href="/admin/backup">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回备份页面
            </Link>
          )}
        </Button>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={onBack} asChild={!onBack}>
            {onBack ? (
              <span>
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </span>
            ) : (
              <Link href="/admin/backup">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </Link>
            )}
          </Button>
          <div>
            <h2 className="text-[0.9375rem] font-semibold">备份任务详情</h2>
            <p className="text-xs text-blue-500 font-medium">
              ID: {execution.id}
            </p>
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchExecution}
          disabled={isLoading}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          刷新
        </Button>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>任务信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-1">
              <p className="text-sm text-gray-500">状态</p>
              <div>{getStatusBadge(execution.status)}</div>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-gray-500">创建时间</p>
              <p className="text-sm">{formatDate(execution.createdAt)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-gray-500">开始时间</p>
              <p className="text-sm">{formatDate(execution.startedAt)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-gray-500">完成时间</p>
              <p className="text-sm">{formatDate(execution.completedAt)}</p>
            </div>
          </div>

          <Separator className="my-4" />

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-1">
              <p className="text-sm text-gray-500">备份类型</p>
              <p className="text-sm">{execution.params.dryRun ? '模拟备份' : '实际备份'}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-gray-500">批处理大小（并发处理数）</p>
              <p className="text-sm">{execution.params.batchSize}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-gray-500">忽略备份状态</p>
              <p className="text-sm">{execution.params.skipBackupCheck ? '是' : '否'}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-gray-500">日期范围</p>
              <p className="text-sm">
                {new Date(execution.params.startDate).toLocaleDateString()} 至 {new Date(execution.params.endDate).toLocaleDateString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="summary">摘要</TabsTrigger>
          <TabsTrigger value="logs">日志</TabsTrigger>
          <TabsTrigger value="details">详细信息</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="mt-4">
          <BackupExecutionSummary execution={execution} />
        </TabsContent>

        <TabsContent value="logs" className="mt-4">
          <BackupExecutionLogs execution={execution} />
        </TabsContent>

        <TabsContent value="details" className="mt-4">
          <BackupExecutionDetails execution={execution} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
