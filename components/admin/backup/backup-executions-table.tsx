"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, RefreshCw, List, AlertCircle } from "lucide-react";
import { useToast } from "@/lib/hooks/use-toast";
import Link from "next/link";
import { ExecutionListItem, Pagination } from "./backup-types";

interface BackupExecutionsTableProps {
  initialPage?: number;
  initialLimit?: number;
}

export function BackupExecutionsTable({ 
  initialPage = 1, 
  initialLimit = 10 
}: BackupExecutionsTableProps) {
  const { toast } = useToast();
  const [executions, setExecutions] = useState<ExecutionListItem[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: initialPage,
    limit: initialLimit,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  });
  const [isLoading, setIsLoading] = useState(false);

  // Fetch executions on mount and when pagination changes
  useEffect(() => {
    fetchExecutions();
  }, [pagination.page, pagination.limit]);

  const fetchExecutions = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/executions?type=BACKUP_IMAGES&page=${pagination.page}&limit=${pagination.limit}`);

      if (!response.ok) {
        throw new Error("Failed to fetch executions");
      }

      const data = await response.json();
      setExecutions(data.executions);
      setPagination(data.pagination);
    } catch (error) {
      toast({
        title: "获取备份执行记录失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">等待中</Badge>;
      case "RUNNING":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">运行中</Badge>;
      case "SUCCESS":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">成功</Badge>;
      case "FAILED":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">失败</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleString();
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>备份历史记录</CardTitle>
          <CardDescription>查看所有备份任务的执行情况</CardDescription>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchExecutions}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
        </Button>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        ) : executions.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8">
            <AlertCircle className="h-10 w-10 text-yellow-500 mb-2" />
            <p className="text-gray-500">没有找到备份任务记录</p>
          </div>
        ) : (
          <>
            <div className="rounded-md border">
              <div className="grid grid-cols-12 gap-2 p-4 bg-gray-50 font-medium text-sm">
                <div className="col-span-2">状态</div>
                <div className="col-span-3">起始时间</div>
                <div className="col-span-3">结束时间</div>
                <div className="col-span-3">完成时间</div>
                <div className="col-span-1">操作</div>
              </div>
              <div className="divide-y">
                {executions.map((execution) => (
                  <div
                    key={execution.id}
                    className="grid grid-cols-12 gap-2 p-4 text-sm items-center"
                  >
                    <div className="col-span-2">
                      {getStatusBadge(execution.status)}
                    </div>
                    <div className="col-span-3 text-xs">
                      {formatDate(execution.params?.startDate)}
                    </div>
                    <div className="col-span-3 text-xs">
                      {formatDate(execution.params?.endDate)}
                    </div>
                    <div className="col-span-3 text-xs">
                      {formatDate(execution.completedAt)}
                    </div>
                    <div className="col-span-1">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/admin/backup/${execution.id}`}>
                          <List className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                共 {pagination.totalCount} 条记录，第 {pagination.page}{" "}
                / {pagination.totalPages} 页
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination.hasPrevPage}
                  onClick={() =>
                    setPagination((prev) => ({
                      ...prev,
                      page: prev.page - 1,
                    }))
                  }
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination.hasNextPage}
                  onClick={() =>
                    setPagination((prev) => ({
                      ...prev,
                      page: prev.page + 1,
                    }))
                  }
                >
                  下一页
                </Button>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
