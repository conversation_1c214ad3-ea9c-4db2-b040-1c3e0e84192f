export interface BackupResult {
  totalProcessed: number;
  succeeded: number;
  failed: number;
  skipped: number;
  errors: Array<{
    historyId: string;
    error: string;
  }>;
  processed: Array<{
    historyId: string;
    oldUrl: string;
    newUrl: string;
    shareId?: string;
    backupStatus?: string;
    lastBackupAt?: string;
  }>;
  logUrl?: string;
}

export interface Execution {
  id: string;
  type: string;
  status: string;
  params: {
    startDate: string;
    endDate: string;
    dryRun: boolean;
    batchSize: number;
    skipBackupCheck: boolean;
    timeoutSeconds?: number;
  };
  summary: BackupResult;
  logs: string;
  startedAt: string | null;
  completedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface ExecutionListItem {
  id: string;
  type: string;
  status: string;
  params: {
    startDate: string;
    endDate: string;
    dryRun: boolean;
    batchSize: number;
    skipBackupCheck: boolean;
    timeoutSeconds?: number;
  };
  summary: BackupResult;
  startedAt: string | null;
  completedAt: string | null;
  createdAt: string;
}

export interface Pagination {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}
