"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Download } from "lucide-react";
import { Execution } from "./backup-types";

interface BackupExecutionLogsProps {
  execution: Execution;
}

export function BackupExecutionLogs({ execution }: BackupExecutionLogsProps) {
  const downloadLogs = () => {
    if (!execution) return;

    // Create a blob with the logs content
    const blob = new Blob([execution.logs], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    // Create a temporary link and trigger download
    const a = document.createElement('a');
    a.href = url;
    a.download = `backup-execution-${execution.id}-logs.txt`;
    document.body.appendChild(a);
    a.click();

    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>执行日志</CardTitle>
          <CardDescription>
            备份任务执行过程中的详细日志
          </CardDescription>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={downloadLogs}
          disabled={!execution.logs}
        >
          <Download className="h-4 w-4 mr-2" />
          下载日志
        </Button>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[500px] w-full rounded-md border p-4">
          <pre className="text-xs font-mono whitespace-pre-wrap">
            {execution.logs || "暂无日志"}
          </pre>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
