"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/lib/hooks/use-toast";
import { format } from "date-fns";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Loader2, Plus, Search } from "lucide-react";

// Define the form schema
const formSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  points: z.number().positive("Points must be a positive number"),
  type: z.enum(["refund", "gift", "award", "affiliate", "other"], {
    required_error: "Please select a type",
  }),
  historyId: z.string().optional(),
  orderId: z.string().optional(),
  description: z.string().optional(),
  note: z.string().optional(),
});

interface User {
  clerkId: string;
  username: string;
  email: string;
  avatarUrl?: string;
  createdAt: string;
  wallet?: {
    permanentPoints: number;
    id?: string;
  };
}

interface Order {
  id: string;
  userId: string;
  buyerId: string;
  type: string;
  amount: number;
  description: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  extra: {
    exchangeType?: string;
    historyId?: string;
    relatedOrderId?: string;
    note?: string;
    pointsExchange?: {
      oldBalance: number;
      newBalance: number;
      timestamp: string;
    };
    [key: string]: any;
  };
  user?: {
    clerkId: string;
    username: string;
    email: string;
    avatarUrl?: string;
  };
}

export function ExchangePointsTable() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedTab, setSelectedTab] = useState<"history" | "order">("history");
  const [historyId, setHistoryId] = useState("");
  const [orderId, setOrderId] = useState("");
  const [exchangeOrders, setExchangeOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [isLoadingUserData, setIsLoadingUserData] = useState(false);
  const [userHistories, setUserHistories] = useState<any[]>([]);
  const [userOrders, setUserOrders] = useState<Order[]>([]);

  const { toast } = useToast();
  const router = useRouter();

  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      userId: "",
      points: 0,
      type: "gift",
      historyId: "",
      orderId: "",
      description: "",
      note: "",
    },
  });

  // Function to fetch exchange orders (orders with exchangeType in extra)
  const fetchExchangeOrders = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/orders?limit=50");
      if (!response.ok) {
        throw new Error("Failed to fetch orders");
      }
      const data = await response.json();

      // Filter orders that have exchangeType in extra
      const exchangeOrders = data.orders.filter(
        (order: Order) => order.extra && order.extra.exchangeType
      );

      setExchangeOrders(exchangeOrders);
    } catch (error) {
      console.error("Error fetching exchange orders:", error);
      toast({
        title: "Error",
        description: "Failed to load exchange orders",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Load exchange orders on component mount
  useEffect(() => {
    fetchExchangeOrders();
  }, []);

  // Search users
  const searchUsers = async () => {
    if (!searchQuery.trim() || searchQuery.length < 2) {
      toast({
        title: "Search query too short",
        description: "Please enter at least 2 characters",
        variant: "default",
      });
      return;
    }

    try {
      setIsSearching(true);
      const response = await fetch(`/api/admin/users/search?query=${encodeURIComponent(searchQuery)}`);

      if (!response.ok) {
        throw new Error("Failed to search users");
      }

      const data = await response.json();
      setSearchResults(data.users);

      if (data.users.length === 0) {
        toast({
          title: "No users found",
          description: "Try a different search term",
          variant: "default",
        });
      }
    } catch (error) {
      console.error("Error searching users:", error);
      toast({
        title: "Error",
        description: "Failed to search users",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };

  // Select user
  const handleSelectUser = (user: User) => {
    setSelectedUser(user);
    form.setValue("userId", user.clerkId);
    setSearchResults([]);
    setSearchQuery("");

    // Fetch user's recent histories and orders
    fetchUserData(user.clerkId);
  };

  // Fetch user's recent histories and orders (last week)
  const fetchUserData = async (userId: string) => {
    setIsLoadingUserData(true);
    try {
      // Calculate date range for last week
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 7);

      // Fetch histories
      const historiesParams = new URLSearchParams({
        userId,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        limit: "20"
      });

      const historiesResponse = await fetch(`/api/admin/histories?${historiesParams.toString()}`);
      if (!historiesResponse.ok) throw new Error("Failed to fetch user histories");
      const historiesData = await historiesResponse.json();
      setUserHistories(historiesData.histories || []);

      // Fetch orders
      const ordersParams = new URLSearchParams({
        userId,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        limit: "20"
      });

      const ordersResponse = await fetch(`/api/admin/orders?${ordersParams.toString()}`);
      if (!ordersResponse.ok) throw new Error("Failed to fetch user orders");
      const ordersData = await ordersResponse.json();
      setUserOrders(ordersData.orders || []);
    } catch (error) {
      console.error("Error fetching user data:", error);
      toast({
        title: "Error",
        description: "Failed to load user data",
        variant: "destructive",
      });
    } finally {
      setIsLoadingUserData(false);
    }
  };

  // Handle history selection
  const handleHistorySelect = (id: string, points: number) => {
    setHistoryId(id);
    form.setValue("historyId", id);
    form.setValue("orderId", ""); // Clear order ID when selecting a history
    setOrderId(""); // Clear order ID state
    form.setValue("points", points);
  };

  // Handle order selection
  const handleOrderSelect = (id: string, amount: number) => {
    setOrderId(id);
    form.setValue("orderId", id);
    form.setValue("historyId", ""); // Clear history ID when selecting an order
    setHistoryId(""); // Clear history ID state
    form.setValue("points", amount);
  };

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsSubmitting(true);

      const response = await fetch("/api/admin/exchange/points", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...values,
          sendBy: "system", // Default to system
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to exchange points");
      }

      await response.json();

      toast({
        title: "Success",
        description: `Added ${values.points} points to user's wallet`,
      });

      // Close dialog and refresh data
      setIsDialogOpen(false);
      form.reset();
      setSelectedUser(null);
      setHistoryId("");
      setOrderId("");

      // Refresh the exchange orders list
      fetchExchangeOrders();

    } catch (error) {
      console.error("Error exchanging points:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to exchange points",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "yyyy-MM-dd HH:mm:ss");
  };

  // Get user initials for avatar
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  // Get badge color based on exchange type
  const getExchangeTypeBadge = (type: string) => {
    switch (type) {
      case "refund":
        return <Badge variant="destructive">{type}</Badge>;
      case "gift":
        return <Badge variant="secondary">{type}</Badge>;
      case "award":
        return <Badge variant="default">{type}</Badge>;
      case "affiliate":
        return <Badge variant="outline">{type}</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between">
        <div></div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Points
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[900px]">
            <DialogHeader>
              <DialogTitle>Add Points to User</DialogTitle>
              <DialogDescription>
                Add points to a user's wallet for refunds, gifts, awards, or affiliate rewards.
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Main form layout - horizontal split */}
                <div className="flex flex-col md:flex-row gap-6">
                  {/* Left column - Required fields */}
                  <div className="space-y-6 flex-1">
                    {/* User Selection */}
                    <FormLabel>Select User</FormLabel>
                    {selectedUser ? (
                    <div className="flex items-center justify-between p-3 border rounded-md">
                      <div className="flex items-center space-x-4">
                        <Avatar>
                          <AvatarImage src={selectedUser.avatarUrl} />
                          <AvatarFallback>{getInitials(selectedUser.username)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{selectedUser.username}</p>
                          <p className="text-sm text-muted-foreground">{selectedUser.email}</p>
                          <div className="flex flex-col space-y-1 mt-1 text-xs text-muted-foreground">
                            <p>ID: <span className="font-mono break-all">{selectedUser.clerkId}</span></p>
                            <p>Registered: {format(new Date(selectedUser.createdAt), "yyyy-MM-dd")}</p>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end space-y-2">
                        <div className="text-right">
                          <p className="text-sm font-medium">Current Balance</p>
                          <p className="text-lg font-bold text-green-600">
                            {selectedUser.wallet?.permanentPoints || 0} points
                          </p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          type="button"
                          onClick={() => {
                            setSelectedUser(null);
                            form.setValue("userId", "");
                          }}
                        >
                          Clear
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="flex space-x-2">
                        <div className="relative flex-1">
                          <Input
                            placeholder="Search by email, username, or user ID"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                searchUsers();
                              }
                            }}
                            className="pr-10"
                          />
                          {searchQuery && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-0 top-0 h-full w-10 p-0"
                              onClick={() => setSearchQuery('')}
                            >
                              <span className="sr-only">Clear</span>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="h-4 w-4"
                              >
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                              </svg>
                            </Button>
                          )}
                        </div>
                        <Button type="button" onClick={searchUsers} disabled={isSearching}>
                          {isSearching ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground">Enter at least 2 characters to search. Results will show user ID, registration date, and current balance.</p>
                      {searchResults.length > 0 && (
                        <div className="border rounded-md overflow-hidden">
                          {searchResults.map((user) => (
                            <div
                              key={user.clerkId}
                              className="flex items-center justify-between p-3 hover:bg-accent cursor-pointer border-b last:border-b-0"
                              onClick={() => handleSelectUser(user)}
                            >
                              <div className="flex items-center space-x-3">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={user.avatarUrl} />
                                  <AvatarFallback>{getInitials(user.username)}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <p className="font-medium">{user.username}</p>
                                  <p className="text-xs text-muted-foreground">{user.email}</p>
                                  <div className="flex space-x-3 mt-1 text-xs text-muted-foreground">
                                    <p className="font-mono break-all">{user.clerkId}</p>
                                    <p>{format(new Date(user.createdAt), "yyyy-MM-dd")}</p>
                                  </div>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="text-sm font-medium">Balance</p>
                                <p className="text-sm font-bold text-green-600">
                                  {user.wallet?.permanentPoints || 0} points
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                    {/* Exchange Type and Points on the same line */}
                    <div className="flex gap-4">
                      <FormField
                        control={form.control}
                        name="type"
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormLabel>Exchange Type</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select exchange type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="refund">Refund</SelectItem>
                                <SelectItem value="gift">Gift</SelectItem>
                                <SelectItem value="award">Award</SelectItem>
                                <SelectItem value="affiliate">Affiliate</SelectItem>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                  {/* Points */}
                      <FormField
                        control={form.control}
                        name="points"
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormLabel>Points</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Description and Note on the same line */}
                    <div className="flex gap-4">
                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormLabel>Description (Optional)</FormLabel>
                            <FormControl>
                              <Textarea {...field} placeholder="Description for the exchange" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Note */}
                      <FormField
                        control={form.control}
                        name="note"
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormLabel>Note (Optional)</FormLabel>
                            <FormControl>
                              <Textarea {...field} placeholder="Additional notes" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Right column - Optional fields */}
                  <div className="space-y-6 flex-1">
                    {/* Associated History or Order - Only shown after user selection */}
                    {selectedUser && (
                      <div className="space-y-2">
                        <FormLabel>Associate with History or Order (Optional)</FormLabel>
                        <Tabs value={selectedTab} onValueChange={(value) => setSelectedTab(value as "history" | "order")}>
                          <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="history">History</TabsTrigger>
                            <TabsTrigger value="order">Order</TabsTrigger>
                          </TabsList>
                          <TabsContent value="history" className="space-y-2 pt-2">
                            <div className="border rounded-md overflow-hidden max-h-[300px] overflow-y-auto">
                              {isLoadingUserData ? (
                                <div className="flex justify-center items-center p-4">
                                  <Loader2 className="h-6 w-6 animate-spin text-primary" />
                                </div>
                              ) : userHistories.length === 0 ? (
                                <div className="p-4 text-center text-muted-foreground">
                                  No recent histories found
                                </div>
                              ) : (
                                <Table>
                                  <TableHeader>
                                    <TableRow>
                                      <TableHead>Image</TableHead>
                                      <TableHead>Time</TableHead>
                                      <TableHead>Points</TableHead>
                                      <TableHead>Status</TableHead>
                                      <TableHead>Select</TableHead>
                                    </TableRow>
                                  </TableHeader>
                                  <TableBody>
                                    {userHistories.map((history) => (
                                      <TableRow key={history.id}>
                                        <TableCell>
                                          {history.resultUrl ? (
                                            <div className="w-12 h-12 relative overflow-hidden rounded-md">
                                              <img
                                                src={history.resultUrl}
                                                alt="Generation result"
                                                className="object-cover w-full h-full"
                                              />
                                            </div>
                                          ) : (
                                            <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center">
                                              <span className="text-xs text-muted-foreground">No image</span>
                                            </div>
                                          )}
                                        </TableCell>
                                        <TableCell>{format(new Date(history.createdAt), "yyyy-MM-dd HH:mm")}</TableCell>
                                        <TableCell>{history.pointsUsed}</TableCell>
                                        <TableCell>
                                          {history.status ? (
                                            <Badge className="bg-green-500">Success</Badge>
                                          ) : (
                                            <Badge className="bg-red-500">Failed</Badge>
                                          )}
                                        </TableCell>
                                        <TableCell>
                                          <Button
                                            type="button"
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleHistorySelect(history.id, history.pointsUsed)}
                                          >
                                            Select
                                          </Button>
                                        </TableCell>
                                      </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          )}
                        </div>
                        {historyId && (
                          <div className="flex space-x-2">
                            <Input
                              placeholder="History ID"
                              value={historyId}
                              readOnly
                              className="flex-1"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setHistoryId("");
                                form.setValue("historyId", "");
                              }}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="h-4 w-4"
                              >
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                              </svg>
                            </Button>
                          </div>
                        )}
                      </TabsContent>
                      <TabsContent value="order" className="space-y-2 pt-2">
                        <div className="border rounded-md overflow-hidden max-h-[300px] overflow-y-auto">
                          {isLoadingUserData ? (
                            <div className="flex justify-center items-center p-4">
                              <Loader2 className="h-6 w-6 animate-spin text-primary" />
                            </div>
                          ) : userOrders.length === 0 ? (
                            <div className="p-4 text-center text-muted-foreground">
                              No recent orders found
                            </div>
                          ) : (
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Time</TableHead>
                                  <TableHead>Status</TableHead>
                                  <TableHead>Amount</TableHead>
                                  <TableHead>Price</TableHead>
                                  <TableHead>Select</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {userOrders.map((order) => (
                                  <TableRow key={order.id}>
                                    <TableCell>{format(new Date(order.createdAt), "yyyy-MM-dd HH:mm")}</TableCell>
                                    <TableCell>
                                      <Badge
                                        variant={order.status === "SUCCESS" ? "default" :
                                                order.status === "PENDING" ? "secondary" :
                                                order.status === "REFUND" ? "outline" : "destructive"}
                                      >
                                        {order.status}
                                      </Badge>
                                    </TableCell>
                                    <TableCell>{order.amount}</TableCell>
                                    <TableCell>{order.extra?.price ? `¥${order.extra.price}` : "-"}</TableCell>
                                    <TableCell>
                                      <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handleOrderSelect(order.id, order.amount)}
                                      >
                                        Select
                                      </Button>
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          )}
                        </div>
                        {orderId && (
                          <div className="flex space-x-2">
                            <Input
                              placeholder="Order ID"
                              value={orderId}
                              readOnly
                              className="flex-1"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setOrderId("");
                                form.setValue("orderId", "");
                              }}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="h-4 w-4"
                              >
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                              </svg>
                            </Button>
                          </div>
                        )}
                      </TabsContent>
                    </Tabs>
                  </div>
                    )}
                  </div>
                </div>

                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting || !form.getValues().userId}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      "Add Points"
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Points Exchange History</CardTitle>
          <CardDescription>
            View all points exchanges made by administrators
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : exchangeOrders.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No exchange records found
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Points</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {exchangeOrders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={order.user?.avatarUrl} />
                            <AvatarFallback>
                              {order.user ? getInitials(order.user.username) : "?"}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{order.user?.username || "Unknown"}</p>
                            <p className="text-xs text-muted-foreground">{order.user?.email || order.userId}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getExchangeTypeBadge(order.extra.exchangeType || "other")}
                      </TableCell>
                      <TableCell>
                        <span className="font-medium text-green-600">+{order.amount}</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">{order.description}</span>
                        {order.extra.note && (
                          <p className="text-xs text-muted-foreground mt-1">{order.extra.note}</p>
                        )}
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">{formatDate(order.createdAt)}</span>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => router.push(`/admin/orders/${order.id}`)}
                        >
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
