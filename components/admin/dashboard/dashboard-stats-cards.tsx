"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Bar<PERSON>hart as BarChartIcon, Users, Image, TrendingUp } from "lucide-react";
import { formatNumber, formatCurrency } from "@/lib/utils";

interface DashboardStatsCardsProps {
  loading: boolean;
  newUsers: number;
  userGrowth: number;
  totalPayments: number;
  paymentGrowth: number;
  historiesTotal: number;
  historiesGrowth: number;
  newShares: number;
  shareGrowth: number;
}

export function DashboardStatsCards({
  loading,
  newUsers,
  userGrowth,
  totalPayments,
  paymentGrowth,
  historiesTotal,
  historiesGrowth,
  newShares,
  shareGrowth,
}: DashboardStatsCardsProps) {
  return (
    <div className="grid gap-4 grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Registration</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {loading ? (
              <div className="h-8 w-16 animate-pulse rounded bg-muted"></div>
            ) : (
              `+${formatNumber(newUsers)}`
            )}
          </div>
          <p className="text-xs text-muted-foreground flex items-center">
            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />+
            {userGrowth}% from last month
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Payments</CardTitle>
          <span className="h-4 w-4 text-muted-foreground flex items-center justify-center">¥</span>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {loading ? (
              <div className="h-8 w-16 animate-pulse rounded bg-muted"></div>
            ) : (
              formatCurrency(totalPayments)
            )}
          </div>
          <p className="text-xs text-muted-foreground flex items-center">
            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />+
            {paymentGrowth}% from last month
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Generations</CardTitle>
          <Image className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {loading ? (
              <div className="h-8 w-16 animate-pulse rounded bg-muted"></div>
            ) : (
              `+${formatNumber(historiesTotal)}`
            )}
          </div>
          <p className="text-xs text-muted-foreground flex items-center">
            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />+
            {historiesGrowth}% from last month
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active Now</CardTitle>
          <BarChartIcon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {loading ? (
              <div className="h-8 w-16 animate-pulse rounded bg-muted"></div>
            ) : (
              `+${formatNumber(newShares)}`
            )}
          </div>
          <p className="text-xs text-muted-foreground flex items-center">
            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />+
            {shareGrowth}% since last hour
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
