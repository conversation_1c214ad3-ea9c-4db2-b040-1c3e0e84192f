"use client";

import { useState, useEffect } from "react";
import { DateRange } from "react-day-picker";

export interface DashboardStats {
  newUsers: number;
  totalPayments: number;
  userGrowth: number;
  paymentGrowth: number;
  histories: {
    total: number;
    success: number;
    failed: number;
    successRate: number;
    growth: number;
    byModel: {
      [key: string]: {
        total: number;
        success: number;
        failed: number;
        successRate: number;
      };
    };
    byStyle: {
      [key: string]: {
        total: number;
        success: number;
        failed: number;
        successRate: number;
      };
    };
  };
  newShares: number;
  shareGrowth: number;
  recentOrders: {
    id: string;
    userId: string;
    amount: number;
    status: string;
    createdAt: string;
    extra: {
      price?: number;
      [key: string]: any;
    };
    user: {
      username: string;
      email: string;
      avatarUrl?: string;
    };
  }[];
  recentHistories: {
    id: string;
    userId: string;
    status: boolean;
    resultUrl?: string;
    prompt: string;
    createdAt: string;
    extra?: {
      model?: string;
      style?: string;
      [key: string]: any;
    };
  }[];
  loading: boolean;
}

interface DashboardDataProviderProps {
  dateRange: DateRange;
  children: (stats: DashboardStats) => React.ReactNode;
}

export function DashboardDataProvider({ dateRange, children }: DashboardDataProviderProps) {
  const [stats, setStats] = useState<DashboardStats>({
    newUsers: 0,
    totalPayments: 0,
    userGrowth: 0,
    paymentGrowth: 0,
    histories: {
      total: 0,
      success: 0,
      failed: 0,
      successRate: 0,
      growth: 0,
      byModel: {},
      byStyle: {},
    },
    newShares: 0,
    shareGrowth: 0,
    recentOrders: [],
    recentHistories: [],
    loading: true,
  });

  useEffect(() => {
    const fetchStats = async () => {
      setStats((prev) => ({ ...prev, loading: true }));
      try {
        const from = dateRange?.from?.toISOString();
        const to = dateRange?.to?.toISOString();
        const queryParams = new URLSearchParams();
        if (from) queryParams.append("from", from);
        if (to) queryParams.append("to", to);

        const response = await fetch(`/api/admin/dashboard/stats?${queryParams.toString()}`);
        if (!response.ok) {
          throw new Error("Failed to fetch dashboard stats");
        }
        const data = await response.json();
        setStats({ ...data, loading: false });
      } catch (error) {
        console.error("Error fetching dashboard stats:", error);
        setStats((prev) => ({ ...prev, loading: false }));
      }
    };

    fetchStats();
  }, [dateRange]);

  return <>{children(stats)}</>;
}
