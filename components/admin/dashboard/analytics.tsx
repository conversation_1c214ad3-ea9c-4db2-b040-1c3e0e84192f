"use client";

import { useState } from "react";
import { DateRange } from "react-day-picker";
import { subDays } from "date-fns";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

// Import modular components
import { DashboardDateFilter } from "@/components/admin/dashboard/dashboard-date-filter";
import { DashboardDataProvider } from "@/components/admin/dashboard/dashboard-data-provider";
import { DashboardHistoryCharts } from "@/components/admin/dashboard/dashboard-history-charts";

export function AdminAnalytics() {
  const [dateRange, setDateRange] = useState<DateRange>({
    from: subDays(new Date(), 7),
    to: new Date()
  });
  const [activeTab, setActiveTab] = useState<"model" | "style">("model");

  return (
    <div className="space-y-4 md:space-y-6">
      <div className="flex flex-col md:justify-between justify-between items-center space-y-2 md:space-y-0 md:flex-row">
        <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Analytics</h1>
        <DashboardDateFilter
          dateRange={dateRange}
          onDateRangeChange={setDateRange}
        />
      </div>

      <DashboardDataProvider dateRange={dateRange}>
        {(stats) => (
          <div className="grid gap-4 md:gap-6">
            <Card className="overflow-hidden">
              <CardContent className="px-3 py-2 md:px-6 md:py-4">
                <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "model" | "style")} className="space-y-3 md:space-y-4">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="model">按模型</TabsTrigger>
                    <TabsTrigger value="style">按风格</TabsTrigger>
                  </TabsList>

                  <TabsContent value="model">
                    <div>
                      {stats.loading ? (
                        <div className="h-full animate-pulse rounded bg-muted"></div>
                      ) : Object.keys(stats.histories.byModel).length === 0 ? (
                        <div className="flex items-center justify-center h-full text-sm text-muted-foreground">
                          No data available
                        </div>
                      ) : (
                        <DashboardHistoryCharts
                          loading={stats.loading}
                          historyStats={stats.histories}
                          fullHeight
                          hideTabsHeader
                          forcedActiveTab="model"
                        />
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="style">
                    <div>
                      {stats.loading ? (
                        <div className="h-full animate-pulse rounded bg-muted"></div>
                      ) : Object.keys(stats.histories.byStyle).length === 0 ? (
                        <div className="flex items-center justify-center h-full text-sm text-muted-foreground">
                          No data available
                        </div>
                      ) : (
                        <DashboardHistoryCharts
                          loading={stats.loading}
                          historyStats={stats.histories}
                          fullHeight
                          hideTabsHeader
                          forcedActiveTab="style"
                        />
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        )}
      </DashboardDataProvider>
    </div>
  );
}
