"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON> } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";

interface RecentOrder {
  id: string;
  userId: string;
  amount: number;
  status: string;
  createdAt: string;
  extra: {
    price?: number;
    [key: string]: any;
  };
  user: {
    username: string;
    email: string;
    avatarUrl?: string;
    clerkId?: string;
  };
}

interface DashboardRecentSalesProps {
  loading: boolean;
  recentOrders: RecentOrder[];
}

export function DashboardRecentSales({ loading, recentOrders }: DashboardRecentSalesProps) {
  function getInitials(name: string) {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Sales</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {loading ? (
            Array.from({ length: 5 }).map((_, i) => (
              <div
                key={i}
                className="flex items-center justify-between"
              >
                <div className="flex items-center space-x-4">
                  <div className="h-10 w-10 animate-pulse rounded-full bg-muted"></div>
                  <div className="space-y-1">
                    <div className="h-4 w-24 animate-pulse rounded bg-muted"></div>
                    <div className="h-3 w-32 animate-pulse rounded bg-muted"></div>
                  </div>
                </div>
                <div className="h-4 w-16 animate-pulse rounded bg-muted"></div>
              </div>
            ))
          ) : recentOrders.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              No recent orders found
            </div>
          ) : (
            recentOrders.map((order) => (
              <div
                key={order.id}
                className="flex items-center justify-between"
              >
                <Link href={`/admin/users/${order.user.clerkId}`} className="flex items-center space-x-4">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={order.user.avatarUrl} />
                    <AvatarFallback>
                      {getInitials(order.user.username)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">
                      {order.user.email}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {order.user.clerkId}
                    </p>
                  </div>
                </Link>
                <Link href={`/admin/orders/${order.id}`} className="flex flex-col items-end">
                  <p className="font-medium">
                    ¥{order.extra.price?.toFixed(2) || "0.00"}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {order.amount} points
                  </p>
                </Link>
              </div>
            ))
          )}
        </div>
        {!loading && recentOrders.length > 0 && (
          <div className="mt-4 flex justify-center">
            <Button variant="outline" size="sm" asChild>
              <Link href="/admin/orders">View All Orders</Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
