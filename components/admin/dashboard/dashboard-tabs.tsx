"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { OrdersTable } from "@/components/admin/order/orders-table";
import { HistoriesTable } from "@/components/admin/history/histories-table";
import { SharesTable } from "@/components/admin/shares-table";

const timeRangeOptions = [
  { value: "1d", label: "Last 24 Hours" },
  { value: "3d", label: "Last 3 Days" },
  { value: "7d", label: "Last 7 Days" },
  { value: "30d", label: "Last 30 Days" },
];

export function DashboardTabs() {
  const [timeRange, setTimeRange] = useState("1d");

  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
  };

  // Calculate the start date based on the selected time range
  const getStartDate = () => {
    const now = new Date();
    switch (timeRange) {
      case "1d":
        return new Date(now.setDate(now.getDate() - 1));
      case "3d":
        return new Date(now.setDate(now.getDate() - 3));
      case "7d":
        return new Date(now.setDate(now.getDate() - 7));
      case "30d":
        return new Date(now.setDate(now.getDate() - 30));
      default:
        return new Date(now.setDate(now.getDate() - 1));
    }
  };

  return (
    <Tabs defaultValue="payments" className="space-y-4">
      <div className="flex justify-between items-center">
        <TabsList>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="generations">Generations</TabsTrigger>
          <TabsTrigger value="shares">Shares</TabsTrigger>
        </TabsList>

        <Select value={timeRange} onValueChange={handleTimeRangeChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            {timeRangeOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <TabsContent value="payments" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Recent Payments</CardTitle>
            <CardDescription>
              Overview of recent payment activity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <OrdersTable
              hideSearch={true}
              hideFilters={true}
              startDate={getStartDate()}
            />
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="generations" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Recent Generations</CardTitle>
            <CardDescription>
              Overview of recent image generation activity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <HistoriesTable
              hideSearch={true}
              hideFilters={true}
              startDate={getStartDate()}
            />
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="shares" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Recent Shares</CardTitle>
            <CardDescription>
              Overview of recently shared content
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SharesTable
              hideSearch={true}
              hideFilters={true}
              startDate={getStartDate()}
            />
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
}
