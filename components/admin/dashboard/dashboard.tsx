"use client";

import { useState } from "react";
import { DateRange } from "react-day-picker";
import { subDays } from "date-fns";

// Import modular components
import { DashboardDateFilter } from "./dashboard-date-filter";
import { DashboardDataProvider } from "./dashboard-data-provider";
import { DashboardStatsCards } from "./dashboard-stats-cards";
import { DashboardRecentSales } from "./dashboard-recent-sales";
import { DashboardHistoryCharts } from "./dashboard-history-charts";

export function AdminDashboard() {
  const [dateRange, setDateRange] = useState<DateRange>({
    from: subDays(new Date(), 3),
    to: new Date()
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:justify-between items-center space-y-2 md:space-y-0 md:flex-row">
        <h1 className="text-3xl font-bold tracking-tight">Overview</h1>
        <DashboardDateFilter
          dateRange={dateRange}
          onDateRangeChange={setDateRange}
        />
      </div>

      <DashboardDataProvider dateRange={dateRange}>
        {(stats) => (
          <>
            <DashboardStatsCards
              loading={stats.loading}
              newUsers={stats.newUsers}
              userGrowth={stats.userGrowth}
              totalPayments={stats.totalPayments}
              paymentGrowth={stats.paymentGrowth}
              historiesTotal={stats.histories.total}
              historiesGrowth={stats.histories.growth}
              newShares={stats.newShares}
              shareGrowth={stats.shareGrowth}
            />

            <div className="grid gap-4">
              <h2 className="text-lg font-semibold mb-4">Activity Summary</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Recent Sales Column */}
                <DashboardRecentSales
                  loading={stats.loading}
                  recentOrders={stats.recentOrders}
                />

                {/* Recent History Column */}
                <DashboardHistoryCharts
                  loading={stats.loading}
                  historyStats={stats.histories}
                />
              </div>
            </div>
          </>
        )}
      </DashboardDataProvider>
    </div>
  );
}
