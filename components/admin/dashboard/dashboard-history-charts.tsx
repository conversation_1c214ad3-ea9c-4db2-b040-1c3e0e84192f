"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Bar, BarChart, CartesianGrid, XAxis, YAxis, ResponsiveContainer } from "recharts";
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
} from "@/components/ui/chart";
import { DRAW_STYLES } from "@/constants/draw";
import { drawModels } from "@/constants/draw/models";
import { useIsMobile } from "@/lib/hooks/use-mobile";

interface HistoryStats {
  byModel: {
    [key: string]: {
      total: number;
      success: number;
      failed: number;
      successRate: number;
    };
  };
  byStyle: {
    [key: string]: {
      total: number;
      success: number;
      failed: number;
      successRate: number;
    };
  };
}

interface DashboardHistoryChartsProps {
  loading: boolean;
  historyStats: HistoryStats;
  fullHeight?: boolean;
  hideTabsHeader?: boolean;
  forcedActiveTab?: "model" | "style";
}

// Helper functions to map IDs to human-readable names
const getModelName = (modelId: string): string => {
  const model = drawModels.find(m => m.id === modelId);
  return model ? model.name : modelId;
};

const getStyleName = (styleId: string): string => {
  // First, try to find a direct match with the style ID
  for (const key in DRAW_STYLES) {
    const styleKey = key as keyof typeof DRAW_STYLES;
    if (DRAW_STYLES[styleKey].id === styleId) {
      return DRAW_STYLES[styleKey].name;
    }
  }

  // If no match found, try to find a match with the uppercase key
  const uppercaseStyleId = styleId.toUpperCase();
  if (uppercaseStyleId in DRAW_STYLES) {
    return DRAW_STYLES[uppercaseStyleId as keyof typeof DRAW_STYLES].name;
  }

  // If still no match, return the original ID
  return styleId;
};

export function DashboardHistoryCharts({
  loading,
  historyStats,
  fullHeight = false,
  hideTabsHeader = false,
  forcedActiveTab
}: DashboardHistoryChartsProps) {
  const [internalActiveTab, setInternalActiveTab] = useState<"model" | "style">("model");
  const isMobile = useIsMobile();

  // Use forcedActiveTab if provided, otherwise use internal state
  const activeTab = forcedActiveTab !== undefined ? forcedActiveTab : internalActiveTab;

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Recent History</CardTitle>
          {!hideTabsHeader && (
            <Tabs value={activeTab} onValueChange={(value) => setInternalActiveTab(value as "model" | "style")} className="w-auto">
              <TabsList className="grid w-[200px] grid-cols-2">
                <TabsTrigger value="model">By Model</TabsTrigger>
                <TabsTrigger value="style">By Style</TabsTrigger>
              </TabsList>
            </Tabs>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => {
          if (forcedActiveTab === undefined) {
            setInternalActiveTab(value as "model" | "style");
          }
        }} className="space-y-4">
          {/* Model Statistics Tab */}
          <TabsContent value="model" className="space-y-4">
            {loading ? (
              <div className={`animate-pulse rounded bg-muted ${isMobile ? 'h-[250px]' : 'h-[300px]'}`}></div>
            ) : Object.keys(historyStats.byModel).length === 0 ? (
              <div className={`flex items-center justify-center text-sm text-muted-foreground ${isMobile ? 'h-[250px]' : 'h-[300px]'}`}>
                No data available
              </div>
            ) : (
              <div>
                {(() => {
                  // Transform data for the chart
                  console.log('Model data before mapping:', Object.keys(historyStats.byModel));
                  const chartData = Object.entries(
                    historyStats.byModel
                  )
                    .sort(([, a], [, b]) => b.total - a.total)
                    .slice(0, 8)
                    .map(([model, data]) => {
                      const mappedName = getModelName(model);
                      console.log(`Mapping model: ${model} -> ${mappedName}`);
                      return {
                        name: mappedName,
                        originalId: model,
                        success: data.success,
                        failed: data.failed,
                        successRate: Math.round(data.successRate * 100),
                      };
                    });

                  const chartConfig = {
                    success: {
                      label: "Success",
                      color: "hsl(142, 76%, 36%)", // Green
                    },
                    failed: {
                      label: "Failed",
                      color: "hsl(0, 84%, 60%)", // Red
                    },
                  } satisfies ChartConfig;

                  // Determine chart height based on device and fullHeight prop
                  const chartHeight = isMobile
                    ? (fullHeight ? "h-[300px]" : "h-[250px]")
                    : (fullHeight ? "h-[450px]" : "h-[300px]");

                  return (
                    <ChartContainer config={chartConfig} className={chartHeight}>
                      <BarChart
                        accessibilityLayer
                        data={chartData}
                        margin={isMobile ? { top: 10, right: 10, left: 0, bottom: 50 } : { top: 20, right: 30, left: 0, bottom: 20 }}
                      >
                        <CartesianGrid vertical={false} />
                        <YAxis width={isMobile ? 30 : 40} />
                        <XAxis
                          dataKey="name"
                          tickLine={false}
                          tickMargin={10}
                          axisLine={false}
                          height={isMobile ? 60 : 30}
                          interval={isMobile ? 0 : "preserveStartEnd"}
                          tickFormatter={(value) =>
                            isMobile
                              ? (value.length > 8 ? `${value.slice(0, 5)}...` : value)
                              : (value.length > 15 ? `${value.slice(0, 12)}...` : value)
                          }
                          tick={({ x, y, payload }) => {
                            const displayText = isMobile
                              ? (payload.value.length > 8 ? `${payload.value.slice(0, 5)}...` : payload.value)
                              : (payload.value.length > 15 ? `${payload.value.slice(0, 12)}...` : payload.value);
                            return (
                              <g transform={`translate(${x},${y})`}>
                                <title>{payload.value}</title>
                                <text
                                  x={0}
                                  y={0}
                                  dy={16}
                                  textAnchor="middle"
                                  fill="#666"
                                  fontSize={isMobile ? "10px" : "12px"}
                                  transform={isMobile ? "rotate(-45)" : ""}
                                >
                                  {displayText}
                                </text>
                              </g>
                            );
                          }}
                        />
                        <ChartTooltip
                          content={({ active, payload }) => {
                            if (active && payload && payload.length) {
                              const itemName =
                                payload[0]?.payload?.name || "";
                              const originalId = payload[0]?.payload?.originalId || "";
                              return (
                                <div className={`rounded-lg border bg-background ${isMobile ? 'p-1.5 max-w-[200px]' : 'p-2'} shadow-md`}>
                                  <div className={`${isMobile ? 'mb-0.5 text-sm' : 'mb-1'} font-medium`}>
                                    {itemName}
                                    {originalId !== itemName && !isMobile && (
                                      <div className="text-xs text-muted-foreground">
                                        ID: {originalId}
                                      </div>
                                    )}
                                  </div>
                                  <div className="flex flex-col gap-0.5">
                                    {payload.map((entry, index) => (
                                      <div
                                        key={`item-${index}`}
                                        className={`flex items-center ${isMobile ? 'gap-1' : 'gap-1.5'}`}
                                      >
                                        <div
                                          className={`${isMobile ? 'h-1.5 w-1.5' : 'h-2 w-2'} rounded-sm`}
                                          style={{
                                            backgroundColor:
                                              entry.color,
                                          }}
                                        />
                                        <span className={`${isMobile ? 'text-[10px]' : 'text-xs'} text-muted-foreground`}>
                                          {entry.name === "success"
                                            ? "成功"
                                            : "失败"}
                                          :
                                        </span>
                                        <span className={`${isMobile ? 'text-[10px]' : 'text-xs'} font-medium`}>
                                          {entry.value}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              );
                            }
                            return null;
                          }}
                        />
                        <ChartLegend
                          content={<ChartLegendContent />}
                        />
                        <Bar
                          dataKey="success"
                          stackId="a"
                          fill="var(--color-success)"
                          radius={[0, 0, 4, 4]}
                        />
                        <Bar
                          dataKey="failed"
                          stackId="a"
                          fill="var(--color-failed)"
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ChartContainer>
                  );
                })()}
              </div>
            )}

            {!loading && !fullHeight && (
              <div className="mt-4 flex justify-center">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/admin/analytics">View Full Analytics</Link>
                </Button>
              </div>
            )}
          </TabsContent>

          {/* Style Statistics Tab */}
          <TabsContent value="style" className="space-y-4">
            {loading ? (
              <div className={`animate-pulse rounded bg-muted ${isMobile ? 'h-[250px]' : 'h-[300px]'}`}></div>
            ) : Object.keys(historyStats.byStyle).length === 0 ? (
              <div className={`flex items-center justify-center text-sm text-muted-foreground ${isMobile ? 'h-[250px]' : 'h-[300px]'}`}>
                No data available
              </div>
            ) : (
              <div>
                {(() => {
                  // Transform data for the chart
                  console.log('Style data before mapping:', Object.keys(historyStats.byStyle));
                  const chartData = Object.entries(
                    historyStats.byStyle
                  )
                    .sort(([, a], [, b]) => b.total - a.total)
                    .slice(0, 8)
                    .map(([style, data]) => {
                      const mappedName = getStyleName(style);
                      console.log(`Mapping style: ${style} -> ${mappedName}`);
                      return {
                        name: mappedName,
                        originalId: style,
                        success: data.success,
                        failed: data.failed,
                        successRate: Math.round(data.successRate * 100),
                      };
                    });

                  const chartConfig = {
                    success: {
                      label: "Success",
                      color: "hsl(142, 76%, 36%)", // Green
                    },
                    failed: {
                      label: "Failed",
                      color: "hsl(0, 84%, 60%)", // Red
                    },
                  } satisfies ChartConfig;

                  // Determine chart height based on device and fullHeight prop
                  const chartHeight = isMobile
                    ? (fullHeight ? "h-[300px]" : "h-[250px]")
                    : (fullHeight ? "h-[450px]" : "h-[300px]");

                  return (
                    <ChartContainer config={chartConfig} className={chartHeight}>
                      <BarChart
                        accessibilityLayer
                        data={chartData}
                        margin={isMobile ? { top: 10, right: 10, left: 0, bottom: 50 } : { top: 20, right: 30, left: 0, bottom: 20 }}
                      >
                        <CartesianGrid vertical={false} />
                        <YAxis width={isMobile ? 30 : 40} />
                        <XAxis
                          dataKey="name"
                          tickLine={false}
                          tickMargin={10}
                          axisLine={false}
                          height={isMobile ? 60 : 30}
                          interval={isMobile ? 0 : "preserveStartEnd"}
                          tickFormatter={(value) =>
                            isMobile
                              ? (value.length > 8 ? `${value.slice(0, 5)}...` : value)
                              : (value.length > 15 ? `${value.slice(0, 12)}...` : value)
                          }
                          tick={({ x, y, payload }) => {
                            const displayText = isMobile
                              ? (payload.value.length > 8 ? `${payload.value.slice(0, 5)}...` : payload.value)
                              : (payload.value.length > 15 ? `${payload.value.slice(0, 12)}...` : payload.value);
                            return (
                              <g transform={`translate(${x},${y})`}>
                                <title>{payload.value}</title>
                                <text
                                  x={0}
                                  y={0}
                                  dy={16}
                                  textAnchor="middle"
                                  fill="#666"
                                  fontSize={isMobile ? "10px" : "12px"}
                                  transform={isMobile ? "rotate(-45)" : ""}
                                >
                                  {displayText}
                                </text>
                              </g>
                            );
                          }}
                        />
                        <ChartTooltip
                          content={({ active, payload }) => {
                            if (active && payload && payload.length) {
                              const itemName =
                                payload[0]?.payload?.name || "";
                              const originalId = payload[0]?.payload?.originalId || "";
                              return (
                                <div className={`rounded-lg border bg-background ${isMobile ? 'p-1.5 max-w-[200px]' : 'p-2'} shadow-md`}>
                                  <div className={`${isMobile ? 'mb-0.5 text-sm' : 'mb-1'} font-medium`}>
                                    {itemName}
                                    {originalId !== itemName && !isMobile && (
                                      <div className="text-xs text-muted-foreground">
                                        ID: {originalId}
                                      </div>
                                    )}
                                  </div>
                                  <div className="flex flex-col gap-0.5">
                                    {payload.map((entry, index) => (
                                      <div
                                        key={`item-${index}`}
                                        className={`flex items-center ${isMobile ? 'gap-1' : 'gap-1.5'}`}
                                      >
                                        <div
                                          className={`${isMobile ? 'h-1.5 w-1.5' : 'h-2 w-2'} rounded-sm`}
                                          style={{
                                            backgroundColor:
                                              entry.color,
                                          }}
                                        />
                                        <span className={`${isMobile ? 'text-[10px]' : 'text-xs'} text-muted-foreground`}>
                                          {entry.name === "success"
                                            ? "成功"
                                            : "失败"}
                                          :
                                        </span>
                                        <span className={`${isMobile ? 'text-[10px]' : 'text-xs'} font-medium`}>
                                          {entry.value}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              );
                            }
                            return null;
                          }}
                        />
                        <ChartLegend
                          content={<ChartLegendContent />}
                        />
                        <Bar
                          dataKey="success"
                          stackId="a"
                          fill="var(--color-success)"
                          radius={[0, 0, 4, 4]}
                        />
                        <Bar
                          dataKey="failed"
                          stackId="a"
                          fill="var(--color-failed)"
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ChartContainer>
                  );
                })()}
              </div>
            )}

            {!loading && !fullHeight && (
              <div className="mt-4 flex justify-center">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/admin/analytics">View Full Analytics</Link>
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
