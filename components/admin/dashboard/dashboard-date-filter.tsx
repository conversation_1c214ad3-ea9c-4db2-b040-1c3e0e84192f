"use client";

import { useState } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePickerWithRange } from "@/components/ui/date-picker-with-range";
import { DateRange } from "react-day-picker";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import { subDays } from "date-fns";
import { useIsMobile } from "@/lib/hooks/use-mobile";

type TimeRange = "1d" | "3d" | "7d" | "30d" | "custom";

interface DashboardDateFilterProps {
  dateRange: DateRange;
  onDateRangeChange: (range: DateRange) => void;
}

export function DashboardDateFilter({ dateRange, onDateRangeChange }: DashboardDateFilterProps) {
  const [timeRange, setTimeRange] = useState<TimeRange>("3d");
  const isMobile = useIsMobile();

  const handleTimeRangeChange = (value: string) => {
    const newTimeRange = value as TimeRange;
    setTimeRange(newTimeRange);

    if (newTimeRange === "custom") {
      return; // Don't update dates for custom, let the date picker handle it
    }

    const to = new Date();
    let from = new Date();

    switch (newTimeRange) {
      case "1d":
        from = subDays(new Date(), 1);
        break;
      case "3d":
        from = subDays(new Date(), 3);
        break;
      case "7d":
        from = subDays(new Date(), 7);
        break;
      case "30d":
        from = subDays(new Date(), 30);
        break;
    }

    onDateRangeChange({ from, to });
  };

  return (
    <div className={`flex flex-col-2 gap-2`}>
      <Select value={timeRange} onValueChange={handleTimeRangeChange}>
        <SelectTrigger className={isMobile ? "w-full" : "w-[180px]"}>
          <SelectValue placeholder="选择时间范围" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="1d">今天</SelectItem>
          <SelectItem value="3d">最近3天</SelectItem>
          <SelectItem value="7d">最近一周</SelectItem>
          <SelectItem value="30d">最近一月</SelectItem>
          <SelectItem value="custom">自定义范围</SelectItem>
        </SelectContent>
      </Select>
      <Button
        variant="outline"
        size="icon"
        onClick={() => {
          // Force refresh by creating a new reference to the dateRange object
          const refreshedDateRange = { ...dateRange };
          onDateRangeChange(refreshedDateRange);
        }}
        className="shrink-0"
      >
        <Search className="h-4 w-4" />
      </Button>
    </div>
  );
}
