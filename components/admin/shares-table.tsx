"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, Search, ArrowUpDown } from "lucide-react";
import { useToast } from "@/lib/hooks/use-toast";
import { DatePickerWithRange } from "@/components/ui/date-picker-with-range";
import { DateRange } from "react-day-picker";
import { startOfDay, endOfDay, subDays } from "date-fns";
import { ClearFiltersButton } from "@/components/admin/clear-filters-button";


interface Share {
  id: string;
  shareId: string;
  historyId: string;
  userId: string;
  isPublic: boolean;
  allowFork: boolean;
  imageUrl: string;
  viewCount: number;
  likeCount: number;
  forkCount: number;
  forkEarnings: number;
  sharedAt: string;
  createdAt: string;
  updatedAt: string;
  user?: {
    clerkId: string;
    username: string;
    email: string;
    avatarUrl?: string;
  };
  history?: {
    prompt: string;
  };
}

interface Pagination {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface SharesTableProps {
  initialShares?: Share[];
  fixedUserId?: string;
  hideSearch?: boolean;
  hideFilters?: boolean;
  hidePagination?: boolean;
  hideUserColumn?: boolean;
  startDate?: Date;
  endDate?: Date;
}

export function SharesTable({
  initialShares,
  fixedUserId,
  hideSearch = false,
  hideFilters = false,
  hidePagination = false,
  hideUserColumn = false,
  startDate,
  endDate
}: SharesTableProps = {}) {
  const [shares, setShares] = useState<Share[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  });
  const [loading, setLoading] = useState(true);
  const [userId, setUserId] = useState("");
  const [id, setId] = useState("");
  const [shareId, setShareId] = useState("");
  const [isPublic, setIsPublic] = useState("");
  const [allowFork, setAllowFork] = useState("");
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: startDate || subDays(new Date(), 7),
    to: endDate || new Date()
  });
  const [orderBy, setOrderBy] = useState("sharedAt");
  const [order, setOrder] = useState("desc");
  const [pageInput, setPageInput] = useState<string>("");
  const { toast } = useToast();

  const fetchShares = async () => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        orderBy,
        order,
      });

      if (fixedUserId) {
        queryParams.append("userId", fixedUserId);
      } else if (userId) {
        queryParams.append("userId", userId);
      }
      if (id) queryParams.append("id", id);
      if (shareId) queryParams.append("shareId", shareId);
      if (isPublic && isPublic !== 'all') queryParams.append("isPublic", isPublic);
      if (allowFork && allowFork !== 'all') queryParams.append("allowFork", allowFork);
      if (dateRange?.from) queryParams.append("startDate", startOfDay(dateRange.from).toISOString());
      if (dateRange?.to) queryParams.append("endDate", endOfDay(dateRange.to).toISOString());

      const response = await fetch(`/api/admin/shares?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error("Failed to fetch shares");
      }

      const data = await response.json();
      setShares(data.shares);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching shares:", error);
      toast({
        title: "Error",
        description: "Failed to load shares. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (initialShares) {
      setShares(initialShares);
      setLoading(false);
    } else {
      fetchShares();
    }
    // Update pageInput when page changes
    setPageInput(pagination.page.toString());
  }, [initialShares, pagination.page, orderBy, order]);

  // Update dateRange when props change
  useEffect(() => {
    if (startDate !== undefined || endDate !== undefined) {
      setDateRange({
        from: startDate || subDays(new Date(), 7),
        to: endDate || new Date()
      });
      fetchShares();
    }
  }, [startDate, endDate]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchShares();
  };

  const handleSort = (column: string) => {
    if (orderBy === column) {
      setOrder(order === "asc" ? "desc" : "asc");
    } else {
      setOrderBy(column);
      setOrder("desc");
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPageInput(e.target.value);
  };

  const handlePageInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const page = parseInt(pageInput);
    if (!isNaN(page) && page > 0 && page <= pagination.totalPages) {
      handlePageChange(page);
    } else {
      toast({
        title: "Invalid page number",
        description: `Please enter a number between 1 and ${pagination.totalPages}`,
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getVisibilityBadge = (isPublic: boolean) => {
    return isPublic ? (
      <Badge className="bg-green-500">Public</Badge>
    ) : (
      <Badge className="bg-yellow-500">Private</Badge>
    );
  };

  const getForkBadge = (allowFork: boolean) => {
    return allowFork ? (
      <Badge className="bg-green-500">Allowed</Badge>
    ) : (
      <Badge className="bg-red-500">Disabled</Badge>
    );
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="pt-6">
          {!hideSearch && (
          <form onSubmit={handleSearch} className="flex flex-col gap-4 mb-6">
            <div className="md:w-[220px]">
              <Input
                placeholder="User ID"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="md:w-[220px]">
              <Input
                placeholder="ID"
                value={id}
                onChange={(e) => setId(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="md:w-[220px]">
              <Input
                placeholder="Share ID"
                value={shareId}
                onChange={(e) => setShareId(e.target.value)}
                className="w-full"
              />
            </div>
            {!hideFilters && (
              <>
                <div className="md:w-[150px]">
                  <Select value={isPublic} onValueChange={setIsPublic}>
                    <SelectTrigger>
                      <SelectValue placeholder="Visibility" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="true">Public</SelectItem>
                      <SelectItem value="false">Private</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="md:w-[150px]">
                  <Select value={allowFork} onValueChange={setAllowFork}>
                    <SelectTrigger>
                      <SelectValue placeholder="Fork Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="true">Allow Fork</SelectItem>
                      <SelectItem value="false">No Fork</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex gap-2">
                  <div className="md:w-[300px]">
                    <DatePickerWithRange
                      date={dateRange}
                      onSelect={(range) => setDateRange(range)}
                    />
                  </div>
                  <Button
                    type="button"
                    className="flex-shrink-0"
                    onClick={(e) => {
                      e.preventDefault();
                      fetchShares();
                    }}
                  >
                    <Search className="h-4 w-4 mr-2" />
                    Search
                  </Button>
                </div>
              </>
            )}
            {!hideFilters && (
              <ClearFiltersButton
                onClick={() => {
                  // 先清空所有过滤条件
                  const newUserId = fixedUserId ? fixedUserId : "";
                  const newId = "";
                  const newShareId = "";
                  const newIsPublic = "all";
                  const newAllowFork = "all";
                  const newDateRange = undefined;

                  // 更新所有状态
                  if (!fixedUserId) setUserId(newUserId);
                  setId(newId);
                  setShareId(newShareId);
                  setIsPublic(newIsPublic);
                  setAllowFork(newAllowFork);
                  setDateRange(newDateRange);
                  setPagination((prev) => ({ ...prev, page: 1 }));

                  // 使用新的值直接构建查询参数，而不是依赖状态
                  const queryParams = new URLSearchParams({
                    page: "1",
                    limit: pagination.limit.toString(),
                    orderBy,
                    order,
                  });

                  if (fixedUserId) {
                    queryParams.append("userId", fixedUserId);
                  }

                  // 执行搜索请求
                  setLoading(true);
                  fetch(`/api/admin/shares?${queryParams.toString()}`)
                    .then(response => {
                      if (!response.ok) {
                        throw new Error("Failed to fetch shares");
                      }
                      return response.json();
                    })
                    .then(data => {
                      setShares(data.shares);
                      setPagination(data.pagination);
                    })
                    .catch(error => {
                      console.error("Error fetching shares:", error);
                      toast({
                        title: "Error",
                        description: "Failed to load shares. Please try again.",
                        variant: "destructive",
                      });
                    })
                    .finally(() => {
                      setLoading(false);
                    });
                }}
                disabled={(!fixedUserId && !userId) && !id && !shareId && isPublic === 'all' && allowFork === 'all' && !dateRange}
              />
            )}
          </form>
          )}

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Image</TableHead>
                  {!hideUserColumn && <TableHead>User</TableHead>}
                  <TableHead>Visibility</TableHead>
                  <TableHead>Fork Status</TableHead>
                  <TableHead>
                    <div
                      className="flex items-center cursor-pointer"
                      onClick={() => handleSort("sharedAt")}
                    >
                      Shared At
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={hideUserColumn ? 5 : 6} className="text-center py-10">
                      <div className="flex justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : shares.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={hideUserColumn ? 5 : 6} className="text-center py-10">
                      No shares found
                    </TableCell>
                  </TableRow>
                ) : (
                  shares.map((share) => (
                    <TableRow key={share.id}>
                      <TableCell>
                        <a
                          href={`${process.env.NEXT_PUBLIC_APP_URL}/explore/${share.shareId}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="block w-16 h-16 relative overflow-hidden rounded-md"
                        >
                          <img
                            src={share.imageUrl}
                            alt="Shared image"
                            className="object-cover w-full h-full"
                          />
                        </a>
                      </TableCell>
                      {!hideUserColumn && (
                        <TableCell>
                          {share.user ? (
                            <a
                              href={`/admin/users/${share.user.clerkId}`}
                              className="hover:underline"
                            >
                              <div>
                                {share.user.email}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {share.userId}
                              </div>
                            </a>
                          ) : (
                            share.userId.substring(0, 8) + "..."
                          )}
                        </TableCell>
                      )}

                      <TableCell>{getVisibilityBadge(share.isPublic)}</TableCell>
                      <TableCell>{getForkBadge(share.allowFork)}</TableCell>
                      <TableCell>{formatDate(share.sharedAt)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            asChild
                          >
                            <a href={`/admin/shares/${share.id}`}>View</a>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            asChild
                          >
                            <a
                              href={`${process.env.NEXT_PUBLIC_APP_URL}/explore/${share.shareId}`}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              Share
                            </a>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {!hidePagination && (
            <div className="flex flex-col sm:flex-row items-center justify-between mt-4 gap-4">
              <div className="text-sm text-muted-foreground">
                Showing {shares.length} of {pagination.totalCount} shares
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={!pagination.hasPrevPage}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <form onSubmit={handlePageInputSubmit} className="flex items-center space-x-2">
                  <div className="text-sm">
                    Page
                  </div>
                  <Input
                    type="number"
                    min={1}
                    max={pagination.totalPages}
                    value={pageInput}
                    onChange={handlePageInputChange}
                    onBlur={() => setPageInput(pagination.page.toString())}
                    onClick={(e) => e.currentTarget.select()}
                    className="w-[60px] h-8 text-center"
                    placeholder={pagination.page.toString()}
                  />
                  <div className="text-sm">
                    of {pagination.totalPages}
                  </div>
                </form>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={!pagination.hasNextPage}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Select
                  value={pagination.limit.toString()}
                  onValueChange={(value) =>
                    setPagination((prev) => ({
                      ...prev,
                      limit: parseInt(value),
                      page: 1,
                    }))
                  }
                >
                  <SelectTrigger className="w-[80px]">
                    <SelectValue placeholder="10" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
