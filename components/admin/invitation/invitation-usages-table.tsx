"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ChevronLeft, ChevronRight, ArrowUpDown, RefreshCw } from "lucide-react";
import { useToast } from "@/lib/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { ClearFiltersButton } from "@/components/admin/clear-filters-button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert<PERSON>ialog<PERSON>rig<PERSON>,
} from "@/components/ui/alert-dialog";
import { useInvitationStore } from "@/store/invitation";

interface InvitationUsage {
  id: string;
  invitationId: string;
  invitation: {
    inviteCode: string;
    inviteType: "points" | "cash" | "both";
    refRatio: number;
  };
  referee: {
    clerkId: string;
    username: string;
    email: string;
    avatarUrl?: string;
  };
  registeredAt: string;
  firstRechargeAt?: string;
  rechargeAmount?: number;
  pointsAwarded?: number;
  cashAwarded?: number;
  status: "pending" | "ready" | "completed" | "void";
  redeemedAt?: string;
  operatorId?: string;
  operator?: {
    clerkId: string;
    username: string;
    email: string;
  };
  extra?: {
    redeemType?: "points" | "cash";
    redeemNote?: string;
    [key: string]: any;
  };
}

interface Pagination {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface InvitationUsagesAdminTableProps {
  invitationId?: string;
  refereeId?: string;
  refreshTrigger?: number;
  hideSearch?: boolean;
  hideFilters?: boolean;
  useStore?: boolean;
}

export function InvitationUsagesAdminTable({
  invitationId,
  refereeId,
  refreshTrigger = 0,
  hideSearch = false,
  hideFilters = false,
  useStore = false
}: InvitationUsagesAdminTableProps) {
  const [localUsages, setLocalUsages] = useState<InvitationUsage[]>([]);
  const [localPagination, setLocalPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  });
  const [localLoading, setLocalLoading] = useState(true);
  const [localStatus, setLocalStatus] = useState("all");
  const [localOrderBy, setLocalOrderBy] = useState("registeredAt");
  const [localOrder, setLocalOrder] = useState<"asc" | "desc">("desc");
  const [redeemingId, setRedeemingId] = useState<string | null>(null);
  const [redeemLoading, setRedeemLoading] = useState(false);
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [selectedRedeemType, setSelectedRedeemType] = useState<"points" | "cash" | null>(null);

  // 使用store
  const {
    usages: storeUsages,
    loadingUsages: storeLoading,
    usagesPagination: storePagination,
    usagesFilter: storeFilter,
    selectedUsageIds,
    setUsagesFilter,
    setUsagesPagination,
    fetchInvitationUsages,
    toggleUsageSelection,
    setSelectedUsageIds
  } = useInvitationStore();

  // 根据是否使用store选择数据来源
  const usages = useStore ? storeUsages : localUsages;
  const pagination = useStore ? storePagination : localPagination;
  const loading = useStore ? storeLoading : localLoading;
  const status = useStore ? storeFilter.status : localStatus;
  const orderBy = useStore ? storePagination.orderBy || "registeredAt" : localOrderBy;
  const order = useStore ? (storePagination.order as "asc" | "desc") || "desc" : localOrder;

  const { toast } = useToast();

  const fetchUsages = async () => {
    if (useStore) {
      // 使用store加载数据
      try {
        await fetchInvitationUsages({
          page: pagination.page,
          limit: pagination.limit,
          orderBy,
          order,
          status: status !== "all" ? status : undefined,
          invitationId,
          refereeId,
          isAdmin: true
        });
      } catch (error) {
        console.error("Error fetching invitation usages:", error);
        toast({
          title: "错误",
          description: "加载邀请使用记录失败，请重试。",
          variant: "destructive",
        });
      }
    } else {
      // 使用本地状态加载数据
      setLocalLoading(true);
      try {
        const queryParams = new URLSearchParams({
          page: pagination.page.toString(),
          limit: pagination.limit.toString(),
          orderBy,
          order,
        });

        if (status !== "all") queryParams.append("status", status);
        if (invitationId) queryParams.append("invitationId", invitationId);
        if (refereeId) queryParams.append("refereeId", refereeId);

        const response = await fetch(`/api/admin/invitation_usages?${queryParams.toString()}`);

        if (!response.ok) {
          throw new Error("Failed to fetch invitation usages");
        }

        const data = await response.json();
        setLocalUsages(data.usages);
        setLocalPagination(data.pagination);
      } catch (error) {
        console.error("Error fetching invitation usages:", error);
        toast({
          title: "错误",
          description: "加载邀请使用记录失败，请重试。",
          variant: "destructive",
        });
      } finally {
        setLocalLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchUsages();
  }, [useStore ? storePagination.page : localPagination.page, orderBy, order, status, refreshTrigger, localRefreshTrigger]);

  const handleSort = (column: string) => {
    if (orderBy === column) {
      const newOrder = order === "asc" ? "desc" : "asc";
      if (useStore) {
        setUsagesPagination({ ...storePagination, orderBy: column, order: newOrder });
      } else {
        setLocalOrder(newOrder);
      }
    } else {
      if (useStore) {
        setUsagesPagination({ ...storePagination, orderBy: column, order: "desc" });
      } else {
        setLocalOrderBy(column);
        setLocalOrder("desc");
      }
    }
  };

  const handlePageChange = (newPage: number) => {
    if (useStore) {
      setUsagesPagination({ ...storePagination, page: newPage });
    } else {
      setLocalPagination((prev) => ({ ...prev, page: newPage }));
    }
  };

  // 处理复选框选择
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // 选中所有当前页的记录（只选择状态为 ready 的）
      const readyUsageIds = usages
        .filter(usage => usage.status === "ready")
        .map(usage => usage.id);
      setSelectedUsageIds(readyUsageIds);
    } else {
      // 取消选中所有记录
      setSelectedUsageIds([]);
    }
  };

  // 判断当前页所有可选记录是否全部选中
  const isAllSelected = () => {
    const readyUsages = usages.filter(usage => usage.status === "ready");
    if (readyUsages.length === 0) return false;
    return readyUsages.every(usage => selectedUsageIds.includes(usage.id));
  };

  // 选择奖励类型
  const selectRedeemType = (type: "points" | "cash") => {
    setSelectedRedeemType(type);
  };

  // 确认兑换奖励
  const handleRedeemReward = async (usageId: string) => {
    try {
      // 确保已选择奖励类型
      if (!selectedRedeemType) {
        toast({
          variant: "destructive",
          title: "请选择奖励类型",
          description: "请先选择要兑换的奖励类型",
        });
        return;
      }

      setRedeemingId(usageId);
      setRedeemLoading(true);

      // 查找当前处理的邀请记录
      const currentUsage = usages.find(u => u.id === usageId);
      if (!currentUsage) {
        throw new Error("找不到邀请记录");
      }

      const response = await fetch(`/api/admin/invitation_usages/${usageId}/redeem`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          redeem_type: selectedRedeemType,
          note: `管理员兑换${selectedRedeemType === "points" ? "积分" : "现金"}奖励`,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "兑换奖励失败");
      }

      const rewardAmount = selectedRedeemType === "points"
        ? `${currentUsage.pointsAwarded} 积分`
        : `¥${((currentUsage.cashAwarded || 0) / 100).toFixed(2)}`;

      toast({
        title: "兑换成功",
        description: `已成功为用户 ${currentUsage.referee.username} 兑换 ${rewardAmount}`,
        className: "bg-green-500 text-white border-green-600",
      });

      // 刷新数据
      setLocalRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error("兑换奖励失败:", error);
      toast({
        variant: "destructive",
        title: "兑换失败",
        description: error instanceof Error ? error.message : "兑换奖励时出错",
      });
    } finally {
      setRedeemingId(null);
      setRedeemLoading(false);
      setSelectedRedeemType(null);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="bg-gray-100">待充值</Badge>;
      case "ready":
        return <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300">待兑换</Badge>;
      case "completed":
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">已兑换</Badge>;
      case "void":
        return <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">已作废</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const clearFilters = () => {
    if (useStore) {
      setUsagesFilter({ status: "all" });
    } else {
      setLocalStatus("all");
      setLocalPagination((prev) => ({ ...prev, page: 1 }));
      // Trigger search with cleared filters
      setTimeout(fetchUsages, 0);
    }
  };

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        {!hideFilters && (
          <div className="flex justify-between items-center p-2 border-b">
            <div></div>
            <div className="flex items-center gap-2">
              <Select
                value={status}
                onValueChange={(value) => {
                  if (useStore) {
                    setUsagesFilter({ status: value });
                  } else {
                    setLocalStatus(value);
                  }
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="筛选状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="pending">待充值</SelectItem>
                  <SelectItem value="ready">待兑换</SelectItem>
                  <SelectItem value="completed">已兑换</SelectItem>
                  <SelectItem value="void">已作废</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setLocalRefreshTrigger((prev) => prev + 1)}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
              <ClearFiltersButton
                onClick={clearFilters}
                disabled={status === "all"}
              />
            </div>
          </div>
        )}
        <Table>
          <TableHeader>
            <TableRow>
              {useStore && (
                <TableHead className="w-[50px]">
                  <Checkbox
                    checked={isAllSelected()}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all"
                  />
                </TableHead>
              )}
              <TableHead>
                <div
                  className="flex items-center cursor-pointer"
                  onClick={() => handleSort("referee.username")}
                >
                  被邀请人
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              {!invitationId && <TableHead>邀请码</TableHead>}
              <TableHead>充值情况</TableHead>
              <TableHead>奖励</TableHead>
              <TableHead>
                <div
                  className="flex items-center cursor-pointer"
                  onClick={() => handleSort("status")}
                >
                  状态
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={invitationId ? (useStore ? 7 : 6) : useStore ? 8 : 7}
                  className="text-center py-10"
                >
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : usages.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={invitationId ? (useStore ? 7 : 6) : useStore ? 8 : 7}
                  className="text-center py-10"
                >
                  暂无邀请记录
                </TableCell>
              </TableRow>
            ) : (
              usages.map((usage) => (
                <TableRow key={usage.id}>
                  {useStore && (
                    <TableCell className="w-[50px]">
                      <Checkbox
                        checked={selectedUsageIds.includes(usage.id)}
                        onCheckedChange={() => toggleUsageSelection(usage.id)}
                        disabled={usage.status !== "ready"}
                        aria-label={`Select ${usage.referee.username}`}
                      />
                    </TableCell>
                  )}
                  <TableCell>
                    <Link
                      href={`/admin/users/${usage.referee.clerkId}`}
                      className="flex items-center gap-2"
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={usage.referee.avatarUrl} />
                        <AvatarFallback>
                          {getInitials(usage.referee.username)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {usage.referee.username}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {usage.referee.email}
                        </span>
                      </div>
                    </Link>
                  </TableCell>
                  {!invitationId && (
                    <TableCell>
                      <Link href={`/admin/invitations/${usage.invitationId}`}>
                        {usage.invitation.inviteCode}
                      </Link>
                    </TableCell>
                  )}
                  <TableCell>
                    {usage.firstRechargeAt ? (
                      <div>
                        <div>{formatDate(usage.firstRechargeAt)}</div>
                        <div className="text-sm text-muted-foreground">
                          ¥{((usage.rechargeAmount || 0) / 100).toFixed(2)}
                        </div>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">未充值</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {usage.pointsAwarded ? (
                      <div className="text-blue-600">
                        {usage.pointsAwarded} 积分
                      </div>
                    ) : null}
                    {usage.cashAwarded ? (
                      <div className="text-green-600">
                        ¥{((usage.cashAwarded || 0) / 100).toFixed(2)}
                      </div>
                    ) : null}
                  </TableCell>
                  <TableCell>{getStatusBadge(usage.status)}</TableCell>
                  <TableCell>
                    {usage.status === "ready" && (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={redeemLoading && redeemingId === usage.id}
                            onClick={() => setSelectedRedeemType(null)}
                          >
                            {redeemLoading && redeemingId === usage.id
                              ? "处理中..."
                              : "兑换奖励"}
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>确认兑换奖励</AlertDialogTitle>
                            <AlertDialogDescription>
                              {usage.invitation.inviteType === "both" ? (
                                <div className="space-y-2">
                                  <p>请选择要兑换的奖励类型：</p>
                                  <p className="text-sm text-muted-foreground mt-1">
                                    兑换操作人为当前管理员，接收对象是邀请码所有者，发送人为系统
                                  </p>
                                  <div className="flex space-x-2 mt-4">
                                    <Button
                                      variant={
                                        selectedRedeemType === "points"
                                          ? "default"
                                          : "outline"
                                      }
                                      onClick={() => selectRedeemType("points")}
                                      disabled={!usage.pointsAwarded}
                                      className="flex-1"
                                    >
                                      积分 ({usage.pointsAwarded || 0})
                                    </Button>
                                    <Button
                                      variant={
                                        selectedRedeemType === "cash"
                                          ? "default"
                                          : "outline"
                                      }
                                      onClick={() => selectRedeemType("cash")}
                                      disabled={!usage.cashAwarded}
                                      className="flex-1"
                                    >
                                      现金 (¥
                                      {((usage.cashAwarded || 0) / 100).toFixed(
                                        2
                                      )}
                                      )
                                    </Button>
                                  </div>
                                </div>
                              ) : (
                                <div>
                                  <p>确定要兑换以下奖励吗？</p>
                                  <p className="text-sm text-muted-foreground mt-1">
                                    兑换操作人为当前管理员，接收对象是邀请码所有者，发送人为系统
                                  </p>
                                  {usage.invitation.inviteType === "points" &&
                                  usage.pointsAwarded ? (
                                    <p className="font-medium mt-2">
                                      {usage.pointsAwarded} 积分
                                    </p>
                                  ) : null}
                                  {usage.invitation.inviteType === "cash" &&
                                  usage.cashAwarded ? (
                                    <p className="font-medium mt-2">
                                      ¥{(usage.cashAwarded / 100).toFixed(2)}
                                    </p>
                                  ) : null}
                                </div>
                              )}
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel
                              onClick={() => setSelectedRedeemType(null)}
                            >
                              取消
                            </AlertDialogCancel>
                            {usage.invitation.inviteType !== "both" ? (
                              <AlertDialogAction
                                onClick={() => handleRedeemReward(usage.id)}
                              >
                                确认兑换
                              </AlertDialogAction>
                            ) : (
                              <AlertDialogAction
                                onClick={() => handleRedeemReward(usage.id)}
                                disabled={selectedRedeemType === null}
                              >
                                确认兑换
                              </AlertDialogAction>
                            )}
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}
                    {usage.status === "completed" && usage.redeemedAt && (
                      <div className="text-sm text-muted-foreground">
                        <div>时间: {formatDate(usage.redeemedAt)}</div>
                        <div>奖励: {usage?.extra?.redeemType}</div>
                        {usage.operator && (
                          <div>操作: {usage.operator.username}</div>
                        )}
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {!hideSearch && pagination.totalPages > 1 && (
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="text-sm text-muted-foreground">
            显示 {pagination.totalCount} 条结果中的第{" "}
            {(pagination.page - 1) * pagination.limit + 1} 到{" "}
            {Math.min(
              pagination.page * pagination.limit,
              pagination.totalCount
            )}{" "}
            条
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={!pagination.hasPrevPage}
            >
              <ChevronLeft className="h-4 w-4" />
              上一页
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={!pagination.hasNextPage}
            >
              下一页
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
