"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ChevronLeft, ChevronRight, Search, ArrowUpDown, Plus, Edit2 } from "lucide-react";
import { useToast } from "@/lib/hooks/use-toast";
import { ClearFiltersButton } from "@/components/admin/clear-filters-button";
import { Badge } from "@/components/ui/badge";
import { InvitationForm } from "@/components/admin/invitation/invitation-form";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/dialog";

interface Invitation {
  id: string;
  inviteCode: string;
  inviteType: "points" | "cash" | "both";
  refRatio: number;
  channel?: string;
  maxUses: number;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
  referrer: {
    clerkId: string;
    username: string;
    email: string;
    avatarUrl?: string;
  };
  usageCount: number;
  pendingCount: number;
  readyCount: number;
  completedCount: number;
}

interface Pagination {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export function InvitationsAdminTable() {
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  });
  const [loading, setLoading] = useState(true);
  const [searchCode, setSearchCode] = useState("");
  const [searchEmail, setSearchEmail] = useState("");
  const [inviteType, setInviteType] = useState("all");
  const [orderBy, setOrderBy] = useState("createdAt");
  const [order, setOrder] = useState<"asc" | "desc">("desc");
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedInvitation, setSelectedInvitation] = useState<Invitation | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const { toast } = useToast();

  const fetchInvitations = async () => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        orderBy,
        order,
      });

      if (searchCode) queryParams.append("code", searchCode);
      if (searchEmail) queryParams.append("email", searchEmail);
      if (inviteType !== "all") queryParams.append("type", inviteType);

      const response = await fetch(`/api/admin/invitations?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error("Failed to fetch invitations");
      }

      const data = await response.json();
      setInvitations(data.invitations);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching invitations:", error);
      toast({
        title: "错误",
        description: "加载邀请码列表失败，请重试。",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInvitations();
  }, [pagination.page, orderBy, order, refreshTrigger]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchInvitations();
  };

  const handleSort = (column: string) => {
    if (orderBy === column) {
      setOrder(order === "asc" ? "desc" : "asc");
    } else {
      setOrderBy(column);
      setOrder("desc");
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const handleCreateSuccess = () => {
    setCreateDialogOpen(false);
    toast({
      title: "创建成功",
      description: "邀请码已成功创建",
      className: "bg-green-500 text-white border-green-600",
    });
    setRefreshTrigger(prev => prev + 1);
  };

  const handleEditSuccess = () => {
    setEditDialogOpen(false);
    setSelectedInvitation(null);
    toast({
      title: "更新成功",
      description: "邀请码已成功更新",
      className: "bg-green-500 text-white border-green-600",
    });
    setRefreshTrigger(prev => prev + 1);
  };

  const handleEdit = (invitation: Invitation) => {
    setSelectedInvitation(invitation);
    setEditDialogOpen(true);
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getInviteTypeText = (type: "points" | "cash" | "both") => {
    switch (type) {
      case "points": return "积分奖励";
      case "cash": return "现金奖励";
      case "both": return "积分或现金";
      default: return type;
    }
  };

  const clearFilters = () => {
    setSearchCode("");
    setSearchEmail("");
    setInviteType("all");
    setPagination((prev) => ({ ...prev, page: 1 }));
    // Trigger search with cleared filters
    setTimeout(fetchInvitations, 0);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              创建邀请码
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>创建新邀请码</DialogTitle>
            </DialogHeader>
            <InvitationForm onSuccess={handleCreateSuccess} />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardContent className="pt-6">
          <form onSubmit={handleSearch} className="flex flex-wrap gap-4 mb-6">
            <div className="flex-1 min-w-[200px]">
              <Input
                placeholder="搜索邀请码"
                value={searchCode}
                onChange={(e) => setSearchCode(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="flex-1 min-w-[200px]">
              <Input
                placeholder="搜索邀请人邮箱"
                value={searchEmail}
                onChange={(e) => setSearchEmail(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="w-[150px]">
              <Select value={inviteType} onValueChange={setInviteType}>
                <SelectTrigger>
                  <SelectValue placeholder="奖励类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="points">积分奖励</SelectItem>
                  <SelectItem value="cash">现金奖励</SelectItem>
                  <SelectItem value="both">积分或现金</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button type="submit" className="flex-shrink-0">
              <Search className="h-4 w-4 mr-2" />
              搜索
            </Button>
            <ClearFiltersButton
              onClick={clearFilters}
              disabled={!searchCode && !searchEmail && inviteType === "all"}
            />
          </form>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>
                    <div
                      className="flex items-center cursor-pointer"
                      onClick={() => handleSort("inviteCode")}
                    >
                      邀请码
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead>
                    <div
                      className="flex items-center cursor-pointer"
                      onClick={() => handleSort("referrer.username")}
                    >
                      邀请人
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead>奖励类型</TableHead>
                  <TableHead>奖励比例</TableHead>
                  <TableHead>使用情况</TableHead>
                  <TableHead>
                    <div
                      className="flex items-center cursor-pointer"
                      onClick={() => handleSort("createdAt")}
                    >
                      创建时间
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-10">
                      <div className="flex justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : invitations.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-10">
                      没有找到邀请码
                    </TableCell>
                  </TableRow>
                ) : (
                  invitations.map((invitation) => (
                    <TableRow key={invitation.id}>
                      <TableCell className="font-medium">
                        {invitation.inviteCode}
                        {invitation.channel && (
                          <Badge variant="outline" className="ml-2">
                            {invitation.channel}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <Link href={`/admin/users/${invitation.referrer.clerkId}`} className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={invitation.referrer.avatarUrl} />
                            <AvatarFallback>
                              {getInitials(invitation.referrer.username)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex flex-col">
                            <span className="font-medium">{invitation.referrer.username}</span>
                            <span className="text-xs text-muted-foreground">
                              {invitation.referrer.email}
                            </span>
                          </div>
                        </Link>
                      </TableCell>
                      <TableCell>{getInviteTypeText(invitation.inviteType)}</TableCell>
                      <TableCell>{(invitation.refRatio * 100).toFixed(0)}%</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>
                            已使用: {invitation.usageCount}
                            {invitation.maxUses > 0 && `/${invitation.maxUses}`}
                          </span>
                          {invitation.readyCount > 0 && (
                            <span className="text-amber-500 text-sm">
                              待兑换: {invitation.readyCount}
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{formatDate(invitation.createdAt)}</span>
                          {invitation.expiresAt && (
                            <span className="text-sm text-muted-foreground">
                              过期: {formatDate(invitation.expiresAt)}
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEdit(invitation)}
                            title="编辑邀请码"
                          >
                            <Edit2 className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/admin/invitations/${invitation.id}`}>
                              查看详情
                            </Link>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between space-x-2 py-4">
              <div className="text-sm text-muted-foreground">
                显示 {pagination.totalCount} 条结果中的第{" "}
                {(pagination.page - 1) * pagination.limit + 1} 到{" "}
                {Math.min(pagination.page * pagination.limit, pagination.totalCount)} 条
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={!pagination.hasPrevPage}
                >
                  <ChevronLeft className="h-4 w-4" />
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={!pagination.hasNextPage}
                >
                  下一页
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 编辑邀请码对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑邀请码</DialogTitle>
          </DialogHeader>
          {selectedInvitation && (
            <InvitationForm
              initialData={{
                id: selectedInvitation.id,
                inviteCode: selectedInvitation.inviteCode,
                inviteType: selectedInvitation.inviteType,
                refRatio: selectedInvitation.refRatio,
                channel: selectedInvitation.channel,
                maxUses: selectedInvitation.maxUses,
                expiresAt: selectedInvitation.expiresAt,
                referrerId: selectedInvitation.referrer.clerkId
              }}
              onSuccess={handleEditSuccess}
              isEdit={true}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
