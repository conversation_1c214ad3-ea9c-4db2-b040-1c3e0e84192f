"use client";

import { useState } from "react";
import { DateRange } from "react-day-picker";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { nanoid } from "nanoid";
import { format } from "date-fns";
import { RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePickerWithRange } from "@/components/ui/date-picker-with-range";
import { cn } from "@/lib/utils";
import { useToast } from "@/lib/hooks/use-toast";
import { INVITE_CODE_LENGTH, INVITE_CODE_REGEX, INVITE_CODE_ERROR_MESSAGE } from "@/constants/invitation";

// 表单验证模式
const formSchema = z.object({
  invite_code: z.string()
    .min(3, "邀请码至少需要3个字符")
    .max(20, "邀请码最多20个字符")
    .refine((value) => !value || INVITE_CODE_REGEX.test(value), {
      message: INVITE_CODE_ERROR_MESSAGE,
    })
    .optional(),
  invite_type: z.enum(["points", "cash", "both"], {
    required_error: "请选择奖励类型",
  }),
  ref_ratio: z.coerce.number().min(0.01, "奖励比例最小为0.01").max(1, "奖励比例最大为1"),
  channel: z.string().optional(),
  max_uses: z.coerce.number().int("必须是整数").min(0, "不能为负数"),
  expires_at: z.date().optional(),
  referrer_id: z.string().optional(),
});

interface InvitationFormProps {
  onSuccess?: () => void;
  initialData?: {
    id?: string;
    inviteCode?: string;
    inviteType?: "points" | "cash" | "both";
    refRatio?: number;
    channel?: string;
    maxUses?: number;
    expiresAt?: string;
    referrerId?: string;
  };
  isEdit?: boolean;
}

export function InvitationForm({ onSuccess, initialData, isEdit = false }: InvitationFormProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      invite_code: initialData?.inviteCode || "",
      invite_type: initialData?.inviteType || "points",
      ref_ratio: initialData?.refRatio || 0.1,
      channel: initialData?.channel || "",
      max_uses: initialData?.maxUses || 0,
      expires_at: initialData?.expiresAt ? new Date(initialData.expiresAt) : undefined,
      referrer_id: initialData?.referrerId,
    },
  });

  const generateRandomCode = () => {
    // 生成符合规则的随机邀请码（只包含小写字母和数字）
    const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < INVITE_CODE_LENGTH; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    form.setValue("invite_code", result);
  };

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setLoading(true);

      // 准备请求数据
      const requestData = {
        ...values,
        expires_at: values.expires_at ? values.expires_at.toISOString() : undefined,
      };

      // 发送请求
      const url = isEdit && initialData?.id
        ? `/api/admin/invitations/${initialData.id}`
        : "/api/admin/invitations";

      const method = isEdit ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || (isEdit ? "更新邀请码失败" : "创建邀请码失败"));
      }

      // 调用成功回调
      if (onSuccess) {
        onSuccess();
      }

      toast({
        title: isEdit ? "更新成功" : "创建成功",
        description: isEdit ? "邀请码已成功更新" : "邀请码已成功创建",
        className: "bg-green-500 text-white border-green-600",
      });

      // 如果不是编辑模式，重置表单
      if (!isEdit) {
        form.reset({
          invite_code: "",
          invite_type: "points",
          ref_ratio: 0.1,
          channel: "",
          max_uses: 0,
          expires_at: undefined,
        });
      }
    } catch (error) {
      console.error(isEdit ? "更新邀请码失败:" : "创建邀请码失败:", error);
      toast({
        variant: "destructive",
        title: isEdit ? "更新失败" : "创建失败",
        description: error instanceof Error ? error.message : "发生未知错误",
      });
    } finally {
      setLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 第一行：邀请码和奖励类型 */}
          <FormField
            control={form.control}
            name="invite_code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>邀请码</FormLabel>
                <div className="flex gap-2">
                  <FormControl>
                    <Input
                      placeholder="留空自动生成"
                      {...field}
                    />
                  </FormControl>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={generateRandomCode}
                    title="生成随机邀请码"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
                <FormDescription>
                  自定义邀请码或留空自动生成
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="invite_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>奖励类型</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="选择奖励类型" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="points">积分奖励</SelectItem>
                    <SelectItem value="cash">现金奖励</SelectItem>
                    <SelectItem value="both">积分或现金</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  被邀请人首次充值后，邀请人将获得的奖励类型
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 第二行：奖励比例和渠道标识 */}
          <FormField
            control={form.control}
            name="ref_ratio"
            render={({ field }) => (
              <FormItem>
                <FormLabel>奖励比例</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    min="0.01"
                    max="1"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  奖励比例，范围0.01-1（1%到100%）
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="channel"
            render={({ field }) => (
              <FormItem>
                <FormLabel>渠道标识</FormLabel>
                <FormControl>
                  <Input
                    placeholder="可选"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
                <FormDescription>
                  可选的渠道标识，用于区分不同来源的邀请
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button type="submit" className="w-full mt-6" disabled={loading}>
          {loading ? "处理中..." : isEdit ? "更新邀请码" : "创建邀请码"}
        </Button>
      </form>
    </Form>
  );
}
