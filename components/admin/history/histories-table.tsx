"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, Search, ArrowUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/lib/hooks/use-toast";
import { DatePickerWithRange } from "@/components/ui/date-picker-with-range";
import { DateRange } from "react-day-picker";
import { startOfDay, endOfDay, subDays } from "date-fns";
import { ClearFiltersButton } from "@/components/admin/clear-filters-button";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";

interface History {
  id: string;
  userId: string;
  status: boolean;
  resultUrl?: string;
  prompt: string;
  description?: string;
  pointsUsed: number;
  parameters: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  drawStatus?: "PENDING" | "PROCESSING" | "SUCCESS" | "FAILED";
  drawResult?: string;
  user?: {
    clerkId: string;
    username: string;
    email: string;
  };
  extra?: {
    isWebp?: boolean;
    originalUrl?: string;
    [key: string]: any;
  };
}

interface Pagination {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface HistoriesTableProps {
  initialHistories?: History[];
  fixedUserId?: string;
  hideSearch?: boolean;
  hideFilters?: boolean;
  hidePagination?: boolean;
  hideUserColumn?: boolean;
  startDate?: Date;
  endDate?: Date;
}

export function HistoriesTable({
  initialHistories,
  fixedUserId,
  hideSearch = false,
  hideFilters = false,
  hidePagination = false,
  hideUserColumn = false,
  startDate,
  endDate
}: HistoriesTableProps = {}) {
  const [histories, setHistories] = useState<History[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  });
  const [loading, setLoading] = useState(true);
  const [userId, setUserId] = useState("");
  const [historyId, setHistoryId] = useState("");
  const [status, setStatus] = useState("");
  const [drawStatus, setDrawStatus] = useState("");
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: startDate || subDays(new Date(), 7),
    to: endDate || new Date()
  });
  const [orderBy, setOrderBy] = useState("createdAt");
  const [order, setOrder] = useState("desc");
  const [pageInput, setPageInput] = useState<string>("");
  const { toast } = useToast();

  const fetchHistories = async () => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        orderBy,
        order,
      });

      if (fixedUserId) {
        queryParams.append("userId", fixedUserId);
      } else if (userId) {
        queryParams.append("userId", userId);
      }
      if (historyId) queryParams.append("id", historyId);
      if (status && status !== 'all') queryParams.append("status", status);
      if (drawStatus && drawStatus !== 'all') queryParams.append("drawStatus", drawStatus);
      if (dateRange?.from) queryParams.append("startDate", startOfDay(dateRange.from).toISOString());
      if (dateRange?.to) queryParams.append("endDate", endOfDay(dateRange.to).toISOString());

      const response = await fetch(`/api/admin/histories?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error("Failed to fetch histories");
      }

      const data = await response.json();
      setHistories(data.histories);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching histories:", error);
      toast({
        title: "Error",
        description: "Failed to load histories. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (initialHistories) {
      setHistories(initialHistories);
      setLoading(false);
    } else {
      fetchHistories();
    }
    // Update pageInput when page changes
    setPageInput(pagination.page.toString());
  }, [initialHistories, pagination.page, orderBy, order]);

  // Update dateRange when props change
  useEffect(() => {
    if (startDate !== undefined || endDate !== undefined) {
      setDateRange({
        from: startDate || subDays(new Date(), 7),
        to: endDate || new Date()
      });
      fetchHistories();
    }
  }, [startDate, endDate]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchHistories();
  };

  const handleSort = (column: string) => {
    if (orderBy === column) {
      setOrder(order === "asc" ? "desc" : "asc");
    } else {
      setOrderBy(column);
      setOrder("desc");
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPageInput(e.target.value);
  };

  const handlePageInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const page = parseInt(pageInput);
    if (!isNaN(page) && page > 0 && page <= pagination.totalPages) {
      handlePageChange(page);
    } else {
      toast({
        title: "Invalid page number",
        description: `Please enter a number between 1 and ${pagination.totalPages}`,
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadge = (status: boolean) => {
    return status ? (
      <Badge className="bg-green-500">Success</Badge>
    ) : (
      <Badge className="bg-red-500">Failed</Badge>
    );
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="pt-6">
          {!hideSearch && (
            <div className="mb-6">
              <form onSubmit={handleSearch} className="space-y-4">
                {/* Basic search fields */}
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  <div className="w-full">
                    <Input
                      placeholder="User ID"
                      value={userId}
                      onChange={(e) => setUserId(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="w-full">
                    <Input
                      placeholder="History ID"
                      value={historyId}
                      onChange={(e) => setHistoryId(e.target.value)}
                      className="w-full"
                    />
                  </div>

                  {!hideFilters && (
                    <>
                      <div className="w-full">
                        <Select value={status} onValueChange={setStatus}>
                          <SelectTrigger>
                            <SelectValue placeholder="Status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Statuses</SelectItem>
                            <SelectItem value="true">Success</SelectItem>
                            <SelectItem value="false">Failed</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="w-full">
                        <Select value={drawStatus} onValueChange={setDrawStatus}>
                          <SelectTrigger>
                            <SelectValue placeholder="Draw Status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Draw Statuses</SelectItem>
                            <SelectItem value="SUCCESS">Success</SelectItem>
                            <SelectItem value="FAILED">Failed</SelectItem>
                            <SelectItem value="PENDING">Pending</SelectItem>
                            <SelectItem value="PROCESSING">Processing</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </>
                  )}
                </div>

                {!hideFilters && (
                  <div className="flex flex-wrap gap-3 items-end">
                    <div className="w-full sm:w-auto flex-1 min-w-[200px]">
                      <DatePickerWithRange
                        date={dateRange}
                        onSelect={(range) => setDateRange(range)}
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button
                        type="submit"
                        className="flex-1 sm:flex-none"
                      >
                        <Search className="h-4 w-4 mr-2" />
                        Search
                      </Button>
                      <ClearFiltersButton
                        onClick={() => {
                          if (!fixedUserId) setUserId("");
                          setHistoryId("");
                          setStatus("");
                          setDrawStatus("");
                          setDateRange(undefined);
                          setPagination((prev) => ({ ...prev, page: 1 }));
                          // Trigger search with cleared filters
                          setTimeout(() => fetchHistories(), 0);
                        }}
                        disabled={
                          !fixedUserId &&
                          !userId &&
                          !historyId &&
                          !status &&
                          drawStatus === "SUCCESS" &&
                          !dateRange
                        }
                      />
                    </div>
                  </div>
                )}

                {hideFilters && (
                  <Button
                    type="submit"
                    className="w-full sm:w-auto"
                  >
                    <Search className="h-4 w-4 mr-2" />
                    Search
                  </Button>
                )}
              </form>
            </div>
          )}

          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px]">Result</TableHead>
                  {!hideUserColumn && <TableHead className="min-w-[150px]">User</TableHead>}
                  <TableHead className="whitespace-nowrap">
                    <div
                      className="flex items-center cursor-pointer"
                      onClick={() => handleSort("status")}
                    >
                      Status
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="whitespace-nowrap">Draw Status</TableHead>
                  <TableHead className="whitespace-nowrap">
                    <div
                      className="flex items-center cursor-pointer"
                      onClick={() => handleSort("pointsUsed")}
                    >
                      Points
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="whitespace-nowrap min-w-[120px]">
                    <div
                      className="flex items-center cursor-pointer"
                      onClick={() => handleSort("createdAt")}
                    >
                      Created
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell
                      colSpan={hideUserColumn ? 6 : 7}
                      className="text-center py-10"
                    >
                      <div className="flex justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : histories.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={hideUserColumn ? 6 : 7}
                      className="text-center py-10"
                    >
                      No histories found
                    </TableCell>
                  </TableRow>
                ) : (
                  histories.map((history) => (
                    <TableRow key={history.id}>
                      <TableCell>
                        {history.resultUrl ? (
                          <HoverCard>
                            <HoverCardTrigger asChild>
                              <a
                                href={history.resultUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="block w-12 h-12 sm:w-16 sm:h-16 relative overflow-hidden rounded-md"
                              >
                                <img
                                  src={history.resultUrl}
                                  alt="Generation result"
                                  className="object-cover w-full h-full"
                                />
                              </a>
                            </HoverCardTrigger>
                            <HoverCardContent className="w-80">
                              <div className="flex flex-col space-y-2">
                                <img
                                  src={history.resultUrl}
                                  alt="Generation result preview"
                                  className="rounded-md object-contain max-h-64 w-full"
                                />
                                {history.prompt && (
                                  <p className="text-sm text-muted-foreground mt-2">
                                    {history.prompt.length > 100 ? `${history.prompt.substring(0, 100)}...` : history.prompt}
                                  </p>
                                )}
                              </div>
                            </HoverCardContent>
                          </HoverCard>
                        ) : (
                          <div className="w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center bg-muted rounded-md">
                            <span className="text-xs text-muted-foreground">
                              No image
                            </span>
                          </div>
                        )}
                      </TableCell>
                      {!hideUserColumn && (
                        <TableCell className="max-w-[150px] truncate">
                          {history.user ? (
                            <a
                              href={`/admin/users/${history.user.clerkId}`}
                              className="hover:underline"
                              title={history.user.email}
                            >
                              <div className="truncate">{history.user.email}</div>
                            </a>
                          ) : (
                            <span title={history.userId}>{history.userId.substring(0, 8) + "..."}</span>
                          )}
                        </TableCell>
                      )}
                      <TableCell>{getStatusBadge(history.status)}</TableCell>
                      <TableCell>
                        <Badge
                          className={cn(
                            history.drawStatus === "SUCCESS" && "bg-green-500",
                            history.drawStatus === "FAILED" && "bg-red-500",
                            history.drawStatus === "PENDING" && "bg-yellow-500",
                            history.drawStatus === "PROCESSING" && "bg-blue-500"
                          )}
                        >
                          {history.drawStatus || "SUCCESS"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span
                          className={cn(
                            "text-sm",
                            history.status && !history?.extra?.isWebp
                              ? "text-green-500"
                              : "text-red-500 line-through"
                          )}
                        >
                          {history.pointsUsed}
                        </span>
                      </TableCell>
                      <TableCell className="whitespace-nowrap">{formatDate(history.createdAt)}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="outline" size="sm" asChild>
                          <a href={`/admin/histories/${history.id}`}>View</a>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {!hidePagination && (
            <div className="flex flex-col sm:flex-row items-center justify-between mt-4 gap-4">
              <div className="text-sm text-muted-foreground">
                Showing {histories.length} of {pagination.totalCount} histories
              </div>
              <div className="flex flex-wrap items-center gap-2 justify-end">
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={!pagination.hasPrevPage}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <form
                    onSubmit={handlePageInputSubmit}
                    className="flex items-center gap-2"
                  >
                    <span className="text-sm hidden sm:inline">Page</span>
                    <Input
                      type="number"
                      min={1}
                      max={pagination.totalPages}
                      value={pageInput}
                      onChange={handlePageInputChange}
                      onBlur={() => setPageInput(pagination.page.toString())}
                      onClick={(e) => e.currentTarget.select()}
                      className="w-[50px] h-8 text-center"
                      placeholder={pagination.page.toString()}
                    />
                    <span className="text-sm whitespace-nowrap">of {pagination.totalPages}</span>
                  </form>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={!pagination.hasNextPage}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
                <Select
                  value={pagination.limit.toString()}
                  onValueChange={(value) =>
                    setPagination((prev) => ({
                      ...prev,
                      limit: parseInt(value),
                      page: 1,
                    }))
                  }
                >
                  <SelectTrigger className="w-[70px]">
                    <SelectValue placeholder="10" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
