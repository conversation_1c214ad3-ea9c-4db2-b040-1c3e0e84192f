"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { TransactionsTable } from "@/components/admin/transactions-table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { ChevronLeft, ExternalLink, Share2, Eye, ThumbsUp, GitFork, Trash2 } from "lucide-react";
import { useToast } from "@/lib/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rig<PERSON>,
} from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";

interface HistoryDetail {
  history: {
    id: string;
    userId: string;
    status: boolean;
    resultUrl?: string;
    prompt: string;
    description?: string;
    pointsUsed: number;
    parameters: any;
    forkedFromShareId?: string;
    extra: any;
    createdAt: string;
    updatedAt: string;
    drawStatus?: "PENDING" | "PROCESSING" | "SUCCESS" | "FAILED";
    drawResult?: string;
    user?: {
      clerkId: string;
      username: string;
      email: string;
      avatarUrl?: string;
      createdAt: string;
    };
  };
  share?: {
    id: string;
    shareId: string;
    historyId: string;
    userId: string;
    isPublic: boolean;
    allowFork: boolean;
    forkTipPoints: number;
    imageUrl: string;
    viewCount: number;
    likeCount: number;
    forkCount: number;
    forkEarnings: number;
    sharedAt: string;
  };
}

interface HistoryDetailProps {
  id: string;
  onBack?: () => void;
}

export function HistoryDetail({ id, onBack }: HistoryDetailProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [historyDetail, setHistoryDetail] = useState<HistoryDetail | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  useEffect(() => {
    const fetchHistoryDetail = async () => {
      try {
        const response = await fetch(`/api/admin/histories/${id}`);

        if (!response.ok) {
          throw new Error("Failed to fetch history details");
        }

        const data = await response.json();
        setHistoryDetail(data);
      } catch (error) {
        console.error("Error fetching history details:", error);
        toast({
          title: "Error",
          description: "Failed to load history details. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchHistoryDetail();
  }, [id, toast]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const getStatusBadge = (status: boolean) => {
    return status ? (
      <Badge className="bg-green-500">Success</Badge>
    ) : (
      <Badge className="bg-red-500">Failed</Badge>
    );
  };

  const getDrawStatusBadge = (drawStatus?: string) => {
    if (!drawStatus) return null;
    
    switch (drawStatus) {
      case "SUCCESS":
        return <Badge className="bg-green-500">SUCCESS</Badge>;
      case "PENDING":
        return <Badge className="bg-yellow-500">PENDING</Badge>;
      case "PROCESSING":
        return <Badge className="bg-blue-500">PROCESSING</Badge>;
      case "FAILED":
        return <Badge className="bg-red-500">FAILED</Badge>;
      default:
        return <Badge>{drawStatus}</Badge>;
    }
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!historyDetail) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleBack}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">History Not Found</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center py-8">The requested history could not be found.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { history, share } = historyDetail;
  const parameters = history.parameters || {};
  const extra = history.extra || {};

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleBack}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">History Details</h1>
        </div>
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogTrigger asChild>
            <Button variant="destructive" size="sm" disabled={isDeleting || history.drawStatus === "PROCESSING"}>
              <Trash2 className="h-4 w-4 mr-2" />
              {isDeleting ? "Deleting..." : "Delete History"}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will archive this history record, delete any R2 files if they exist, and make any associated share private.
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={async (e) => {
                  e.preventDefault();
                  setIsDeleting(true);
                  try {
                    const response = await fetch(`/api/admin/histories/${history.id}`, {
                      method: "DELETE",
                    });

                    if (!response.ok) {
                      throw new Error("Failed to delete history");
                    }

                    const result = await response.json();
                    toast({
                      title: "Success",
                      description: `History archived successfully. ${result.deletedFiles} files deleted.`,
                    });

                    // Redirect back to histories list
                    router.push("/admin/histories");
                  } catch (error) {
                    console.error("Error deleting history:", error);
                    toast({
                      title: "Error",
                      description: "Failed to delete history. Please try again.",
                      variant: "destructive",
                    });
                    setIsDeleting(false);
                  }
                }}
                className="bg-red-600 hover:bg-red-700"
              >
                {isDeleting ? "Deleting..." : "Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>Details about this generation history</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">ID:</span>
                <span className="text-muted-foreground">{history.id}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Status:</span>
                <span>{getStatusBadge(history.status)}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Draw Status:</span>
                <span>{getDrawStatusBadge(history.drawStatus)}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Points Used:</span>
                <span className="text-muted-foreground">{history.pointsUsed}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Created At:</span>
                <span className="text-muted-foreground">{formatDate(history.createdAt)}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Updated At:</span>
                <span className="text-muted-foreground">{formatDate(history.updatedAt)}</span>
              </div>
              {history.forkedFromShareId && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">Forked From:</span>
                  <a
                    href={`/admin/shares/${history.forkedFromShareId}`}
                    className="text-blue-500 hover:underline flex items-center"
                  >
                    View Original <ExternalLink className="ml-1 h-3 w-3" />
                  </a>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* User Information */}
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
            <CardDescription>User who created this generation</CardDescription>
          </CardHeader>
          <CardContent>
            {history.user ? (
              <div className="flex flex-col items-center gap-4 mb-6">
                <Avatar className="h-24 w-24">
                  <AvatarImage src={history.user.avatarUrl} />
                  <AvatarFallback className="text-lg">
                    {getInitials(history.user.username)}
                  </AvatarFallback>
                </Avatar>
                <div className="text-center">
                  <h2 className="text-xl font-semibold">{history.user.username}</h2>
                  <p className="text-muted-foreground">{history.user.email}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  asChild
                >
                  <a href={`/admin/users/${history.user.clerkId}`}>View User Profile</a>
                </Button>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">User information not available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Prompt and Result */}
      <Card>
        <CardHeader>
          <CardTitle>Prompt and Result</CardTitle>
          <CardDescription>Generation prompt and result details</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Prompt</h3>
              <div className="bg-muted p-4 rounded-md whitespace-pre-wrap">
                {history.prompt}
              </div>
            </div>

            {history.description && (
              <div>
                <h3 className="text-lg font-medium mb-2">Description</h3>
                <div className="bg-muted p-4 rounded-md whitespace-pre-wrap">
                  {history.description}
                </div>
              </div>
            )}

            <div>
              <h3 className="text-lg font-medium mb-2">Result</h3>
              {history.resultUrl ? (
                <div className="flex flex-col items-center">
                  <div className="relative w-full max-w-md h-64 mb-4 overflow-hidden rounded-md">
                    <img
                      src={history.resultUrl}
                      alt="Generation result"
                      className="object-contain w-full h-full"
                    />
                  </div>
                  <a
                    href={history.resultUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline flex items-center"
                  >
                    View Full Size <ExternalLink className="ml-1 h-4 w-4" />
                  </a>
                </div>
              ) : (
                <div className="bg-muted p-4 rounded-md text-center">
                  No result available
                </div>
              )}
            </div>

            {history.drawResult && (
              <div>
                <h3 className="text-lg font-medium mb-2">Draw Result</h3>
                <div className="bg-muted p-4 rounded-md whitespace-pre-wrap overflow-auto max-h-[300px]">
                  {history.drawResult}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Parameters */}
      <Card>
        <CardHeader>
          <CardTitle>Generation Parameters</CardTitle>
          <CardDescription>Technical parameters used for this generation</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Parameter</TableHead>
                  <TableHead>Value</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Object.entries(parameters).length > 0 ? (
                  Object.entries(parameters).map(([key, value]) => (
                    <TableRow key={key}>
                      <TableCell className="font-medium">{key}</TableCell>
                      <TableCell>
                        {typeof value === 'object'
                          ? JSON.stringify(value)
                          : String(value)
                        }
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={2} className="text-center py-4">
                      No parameters available
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Share Information */}
      {share && (
        <Card>
          <CardHeader>
            <CardTitle>Share Information</CardTitle>
            <CardDescription>Details about how this generation was shared</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <div className="space-y-2">
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Share ID:</span>
                    <span className="text-muted-foreground">{share.shareId}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Visibility:</span>
                    <Badge className={share.isPublic ? "bg-green-500" : "bg-yellow-500"}>
                      {share.isPublic ? "Public" : "Private"}
                    </Badge>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Allow Fork:</span>
                    <Badge className={share.allowFork ? "bg-green-500" : "bg-red-500"}>
                      {share.allowFork ? "Allowed" : "Not Allowed"}
                    </Badge>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Fork Tip Points:</span>
                    <span className="text-muted-foreground">{share.forkTipPoints}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Shared At:</span>
                    <span className="text-muted-foreground">{formatDate(share.sharedAt)}</span>
                  </div>
                </div>
              </div>
              <div>
                <div className="flex flex-col items-center gap-4">
                  <div className="flex gap-6 text-center">
                    <div className="flex flex-col items-center">
                      <Eye className="h-5 w-5 mb-1 text-blue-500" />
                      <span className="text-xl font-bold">{share.viewCount}</span>
                      <span className="text-sm text-muted-foreground">Views</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <ThumbsUp className="h-5 w-5 mb-1 text-green-500" />
                      <span className="text-xl font-bold">{share.likeCount}</span>
                      <span className="text-sm text-muted-foreground">Likes</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <GitFork className="h-5 w-5 mb-1 text-purple-500" />
                      <span className="text-xl font-bold">{share.forkCount}</span>
                      <span className="text-sm text-muted-foreground">Forks</span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <span className="text-lg font-medium">Fork Earnings: </span>
                    <span className="text-xl font-bold">{share.forkEarnings}</span>
                    <span className="text-sm text-muted-foreground ml-1">points</span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    asChild
                  >
                    <a href={`/admin/shares/${share.id}`}>
                      <Share2 className="h-4 w-4 mr-2" />
                      View Share Details
                    </a>
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Transactions */}
      {extra.transactions && extra.transactions.length > 0 && (
        <TransactionsTable
          transactions={extra.transactions}
          title="Points Transactions"
          description="Points transactions associated with this generation"
        />
      )}

      {/* Extra Information */}
      {Object.keys(extra).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
            <CardDescription>Extra metadata about this generation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Field</TableHead>
                    <TableHead>Value</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(extra)
                    .filter(([key]) => key !== 'transactions') // Skip transactions as they're shown in their own component
                    .map(([key, value]) => (
                      <TableRow key={key}>
                        <TableCell className="font-medium">{key}</TableCell>
                        <TableCell>
                          {typeof value === 'object'
                            ? JSON.stringify(value)
                            : String(value)
                          }
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
