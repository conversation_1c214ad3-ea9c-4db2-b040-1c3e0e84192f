"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth, useOrganization } from "@clerk/nextjs";

export function AdminCheck({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [authorized, setAuthorized] = useState(false);

  const { isLoaded: isAuthLoaded, userId } = useAuth();
  const { isLoaded: isOrgLoaded, organization } = useOrganization();

  useEffect(() => {
    // Wait for both auth and organization to load
    if (!isAuthLoaded || !isOrgLoaded) return;

    // If user is not authenticated, redirect to home
    if (!userId) {
      router.push("/");
      return;
    }

    // Check if user belongs to an organization with slug "root"
    const isAdminUser = organization?.slug === "root";

    if (!isAdminUser) {
      router.push("/");
    } else {
      setAuthorized(true);
    }

    setLoading(false);
  }, [isAuthLoaded, isOrgLoaded, userId, organization, router]);

  if (loading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
      </div>
    );
  }

  if (!authorized) {
    return null; // Will redirect in useEffect
  }

  return <>{children}</>;
}
