"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { BLOCKLIST_TYPE_OPTIONS } from "@/constants/blocklist";
import { useToast } from "@/lib/hooks/use-toast";
import { Loader2, CheckCircle2, XCircle } from "lucide-react";
import { cn } from "@/lib/utils";

// 表单验证模式
const formSchema = z.object({
  type: z.string({
    required_error: "请选择黑名单类型",
  }),
  pattern: z
    .string({
      required_error: "请输入匹配规则",
    })
    .min(1, "匹配规则不能为空")
    .max(255, "匹配规则不能超过255个字符"),
  description: z.string().optional(),
  enabled: z.boolean().default(true),
});

type FormValues = z.infer<typeof formSchema>;

interface BlocklistFormProps {
  initialData?: {
    id?: string;
    type?: string;
    pattern?: string;
    description?: string;
    enabled?: boolean;
  };
  onSubmit: (values: FormValues) => Promise<void>;
  onCancel: () => void;
  isEdit?: boolean;
}

export function BlocklistForm({
  initialData,
  onSubmit,
  onCancel,
  isEdit = false,
}: BlocklistFormProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [testString, setTestString] = useState("");
  const [testResult, setTestResult] = useState<boolean | null>(null);

  // 测试正则表达式匹配
  const handleTestPattern = () => {
    const pattern = form.getValues("pattern");

    if (!pattern || !testString) {
      setTestResult(null);
      return;
    }

    try {
      const regex = new RegExp(pattern);
      const result = regex.test(testString);
      setTestResult(result);
    } catch (error) {
      setTestResult(null);
      toast({
        title: "无效的正则表达式",
        description: "请检查您的正则表达式语法",
        variant: "destructive",
      });
    }
  };

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema) as any,
    defaultValues: {
      type: initialData?.type || "",
      pattern: initialData?.pattern || "",
      description: initialData?.description || "",
      enabled: initialData?.enabled !== undefined ? initialData.enabled : true,
    },
  });

  // 提交表单
  const handleSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);
      await onSubmit(values);
      toast({
        title: isEdit ? "更新成功" : "创建成功",
        description: isEdit
          ? "黑名单规则已更新"
          : "新的黑名单规则已创建",
      });
    } catch (error) {
      console.error("Form submission error:", error);
      toast({
        title: "操作失败",
        description:
          error instanceof Error
            ? error.message
            : "提交表单时发生错误",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>黑名单类型</FormLabel>
              <Select
                disabled={isEdit || isSubmitting}
                onValueChange={field.onChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择黑名单类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {BLOCKLIST_TYPE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                选择要匹配的数据类型
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="pattern"
          render={({ field }) => (
            <FormItem>
              <FormLabel>匹配规则</FormLabel>
              <FormControl>
                <Input
                  placeholder="输入正则表达式匹配规则"
                  {...field}
                  disabled={isSubmitting}
                />
              </FormControl>
              <FormDescription>
                使用正则表达式匹配规则，例如：.*@example\\.com
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 正则表达式测试区域 */}
        <div className="space-y-4 rounded-lg border p-4">
          <h3 className="text-sm font-medium">测试匹配规则</h3>
          <div className="flex gap-2">
            <Input
              placeholder="输入测试字符串"
              value={testString}
              onChange={(e) => setTestString(e.target.value)}
              className="flex-1"
            />
            <Button
              type="button"
              variant="secondary"
              onClick={handleTestPattern}
              disabled={!testString || !form.getValues("pattern")}
            >
              测试
            </Button>
          </div>

          {testResult !== null && (
            <div className={cn(
              "flex items-center gap-2 rounded-md p-2",
              testResult ? "bg-green-50 text-green-700" : "bg-red-50 text-red-700"
            )}>
              {testResult ? (
                <CheckCircle2 className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              <span>
                {testResult
                  ? "匹配成功：该字符串符合规则"
                  : "匹配失败：该字符串不符合规则"}
              </span>
            </div>
          )}
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>描述</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="输入规则描述（可选）"
                  {...field}
                  disabled={isSubmitting}
                />
              </FormControl>
              <FormDescription>
                添加规则的描述信息，方便管理
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="enabled"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">启用状态</FormLabel>
                <FormDescription>
                  设置规则是否立即生效
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  disabled={isSubmitting}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            取消
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            {isEdit ? "更新" : "创建"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
