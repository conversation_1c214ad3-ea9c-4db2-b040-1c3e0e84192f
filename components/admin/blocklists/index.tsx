"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BlocklistsTable } from "./blocklists-table";
import { useToast } from "@/lib/hooks/use-toast";
import { Plus, RefreshCw } from "lucide-react";
import { Pagination } from "@/components/ui/pagination";
import { BLOCKLIST_TYPE_OPTIONS, BLOCKLIST_STATUS_OPTIONS } from "@/constants/blocklist";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useRouter } from "next/navigation";

type BlocklistRule = {
  id: string;
  type: string;
  pattern: string;
  enabled: boolean;
  description?: string;
  createdAt: string;
  updatedAt: string;
  creator?: {
    username: string;
    email: string;
  };
};

type FormValues = {
  type: string;
  pattern: string;
  description?: string;
  enabled: boolean;
};

export function AdminBlocklists() {
  const { toast } = useToast();
  const router = useRouter();
  const [rules, setRules] = useState<BlocklistRule[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [typeFilter, setTypeFilter] = useState<string | undefined>(undefined);
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined);

  // 加载黑名单规则
  const loadRules = async () => {
    try {
      setIsLoading(true);

      let url = `/api/admin/blocklists?page=${page}&limit=10`;

      if (typeFilter) {
        url += `&type=${typeFilter}`;
      }

      if (statusFilter !== undefined) {
        url += `&enabled=${statusFilter}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error("Failed to load blocklist rules");
      }

      const data = await response.json();
      setRules(data.rules);
      setTotalItems(data.total);
      setTotalPages(Math.ceil(data.total / data.limit));
    } catch (error) {
      console.error("Error loading blocklist rules:", error);
      toast({
        title: "加载失败",
        description: "无法加载黑名单规则",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadRules();
  }, [page, typeFilter, statusFilter]);

  // 删除规则的方法保留，其他方法在子页面中实现

  // 删除规则
  const handleDeleteRule = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/blocklists/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete blocklist rule");
      }

      toast({
        title: "删除成功",
        description: "黑名单规则已删除",
      });

      loadRules();
    } catch (error) {
      console.error("Error deleting blocklist rule:", error);
      throw error;
    }
  };

  // 切换规则状态
  const handleToggleStatus = async (id: string, enabled: boolean) => {
    try {
      const response = await fetch(`/api/admin/blocklists/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ enabled }),
      });

      if (!response.ok) {
        throw new Error("Failed to update blocklist rule status");
      }

      toast({
        title: enabled ? "规则已启用" : "规则已禁用",
        description: `黑名单规则状态已更新为${enabled ? "启用" : "禁用"}`,
      });

      loadRules();
    } catch (error) {
      console.error("Error updating blocklist rule status:", error);
      throw error;
    }
  };

  // 编辑规则
  const handleEditRule = (rule: BlocklistRule) => {
    router.push(`/admin/blocklists/edit/${rule.id}`);
  };

  // 打开创建表单
  const openCreateForm = () => {
    router.push('/admin/blocklists/add');
  };

  // 处理分页
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // 处理类型筛选
  const handleTypeFilterChange = (value: string) => {
    setTypeFilter(value === "all" ? undefined : value);
    setPage(1);
  };

  // 处理状态筛选
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value === "all" ? undefined : value);
    setPage(1);
  };

  // 重置筛选
  const resetFilters = () => {
    setTypeFilter(undefined);
    setStatusFilter(undefined);
    setPage(1);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">黑名单管理</h1>
        <Button onClick={openCreateForm}>
          <Plus className="mr-2 h-4 w-4" />
          添加规则
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>黑名单规则</CardTitle>
          <CardDescription>
            管理用户注册时的黑名单规则，匹配的用户将只获得1积分而不是标准的300积分
          </CardDescription>
          <div className="flex flex-wrap gap-4 mt-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">类型:</span>
              <Select
                value={typeFilter || "all"}
                onValueChange={handleTypeFilterChange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="所有类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  {BLOCKLIST_TYPE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">状态:</span>
              <Select
                value={statusFilter !== undefined ? String(statusFilter) : "all"}
                onValueChange={handleStatusFilterChange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="所有状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  {BLOCKLIST_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={resetFilters}
              className="ml-auto"
            >
              重置筛选
            </Button>

            <Button
              variant="outline"
              size="icon"
              onClick={() => loadRules()}
              className="ml-2"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <BlocklistsTable
            rules={rules}
            onEdit={handleEditRule}
            onDelete={handleDeleteRule}
            onToggleStatus={handleToggleStatus}
            isLoading={isLoading}
          />

          {totalPages > 1 && (
            <div className="flex justify-center mt-4">
              <Pagination
                currentPage={page}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}

          <div className="text-sm text-muted-foreground mt-4 text-center">
            共 {totalItems} 条规则
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
