"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Pencil, Trash2 } from "lucide-react";

import { useToast } from "@/lib/hooks/use-toast";
import { BLOCKLIST_TYPES } from "@/constants/blocklist";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";

type BlocklistRule = {
  id: string;
  type: string;
  pattern: string;
  enabled: boolean;
  description?: string;
  createdAt: string;
  updatedAt: string;
  creator?: {
    username: string;
    email: string;
  };
};

interface BlocklistsTableProps {
  rules: BlocklistRule[];
  onEdit: (rule: BlocklistRule) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, enabled: boolean) => void;
  isLoading?: boolean;
}

export function BlocklistsTable({
  rules,
  onEdit,
  onDelete,
  onToggleStatus,
  isLoading = false,
}: BlocklistsTableProps) {
  const { toast } = useToast();
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [ruleToDelete, setRuleToDelete] = useState<string | null>(null);

  const handleToggleStatus = async (rule: BlocklistRule) => {
    try {
      await onToggleStatus(rule.id, !rule.enabled);
    } catch (error) {
      toast({
        title: "操作失败",
        description: "更新规则状态时出错",
        variant: "destructive",
      });
    }
  };

  const confirmDelete = (id: string) => {
    setRuleToDelete(id);
    setDeleteConfirmOpen(true);
  };

  const handleDelete = async () => {
    if (ruleToDelete) {
      try {
        await onDelete(ruleToDelete);
        setDeleteConfirmOpen(false);
        setRuleToDelete(null);
      } catch (error) {
        toast({
          title: "删除失败",
          description: "删除规则时出错",
          variant: "destructive",
        });
      }
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case BLOCKLIST_TYPES.EMAIL:
        return "邮箱";
      case BLOCKLIST_TYPES.IP:
        return "IP地址";
      case BLOCKLIST_TYPES.KEYWORD:
        return "关键词";
      default:
        return type;
    }
  };

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">类型</TableHead>
              <TableHead>描述</TableHead>
              <TableHead className="w-[100px]">状态</TableHead>
              <TableHead className="w-[100px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  加载中...
                </TableCell>
              </TableRow>
            ) : rules.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  暂无数据
                </TableCell>
              </TableRow>
            ) : (
              rules.map((rule) => (
                <TableRow key={rule.id}>
                  <TableCell>
                    <Badge variant="outline">{getTypeLabel(rule.type)}</Badge>
                  </TableCell>
                  <TableCell>
                    <span title={rule.pattern}>
                      {rule.description || "-"}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Switch
                      checked={rule.enabled}
                      onCheckedChange={() => handleToggleStatus(rule)}
                    />
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEdit(rule)}
                        className="h-8 px-2"
                      >
                        <Pencil className="h-4 w-4" />
                        <span className="ml-1">编辑</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => confirmDelete(rule.id)}
                        className="h-8 px-2 text-destructive hover:text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="ml-1">删除</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这条黑名单规则吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
