"use client";

import { useState } from "react";
import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp, ExternalLink } from "lucide-react";
import Link from "next/link";

interface Transaction {
  id: string;
  type: string;
  amount: number;
  description: string;
  exchangeType?: string;
  timestamp: string;
  userId: string;
  note?: string;
  [key: string]: any;
}

interface TransactionsTableProps {
  transactions?: Transaction[];
  title?: string;
  description?: string;
}

export function TransactionsTable({
  transactions = [],
  title = "Related Transactions",
  description = "Transactions associated with this record",
}: TransactionsTableProps) {
  const [expanded, setExpanded] = useState(false);
  
  // If no transactions, don't render anything
  if (!transactions || transactions.length === 0) {
    return null;
  }

  // Sort transactions by timestamp (newest first)
  const sortedTransactions = [...transactions].sort((a, b) => {
    return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
  });

  // Show only the latest 3 transactions unless expanded
  const displayedTransactions = expanded 
    ? sortedTransactions 
    : sortedTransactions.slice(0, 3);

  // Get exchange type badge
  const getExchangeTypeBadge = (type: string) => {
    switch (type) {
      case "refund":
        return <Badge className="bg-blue-500">Refund</Badge>;
      case "gift":
        return <Badge className="bg-green-500">Gift</Badge>;
      case "award":
        return <Badge className="bg-yellow-500">Award</Badge>;
      case "affiliate":
        return <Badge className="bg-purple-500">Affiliate</Badge>;
      default:
        return <Badge>{type}</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Details</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {displayedTransactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell>
                    {format(new Date(transaction.timestamp), "yyyy-MM-dd HH:mm:ss")}
                  </TableCell>
                  <TableCell>
                    {transaction.exchangeType && getExchangeTypeBadge(transaction.exchangeType)}
                    {!transaction.exchangeType && (
                      <Badge variant={transaction.type === "credit" ? "default" : "destructive"}>
                        {transaction.type === "credit" ? "Credit" : "Debit"}
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <span className={transaction.type === "credit" ? "text-green-600" : "text-red-600"}>
                      {transaction.type === "credit" ? "+" : "-"}{transaction.amount}
                    </span>
                  </TableCell>
                  <TableCell>{transaction.description}</TableCell>
                  <TableCell>
                    <Link href={`/admin/orders/${transaction.id}`} className="text-blue-600 hover:underline flex items-center">
                      View
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        
        {transactions.length > 3 && (
          <div className="flex justify-center mt-4">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? (
                <>
                  Show Less <ChevronUp className="ml-2 h-4 w-4" />
                </>
              ) : (
                <>
                  Show All ({transactions.length}) <ChevronDown className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
