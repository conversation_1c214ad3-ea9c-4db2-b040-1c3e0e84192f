import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface ClearFiltersButtonProps {
  onClick: () => void;
  disabled?: boolean;
}

export function ClearFiltersButton({ onClick, disabled = false }: ClearFiltersButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            size="icon"
            onClick={onClick}
            disabled={disabled}
            className="flex-shrink-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Clear all filters</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
