"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";

import { OrderStatusSelector } from "@/components/admin/order/order-status-selector";
import { ChevronLeft, ChevronRight, Search, ArrowUpDown } from "lucide-react";
import { useToast } from "@/lib/hooks/use-toast";
import { DatePickerWithRange } from "@/components/ui/date-picker-with-range";
import { DateRange } from "react-day-picker";
import { startOfDay, endOfDay, subDays } from "date-fns";
import { ClearFiltersButton } from "@/components/admin/clear-filters-button";

interface Order {
  id: string;
  userId: string;
  buyerId: string;
  type: string;
  amount: number;
  description: string;
  status: string;
  paymentMethod?: string;
  createdAt: string;
  updatedAt: string;
  extra: {
    price?: number;
    [key: string]: any;
  };
  user?: {
    clerkId: string;
    username: string;
    email: string;
  };
}

interface Pagination {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface OrdersTableProps {
  initialOrders?: Order[];
  fixedUserId?: string;
  hideSearch?: boolean;
  hideFilters?: boolean;
  hidePagination?: boolean;
  hideUserColumn?: boolean;
  startDate?: Date;
  endDate?: Date;
}

export function OrdersTable({
  initialOrders,
  fixedUserId,
  hideSearch = false,
  hideFilters = false,
  hidePagination = false,
  hideUserColumn = false,
  startDate,
  endDate
}: OrdersTableProps = {}) {
  const [orders, setOrders] = useState<Order[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  });
  const [loading, setLoading] = useState(true);
  const [userId, setUserId] = useState("");
  const [orderId, setOrderId] = useState("");
  const [type, setType] = useState("");
  const [status, setStatus] = useState("");
  const [buyerType, setBuyerType] = useState("");
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: startDate || subDays(new Date(), 7),
    to: endDate || new Date()
  });
  const [orderBy, setOrderBy] = useState("createdAt");
  const [order, setOrder] = useState("desc");
  const [pageInput, setPageInput] = useState<string>("");
  const { toast } = useToast();

  const fetchOrders = async () => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        orderBy,
        order,
      });

      if (fixedUserId) {
        queryParams.append("userId", fixedUserId);
      } else if (userId) {
        queryParams.append("userId", userId);
      }
      if (orderId) queryParams.append("id", orderId);
      if (type && type !== 'all') queryParams.append("type", type);
      if (status && status !== 'all') queryParams.append("status", status);
      if (buyerType && buyerType !== 'all') queryParams.append("buyerType", buyerType);
      if (dateRange?.from) queryParams.append("startDate", startOfDay(dateRange.from).toISOString());
      if (dateRange?.to) queryParams.append("endDate", endOfDay(dateRange.to).toISOString());

      const response = await fetch(`/api/admin/orders?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error("Failed to fetch orders");
      }

      const data = await response.json();
      setOrders(data.orders);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching orders:", error);
      toast({
        title: "Error",
        description: "Failed to load orders. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (initialOrders) {
      setOrders(initialOrders);
      setLoading(false);
    } else {
      fetchOrders();
    }
    // Update pageInput when page changes
    setPageInput(pagination.page.toString());
  }, [initialOrders, pagination.page, orderBy, order]);

  // Update dateRange when props change
  useEffect(() => {
    if (startDate !== undefined || endDate !== undefined) {
      setDateRange({
        from: startDate || subDays(new Date(), 7),
        to: endDate || new Date()
      });
      fetchOrders();
    }
  }, [startDate, endDate]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchOrders();
  };

  const handleSort = (column: string) => {
    if (orderBy === column) {
      setOrder(order === "asc" ? "desc" : "asc");
    } else {
      setOrderBy(column);
      setOrder("desc");
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPageInput(e.target.value);
  };

  const handlePageInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const page = parseInt(pageInput);
    if (!isNaN(page) && page > 0 && page <= pagination.totalPages) {
      handlePageChange(page);
    } else {
      toast({
        title: "Invalid page number",
        description: `Please enter a number between 1 and ${pagination.totalPages}`,
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="pt-6">
          {!hideSearch && (
            <form onSubmit={handleSearch} className="flex flex-wrap gap-4 mb-6">
              <div className="flex-1 min-w-[160px]">
                <Input
                  placeholder="User ID"
                  value={userId}
                  onChange={(e) => setUserId(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex-1 min-w-[160px]">
                <Input
                  placeholder="Order ID"
                  value={orderId}
                  onChange={(e) => setOrderId(e.target.value)}
                  className="w-full"
                />
              </div>
              {!hideFilters && (
                <>
                  <div className="w-[130px]">
                    <Select value={status} onValueChange={setStatus}>
                      <SelectTrigger>
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="SUCCESS">Success</SelectItem>
                        <SelectItem value="PENDING">Pending</SelectItem>
                        <SelectItem value="FAILED">Failed</SelectItem>
                        <SelectItem value="REFUND">Refund</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="w-[120px]">
                    <Select value={buyerType} onValueChange={setBuyerType}>
                      <SelectTrigger>
                        <SelectValue placeholder="Buyer Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Buyers</SelectItem>
                        <SelectItem value="self">Self</SelectItem>
                        <SelectItem value="system">System</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex gap-2">
                    <div className="w-[220px]">
                      <DatePickerWithRange
                        date={dateRange}
                        onSelect={(range) => setDateRange(range)}
                      />
                    </div>
                    <Button
                      type="button"
                      className="flex-shrink-0"
                      onClick={(e) => {
                        e.preventDefault();
                        fetchOrders();
                      }}
                    >
                      <Search className="h-4 w-4 mr-2" />
                      Search
                    </Button>
                  </div>
                </>
              )}
              {!hideFilters && (
                <ClearFiltersButton
                  onClick={() => {
                    if (!fixedUserId) setUserId("");
                    setOrderId("");
                    setType("");
                    setStatus("");
                    setBuyerType("");
                    setDateRange(undefined);
                    setPagination((prev) => ({ ...prev, page: 1 }));
                    // Trigger search with cleared filters
                    setTimeout(() => fetchOrders(), 0);
                  }}
                  disabled={
                    !fixedUserId &&
                    !userId &&
                    !orderId &&
                    !type &&
                    !status &&
                    !buyerType &&
                    !dateRange
                  }
                />
              )}
            </form>
          )}

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  {!hideUserColumn && <TableHead>User</TableHead>}
                  <TableHead>Buyer</TableHead>
                  <TableHead>
                    <div
                      className="flex items-center cursor-pointer"
                      onClick={() => handleSort("amount")}
                    >
                      Amount
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>
                    <div
                      className="flex items-center cursor-pointer"
                      onClick={() => handleSort("status")}
                    >
                      Status
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead>
                    <div
                      className="flex items-center cursor-pointer"
                      onClick={() => handleSort("createdAt")}
                    >
                      Created At
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell
                      colSpan={hideUserColumn ? 8 : 9}
                      className="text-center py-10"
                    >
                      <div className="flex justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : orders.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={hideUserColumn ? 8 : 9}
                      className="text-center py-10"
                    >
                      No orders found
                    </TableCell>
                  </TableRow>
                ) : (
                  orders.map((order) => (
                    <TableRow key={order.id}>
                      {!hideUserColumn && (
                        <TableCell>
                          {order.user ? (
                            <a
                              href={`/admin/users/${order.user.clerkId}`}
                              className="hover:underline"
                            >
                              <div>{order.user.email}</div>
                              <div className="text-xs  text-muted-foreground">
                                {order.user.clerkId}
                              </div>
                            </a>
                          ) : (
                            order.userId.substring(0, 8) + "..."
                          )}
                        </TableCell>
                      )}
                      <TableCell>
                        {order.buyerId === order.userId ? (
                          <span className="font-medium">Self</span>
                        ) : (
                          <span className="font-medium">System</span>
                        )}
                      </TableCell>
                      <TableCell>{order.amount}</TableCell>
                      <TableCell>
                        {order.extra?.price || "-"}
                        <span className="mx-1 text-muted-foreground">@</span>
                        {order.paymentMethod || "-"}</TableCell>
                      <TableCell>
                        <OrderStatusSelector
                          orderId={order.id}
                          initialStatus={order.status}
                          onStatusChange={(newStatus) => {
                            // Update the local state
                            const updatedOrders = orders.map((o) =>
                              o.id === order.id
                                ? { ...o, status: newStatus }
                                : o
                            );
                            setOrders(updatedOrders);
                          }}
                        />
                      </TableCell>
                      <TableCell>{formatDate(order.createdAt)}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="outline" size="sm" asChild>
                          <a href={`/admin/orders/${order.id}`}>View</a>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {!hidePagination && (
            <div className="flex flex-col sm:flex-row items-center justify-between mt-4 gap-4">
              <div className="text-sm text-muted-foreground">
                Showing {orders.length} of {pagination.totalCount} orders
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={!pagination.hasPrevPage}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <form onSubmit={handlePageInputSubmit} className="flex items-center space-x-2">
                  <div className="text-sm">
                    Page
                  </div>
                  <Input
                    type="number"
                    min={1}
                    max={pagination.totalPages}
                    value={pageInput}
                    onChange={handlePageInputChange}
                    onBlur={() => setPageInput(pagination.page.toString())}
                    onClick={(e) => e.currentTarget.select()}
                    className="w-[60px] h-8 text-center"
                    placeholder={pagination.page.toString()}
                  />
                  <div className="text-sm">
                    of {pagination.totalPages}
                  </div>
                </form>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={!pagination.hasNextPage}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Select
                  value={pagination.limit.toString()}
                  onValueChange={(value) =>
                    setPagination((prev) => ({
                      ...prev,
                      limit: parseInt(value),
                      page: 1,
                    }))
                  }
                >
                  <SelectTrigger className="w-[80px]">
                    <SelectValue placeholder="10" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
