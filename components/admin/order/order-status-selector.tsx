import { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { ALLOWED_STATUS_LIST, ORDER_STATUS_OPTIONS } from "@/constants/order";

interface OrderStatusSelectorProps {
  orderId: string;
  initialStatus: string;
  onStatusChange?: (newStatus: string) => void;
  showLabel?: boolean;
}

export function OrderStatusSelector({
  orderId,
  initialStatus,
  onStatusChange,
  showLabel = false,
}: OrderStatusSelectorProps) {
  const [status, setStatus] = useState(initialStatus);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // Check if the current status is allowed to be modified
  const isStatusModifiable = ALLOWED_STATUS_LIST.includes(initialStatus);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "SUCCESS":
        return <Badge className="bg-green-500">Completed</Badge>;
      case "PENDING":
        return <Badge className="bg-yellow-500">Pending</Badge>;
      case "FAILED":
        return <Badge className="bg-red-500">Failed</Badge>;
      case "REFUND":
        return <Badge className="bg-blue-500">Refunded</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    if (newStatus === status) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/admin/orders/${orderId}/status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update order status");
      }

      setStatus(newStatus);
      if (onStatusChange) {
        onStatusChange(newStatus);
      }

      toast({
        title: "Status Updated",
        description: `Order status has been updated to ${newStatus}`,
      });
    } catch (error) {
      console.error("Error updating order status:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update order status",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center gap-2">
      {showLabel && <span className="text-sm font-medium">Status:</span>}
      <Select value={status} onValueChange={handleStatusChange} disabled={loading || !isStatusModifiable}>
        <SelectTrigger className="w-auto border-0 p-0 h-auto shadow-none focus:ring-0 focus:ring-offset-0 [&>span]:p-0 [&>svg]:hidden">
          <SelectValue>
            <div className={`flex items-center gap-2 ${isStatusModifiable ? "cursor-pointer hover:opacity-80 transition-opacity" : ""}`}>
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                getStatusBadge(status)
              )}
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {ORDER_STATUS_OPTIONS.map((statusOption) => (
            <SelectItem key={statusOption} value={statusOption}>
              {getStatusBadge(statusOption)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
