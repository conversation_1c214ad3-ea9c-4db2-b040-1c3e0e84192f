'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Clock, RefreshCw } from "lucide-react"
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { HistoryItem } from '@/components/settings/history/history'
import { ModelStyleDisplay } from '@/components/global/model-style-display'
import { useDrawStore } from '@/store/draw'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

interface DrawPendingProps {
  pendingDraws?: HistoryItem[];
  loading?: boolean;
  onHistoryClick?: (history: HistoryItem) => void;
  onRefresh?: () => void;
}

// 比较历史记录是否有关键变化
const compareHistoryItems = (oldItems: HistoryItem[], newItems: HistoryItem[]): boolean => {
  // 如果长度不同，肯定有变化
  if (oldItems.length !== newItems.length) {
    return true;
  }

  // 创建映射以便快速查找
  const oldMap = new Map(oldItems.map(h => [h.id, h]));

  // 检查每个新历史记录
  for (const newItem of newItems) {
    const oldItem = oldMap.get(newItem.id);

    // 如果找不到对应的旧记录，说明有变化
    if (!oldItem) {
      return true;
    }

    // 检查关键字段
    if (
      oldItem.status !== newItem.status ||
      oldItem.drawStatus !== newItem.drawStatus ||
      JSON.stringify(oldItem.share) !== JSON.stringify(newItem.share)
    ) {
      return true;
    }
  }

  // 没有检测到变化
  return false;
};

export function DrawPending({ pendingDraws: propPendingDraws, loading: propLoading, onHistoryClick, onRefresh }: DrawPendingProps) {
  // 使用 store 中的数据和方法
  const { pendingHistories: storePendingDraws, historiesLoading: storeLoading, fetchCombinedHistories } = useDrawStore();

  // 使用状态来存储实际显示的数据，避免不必要的重新渲染
  const [displayedDraws, setDisplayedDraws] = useState<HistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 优先使用 props 中的数据，如果没有传入则使用 store 中的数据
  const pendingDraws = propPendingDraws || storePendingDraws || [];
  const loading = propLoading !== undefined ? propLoading : storeLoading;

  // 当外部数据变化时，检查是否需要更新显示的数据
  useEffect(() => {
    // 检查数据是否有变化
    if (compareHistoryItems(displayedDraws, pendingDraws)) {
      setDisplayedDraws(pendingDraws);
    }

    // 更新加载状态
    setIsLoading(loading);
  }, [pendingDraws, loading, displayedDraws]);

  // 如果没有传入 onRefresh，则使用 store 中的 fetchCombinedHistories
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    } else {
      fetchCombinedHistories();
    }
  };
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">开始生成</Badge>
      case 'PROCESSING':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">生成中</Badge>
      case 'SUCCESS':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">生成结束</Badge>
      case 'FAILED':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">生成中止</Badge>
      default:
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">未知状态</Badge>
    }
  }


  // 使用 useMemo 计算进行中的绘图数量，避免每次渲染都重新计算
  const pendingOrProcessingCount = useMemo(() => {
    if (!displayedDraws) {
      return 0;
    }
    return displayedDraws.filter(
      (history) =>
        history.drawStatus === "PENDING" || history.drawStatus === "PROCESSING"
    ).length;
  }, [displayedDraws]);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">
          <span>生成队列</span>
          <span className="ml-2 text-xs text-muted-foreground">
            (显示一天内的数据，当前进行中 {pendingOrProcessingCount} 个)
          </span>
        </h3>
        <Button
          variant="ghost"
          size="sm"
          className="text-blue-500 hover:text-blue-700"
          onClick={handleRefresh}
          disabled={isLoading}
        >
          <RefreshCw
            className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
          />
          刷新
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>开始时间</TableHead>
              <TableHead>模型</TableHead>
              <TableHead>风格</TableHead>
              <TableHead>状态</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {displayedDraws.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-10">
                  {isLoading ? '加载中...' : '暂无待处理的绘图请求'}
                </TableCell>
              </TableRow>
            ) : (
              displayedDraws.map((history) => (
                <TableRow
                  key={history.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => onHistoryClick && onHistoryClick(history)}
                >
                  <TableCell>
                    <span className="text-sm flex items-center text-blue-500 hover:text-blue-700">
                      <Clock className="h-3 w-3 mr-1" />
                      {formatDistanceToNow(new Date(history.createdAt), {
                        addSuffix: true,
                        locale: zhCN,
                      })}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-muted-foreground">
                      {history.extra?.model ? (
                        <ModelStyleDisplay
                          modelId={history.extra.model}
                          styleId={undefined}
                          showModelOnly={true}
                        />
                      ) : (
                        "-"
                      )}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-muted-foreground">
                      {history.extra?.style ? (
                        <ModelStyleDisplay
                          modelId={undefined}
                          styleId={history.extra.style}
                          showStyleOnly={true}
                        />
                      ) : (
                        "-"
                      )}
                    </span>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(history.drawStatus || "PENDING")}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
