'use client'

import React, { useEffect, useState, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { debounce } from 'lodash'
import { AlertCircle } from "lucide-react"

import { useUserStore } from '@/components/global/user-initializer'
import { Button } from "@/components/ui/button"

import { useDrawStore } from '@/store/draw'
import { useProfileStore } from '@/store/profile'
import { useToast } from "@/lib/hooks/use-toast"
import { getModelCreditCost } from '@/lib/ai/models'
import { drawModels } from '@/constants/draw/models'
import { DRAW_STYLES } from '@/constants/draw'


import { DrawPreview } from './draw-preview'
import { DrawOptions } from './draw-options'
import { DrawConfirmationDialog } from './draw-confirmation-dialog'

interface DrawGeneratorProps {
  initialData?: {
    model?: string;
    style?: keyof typeof DRAW_STYLES;
    prompt?: string;
  } | null;
}

export function DrawGenerator({ initialData }: DrawGeneratorProps) {
  const {
    style,
    model,
    prompt,
    images,
    setOutput,
    setIsLoading,
    isLoading,
    isModalOpen,
    setModel,
    setStyle,
    setPrompt,
  } = useDrawStore()

  // 使用useUserStore获取钱包信息和用户信息
  const { toast } = useToast()
  const router = useRouter()
  const searchParams = useSearchParams()
  const refreshUserInfo = useUserStore(state => state.refreshUserInfo)
  const credits = useUserStore(state => state.credits)

  // 仍然使用profile以保持兼容性
  const { profile } = useProfileStore()

  // 使用credits作为钱包余额
  const wallet = { permanentPoints: credits }

  // 添加一个 ref 来跟踪上次加载的 ID
  const lastLoadedIdRef = React.useRef<string | null>(null);

  const [activeTab, setActiveTab] = useState<string>("share")
  const [hasInitialized, setHasInitialized] = useState(false)
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false)

  // Initialize form with share data if available
  useEffect(() => {
    if (initialData && !hasInitialized) {
      if (initialData.model) setModel(initialData.model);
      if (initialData.style) setStyle(initialData.style)
      if (initialData.prompt) setPrompt(initialData.prompt)
      setHasInitialized(true)
    }
  }, [initialData, hasInitialized, setModel, setStyle, setPrompt])

  // Handle share data or history data from URL - detect URL changes
  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString())
    const shareId = params.get('shareId')
    const historyId = params.get('historyId')

    // 如果没有 shareId 或 historyId，不执行任何操作
    if (!shareId && !historyId) {
      return;
    }

    // 记录当前处理的 ID，用于防止重复加载
    const currentId = shareId || historyId;

    // 如果当前 ID 与上次加载的 ID 相同，则不重新加载
    if (currentId === lastLoadedIdRef.current) {
      return;
    }

    // 更新上次加载的 ID
    lastLoadedIdRef.current = currentId;

    if (shareId) {
      const fetchShareData = async () => {
        try {
          const response = await fetch(`/api/public/shares/${shareId}`)
          if (!response.ok) {
            throw new Error('Failed to fetch share data')
          }
          const data = await response.json()
          if (data.data) {
            const share = data.data
            // 只有高级用户才能使用分享的模型
            if (profile?.isPaid) {
              setModel(share.model || '')
            }
            setStyle(share.styleId || '')
            setPrompt(share.customPrompt || '')
            setHasInitialized(true)
          }
        } catch (error) {
          console.error('Error fetching share data:', error)
          toast({
            variant: "destructive",
            title: "获取分享数据失败",
            description: "无法加载分享的设置，请重试",
          })
        }
      }

      fetchShareData()
    } else if (historyId) {
      const fetchHistoryData = async () => {
        try {
          const response = await fetch(`/api/history/${historyId}`)

          // 处理 401 或 404 错误，这通常意味着用户尝试访问不属于自己的历史记录
          if (response.status === 401 || response.status === 404) {
            toast({
              variant: "destructive",
              title: "访问被拒绝",
              description: "只能复刻自己的历史记录",
            })
            // 清除 URL 参数并返回到基本绘图页面
            router.replace('/draw')
            return
          }

          if (!response.ok) {
            throw new Error('Failed to fetch history data')
          }

          const history = await response.json()

          // 设置表单数据
          setModel(history.extra?.model || '')
          setStyle(history.extra?.style || '')
          setPrompt(history.prompt || '')
          setHasInitialized(true)
        } catch (error) {
          console.error('Error fetching history data:', error)
          toast({
            variant: "destructive",
            title: "获取历史记录失败",
            description: "无法加载历史记录的设置，请重试",
          })
          // 出错时也清除 URL 参数
          router.replace('/draw')
        }
      }

      fetchHistoryData()
    }
  // 添加 searchParams 到依赖数组，以便在 URL 变化时重新执行
  }, [searchParams, profile, router, setModel, setStyle, setPrompt, toast])

  // 只在组件挂载时获取一次数据，避免重复请求
  useEffect(() => {
    // 只调用一个API，避免重复请求
    refreshUserInfo()

    // 返回清理函数，在组件卸载时执行
    return () => {
      // 清理 DrawStore 中的订阅
      useDrawStore.getState().cleanup();
    };
    // 使用空依赖数组，确保只在组件挂载时运行一次
  }, [])

  const creditCost = getModelCreditCost(model)
  const hasEnoughCredits = wallet && wallet.permanentPoints >= creditCost

  // Get the selected model's maxImages
  const selectedModel = drawModels.find(m => m.id === model)
  const maxImages = selectedModel?.maxImages || 0

  // 验证所有必填参数
  const imagesValid = maxImages > 0 ? images.length : 0
  const isValid = style && (imagesValid > 0 || prompt) && model && imagesValid <= maxImages

  // 使用防抖机制，防止短时间内多次点击
  const debouncedGenerate = useCallback(
    debounce(async () => {
      try {
        setActiveTab("generate")
        // 验证必填参数
        if (!style) {
          toast({
            variant: "destructive",
            title: "参数错误",
            description: "请选择生成风格",
          })
          return
        }

        // 验证参考图片或提示词必须有一个
        if (images.length === 0 && !prompt) {
          toast({
            variant: "destructive",
            title: "参数错误",
            description: maxImages > 0 ? '请上传参考图片或输入提示词，至少填写一项' : "请输入提示词",
          })
          return
        }

        // 验证图片数量
        if (images.length > maxImages && maxImages > 0) {
          toast({
            variant: "destructive",
            title: "参数错误",
            description: `当前模型最多支持${maxImages}张参考图片`,
          })
          return
        }

        // 清除之前的结果
        setOutput("")
        setIsLoading(true)

        const formData = new FormData()
        formData.append('style', style)
        formData.append('model', model)

        // 可选参数
        maxImages > 0 && images.forEach((image, index) => {
          formData.append(`image${index}`, image)
        })
        if (prompt) {
          formData.append('prompt', prompt)
        }

        const response = await fetch('/api/draw', {
          method: 'POST',
          body: formData,
        })

        // 如果是 JSON 响应，说明是异步模式
        const contentType = response.headers.get('content-type')
        if (contentType && contentType.includes('application/json')) {
          await response.json()

          // 显示确认对话框
          setIsConfirmDialogOpen(true)

          // 切换到生成页面
          setActiveTab("generate")
          setIsLoading(false)
          return
        }

        if (!response.ok) {
          const error = await response.text()
          // 检查是否是付费模型相关的错误
          if (response.status === 403 && error.includes("paid subscription")) {
            toast({
              variant: "destructive",
              title: "需要付费",
              description: "该模型需要付费才能使用，请先充值",
              action: (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push('/pricing')}
                  className="bg-white hover:bg-white/90"
                >
                  立即充值
                </Button>
              ),
            })
          } else {
            toast({
              variant: "destructive",
              title: "生成失败",
              description: error || "生成图片时出现错误，请重试",
            })
          }
          return
        }

        if (!response.body) {
          toast({
            variant: "destructive",
            title: "生成失败",
            description: "服务器返回数据为空",
          })
          return
        }

        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let accumulatedContent = ''

        while (true) {
          const { value, done } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n')

          for (const line of lines) {
            if (!line) continue

            try {
              if (line.startsWith('0:')) {
                // Content chunk
                const content = line.slice(2)
                if (content.startsWith('"') && content.endsWith('"')) {
                  // Handle quoted string
                  accumulatedContent += JSON.parse(content)
                } else {
                  // Check for error response
                  try {
                    const jsonContent = JSON.parse(content)
                    if (jsonContent.finishReason === 'unknown') {
                      // Check if we already have an image URL before marking as failure
                      const hasImageUrl = accumulatedContent.match(/https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)/i)
                      if (!hasImageUrl) {
                        setOutput("")
                        toast({
                          variant: "destructive",
                          title: "生成失败",
                          description: "图片生成失败，请重试",
                        })
                        return
                      }
                    }
                  } catch {
                    // Not JSON content, proceed as normal
                    accumulatedContent += content
                  }
                }
                setOutput(accumulatedContent)

                // 检查是否有图片URL生成成功
                // 不再需要单独处理图片URL生成，在finally中统一刷新
              }
            } catch (e) {
              console.error('Error parsing line:', line, e)
              toast({
                variant: "destructive",
                title: "解析错误",
                description: "解析服务器返回数据时出错",
              })
            }
          }
        }
    } catch (error) {
      console.error('Error generating:', error)
      toast({
        variant: "destructive",
        title: "生成失败",
        description: "生成图片时出现错误，请重试",
      })
    } finally {
      setIsLoading(false)
      // 不再在这里自动刷新，而是在结果对话框关闭时刷新
    }
    }, 500, { leading: true, trailing: false }), // 500ms 防抖，只执行第一次点击
    [style, model, prompt, images, setOutput, setIsLoading, toast, router, maxImages]
  )

  // 处理生成按钮点击
  const handleGenerate = () => {
    debouncedGenerate()
  }

  // 不再需要单独的图片URL生成处理函数
  // 避免重复调用autoRefreshUserInfo

  // 手动刷新积分
  const handleManualRefresh = async () => {
    try {
      await refreshUserInfo();
      toast({
        title: "刷新成功",
        description: "积分已更新",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "刷新失败",
        description: "无法获取最新积分信息",
      });
    }
  };

  return (
    <div className="mx-auto md:p-4">
      <div className="flex flex-col md:flex-row gap-4">
        <div className="w-full md:w-5/12 space-y-4">
          <DrawOptions />
          <div className="space-y-2">
            {hasEnoughCredits ? (
              <Button
                onClick={handleGenerate}
                className="w-full"
                disabled={
                  !isValid || isLoading || isModalOpen || isConfirmDialogOpen
                }
              >
                {isLoading ? "生成中..." : `生成 (消耗 ${creditCost} 积分)`}
              </Button>
            ) : (
              <Button
                onClick={() => router.push("/pricing")}
                className="w-full"
                variant="destructive"
              >
                {`积分不足 (需要 ${creditCost} 积分)`}
              </Button>
            )}
            {!isValid && !isLoading && (
              <div className="flex items-center justify-center gap-2 text-red-500">
                <AlertCircle className="h-4 w-4" />
                <span className="text-xs">
                  {!style
                    ? "请选择风格"
                    : images.length > maxImages && maxImages > 0
                    ? `图片数量不能超过${maxImages}张`
                    : maxImages > 0
                    ? "请上传参考图片或输入提示词"
                    : "请填写提示词"}
                </span>
              </div>
            )}
          </div>
          <div className="text-center text-sm text-muted-foreground flex items-center justify-center gap-2">
            <button
              onClick={handleManualRefresh}
              className="text-muted-foreground hover:text-primary transition-colors"
            >
              剩余积分 {credits}，点击刷新
            </button>
            <Button
              variant="link"
              className="px-1 h-auto text-sm font-medium bg-gradient-to-r from-blue-500 to-pink-500 bg-clip-text text-transparent hover:from-pink-500 hover:to-blue-500 transition-all duration-300"
              onClick={() => router.push("/pricing")}
            >
              立即充值 🚀
            </Button>
          </div>
        </div>

        <div className="w-full md:w-7/12 md:h-auto">
          <DrawPreview activeTab={activeTab} onTabChange={setActiveTab} />
        </div>
      </div>

      {/* 确认对话框 */}
      <DrawConfirmationDialog
        isOpen={isConfirmDialogOpen}
        onClose={() => setIsConfirmDialogOpen(false)}
      />
    </div>
  );
}
