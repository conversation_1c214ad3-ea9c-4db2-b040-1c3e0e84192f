import { useEffect, useState } from "react";
import { useDrawStore } from "@/store/draw";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronRight, RefreshCw } from "lucide-react";
import { HistoryItemDetailsModal } from "@/components/settings/history/history-item-details-modal";
import { ShareItemDetailsModal } from "@/components/shared/share-item-details-modal";
import { Share as ShareType } from "@/types/share";
import { HistoryItem as HistoryItemType } from "@/components/settings/history/history";

// Using the imported type instead
type Share = ShareType;

// Using the imported type instead
type HistoryItem = HistoryItemType;

interface DrawPlaceholderProps {
  latestGeneratedImage?: string;
}

export function DrawPlaceholder({ latestGeneratedImage }: DrawPlaceholderProps = {}) {
  const { style } = useDrawStore();
  const [recentHistories, setRecentHistories] = useState<HistoryItem[]>([]);
  const [styleShares, setStyleShares] = useState<Share[]>([]);
  const [otherStyleShares, setOtherStyleShares] = useState<Share[]>([]);
  const [loading, setLoading] = useState(false);
  const [historiesLoading, setHistoriesLoading] = useState(false);

  // Modal states
  const [selectedHistory, setSelectedHistory] = useState<HistoryItem | null>(null);
  const [selectedShare, setSelectedShare] = useState<Share | null>(null);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);

  // Function to fetch recent histories
  const fetchRecentHistories = async () => {
    try {
      setHistoriesLoading(true);
      const response = await fetch('/api/history?status=true&limit=5&includeShare=true');
      if (response.ok) {
        const data = await response.json();
        console.log('Recent histories response:', data);
        setRecentHistories(data.histories || []);
      }
    } catch (error) {
      console.error("Error fetching recent histories:", error);
    } finally {
      setHistoriesLoading(false);
    }
  };

  // Fetch recent histories on component mount
  useEffect(() => {
    fetchRecentHistories();
  }, []);

  // Fetch recommended shares
  const fetchRecommendedShares = async (isRandom = false) => {
    if (!style) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/recommend?styleId=${style}&styleLimit=8&totalLimit=8&isRandom=${isRandom}`);
      if (response.ok) {
        const data = await response.json();
        // Split the data into style shares and other shares
        const styleSharesData = data.filter((share: Share) => share.styleId === style);
        const otherSharesData = data.filter((share: Share) => share.styleId !== style);

        setStyleShares(styleSharesData);
        setOtherStyleShares(otherSharesData);
      }
    } catch (error) {
      console.error("Error fetching recommended shares:", error);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch of recommended shares
  useEffect(() => {
    fetchRecommendedShares(false);
  }, [style]);

  // Get recent successful images
  let recentSuccessfulImages = recentHistories.filter(history => history.status).slice(0, 4);

  // If we have a latest generated image and it's not already in the list, add it at the beginning
  if (latestGeneratedImage) {
    // Check if the latest image is already in the list
    const imageExists = recentSuccessfulImages.some(history => history.resultUrl === latestGeneratedImage);

    if (!imageExists) {
      // Create a temporary history item for the latest image
      const latestImage: HistoryItem = {
        id: 'latest-' + Date.now(),
        userId: 'current', // 添加临时 userId
        status: true,
        prompt: '',
        pointsUsed: 0,
        createdAt: new Date().toISOString(),
        resultUrl: latestGeneratedImage,
        description: null,
        parameters: { model: 'unknown' }
      };

      // Add it to the beginning of the list and limit to 4 items
      recentSuccessfulImages = [latestImage, ...recentSuccessfulImages].slice(0, 4);
    }
  }

  // Combine and deduplicate shares
  const combinedShares = [...styleShares];

  // Add other style shares if we need more to reach 8
  if (styleShares.length < 8) {
    // Create a Set of existing share IDs for quick lookup
    const existingShareIds = new Set(styleShares.map(share => share.id));

    // Add shares from other styles that aren't already included
    otherStyleShares.forEach(share => {
      if (!existingShareIds.has(share.id) && combinedShares.length < 8) {
        combinedShares.push(share);
        existingShareIds.add(share.id);
      }
    });
  }

  return (
    <div className="flex flex-col space-y-8 w-full">
      {/* Recent successful images section - FIRST */}
      <div className="w-full">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-lg font-medium">最近生成</h3>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-500 hover:text-blue-700"
              onClick={fetchRecentHistories}
              disabled={historiesLoading}
            >
              <RefreshCw className="h-4 w-4 mr-1" /> 刷新
            </Button>
            <Link href="/settings/history" passHref>
              <Button variant="ghost" size="sm" className="text-blue-500 hover:text-blue-700">
                查看更多 <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </Link>
          </div>
        </div>
        <div className="grid grid-cols-4 gap-4">
          {historiesLoading ? (
            <>
              {Array.from({ length: 4 }).map((_, index) => (
                <Card key={`history-skeleton-${index}`} className="overflow-hidden">
                  <CardContent className="p-0">
                    <Skeleton className="w-full aspect-square" />
                  </CardContent>
                </Card>
              ))}
            </>
          ) : recentSuccessfulImages.length === 0 ? (
            <div className="col-span-4 text-center text-muted-foreground py-8">
              暂无生成成功的图片
            </div>
          ) : (
            <>
              {recentSuccessfulImages.map((history) => (
                <Card
                  key={history.id}
                  className="overflow-hidden cursor-pointer"
                  onClick={() => {
                    setSelectedHistory(history);
                    setIsHistoryModalOpen(true);
                  }}
                >
                  <CardContent className="p-0">
                    <img
                      src={history.resultUrl || '/images/placeholder.png'}
                      alt="Generated image"
                      className="w-full aspect-square object-cover"
                    />
                  </CardContent>
                </Card>
              ))}
              {recentSuccessfulImages.length < 4 && recentSuccessfulImages.length > 0 && (
                <Link href="/settings/history" passHref>
                  <Card className="flex items-center justify-center cursor-pointer hover:bg-slate-50 transition-colors">
                    <CardContent className="flex items-center justify-center h-full p-4">
                      <span className="text-blue-500">查看更多</span>
                      <ChevronRight className="h-4 w-4 ml-1 text-blue-500" />
                    </CardContent>
                  </Card>
                </Link>
              )}
            </>
          )}
        </div>
      </div>

      {/* Latest updates section - SECOND */}
      <div className="w-full">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-lg font-medium">相关分享</h3>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-500 hover:text-blue-700"
              onClick={() => {
                fetchRecommendedShares(true);
              }}
            >
              <RefreshCw className="h-4 w-4 mr-1" /> 换一批
            </Button>
            <Link href="/explore" passHref>
              <Button variant="ghost" size="sm" className="text-blue-500 hover:text-blue-700">
                查看更多 <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </Link>
          </div>
        </div>
        <div className="grid grid-cols-4 gap-4">
          {loading ? (
            <>
              {Array.from({ length: 8 }).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <CardContent className="p-0">
                    <Skeleton className="w-full aspect-square" />
                  </CardContent>
                </Card>
              ))}
            </>
          ) : combinedShares.length === 0 ? (
            <div className="col-span-4 text-center text-muted-foreground py-8">
              暂无最新动态
            </div>
          ) : (
            <>
              {combinedShares.slice(0, 8).map((share) => (
                <Card
                  key={share.id}
                  className="overflow-hidden cursor-pointer"
                  onClick={() => {
                    setSelectedShare(share);
                    setIsShareModalOpen(true);
                  }}
                >
                  <CardContent className="p-0">
                    <img
                      src={share.imageUrl}
                      alt="Shared image"
                      className="w-full aspect-square object-cover"
                    />
                  </CardContent>
                </Card>
              ))}
              {combinedShares.length < 8 && combinedShares.length > 0 && (
                <Link href="/explore" passHref>
                  <Card className="flex items-center justify-center cursor-pointer hover:bg-slate-50 transition-colors">
                    <CardContent className="flex items-center justify-center h-full p-4">
                      <span className="text-blue-500">查看更多</span>
                      <ChevronRight className="h-4 w-4 ml-1 text-blue-500" />
                    </CardContent>
                  </Card>
                </Link>
              )}
            </>
          )}
        </div>
      </div>

      {/* Tips section - hidden */}
      {/* <div className="flex flex-col items-center justify-center">
        <div className="max-w-md space-y-2 px-4">
          <ul>
            {DRAW_TIPS.map((tip, index) => (
              <li key={index} className="flex items-center p-2 rounded-md transition-colors hover:bg-slate-100 dark:hover:bg-slate-800 cursor-pointer">
                <span className="mr-2">{tip.icon}</span>
                <span className={`text-sm ${tip.iconColor}`}>{tip.text}</span>
              </li>
            ))}
          </ul>
        </div>
      </div> */}

      {/* Modals */}
      <HistoryItemDetailsModal
        isOpen={isHistoryModalOpen}
        onOpenChange={setIsHistoryModalOpen}
        item={selectedHistory}
        onItemUpdate={(updatedItem) => {
          // Update the item in the recentHistories array
          setRecentHistories(recentHistories.map(h => h.id === updatedItem.id ? updatedItem : h));
          // Update the selected history
          setSelectedHistory(updatedItem);
        }}
      />

      <ShareItemDetailsModal
        isOpen={isShareModalOpen}
        onOpenChange={setIsShareModalOpen}
        item={selectedShare}
      />
    </div>
  );
}
