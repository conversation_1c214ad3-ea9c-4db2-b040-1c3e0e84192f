import React, { useEffect, useState, useRef, useCallback } from "react";
import Image from 'next/image'
import Link from "next/link";

import { compressImages, getTotalSizeMB } from "@/lib/utils/image-compressor";
import { useDrawStore } from "@/store/draw";
import { useProfileStore } from "@/store/profile";
import { useCopy } from "@/lib/hooks/use-copy";
import { useToast } from "@/lib/hooks/use-toast";

import {
  X,
  Lock,
  AlertCircle,
  Plus,
  Loader2,
  Square,
  RectangleVertical,
  RectangleHorizontal,
  Sparkles,
  HelpCircle,
  Copy,
  Trash2,
  Wand2,
  ClipboardPaste,
} from "lucide-react";
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { drawModels } from '@/constants/draw/models'
import { Label } from "@/components/ui/label"
import { useUserStore } from '@/components/global/user-initializer'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

import { DrawStyleSelector } from './draw-style-selector'
import { ModelInfoModal } from './model-info-modal'
import { ExploreModal } from './explore-modal'
import { PromptAssistModal } from './prompt-assist-modal'

export function DrawOptions() {
  const {
    style,
    model,
    prompt,
    images,
    previewUrls,
    setStyle,
    setModel,
    setPrompt,
    setImages,
    setPreviewUrls,
  } = useDrawStore()

  const { profile } = useProfileStore()
  const { currentUser } = useUserStore()
  const { copyToClipboard } = useCopy()
  const { toast } = useToast()

  // 使用currentUser或profile来确定付费状态
  const isPaid = (currentUser as any)?.isPaid || profile?.isPaid || false

  const [sizeWarning, setSizeWarning] = useState<string>('')
  const [imageTotalSize, setImageTotalSize] = useState<number>(0)
  const [isCompressing, setIsCompressing] = useState<boolean>(false)
  const [selectedAspectRatio, setSelectedAspectRatio] = useState<string | null>(null)
  const [isExploreModalOpen, setIsExploreModalOpen] = useState<boolean>(false)
  const [isModelInfoModalOpen, setIsModelInfoModalOpen] = useState<boolean>(false)
  const [isPromptAssistModalOpen, setIsPromptAssistModalOpen] = useState<boolean>(false)
  const [isDragging, setIsDragging] = useState<boolean>(false)

  const dropZoneRef = useRef<HTMLDivElement>(null)

  // Get the selected model's maxImages
  const selectedModel = drawModels.find(m => m.id === model)
  const maxImages = selectedModel?.maxImages || 0

  // 检查提示词中是否已经包含尺寸信息，并设置相应的选中状态
  useEffect(() => {
    const match = prompt.match(/<image size: (\d+)x(\d+)>$/i);
    if (match) {
      const width = parseInt(match[1])
      const height = parseInt(match[2])

      if (width === 1024 && height === 1024) {
        setSelectedAspectRatio('square')
      } else if (width === 1024 && height === 1536) {
        setSelectedAspectRatio("portrait");
      } else if (width === 1536 && height === 1024) {
        setSelectedAspectRatio("landscape");
      } else {
        setSelectedAspectRatio(null);
      }
    } else {
      setSelectedAspectRatio(null)
    }
  }, [prompt])

  // Function to check total size
  const checkTotalSize = (newImages: File[], newPrompt: string) => {
    const MAX_SIZE = 4 * 1024 * 1024 // 4MB in bytes
    const imageSize = newImages.reduce((total, file) => total + file.size, 0)
    const promptSize = new Blob([newPrompt]).size
    const totalSize = imageSize + promptSize

    // Update the total size in MB with one decimal place
    setImageTotalSize(Number((totalSize / (1024 * 1024)).toFixed(1)))

    if (totalSize > MAX_SIZE) {
      setSizeWarning('图片和提示词总大小不能超过4MB')
    } else {
      setSizeWarning('')
    }
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    // 计算可以添加的最大图片数量
    const remainingSlots = maxImages - images.length

    // 如果没有剩余空间，直接返回
    if (remainingSlots <= 0) return

    // 限制添加的图片数量不超过剩余空间
    const filesToAdd = Array.from(files).slice(0, remainingSlots)

    // 添加新图片到现有图片数组
    const newImages = [...images, ...filesToAdd]
    setImages(newImages)
    checkTotalSize(newImages, prompt)

    // 为每个新图片创建预览URL
    const newUrls = filesToAdd.map(file => URL.createObjectURL(file))
    const newPreviewUrls = [...previewUrls, ...newUrls]
    setPreviewUrls(newPreviewUrls)
  }

  const handleRemoveImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index)
    setImages(newImages)
    checkTotalSize(newImages, prompt)

    if (previewUrls[index]) {
      URL.revokeObjectURL(previewUrls[index])
    }
    const newPreviewUrls = previewUrls.filter((_, i) => i !== index)
    setPreviewUrls(newPreviewUrls)
  }

  const handlePromptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newPrompt = e.target.value
    setPrompt(newPrompt)
    checkTotalSize(images, newPrompt)
  }

  // 处理从剪贴板粘贴文本到提示词
  const handlePasteText = async () => {
    try {
      const clipboardText = await navigator.clipboard.readText();
      if (clipboardText) {
        setPrompt(clipboardText);
        checkTotalSize(images, clipboardText);
        toast({
          title: "成功",
          description: "已从剪贴板粘贴文本到提示词",
          className: "bg-green-500 text-white border-green-600",
        });
      }
    } catch (error) {
      console.error("粘贴文本失败:", error);
      toast({
        variant: "destructive",
        title: "错误",
        description: "粘贴文本失败，请重试",
      });
    }
  }

  // 处理尺寸选择
  const handleAspectRatioSelect = (ratio: string) => {
    // 如果已经选中了这个比例，则取消选择
    if (selectedAspectRatio === ratio) {
      setSelectedAspectRatio(null)

      // 移除提示词中的图片比例标记
      const newPrompt = prompt.replace(/<image size: \d+x\d+>$/i, '')
      setPrompt(newPrompt)
      checkTotalSize(images, newPrompt)
    } else {
      setSelectedAspectRatio(ratio)

      // 先移除可能存在的旧图片比例标记
      let newPrompt = prompt.replace(/<image size: \d+x\d+>$/i, '')

      // 添加新的图片比例标记
      const sizeText =
        {
          square: "1024x1024",
          portrait: "1024x1536",
          landscape: "1536x1024",
        }[ratio] || "1024x1024";

      newPrompt = `${newPrompt}<image size: ${sizeText}>`
      setPrompt(newPrompt)
      checkTotalSize(images, newPrompt)
    }
  }

  // 处理从剪贴板粘贴图片
  const handlePaste = useCallback(async (event: ClipboardEvent) => {
    if (!event.clipboardData || images.length >= maxImages) return;

    const items = event.clipboardData.items;
    const imageItems = Array.from(items).filter(item => item.type.startsWith('image/'));

    if (imageItems.length === 0) return;

    // 计算可以添加的最大图片数量
    const remainingSlots = maxImages - images.length;

    // 如果没有剩余空间，直接返回
    if (remainingSlots <= 0) {
      toast({
        title: "提示",
        description: `已达到最大图片数量限制 (${maxImages} 张)`,
        variant: "default",
      });
      return;
    }

    // 限制添加的图片数量不超过剩余空间
    const filesToProcess = imageItems.slice(0, remainingSlots);

    const newFiles: File[] = [];

    for (const item of filesToProcess) {
      const file = item.getAsFile();
      if (file) {
        // 创建一个新的文件对象，确保有正确的文件名
        const timestamp = new Date().getTime();
        const newFile = new File([file], `pasted-image-${timestamp}.${file.type.split('/')[1] || 'png'}`, {
          type: file.type,
        });
        newFiles.push(newFile);
      }
    }

    if (newFiles.length > 0) {
      // 添加新图片到现有图片数组
      const newImages = [...images, ...newFiles];
      setImages(newImages);
      checkTotalSize(newImages, prompt);

      // 为每个新图片创建预览URL
      const newUrls = newFiles.map(file => URL.createObjectURL(file));
      const newPreviewUrls = [...previewUrls, ...newUrls];
      setPreviewUrls(newPreviewUrls);

      toast({
        title: "成功",
        description: `已添加 ${newFiles.length} 张图片`,
        className: "bg-green-500 text-white border-green-600",
      });
    }
  }, [images, previewUrls, maxImages, prompt, setImages, setPreviewUrls, toast]);

  // 处理拖放图片
  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isDragging) {
      setIsDragging(true);
    }
  }, [isDragging]);

  const handleDragEnter = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (images.length >= maxImages) {
      toast({
        title: "提示",
        description: `已达到最大图片数量限制 (${maxImages} 张)`,
        variant: "default",
      });
      return;
    }

    const files = e.dataTransfer.files;
    if (!files || files.length === 0) return;

    // 过滤出图片文件
    const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
    if (imageFiles.length === 0) return;

    // 计算可以添加的最大图片数量
    const remainingSlots = maxImages - images.length;

    // 如果没有剩余空间，直接返回
    if (remainingSlots <= 0) return;

    // 限制添加的图片数量不超过剩余空间
    const filesToAdd = imageFiles.slice(0, remainingSlots);

    // 添加新图片到现有图片数组
    const newImages = [...images, ...filesToAdd];
    setImages(newImages);
    checkTotalSize(newImages, prompt);

    // 为每个新图片创建预览URL
    const newUrls = filesToAdd.map(file => URL.createObjectURL(file));
    const newPreviewUrls = [...previewUrls, ...newUrls];
    setPreviewUrls(newPreviewUrls);

    toast({
      title: "成功",
      description: `已添加 ${filesToAdd.length} 张图片`,
      className: "bg-green-500 text-white border-green-600",
    });
  }, [images, previewUrls, maxImages, prompt, isDragging, setImages, setPreviewUrls, toast]);

  // 添加和移除事件监听器
  useEffect(() => {
    // 只有当maxImages > 0时才添加粘贴事件监听器
    if (maxImages > 0) {
      document.addEventListener('paste', handlePaste);

      return () => {
        document.removeEventListener('paste', handlePaste);
      };
    }
  }, [handlePaste, maxImages]);

  // 处理图片压缩
  const handleCompressImages = async () => {
    if (images.length === 0 || isCompressing) return;

    try {
      setIsCompressing(true);

      // 压缩图片，使用 80% 质量
      const compressedImages = await compressImages(images, 80);

      // 释放旧的预览 URL
      previewUrls.forEach(url => URL.revokeObjectURL(url));

      // 创建新的预览 URL
      const newPreviewUrls = compressedImages.map(file => URL.createObjectURL(file));

      // 更新状态
      setImages(compressedImages);
      setPreviewUrls(newPreviewUrls);

      // 重新检查大小
      checkTotalSize(compressedImages, prompt);

      // 显示压缩结果
      const oldSize = getTotalSizeMB(images);
      const newSize = getTotalSizeMB(compressedImages);
      const savedPercent = Math.round((1 - newSize / oldSize) * 100);

      console.log(`压缩完成: ${oldSize.toFixed(2)}MB -> ${newSize.toFixed(2)}MB (节省 ${savedPercent}%)`);

      toast({
        title: "压缩完成",
        description: `${oldSize.toFixed(2)}MB → ${newSize.toFixed(2)}MB (节省 ${savedPercent}%)`,
        className: "bg-green-500 text-white border-green-600",
      });
    } catch (error) {
      console.error('图片压缩失败:', error);
      toast({
        variant: "destructive",
        title: "错误",
        description: "图片压缩失败，请重试",
      });
    } finally {
      setIsCompressing(false);
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4">
        <div className="w-full space-y-2">
          <div className="flex items-center gap-1.5">
            <Label htmlFor="model-select">选择模型</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-5 w-5 p-0"
                    onClick={(e) => {
                      e.preventDefault();
                      setIsModelInfoModalOpen(true);
                    }}
                  >
                    <HelpCircle className="h-4 w-4 text-muted-foreground" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>查看模型说明</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Select value={model} onValueChange={setModel}>
            <SelectTrigger id="model-select">
              <SelectValue placeholder="选择模型" />
            </SelectTrigger>
            <SelectContent>
              {drawModels
                .filter((model) => !model.disabled)
                .map((model) => (
                  <SelectItem
                    key={model.id}
                    value={model.id}
                    disabled={model.disabled || (model.paidOnly && !isPaid)}
                  >
                    <div className="flex w-full items-center justify-between">
                      <div>
                        {model.name}
                        <span className="ml-2 text-xs text-gray-500">
                          {model.description}
                        </span>
                        <small className="ml-2 text-xs text-blue-500">
                          {model.points} 积分
                        </small>
                      </div>
                      {model.paidOnly && !isPaid && (
                        <Link
                          href="/pricing"
                          className="flex items-center text-xs text-amber-500 hover:text-amber-600 whitespace-nowrap ml-2"
                        >
                          <Lock className="w-3 h-3 mr-1" />
                          需要付费
                        </Link>
                      )}
                    </div>
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>
        <div className="w-full">
          <div className="flex items-center justify-between">
            <Label htmlFor="style-select">选择生成风格</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="link"
                    size="sm"
                    className="p-0 bg-gradient-to-r from-purple-600 via-fuchsia-500 via-blue-500 via-cyan-400 to-purple-600 animate-magical-text
                             transition-all duration-300 hover:scale-105 font-medium"
                    onClick={() => setIsExploreModalOpen(true)}
                  >
                    <Sparkles className="h-4 w-4 mr-1 text-purple-500" />
                    创意探索
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>浏览其他用户分享的创作，获取灵感或直接复刻</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <DrawStyleSelector selectedStyle={style} onSelectStyle={setStyle} />
          <ExploreModal
            open={isExploreModalOpen}
            onOpenChange={setIsExploreModalOpen}
          />
        </div>
      </div>
      <div className="space-y-2">
        <div className="flex flex-col gap-4">
          {maxImages > 0 && (
            <div className="w-full">
              <Label>
                参考图片 (最多 {maxImages} 张，可选{" "}
                {maxImages - images.length > 0 ? maxImages - images.length : 0}{" "}
                张，体积 4MB，已使用 {imageTotalSize}
                MB)
              </Label>
              <div
                ref={dropZoneRef}
                className={`grid grid-cols-5 gap-2 w-full mt-2 justify-start ${
                  isDragging
                    ? "bg-purple-50 border-2 border-dashed border-purple-300 rounded-lg p-2"
                    : ""
                }`}
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                {images.map((_, index) => (
                  <div
                    key={index}
                    className="relative w-16 h-16 sm:w-20 sm:h-20"
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute -top-2 -right-2 z-10 h-5 w-5 sm:h-6 sm:w-6 rounded-full bg-background border"
                      onClick={() => handleRemoveImage(index)}
                    >
                      <X className="h-3 w-3 sm:h-4 sm:w-4" />
                    </Button>
                    <div className="w-full h-full relative rounded-lg border border-gray-200 overflow-hidden">
                      <Image
                        src={previewUrls[index]}
                        alt={`Preview ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                    </div>
                  </div>
                ))}
                {images.length < maxImages && (
                  <div className="draw-image-upload-button w-16 h-16 sm:w-20 sm:h-20">
                    <Button
                      variant="outline"
                      className="w-full h-full flex flex-col items-center justify-center gap-0.5 sm:gap-1 p-0"
                      onClick={() =>
                        document.getElementById("file-upload")?.click()
                      }
                    >
                      <Plus className="w-3 h-3 sm:w-6 sm:h-6" />
                      <span className="text-[10px] sm:text-xs">添加图片</span>
                      <span className="text-[8px] text-gray-500">
                        支持拖放和粘贴
                      </span>
                    </Button>
                    <input
                      id="file-upload"
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleImageChange}
                      className="hidden"
                    />
                  </div>
                )}
              </div>
              {sizeWarning && (
                <div className="flex items-center gap-2 text-red-500 text-sm mt-2">
                  <AlertCircle className="w-4 h-4" />
                  <span>{sizeWarning}</span>
                  {!isCompressing && (
                    <Button
                      size="sm"
                      className="ml-2 h-6 px-3 py-0 text-xs font-medium text-white shadow-md
                             bg-gradient-to-r from-purple-600 via-amber-500 to-pink-600 hover:from-purple-700 hover:via-amber-600 hover:to-pink-700
                             border border-purple-300/30 animate-gradient-normal bg-[length:200%_200%]
                             transition-all duration-300 hover:shadow-lg hover:scale-105"
                      onClick={handleCompressImages}
                    >
                      立即压缩
                    </Button>
                  )}
                  {isCompressing && (
                    <div
                      className="ml-2 inline-flex items-center h-6 px-3 py-0 text-xs font-medium text-white shadow-md
                              bg-gradient-to-r from-purple-600 via-amber-500 to-pink-600
                              border border-purple-300/30 animate-gradient-normal bg-[length:200%_200%]
                              rounded-md"
                    >
                      <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                      正在压缩...
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          <div className="w-full">
            <div className="flex items-center justify-between">
              <Label htmlFor="prompt">提示词</Label>
              <div className="flex space-x-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className={`h-7 w-7 ${
                          selectedAspectRatio === "square"
                            ? "border-2 border-purple-500 text-purple-600 bg-gradient-to-r from-purple-50 to-amber-50 shadow-md"
                            : ""
                        }`}
                        onClick={() => handleAspectRatioSelect("square")}
                      >
                        <Square className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>正方形</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className={`h-7 w-7 ${
                          selectedAspectRatio === "portrait"
                            ? "border-2 border-purple-500 text-purple-600 bg-gradient-to-r from-purple-50 to-amber-50 shadow-md"
                            : ""
                        }`}
                        onClick={() => handleAspectRatioSelect("portrait")}
                      >
                        <RectangleVertical className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>竖向</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className={`h-7 w-7 ${
                          selectedAspectRatio === "landscape"
                            ? "border-2 border-purple-500 text-purple-600 bg-gradient-to-r from-purple-50 to-amber-50 shadow-md"
                            : ""
                        }`}
                        onClick={() => handleAspectRatioSelect("landscape")}
                      >
                        <RectangleHorizontal className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>横向</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-7 px-2 text-xs flex items-center gap-1 bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200"
                        onClick={() => setIsPromptAssistModalOpen(true)}
                      >
                        <Wand2 className="h-3.5 w-3.5 text-purple-500" />
                        AI 辅助
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>AI 辅助创作提示词</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            <div className="relative">
              <Textarea
                id="prompt"
                placeholder="输入提示词，或粘贴图片..."
                value={prompt}
                onChange={handlePromptChange}
                className="h-[160px] mt-2"
              />
              <div className="absolute bottom-2 right-2 flex gap-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7 bg-background/80 hover:bg-background/90"
                        disabled={prompt !== ""}
                        onClick={handlePasteText}
                      >
                        <ClipboardPaste className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>一键粘贴</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7 bg-background/80 hover:bg-background/90"
                        disabled={prompt === ""}
                        onClick={() =>
                          copyToClipboard(
                            prompt,
                            "提示词已复制到剪贴板",
                            "复制提示词失败，请重试"
                          )
                        }
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>复制提示词</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7 bg-background/80 hover:bg-background/90"
                        disabled={prompt === ""}
                        onClick={() => {
                          setPrompt("");
                          checkTotalSize(images, "");
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>清空提示词</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 模型信息模态框 */}
      <ModelInfoModal
        isOpen={isModelInfoModalOpen}
        onOpenChange={setIsModelInfoModalOpen}
      />

      {/* AI 辅助提示词模态框 */}
      <PromptAssistModal
        open={isPromptAssistModalOpen}
        onOpenChange={setIsPromptAssistModalOpen}
        styleId={style}
        currentPrompt={prompt}
        onPromptReplace={setPrompt}
      />
    </div>
  );
}
