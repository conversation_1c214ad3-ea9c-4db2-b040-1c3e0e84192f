'use client'

import React from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { InfoIcon } from "lucide-react"

interface DrawConfirmationDialogProps {
  isOpen: boolean
  onClose: () => void
}

export function DrawConfirmationDialog({
  isOpen,
  onClose,
}: DrawConfirmationDialogProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <InfoIcon className="h-5 w-5 text-blue-500" />
            图片生成已开始
          </AlertDialogTitle>
          <AlertDialogDescription className="pt-2">
            图片已经在后台开始生成，如果成功会在"最新作品"中刷新显示，您也可以在"生成队列"中查看最近未成功的项目。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction>
            我知道了
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
