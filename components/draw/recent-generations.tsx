import React, { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronRight, RefreshCw } from "lucide-react";
import Link from "next/link";
import { HistoryItem } from "@/components/settings/history/history";
import { useDrawStore } from "@/store/draw";
import { DrawResultModal } from "./draw-result-modal";

interface RecentGenerationsProps {
  histories?: HistoryItem[];
  loading?: boolean;
  onHistoryClick?: (history: HistoryItem) => void;
  onRefresh?: () => void;
  latestGeneratedImage?: string;
}

export function RecentGenerations({
  histories: propHistories,
  loading: propLoading,
  onHistoryClick,
  onRefresh,
  latestGeneratedImage
}: RecentGenerationsProps) {
  console.log('[DEBUG RecentGenerations] Component rendered with latestGeneratedImage:', latestGeneratedImage);
  // 使用 store 中的数据和方法
  const {
    recentHistories: storeHistories,
    historiesLoading: storeLoading,
    fetchCombinedHistories,
    isHistoryAlreadyShown,
    setPreviousShownHistoryId
  } = useDrawStore();

  // 优先使用 props 中的数据，如果没有传入则使用 store 中的数据
  const histories = propHistories || storeHistories || [];
  const loading = propLoading !== undefined ? propLoading : storeLoading;

  // 如果没有传入 onRefresh，则使用 store 中的 fetchCombinedHistories
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    } else {
      fetchCombinedHistories();
    }
  };
  // 使用状态管理图片列表
  const [recentSuccessfulImages, setRecentSuccessfulImages] = useState<HistoryItem[]>([]);
  // 状态用于控制新作品完成通知模态框
  const [isNewImageModalOpen, setIsNewImageModalOpen] = useState(false);
  const [newImageUrl, setNewImageUrl] = useState<string | undefined>();

  // 更新图片列表
  useEffect(() => {
    // 从历史记录中获取成功的图片
    const successfulImages = histories.filter(history => history.status).slice(0, 5);
    setRecentSuccessfulImages(successfulImages);
  }, [histories]);

  // 直接在组件渲染时检测新图片，而不使用 useEffect
  useEffect(() => {
    console.log('[DEBUG] latestGeneratedImage changed:', latestGeneratedImage);

    if (latestGeneratedImage) {
      console.log('[DEBUG] latestGeneratedImage exists, checking if it\'s new');

      // 生成一个临时的历史记录ID
      const tempHistoryId = `recent-${Date.now()}`;

      // 检查是否与上次显示的历史记录相同
      if (isHistoryAlreadyShown(tempHistoryId)) {
        console.log('[DEBUG] 跳过显示模态框，历史记录与上次相同:', tempHistoryId);
        return;
      }

      console.log('[DEBUG] Setting new image URL:', latestGeneratedImage, '历史记录ID:', tempHistoryId);
      setNewImageUrl(latestGeneratedImage);

      // 更新上次显示的历史记录ID
      setPreviousShownHistoryId(tempHistoryId);

      console.log('[DEBUG] Opening modal notification');
      setIsNewImageModalOpen(true);

      // 使用函数形式获取最新的 recentSuccessfulImages
      setRecentSuccessfulImages(prevImages => {
        console.log('[DEBUG] Current images in list:', prevImages.map(img => img.resultUrl));

        // Check if the latest image is already in the list
        const imageExists = prevImages.some(history => history.resultUrl === latestGeneratedImage);
        console.log('[DEBUG] Image exists in list?', imageExists);

        if (!imageExists) {
          console.log('[DEBUG] Image is new, creating temporary history item');

          // Create a temporary history item for the latest image
          const latestImage: HistoryItem = {
            id: 'latest-' + Date.now(),
            userId: 'current', // 添加临时 userId
            status: true,
            prompt: '',
            pointsUsed: 0,
            createdAt: new Date().toISOString(),
            resultUrl: latestGeneratedImage,
            description: null,
            parameters: { model: 'unknown' }
          };

          // Add it to the beginning of the list and limit to 4 items
          return [latestImage, ...prevImages].slice(0, 4);
        }

        // 如果图片已存在，返回原数组
        console.log('[DEBUG] Image already exists, not updating list');
        return prevImages;
      });
    }
  }, [latestGeneratedImage, isHistoryAlreadyShown, setPreviousShownHistoryId]);

  // 处理模态框关闭
  const handleModalClose = () => {
    console.log('[DEBUG] Modal close handler called');
    setIsNewImageModalOpen(false);
    // 关闭模态框后刷新列表
    console.log('[DEBUG] Refreshing list after modal close');
    handleRefresh();
  };

  return (
    <div className="w-full mb-8">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-medium">最新作品</h3>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="text-blue-500 hover:text-blue-700"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} /> 刷新
          </Button>
          <Link href="/settings/history" passHref>
            <Button variant="ghost" size="sm" className="text-blue-500 hover:text-blue-700">
              查看更多 <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </Link>
        </div>
      </div>
      <div className="grid grid-cols-5 gap-2">
        {recentSuccessfulImages.length === 0 ? (
          <div className="col-span-5 text-center text-muted-foreground py-8">
            暂无生成成功的图片
          </div>
        ) : (
          <>
            {recentSuccessfulImages.map((history) => (
              <Card
                key={history.id}
                className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => onHistoryClick && onHistoryClick(history)}
              >
                <CardContent className="p-0">
                  <img
                    src={history.resultUrl || '/images/placeholder.png'}
                    alt="Generated image"
                    className="w-full aspect-square object-cover"
                  />
                </CardContent>
              </Card>
            ))}
          </>
        )}
      </div>

      {/* 新作品完成通知模态框 */}
      <DrawResultModal
        isOpen={isNewImageModalOpen}
        onClose={handleModalClose}
        type="success"
        latestGeneratedImage={newImageUrl}
        onAfterClose={handleRefresh}
      />

      {/* 调试信息 */}
      {/* <div className="fixed bottom-4 right-4 p-4 bg-black/80 text-white rounded-md z-50 text-xs max-w-xs">
        <p>isNewImageModalOpen: {isNewImageModalOpen ? '是' : '否'}</p>
        <p>newImageUrl: {newImageUrl || '无'}</p>
        <p>latestGeneratedImage: {latestGeneratedImage || '无'}</p>
        <button
          className="mt-2 px-2 py-1 bg-blue-500 text-white rounded-md text-xs"
          onClick={() => {
            console.log('[DEBUG] Manual trigger modal');
            if (latestGeneratedImage) {
              setNewImageUrl(latestGeneratedImage);
              setIsNewImageModalOpen(true);
            } else {
              // 如果没有最新图片，使用第一个图片
              const firstImage = recentSuccessfulImages[0]?.resultUrl;
              if (firstImage) {
                setNewImageUrl(firstImage);
                setIsNewImageModalOpen(true);
              }
            }
          }}
        >
          手动触发模态框
        </button>
      </div> */}
    </div>
  );
}
