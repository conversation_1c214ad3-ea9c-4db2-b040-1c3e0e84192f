'use client'

import React, { useMemo } from 'react'
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { modelsFAQ } from '@/constants/faq/models'
import { marked } from 'marked'

// Configure marked options for rendering markdown
marked.use({
  gfm: true, // GitHub Flavored Markdown
  breaks: true // Convert \n to <br>
})

interface ModelInfoModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
}

export function ModelInfoModal({ isOpen, onOpenChange }: ModelInfoModalProps) {
  // 使用 useMemo 缓存解析后的 markdown
  const renderedContent = useMemo(() => {
    return marked.parse(modelsFAQ.content) as string
  }, [])

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{modelsFAQ.title}</DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          <div
            className="prose prose-sm max-w-none dark:prose-invert prose-headings:font-semibold prose-headings:text-foreground prose-p:text-muted-foreground prose-strong:text-foreground prose-strong:font-medium"
            dangerouslySetInnerHTML={{ __html: renderedContent }}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}
