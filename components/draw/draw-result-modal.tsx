'use client'

import React from 'react'
import {
  <PERSON>alog,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { CheckCircle2, AlertCircle } from "lucide-react"
import { useDrawStore } from '@/store/draw'

interface DrawResultModalProps {
  isOpen?: boolean
  onClose?: () => void
  type?: 'success' | 'error'
  latestGeneratedImage?: string
  onAfterClose?: () => void // 新增回调，用于在关闭后刷新用户信息
  mode?: 'normal' | 'notification' // 模态框模式，默认为 normal
}

export function DrawResultModal({
  isOpen,
  onClose,
  type = 'success',
  latestGeneratedImage,
  onAfterClose,
  mode = 'normal'
}: DrawResultModalProps) {
  // 使用 useDrawStore 中的状态和方法
  const {
    setIsModalOpen,
    newImageNotificationOpen,
    latestGeneratedImageUrl,
    closeNewImageNotification,
    fetchCombinedHistories,
    isHistoryAlreadyShown,
    setPreviousShownHistoryId
  } = useDrawStore()

  // 强制显示模态框，即使 isOpen 为 false
  const [forceOpen, setForceOpen] = React.useState(false);

  // 根据模式决定使用哪个状态
  const effectiveIsOpen = mode === 'normal' ? (isOpen || forceOpen) : newImageNotificationOpen;
  const effectiveImage = mode === 'normal' ? latestGeneratedImage : latestGeneratedImageUrl;

  // 生成一个临时的历史记录ID，用于跟踪当前模态框中的图片
  const [modalHistoryId, setModalHistoryId] = React.useState<string | null>(null);

  // 当 isOpen 或 latestGeneratedImage 变化时强制打开模态框
  React.useEffect(() => {
    if (mode === 'normal' && isOpen && latestGeneratedImage) {
      // 生成一个临时的历史记录ID
      const tempHistoryId = `modal-${Date.now()}`;
      setModalHistoryId(tempHistoryId);

      // 检查是否与上次显示的历史记录相同
      if (isHistoryAlreadyShown(tempHistoryId)) {
        console.log('跳过显示模态框，历史记录与上次相同:', tempHistoryId);
        return;
      }

      console.log('显示新图片模态框，图片URL:', latestGeneratedImage, '历史记录ID:', tempHistoryId);
      setForceOpen(true);

      // 更新上次显示的历史记录ID
      setPreviousShownHistoryId(tempHistoryId);
    }
  }, [isOpen, latestGeneratedImage, mode, isHistoryAlreadyShown, setPreviousShownHistoryId]);

  // 处理模态框关闭
  const handleClose = () => {
    if (mode === 'normal') {
      // 正常模式下的关闭逻辑
      setIsModalOpen(false);
      setForceOpen(false);
      if (onClose) {
        onClose();
      }
      if (onAfterClose) {
        onAfterClose();
      }
    } else {
      // 通知模式下的关闭逻辑
      closeNewImageNotification();
      fetchCombinedHistories();
    }
  };

  return (
    <Dialog
      open={effectiveIsOpen}
      onOpenChange={(open) => {
        // 当对话框关闭时，同时更新 store 中的状态
        if (!open) {
          handleClose();
        }
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {type === "success" ? (
              <>
                <CheckCircle2 className="h-5 w-5 text-green-500" />
                生成成功
              </>
            ) : (
              <>
                <AlertCircle className="h-5 w-5 text-red-500" />
                生成失败
              </>
            )}
          </DialogTitle>
        </DialogHeader>
        {effectiveImage && (
          <div className="relative h-[300px] flex items-center justify-center bg-gray-50 rounded-lg">
            <a
              href={effectiveImage}
              target="_blank"
              rel="noopener noreferrer"
              className="w-full h-full flex items-center justify-center"
            >
              <img
                src={effectiveImage}
                alt="Preview"
                className="w-full h-full object-contain cursor-pointer"
              />
            </a>
          </div>
        )}
        {effectiveImage && effectiveImage.includes(".webp") && (
          <div className="text-sm text-muted-foreground text-red-500 mt-2">
            图片后缀名为
            WebP，是官方低版本模型创建的图片，本次未消费积分
          </div>
        )}
        <div className="flex items-center justify-between">
          <DialogDescription>
            {type === "success"
              ? "图片已生成完成，请注意保存图片"
              : "图片生成失败，请重新尝试生成"}
          </DialogDescription>
          <Button
            onClick={handleClose}
          >
            确定
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
