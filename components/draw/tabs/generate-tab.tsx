import React, { useRef, useEffect, useState } from 'react';
import { DrawPending } from '../draw-pending';
import { RecentGenerations } from '../recent-generations';
import { useToast } from '@/lib/hooks/use-toast';
import { HistoryItem } from '@/components/settings/history/history';
import { HistoryItemDetailsModal } from '@/components/settings/history/history-item-details-modal';
import { DrawPendingDetailsModal } from '../draw-pending-details-modal';
import { marked } from 'marked';
import { useUserStore } from '@/components/global/user-initializer';
import { useDrawStore } from '@/store/draw';

// Configure marked options for rendering markdown
marked.use({
  gfm: true, // GitHub Flavored Markdown
  breaks: true // Convert \n to <br>
});

interface GenerateTabProps {
  output: string;
  isLoading: boolean;
  renderedContent: string;
  latestGeneratedImage?: string;
}

export function GenerateTab({ output, renderedContent, latestGeneratedImage }: GenerateTabProps) {
  console.log('[DEBUG GenerateTab] Received latestGeneratedImage:', latestGeneratedImage);
  const outputRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const refreshUserInfo = useUserStore(state => state.refreshUserInfo);
  const [lastRecentHistoryId, setLastRecentHistoryId] = useState<string | null>(null);

  // 使用 store 中的数据和方法
  const {
    recentHistories,
    pendingHistories,
    historiesLoading,
    fetchCombinedHistories,
    startHistoryPolling,
    stopHistoryPolling
  } = useDrawStore();

  // Modal states
  const [selectedHistory, setSelectedHistory] = useState<HistoryItem | null>(null);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [selectedPendingHistory, setSelectedPendingHistory] = useState<HistoryItem | null>(null);
  const [isPendingModalOpen, setIsPendingModalOpen] = useState(false);

  // Auto scroll to bottom when output changes
  useEffect(() => {
    if (outputRef.current && output) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [output]);

  // Set links to open in new window
  useEffect(() => {
    const links = outputRef.current?.querySelectorAll('a');
    links?.forEach(link => {
      link.setAttribute('target', '_blank');
      link.setAttribute('rel', 'noopener noreferrer');
    });
  }, [renderedContent]);

  // 开始轮询历史记录
  useEffect(() => {
    console.log('GenerateTab: Starting history polling');
    // 开始轮询，并获取清理函数
    const stopPolling = startHistoryPolling();

    // 组件卸载时停止轮询
    return () => {
      console.log('GenerateTab: Stopping history polling');
      stopPolling();
    };
  }, []);

  // Monitor changes in the first item of recentHistories
  useEffect(() => {
    // Check if recentHistories has items and the first item's ID is different from the last one we tracked
    if (recentHistories.length > 0 && recentHistories[0].id !== lastRecentHistoryId) {
      // Update the last tracked ID
      setLastRecentHistoryId(recentHistories[0].id);

      // If this isn't the first time we're seeing histories (lastRecentHistoryId is not null),
      // then refresh user info to update credits
      if (lastRecentHistoryId !== null) {
        console.log('Recent history changed, refreshing user info');
        refreshUserInfo();
      }
    }
  }, [recentHistories, lastRecentHistoryId, refreshUserInfo]);

  return (
    <div className="space-y-8">
      <RecentGenerations
        onHistoryClick={(history) => {
          setSelectedHistory(history);
          setIsHistoryModalOpen(true);
        }}
        latestGeneratedImage={latestGeneratedImage}
      />
      {/* 调试信息 */}
      {latestGeneratedImage && (
        <div className="p-4 bg-yellow-100 rounded-md mt-4">
          <p className="text-sm font-medium">调试信息：检测到新图片</p>
          <p className="text-xs mt-1">图片URL: {latestGeneratedImage}</p>
        </div>
      )}

      <DrawPending
        onHistoryClick={(history) => {
          setSelectedPendingHistory(history);
          setIsPendingModalOpen(true);
        }}
      />

      {/* History Modal */}
      <HistoryItemDetailsModal
        isOpen={isHistoryModalOpen}
        onOpenChange={setIsHistoryModalOpen}
        item={selectedHistory}
        onItemUpdate={(updatedItem) => {
          // 只需要更新选中的历史记录，store 中的数据会在下次轮询时自动更新
          setSelectedHistory(updatedItem);
          // 手动触发一次刷新以立即更新界面
          fetchCombinedHistories();
        }}
      />

      {/* Pending History Modal */}
      <DrawPendingDetailsModal
        isOpen={isPendingModalOpen}
        onOpenChange={setIsPendingModalOpen}
        item={selectedPendingHistory}
      />
    </div>
  );
}
