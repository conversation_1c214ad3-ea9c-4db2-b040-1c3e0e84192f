import React from 'react';
import { useDrawStore } from '@/store/draw';
import { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronRight, RefreshCw } from "lucide-react";
import { ShareItemDetailsModal } from "@/components/shared/share-item-details-modal";
import { Share as ShareType } from "@/types/share";
import { ExploreModal } from "@/components/draw/explore-modal";

// Using the imported type
type Share = ShareType;

interface ShareTabProps {
  refreshKey?: string;
}

export function ShareTab({ refreshKey = "initial" }: ShareTabProps) {
  const { style } = useDrawStore();
  const [styleShares, setStyleShares] = useState<Share[]>([]);
  const [otherStyleShares, setOtherStyleShares] = useState<Share[]>([]);
  const [loading, setLoading] = useState(false);

  // Modal states
  const [selectedShare, setSelectedShare] = useState<Share | null>(null);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isExploreModalOpen, setIsExploreModalOpen] = useState(false);

  // Fetch recommended shares
  const fetchRecommendedShares = async (isRandom = false) => {
    if (!style) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/recommend?styleId=${style}&styleLimit=16&totalLimit=16&isRandom=${isRandom}`);
      if (response.ok) {
        const data = await response.json();
        // Split the data into style shares and other shares
        const styleSharesData = data.filter((share: Share) => share.styleId === style);
        const otherSharesData = data.filter((share: Share) => share.styleId !== style);

        setStyleShares(styleSharesData);
        setOtherStyleShares(otherSharesData);
      }
    } catch (error) {
      console.error("Error fetching recommended shares:", error);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch of recommended shares
  useEffect(() => {
    fetchRecommendedShares(false);
  }, [style, refreshKey]);

  // Combine and deduplicate shares
  const combinedShares = [...styleShares];

  // Add other style shares if we need more to reach 16
  if (styleShares.length < 16) {
    // Create a Set of existing share IDs for quick lookup
    const existingShareIds = new Set(styleShares.map(share => share.id));

    // Add shares from other styles that aren't already included
    otherStyleShares.forEach(share => {
      if (!existingShareIds.has(share.id) && combinedShares.length < 16) {
        combinedShares.push(share);
        existingShareIds.add(share.id);
      }
    });
  }

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-medium">相关分享</h3>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="text-blue-500 hover:text-blue-700"
            onClick={() => {
              fetchRecommendedShares(true);
            }}
          >
            <RefreshCw className="h-4 w-4 mr-1" /> 换一批
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="text-blue-500 hover:text-blue-700"
            onClick={() => setIsExploreModalOpen(true)}
          >
            查看更多 <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-4 gap-4">
        {loading ? (
          <>
            {Array.from({ length: 16 }).map((_, index) => (
              <Card key={`skeleton-${index}`} className="overflow-hidden">
                <CardContent className="p-0">
                  <Skeleton className="w-full aspect-square" />
                </CardContent>
              </Card>
            ))}
          </>
        ) : combinedShares.length === 0 ? (
          <div className="col-span-4 text-center text-muted-foreground py-8">
            暂无相关分享
          </div>
        ) : (
          <>
            {combinedShares.slice(0, 16).map((share) => (
              <Card
                key={share.id}
                className="overflow-hidden cursor-pointer"
                onClick={() => {
                  setSelectedShare(share);
                  setIsShareModalOpen(true);
                }}
              >
                <CardContent className="p-0">
                  <img
                    src={share.imageUrl}
                    alt="Shared image"
                    className="w-full aspect-square object-cover"
                  />
                </CardContent>
              </Card>
            ))}
            {/* No additional 'View More' button at the end of the grid */}
          </>
        )}
      </div>

      {/* Share Modal */}
      <ShareItemDetailsModal
        isOpen={isShareModalOpen}
        onOpenChange={setIsShareModalOpen}
        item={selectedShare}
      />

      {/* Explore Modal */}
      <ExploreModal
        open={isExploreModalOpen}
        onOpenChange={setIsExploreModalOpen}
      />
    </div>
  );
}
