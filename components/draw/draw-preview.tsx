import React, { useMemo, useEffect, useState } from 'react'
import { marked } from 'marked'
import { useDrawStore } from '@/store/draw'
import { useUserStore } from '@/components/global/user-initializer'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { DrawResultModal } from './draw-result-modal'
import { extractAndValidateImageUrl } from '@/lib/draw/image-validator'
import { GenerateTab } from './tabs/generate-tab'
import { ShareTab } from './tabs/share-tab'

interface DrawPreviewProps {
  activeTab: string
  onTabChange: (value: string) => void
}

export function DrawPreview({ activeTab, onTabChange }: DrawPreviewProps) {
  const { output, isLoading, isModalOpen, setIsModalOpen, isHistoryAlreadyShown, setPreviousShownHistoryId } = useDrawStore()
  const [imageUrls, setImageUrls] = useState<string[]>([])
  const [resultType, setResultType] = useState<'success' | 'error'>('success')
  const prevTabRef = React.useRef(activeTab)
  // 使用一个标志来记录是否需要清除结果
  const [_, setShouldClearResults] = useState(false)
  const refreshUserInfo = useUserStore(state => state.refreshUserInfo)
  const [refreshPlaceholder, setRefreshPlaceholder] = useState(false)

  // Clear results when generate button is clicked
  useEffect(() => {
    if (isLoading) {
      setShouldClearResults(true)
      setImageUrls([])
    }
  }, [isLoading])

  // Extract image URLs from output using image-validator
  useEffect(() => {
    if (output) {
      const extractImages = async () => {
        try {
          // 使用 image-validator 中的函数提取图片URL
          const result = await extractAndValidateImageUrl(output);

          if (result.isValid && (result.url || result.originalUrl)) {
            // 优先使用 url，如果没有则使用 originalUrl
            const imageUrl = result.url || result.originalUrl;
            if (imageUrl) {
              setImageUrls([imageUrl]);
              setShouldClearResults(false);
              onTabChange("share");

              // 生成一个临时的历史记录ID
              const tempHistoryId = `temp-${Date.now()}`;

              // 显示新图片通知，传递临时历史记录ID
              const { showNewImageNotification } = useDrawStore.getState();
              showNewImageNotification(imageUrl, tempHistoryId);
            }
          }
        } catch (error) {
          console.error('Error extracting image URL:', error);

          // 如果提取失败，回退到原来的方法
          let urls: string[] = [];

          // 1. 先尝试匹配Markdown图片格式
          const markdownRegex = /!\[[^\]]*\]\((https?:\/\/[^)]+)\)/gi;
          const markdownMatches = output.match(markdownRegex);
          if (markdownMatches) {
            // 提取所有Markdown图片中的URL
            const markdownUrls = markdownMatches.map(match => {
              const urlMatch = match.match(/\((https?:\/\/[^)]+)\)/i);
              if (urlMatch) {
                // 解码URL中的特殊字符
                return decodeURIComponent(urlMatch[1]);
              }
              return null;
            }).filter((url): url is string => url !== null);
            urls = [...urls, ...markdownUrls];
          }

          // 2. 如果没有找到Markdown格式的URL，尝试匹配普通图片URL
          if (urls.length === 0) {
            const urlRegex = /(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp))/gi;
            const imageUrls = output.match(urlRegex) || [];
            // 解码所有URL中的特殊字符
            urls = [...urls, ...imageUrls.map(url => decodeURIComponent(url))];
          }

          if (urls.length > 0) {
            setImageUrls(urls);
            setShouldClearResults(false);
            onTabChange("share");

            // 生成一个临时的历史记录ID
            const tempHistoryId = `temp-${Date.now()}`;

            // 显示新图片通知，传递临时历史记录ID
            const { showNewImageNotification } = useDrawStore.getState();
            showNewImageNotification(urls[0], tempHistoryId); // 使用第一个 URL 显示通知
          }
        }
      };

      extractImages();
    }
  }, [output, onTabChange]);

  // Use useMemo to cache the parsed markdown
  const renderedContent = useMemo(() => {
    if (!output) return '';
    // 在渲染前解码所有URL中的特殊字符
    const decodedOutput = output.replace(
      /(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp))/gi,
      (match) => decodeURIComponent(match)
    );
    return marked.parse(decodedOutput, { breaks: true }) as string;
  }, [output]);

  // 生成一个临时的历史记录ID，用于跟踪当前会话中的图片
  const [sessionHistoryId, setSessionHistoryId] = useState<string | null>(null);

  // 当图片URL变化时生成一个新的会话历史记录ID
  useEffect(() => {
    if (imageUrls.length > 0) {
      // 生成一个基于当前时间和图片URL的临时ID
      const tempId = `temp-${Date.now()}-${imageUrls.length}`;
      setSessionHistoryId(tempId);
    }
  }, [imageUrls]);

  // Handle modal display when switching to share tab with new results
  useEffect(() => {
    if (activeTab === "share" && prevTabRef.current !== "share" && imageUrls.length > 0 && sessionHistoryId) {
      const latestImage = getLatestImage();

      // 检查是否与上次显示的历史记录相同
      if (isHistoryAlreadyShown(sessionHistoryId)) {
        console.log('跳过显示模态框，历史记录与上次相同:', sessionHistoryId);
      } else {
        console.log('显示新图片模态框，图片URL:', latestImage, '历史记录ID:', sessionHistoryId);
        setResultType('success');
        setIsModalOpen(true);

        // 更新上次显示的历史记录ID
        setPreviousShownHistoryId(sessionHistoryId);
      }
    }
    prevTabRef.current = activeTab;
  }, [activeTab, imageUrls.length, sessionHistoryId, setIsModalOpen, isHistoryAlreadyShown, setPreviousShownHistoryId])

  const getLatestImage = () => {
    // 返回最后一个图片URL
    return imageUrls[imageUrls.length - 1];
  }

  const handleModalClose = () => {
    setIsModalOpen(false)
    // Switch to share tab and trigger refresh
    onTabChange("share")
    setRefreshPlaceholder(prev => !prev)
  }

  return (
    <>
      <Tabs
        value={activeTab}
        onValueChange={onTabChange}
        className="flex flex-col h-full space-y-4"
      >
        <div className="md:px-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="generate">生成</TabsTrigger>
            <TabsTrigger value="share">创意</TabsTrigger>
          </TabsList>
        </div>

        <div className="tabs-content-container flex-1 min-h-0 md:px-4">
          <div className="h-full border rounded-lg">
            <TabsContent value="generate" className="h-full p-6 overflow-auto">
              <GenerateTab
                output={output}
                isLoading={isLoading}
                renderedContent={renderedContent}
                latestGeneratedImage={imageUrls.length > 0 ? getLatestImage() : undefined}
              />
            </TabsContent>

            <TabsContent value="share" className="h-full p-6 overflow-auto">
              <ShareTab
                refreshKey={refreshPlaceholder ? "refresh" : "initial"}
              />
            </TabsContent>
          </div>
        </div>
      </Tabs>

      <DrawResultModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        type={resultType}
        latestGeneratedImage={
          imageUrls.length > 0 ? getLatestImage() : undefined
        }
        onAfterClose={refreshUserInfo}
      />
    </>
  );
}
