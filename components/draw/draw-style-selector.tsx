'use client'

import { useState, useMemo, useEffect, useCallback } from 'react'
import { <PERSON>alog, DialogContent, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { DRAW_STYLES } from '@/constants/draw'
import { ChevronRight } from 'lucide-react'
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { DialogDescription } from '@radix-ui/react-dialog'
import { cn } from '@/lib/utils'

type DrawStyleKey = keyof typeof DRAW_STYLES

interface DrawStyleSelectorProps {
  selectedStyle: DrawStyleKey | null
  onSelectStyle: (style: DrawStyleKey) => void
}

export function DrawStyleSelector({ selectedStyle, onSelectStyle }: DrawStyleSelectorProps) {
  const [open, setOpen] = useState(false)
  // 添加一个状态来跟踪是否已经在客户端渲染
  const [isClientSide, setIsClientSide] = useState(false)
  // 添加一个状态来存储随机样式
  const [randomPreviewStyles, setRandomPreviewStyles] = useState<Array<[string, any]>>([])

  // Get the first 4 styles (always include the selected style)
  const horizontalStyles = useMemo(() => {
    const allStyles = Object.entries(DRAW_STYLES);
    const selectedIndex = selectedStyle ? allStyles.findIndex(([key]) => key === selectedStyle) : -1;

    // If selected style is in first 4, just take first 4
    if (selectedIndex < 4 && selectedIndex >= 0) {
      return allStyles.slice(0, 4);
    }

    // If selected style is not in first 4, take first 3 and add selected style at beginning
    const firstThree = allStyles.slice(0, 3);
    if (selectedStyle) {
      const selectedStyleEntry = allStyles.find(([key]) => key === selectedStyle);
      if (selectedStyleEntry) {
        return [selectedStyleEntry, ...firstThree];
      }
    }

    return allStyles.slice(0, 4);
  }, [selectedStyle]);

  // 初始渲染时使用固定的样式（确保服务器和客户端渲染一致）
  const previewStyles = useMemo(() => {
    const allStyles = Object.entries(DRAW_STYLES);
    const selectedStyles = horizontalStyles.map(([key]) => key);

    // Filter out styles that are already shown in the main grid
    const remainingStyles = allStyles.filter(([key]) => !selectedStyles.includes(key));

    // Take the first 4 remaining styles in a deterministic way
    return remainingStyles.slice(0, 4);
  }, [horizontalStyles]);

  // 生成随机样式的函数
  const generateRandomStyles = useCallback(() => {
    const allStyles = Object.entries(DRAW_STYLES);
    const selectedStyles = horizontalStyles.map(([key]) => key);

    // 过滤掉已经显示在主网格中的样式
    const remainingStyles = allStyles.filter(([key]) => !selectedStyles.includes(key));

    // 随机打乱剩余样式并取前4个
    const shuffled = [...remainingStyles].sort(() => 0.5 - Math.random());
    setRandomPreviewStyles(shuffled.slice(0, 4));
  }, [horizontalStyles]);

  // 在客户端渲染完成后，生成随机样式
  useEffect(() => {
    setIsClientSide(true)
    generateRandomStyles();
  }, [generateRandomStyles]);

  // 跟踪是否有样式被选择
  const [styleSelected, setStyleSelected] = useState(false);

  // 处理对话框打开/关闭
  const handleDialogOpenChange = (isOpen: boolean) => {
    // 如果对话框正在关闭，并且有样式被选择，则重新生成随机样式
    if (!isOpen && styleSelected && isClientSide) {
      generateRandomStyles();
      setStyleSelected(false); // 重置选择状态
    }
    setOpen(isOpen);
  };

  // Selected style gradient border class
  const selectedStyleClass = "border-2 border-transparent bg-gradient-to-r from-purple-500 to-amber-500 p-0";
  const selectedInnerClass = "bg-[linear-gradient(45deg,rgba(0,0,0,0.95),rgba(59,48,10,0.9),rgba(96,82,25,0.85),rgba(30,30,30,0.9))] rounded-lg p-1 h-full w-full shadow-inner text-white";

  return (
    <div className="space-y-2">
      <TooltipProvider>
        {/* Horizontal style selector - 5 column grid */}
        <div className="grid grid-cols-5 gap-3">
          {/* Style cards */}
          {horizontalStyles.map(([key, style]) => (
            <Tooltip key={key}>
              <TooltipTrigger asChild>
                <div
                  className={`rounded-lg cursor-pointer ${
                    key === selectedStyle
                      ? selectedStyleClass
                      : "border hover:bg-accent"
                  }`}
                  onClick={() => {
                    onSelectStyle(key as DrawStyleKey);
                    setStyleSelected(true); // 标记有样式被选择
                  }}
                >
                  <div
                    className={
                      key === selectedStyle ? selectedInnerClass : "p-1.5"
                    }
                  >
                    <div className="relative w-full aspect-square rounded overflow-hidden">
                      <img
                        src={style.image}
                        alt={style.name}
                        className="object-contain w-full h-full absolute inset-0"
                      />
                    </div>
                    <div className="text-center">
                      <h3 className="font-medium text-xs pt-1.5 truncate">
                        {style.name}
                      </h3>
                    </div>
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent
                side="bottom"
                className="max-w-[200px] p-2 text-xs"
              >
                <p className="font-medium">{style.name}</p>
                <p className="text-muted-foreground mt-1">
                  {style.description}
                </p>
              </TooltipContent>
            </Tooltip>
          ))}

          {/* More styles button with 2x2 grid preview */}
          <Dialog open={open} onOpenChange={handleDialogOpenChange}>
            <Tooltip>
              <TooltipTrigger asChild>
                <DialogTrigger asChild>
                  <div className="rounded-lg border hover:bg-accent cursor-pointer flex flex-col aspect-square">
                    <div className="flex-1 grid grid-cols-2 gap-0.5 p-0.5">
                      {(isClientSide ? randomPreviewStyles : previewStyles).map(
                        ([key, style]) => (
                          <div
                            key={key}
                            className="relative aspect-square w-full h-full overflow-hidden"
                          >
                            <img
                              src={style.image}
                              alt={style.name}
                              className="object-cover w-full h-full absolute inset-0"
                            />
                          </div>
                        )
                      )}
                    </div>
                    <div className="flex items-center justify-center py-0.5 bg-gray-50 dark:bg-gray-800">
                      <span className="text-xs font-medium">更多</span>
                      <ChevronRight className="w-4 h-4 ml-1" />
                    </div>
                  </div>
                </DialogTrigger>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="p-2 text-xs">
                <p className="font-medium">查看更多风格</p>
                <p className="text-muted-foreground mt-1">
                  点击查看所有可用的风格
                </p>
              </TooltipContent>
            </Tooltip>

            <DialogContent className="sm:max-w-[800px] lg:max-w-[1000px]">
              <div className="flex space-x-2">
                <DialogTitle>选择生成风格</DialogTitle>
                <DialogDescription className="text-sm text-muted-foreground">
                  选择一个生成风格，以获得不同的效果和结果
                </DialogDescription>
              </div>
              <ScrollArea className="h-[600px] pr-4">
                <div className="grid grid-cols-3 md:grid-cols-4  lg:grid-cols-5 gap-3">
                  {Object.entries(DRAW_STYLES).map(([key, style]) => (
                    <Tooltip key={key}>
                      <TooltipTrigger asChild>
                        <div
                          className={`rounded-lg cursor-pointer ${
                            key === selectedStyle
                              ? selectedStyleClass
                              : "border hover:bg-accent"
                          }`}
                          onClick={() => {
                            onSelectStyle(key as DrawStyleKey);
                            setStyleSelected(true); // 标记有样式被选择
                            setOpen(false);
                          }}
                        >
                          <div
                            className={
                              key === selectedStyle
                                ? selectedInnerClass
                                : "p-1.5"
                            }
                          >
                            <div className="relative w-full aspect-square rounded overflow-hidden">
                              <img
                                src={style.image}
                                alt={style.name}
                                className="object-contain w-full h-full absolute inset-0"
                              />
                            </div>
                            <div className="space-y-0.5">
                              <h3 className="font-medium pt-1.5 text-xs">
                                {style.name}
                              </h3>
                              <p
                                className={cn(
                                  key === selectedStyle
                                    ? "text-white/80"
                                    : "text-muted-foreground",
                                  "text-xs line-clamp-2"
                                )}
                              >
                                {style.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent
                        side="bottom"
                        className="max-w-[200px] p-2 text-xs"
                      >
                        <p className="font-medium">{style.name}</p>
                        <p className="text-muted-foreground mt-1">
                          {style.description}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  ))}
                </div>
              </ScrollArea>
            </DialogContent>
          </Dialog>
        </div>
      </TooltipProvider>
    </div>
  );
}
