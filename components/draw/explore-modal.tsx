'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Button } from "@/components/ui/button"
import { Wand2, ExternalLink, RefreshCw } from 'lucide-react'
import { DRAW_STYLES } from '@/constants/draw'
import { useToast } from "@/lib/hooks/use-toast"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useDrawStore } from '@/store/draw'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface Share {
  id: string
  shareId: string
  imageUrl: string
  model: string
  styleId: string
  customPrompt: string
  viewCount: number
  likeCount: number
  forkCount: number
  userId: string
  allowFork: boolean
}

interface ExploreModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ExploreModal({ open, onOpenChange }: ExploreModalProps) {
  const { style: currentStyle } = useDrawStore()
  const [styleFilter, setStyleFilter] = useState<string>(currentStyle || 'all')
  const [styleShares, setStyleShares] = useState<Share[]>([])
  const [otherStyleShares, setOtherStyleShares] = useState<Share[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const { toast } = useToast()

  // 获取分享列表
  const fetchShares = async (isRandom = true, customStyleId?: string) => {
    try {
      setIsLoading(true)
      setError(null)

      // 构建 URL 参数
      // 使用传入的 customStyleId 或者当前的 styleFilter
      const styleId = customStyleId !== undefined
        ? (customStyleId !== 'all' ? customStyleId : undefined)
        : (styleFilter !== 'all' ? styleFilter : undefined);

      const url = `/api/recommend?styleLimit=15&totalLimit=15&isRandom=${isRandom}${styleId ? `&styleId=${styleId}` : ''}`;

      console.log(`Fetching shares with styleId: ${styleId || 'all'}, URL: ${url}`);

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('获取分享列表失败')
      }

      const data = await response.json()

      // 分离当前风格和其他风格的分享
      if (styleId) {
        // 如果有指定风格，则分开处理
        const styleSharesData = data.filter((share: Share) => share.styleId === styleId);
        const otherSharesData = data.filter((share: Share) => share.styleId !== styleId);

        console.log(`Found ${styleSharesData.length} shares for style ${styleId} and ${otherSharesData.length} other shares`);

        setStyleShares(styleSharesData);
        setOtherStyleShares(otherSharesData);
      } else {
        // 如果是全部风格，则所有分享都放在 styleShares 中
        console.log(`Using all styles, found ${data.length} shares`);

        setStyleShares(data || []);
        setOtherStyleShares([]);
      }
    } catch (error) {
      console.error('Error fetching shares:', error)
      setError('获取分享列表失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 组件挂载时获取数据
  useEffect(() => {
    if (open) {
      // 当模态框打开时，设置默认风格为当前选中的风格
      const initialStyle = currentStyle || 'all';
      setStyleFilter(initialStyle)
      fetchShares(true, initialStyle)
    }
  }, [open, currentStyle])

  // 合并并去重分享
  const combinedShares = [...styleShares];

  // 如果当前风格的分享不足，使用其他风格的分享补充
  if (styleShares.length < 15 && styleFilter !== 'all' && otherStyleShares.length > 0) {
    // 创建一个已存在分享 ID 的 Set 便于快速查找
    const existingShareIds = new Set(styleShares.map(share => share.id));

    // 添加其他风格的分享，确保不重复
    otherStyleShares.forEach(share => {
      if (!existingShareIds.has(share.id) && combinedShares.length < 15) {
        combinedShares.push(share);
        existingShareIds.add(share.id);
      }
    });
  }

  // 当对话框打开/关闭时的处理
  const handleOpenChange = (isOpen: boolean) => {
    onOpenChange(isOpen)
  }

  // 处理一键复刻
  const handleFork = (share: Share) => {
    if (!share.allowFork) {
      toast({
        variant: "destructive",
        description: "该分享不允许复刻",
      })
      return
    }

    // 关闭对话框并跳转到绘图页面
    onOpenChange(false)
    router.push(`/draw?shareId=${share.shareId}`)
  }

  // 样式卡片的样式
  const cardClass = "rounded-lg cursor-pointer border hover:bg-accent"

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[800px]">
        <div className="flex space-x-2">
          <DialogTitle>创意探索</DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground">
            浏览其他用户的创作，获取灵感或直接复刻
          </DialogDescription>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-4 sm:grid-cols-5 gap-3">
            {[...Array(10)].map((_, i) => (
              <div key={i} className="rounded-lg border overflow-hidden">
                <div className="aspect-square bg-gray-100 animate-pulse" />
                <div className="p-2">
                  <div className="h-4 w-2/3 bg-gray-100 animate-pulse mb-1" />
                  <div className="h-3 w-full bg-gray-100 animate-pulse" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center text-destructive">
            {error}
            <Button
              onClick={() => fetchShares(true, styleFilter)}
              variant="outline"
              size="sm"
              className="ml-2"
            >
              重试
            </Button>
          </div>
        ) : (
          <ScrollArea className="h-[500px] pr-4">
            <TooltipProvider>
              <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-3">
                {combinedShares.map((share) => (
                  <Tooltip key={share.shareId}>
                    <TooltipTrigger asChild>
                      <div className={cardClass}>
                        <div className="p-1.5">
                          <div className="relative w-full aspect-square rounded overflow-hidden">
                            <img
                              src={share.imageUrl}
                              alt={DRAW_STYLES[share.styleId as keyof typeof DRAW_STYLES]?.name || share.styleId}
                              className="object-cover w-full h-full absolute inset-0"
                            />
                            {/* 复刻按钮悬浮在图片上 */}
                            <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-50 transition-all flex items-center justify-center opacity-0 hover:opacity-100">
                              <Button
                                onClick={() => handleFork(share)}
                                disabled={!share.allowFork}
                                variant="secondary"
                                size="sm"
                                className="bg-white/90 hover:bg-white"
                              >
                                <Wand2 className="w-4 h-4 mr-1" />
                                一键复刻
                              </Button>
                            </div>
                          </div>
                          <div className="space-y-0.5 mt-1">
                            <div className="flex justify-between items-center">
                              <h3 className="font-medium text-xs truncate">
                                {DRAW_STYLES[share.styleId as keyof typeof DRAW_STYLES]?.name || share.styleId}
                              </h3>
                              <a
                                href={`${process.env.NEXT_PUBLIC_APP_URL}/explore/${share.shareId}`}
                                target="_blank"
                                onClick={(e) => e.stopPropagation()}
                                className="text-muted-foreground hover:text-foreground"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="bottom" className="max-w-[200px] p-2 text-xs">
                      <p className="font-medium">{DRAW_STYLES[share.styleId as keyof typeof DRAW_STYLES]?.name || share.styleId}</p>
                      <p className="text-muted-foreground mt-1 line-clamp-3">
                        {share.customPrompt || "无提示词"}
                      </p>
                      {!share.allowFork && (
                        <p className="text-destructive mt-1">该分享不允许复刻</p>
                      )}
                    </TooltipContent>
                  </Tooltip>
                ))}
              </div>
            </TooltipProvider>
          </ScrollArea>
        )}

        <div className="flex justify-between items-center mt-4">
          <div className="flex items-center gap-2">
            <Select value={styleFilter} onValueChange={(value) => {
              // 直接使用新选择的值进行查询，而不是等待状态更新
              setStyleFilter(value)
              fetchShares(true, value)
            }}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="选择风格" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部风格</SelectItem>
                {Object.entries(DRAW_STYLES).map(([id, style]) => (
                  <SelectItem key={id} value={id}>
                    {style.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-500 hover:text-blue-700"
              onClick={() => {
                fetchShares(true)
              }}
            >
              <RefreshCw className="h-4 w-4 mr-1" /> 换一批
            </Button>
          </div>
          <Link
            href="/explore"
            target="_blank"
            className="inline-flex items-center justify-center gap-1 rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3"
          >
            探索更多创意
            <ExternalLink className="h-3.5 w-3.5" />
          </Link>
        </div>
      </DialogContent>
    </Dialog>
  )
}
