import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Check } from 'lucide-react';
import { parseStreamData, processStreamResponse } from '@/lib/chat/stream-parser';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/lib/hooks/use-toast';
import { useCopy } from '@/lib/hooks/use-copy';

import { chatModels, DEFAULT_CHAT_MODEL, VIP_DEFAULT_CHAT_MODEL } from '@/constants/chat/models';
import { PROMPT_TYPE_OPTIONS } from '@/lib/chat/prompt-templates';
import { DRAW_STYLES } from '@/constants/draw';
import { useProfileStore } from '@/store/profile';
import { useUserStore } from '@/components/global/user-initializer';

interface PromptAssistModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  styleId: keyof typeof DRAW_STYLES;
  currentPrompt: string;
  onPromptReplace: (newPrompt: string) => void;
}

export function PromptAssistModal({
  open,
  onOpenChange,
  styleId,
  currentPrompt,
  onPromptReplace,
}: PromptAssistModalProps) {
  const { toast } = useToast();
  const { copyToClipboard } = useCopy();
  const { profile } = useProfileStore();
  const { currentUser } = useUserStore();
  const refreshUserInfo = useUserStore(state => state.refreshUserInfo);

  // 使用currentUser或profile来确定付费状态
  const isPaid = (currentUser as any)?.isPaid || profile?.isPaid || false;

  // 根据用户付费状态选择默认模型
  const [model, setModel] = useState(isPaid ? VIP_DEFAULT_CHAT_MODEL : DEFAULT_CHAT_MODEL);
  const [promptType, setPromptType] = useState('expand');
  const [requirements, setRequirements] = useState('');
  const [isCopied, setIsCopied] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<Array<{id: string, role: string, content: string}>>([]);

  const abortControllerRef = useRef<AbortController | null>(null);

  // 获取生成的提示词（最后一条消息的内容）
  const generatedPrompt = messages.length > 0 ? messages[messages.length - 1].content : '';

  // 当模态框关闭时，取消正在进行的请求
  useEffect(() => {
    if (!open) {
      if (isLoading && abortControllerRef.current) {
        abortControllerRef.current.abort();
        setIsLoading(false);
      }
      // 重置状态
      setMessages([]);
    }
  }, [open, isLoading]);

  // 当模态框打开时，根据当前风格和提示词初始化状态
  useEffect(() => {
    if (open) {
      // 如果有当前提示词，则设置为默认操作类型为"增强"，否则为"创作"
      if (currentPrompt && currentPrompt.trim() !== '') {
        setPromptType('enhance');
      } else {
        setPromptType('create');
      }

      // 根据用户付费状态设置默认模型
      setModel(isPaid ? VIP_DEFAULT_CHAT_MODEL : DEFAULT_CHAT_MODEL);

      // 重置其他状态
      setMessages([]);
      setIsCopied(false);
    }
  }, [open, currentPrompt, isPaid, setMessages]);

  // 处理生成提示词
  const handleGeneratePrompt = (e: React.FormEvent) => {
    e.preventDefault();
    setIsCopied(false);

    // 如果已经在生成中，则停止
    if (isLoading && abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsLoading(false);
      toast({
        title: '已停止生成',
        description: '已取消提示词生成过程',
        className: 'bg-yellow-500 text-white border-yellow-600',
      });
      return;
    }

    // 清空之前的消息
    setMessages([]);

    // 提交请求
    console.log('提交请求，参数:', {
      model,
      styleId,
      customPrompt: currentPrompt,
      requirements,
      type: promptType,
    });

    // 使用 fetch 直接发送请求，避免 useChat 可能的问题
    const controller = new AbortController();
    abortControllerRef.current = controller;

    // 显示开始生成的提示
    toast({
      title: '开始生成',
      description: '正在使用 AI 生成提示词，请稍候...',
      className: 'bg-blue-500 text-white border-blue-600',
      duration: 3000,
    });

    // 设置加载状态
    setIsLoading(true);

    fetch('/api/chat/prompts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        styleId,
        customPrompt: currentPrompt,
        requirements,
        type: promptType,
      }),
      signal: controller.signal,
    })
      .then(async response => {
        if (!response.ok) {
          const errorText = await response.text();
          // 处理特定的错误状态码
          if (response.status === 403) {
            throw new Error('积分不足，无法使用该模型生成提示词');
          }
          throw new Error(`请求失败: ${response.status} - ${errorText}`);
        }

        // 处理流式响应
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('无法读取响应流');
        }

        let result = '';
        let accumulatedChunks: string[] = [];
        const decoder = new TextDecoder();

        // 读取流
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          // 解码当前块
          const chunk = decoder.decode(value, { stream: true });

          // 保存原始块用于最终处理
          accumulatedChunks.push(chunk);

          // 解析流数据
          const parsedContent = parseStreamData(chunk);
          if (parsedContent) {
            result += parsedContent;

            // 更新消息，显示实时结果
            setMessages([{ id: '1', role: 'assistant', content: result }]);
          }
        }

        // 完成后处理完整响应
        const finalContent = processStreamResponse(accumulatedChunks);
        if (finalContent) {
          // 更新最终结果
          setMessages([{ id: '1', role: 'assistant', content: finalContent }]);
        } else {
          // 如果处理后的内容为空，使用累积的结果
          setMessages([{ id: '1', role: 'assistant', content: result }]);
        }

        // 显示成功提示
        toast({
          title: '生成完成',
          description: '提示词已生成，您可以复制或替换当前提示词',
          className: 'bg-green-500 text-white border-green-600',
        });

        // 刷新用户信息（更新积分余额）
        try {
          await refreshUserInfo();
          console.log('[PROMPT_GENERATION_COMPLETED] 用户信息已刷新');
        } catch (error) {
          console.error('[PROMPT_GENERATION_ERROR] 刷新用户信息失败:', error);
        }

        // 重置加载状态
        setIsLoading(false);
      })
      .catch(error => {
        // 如果是取消请求，不显示错误
        if (error.name === 'AbortError') {
          console.log('请求已取消');
          return;
        }

        console.error('生成提示词时出错:', error);
        toast({
          variant: 'destructive',
          title: '生成失败',
          description: error.message || '生成提示词时出错，请重试',
        });

        // 重置加载状态
        setIsLoading(false);
      });
  };

  // 处理复制提示词
  const handleCopyPrompt = () => {
    if (generatedPrompt) {
      copyToClipboard(
        generatedPrompt,
        '提示词已复制到剪贴板',
        '复制提示词失败，请重试'
      );
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    }
  };

  // 处理替换提示词
  const handleReplacePrompt = () => {
    if (generatedPrompt) {
      onPromptReplace(generatedPrompt);
      onOpenChange(false);
      toast({
        title: '提示词已替换',
        description: '已将生成的提示词应用到绘图选项',
        className: 'bg-green-500 text-white border-green-600',
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-purple-500" />
            AI 辅助提示词创作
          </DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground hidden md:block">
            根据当前风格和提示词，使用 AI 辅助创作更好的提示词
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleGeneratePrompt} className="space-y-4 mt-2">
          <div className="grid md:grid-cols-4 gap-4">
            <div className="space-y-2 md:col-span-3">
              <Label htmlFor="model-select">选择模型</Label>
              <Select value={model} onValueChange={setModel}>
                <SelectTrigger id="model-select">
                  <SelectValue placeholder="选择模型" />
                </SelectTrigger>
                <SelectContent>
                  {chatModels.filter((model) => !model.disabled).map((model) => (
                    <SelectItem
                      key={model.id}
                      value={model.id}
                      disabled={model.disabled || (model.paidOnly && !isPaid)}
                    >
                      <div className="flex w-full items-center justify-between">
                        <div>
                          {model.name}
                          <span className="ml-2 text-xs text-gray-500">
                            {model.description}
                          </span>
                          {model.points > 0 && (
                            <span className="ml-2 text-xs text-amber-600 font-medium">
                              {model.points} 积分
                            </span>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type-select">操作类型</Label>
              <Select value={promptType} onValueChange={setPromptType}>
                <SelectTrigger id="type-select">
                  <SelectValue placeholder="选择操作类型" />
                </SelectTrigger>
                <SelectContent>
                  {PROMPT_TYPE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="requirements">额外要求（可选）</Label>
            <Textarea
              id="requirements"
              placeholder="输入你的额外要求，例如：添加更多细节描述、使用更专业的术语等"
              value={requirements}
              onChange={(e) => setRequirements(e.target.value)}
              className="h-15 md:h-20"
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="generated-prompt">生成结果</Label>
              {generatedPrompt && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyPrompt}
                  className="h-7 px-2 text-xs"
                >
                  {isCopied ? (
                    <Check className="h-3.5 w-3.5 mr-1" />
                  ) : (
                    <Copy className="h-3.5 w-3.5 mr-1" />
                  )}
                  {isCopied ? '已复制' : '复制'}
                </Button>
              )}
            </div>
            <div className="relative">
              <Textarea
                id="generated-prompt"
                placeholder="生成的提示词将显示在这里..."
                value={generatedPrompt}
                readOnly
                className="h-15 md:h-[120px] font-medium"
              />
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-background/80">
                  <div className="flex flex-col items-center gap-2">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="text-sm text-muted-foreground">正在生成提示词...</span>
                  </div>
                </div>
              )}
            </div>
            {/* 错误信息会在 toast 中显示 */}
          </div>

          <DialogFooter className="flex justify-between sm:justify-between gap-2">
            <div className="flex gap-2">
              <Button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  handleGeneratePrompt(e);
                }}
                className={`${
                  isLoading
                    ? 'bg-red-500 hover:bg-red-600'
                    : 'bg-gradient-to-r from-purple-600 via-blue-500 to-purple-600 hover:from-purple-700 hover:via-blue-600 hover:to-purple-700'
                } text-white shadow-md transition-all duration-300 hover:shadow-lg`}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    停止生成
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    生成提示词
                  </>
                )}
              </Button>
              {generatedPrompt && !isLoading && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={(e) => {
                    // 清空之前的消息
                    setMessages([]);
                    setIsCopied(false);

                    // 调用生成函数
                    handleGeneratePrompt(e);

                    // 显示提示
                    toast({
                      title: '重新生成',
                      description: '正在重新生成提示词...',
                      className: 'bg-blue-500 text-white border-blue-600',
                      duration: 3000,
                    });
                  }}
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  重新生成
                </Button>
              )}
            </div>
            {generatedPrompt && !isLoading && (
              <Button
                type="button"
                onClick={handleReplacePrompt}
                className="bg-green-500 hover:bg-green-600 text-white shadow-md transition-all duration-300 hover:shadow-lg"
              >
                替换提示词
              </Button>
            )}
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
