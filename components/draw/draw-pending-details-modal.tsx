'use client';

import Link from "next/link";
import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { HistoryItem } from "@/components/settings/history/history";
import { ModelStyleDisplay } from "@/components/global/model-style-display";
import { marked } from 'marked';
import { Copy } from "lucide-react";
import { useCopy } from "@/lib/hooks/use-copy";

// Configure marked options for rendering markdown
marked.use({
  gfm: true, // GitHub Flavored Markdown
  breaks: true // Convert \n to <br>
});

interface DrawPendingDetailsModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  item: HistoryItem | null;
}

export function DrawPendingDetailsModal({ isOpen, onOpenChange, item }: DrawPendingDetailsModalProps) {
  const { copyToClipboard } = useCopy();

  if (!item) {
    return null;
  }

  const handleCopyPrompt = () => {
    if (!item.prompt) return;

    copyToClipboard(
      item.prompt,
      "提示词已复制到剪贴板",
      "复制提示词失败，请重试"
    );
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-300">开始生成</Badge>
      case 'PROCESSING':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-300">生成中</Badge>
      case 'SUCCESS':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-300">生成结束</Badge>
      case 'FAILED':
        return <Badge className="bg-red-100 text-red-800 border-red-300">生成中止</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-300">未知状态</Badge>
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <span>生成详情</span>
            {getStatusBadge(item.drawStatus || "PENDING")}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          <ModelStyleDisplay
            modelId={item.extra?.model}
            styleId={item.extra?.style}
            className="mb-4"
          />

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-medium">创建时间</h3>
              <p className="text-sm text-muted-foreground">
                <Link
                  href={`/settings/history/${item.id}`}
                  className="text-blue-500 hover:underline"
                >
                  {format(new Date(item.createdAt), "yyyy-MM-dd HH:mm:ss")}
                </Link>
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium">上传图片</h3>
              <p className="text-sm text-muted-foreground">
                {Array.isArray(item.extra?.originalImages)
                  ? `${item.extra.originalImages.length} 张`
                  : "无"}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">
                <span>提示词</span>
              </h3>
              {item.prompt && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 text-muted-foreground"
                  onClick={handleCopyPrompt}
                >
                  <Copy className="h-4 w-4 mr-1" />
                  复制
                </Button>
              )}
            </div>
            <div className="text-sm text-muted-foreground p-3 bg-gray-50 rounded-md max-h-[80px] md:max-h-[160px] overflow-y-auto">
              {item.prompt || "无提示词"}
            </div>
          </div>

          {item.drawResult && (
            <div className="space-y-2">
              <h3 className="font-medium">生成过程</h3>
              <div className="text-sm p-3 bg-gray-50 rounded-md max-h-[120px] md:max-h-[240px] overflow-y-auto">
                <div
                  className="markdown-content"
                  dangerouslySetInnerHTML={{
                    __html: marked.parse(item.drawResult),
                  }}
                />
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
