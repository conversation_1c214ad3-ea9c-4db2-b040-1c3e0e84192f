'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/hooks/use-toast";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";

import { useRouter } from "next/navigation";
import { Share } from "@/types/share";
import { getModelName, getStyleName } from "@/components/settings/history/history";

interface ShareItemDetailsModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  item: Share | null;
}

export function ShareItemDetailsModal({ isOpen, onOpenChange, item }: ShareItemDetailsModalProps) {
  const { toast } = useToast();
  const router = useRouter();

  if (!item) {
    return null;
  }

  const handleCopyPrompt = async () => {
    const promptText = item.history?.prompt || item.customPrompt;
    if (!promptText) return;

    try {
      await navigator.clipboard.writeText(promptText);
      toast({
        variant: "default",
        title: "复制成功",
        description: "提示词已复制到剪贴板",
        className: "bg-green-500 text-white border-green-600",
      });
    } catch (err) {
      toast({
        variant: "destructive",
        description: "复制失败，请重试",
      });
    }
  };

  const handleFork = () => {
    if (!item.allowFork) {
      toast({
        variant: "destructive",
        description: "该分享不允许复刻",
      });
      return;
    }

    // Redirect to draw page with share parameters
    router.push(`/draw?shareId=${item.shareId}`);
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>分享详情</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          <div className="relative h-[300px] flex items-center justify-center bg-gray-50 rounded-lg">
            {item.imageUrl ? (
              <a
                href={item.imageUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="w-full h-full flex items-center justify-center"
              >
                <img
                  src={item.imageUrl}
                  alt="Preview"
                  className="w-full h-full object-contain cursor-pointer"
                />
              </a>
            ) : (
              <span className="text-4xl text-red-500">❌</span>
            )}
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="font-medium">模型</h3>
                <p className="text-sm text-muted-foreground">
                  {getModelName(
                    item.history?.extra?.model || item.model || "-"
                  )}
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="font-medium">风格</h3>
                <p className="text-sm text-muted-foreground">
                  {getStyleName(
                    item.history?.extra?.style || item.styleId || "-"
                  )}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="font-medium">提示词</h3>
                {item.allowFork ? (
                  <div className="flex items-center gap-2">
                    {item.history?.prompt || item.customPrompt ? (
                      <HoverCard>
                        <HoverCardTrigger asChild>
                          <p className="text-sm text-muted-foreground cursor-help">
                            已设置
                          </p>
                        </HoverCardTrigger>
                        <HoverCardContent className="w-80">
                          <p className="text-sm">
                            {item.history?.prompt || item.customPrompt}
                          </p>
                        </HoverCardContent>
                      </HoverCard>
                    ) : (
                      <p className="text-sm text-muted-foreground">未设置</p>
                    )}
                    {(item.history?.prompt || item.customPrompt) && (
                      <button
                        onClick={() => handleCopyPrompt()}
                        className="text-sm text-blue-500 hover:text-blue-600"
                      >
                        复制
                      </button>
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">已隐藏</p>
                )}
              </div>
              <div className="space-y-2">
                <h3 className="font-medium">上传图片</h3>
                {item.allowFork ? (
                  <Badge variant="outline" className="text-sm">
                    {Array.isArray(item.history?.extra?.originalImages)
                      ? item.history.extra.originalImages.length
                      : Array.isArray(item.originalImages)
                      ? item.originalImages.length
                      : 0}{" "}
                    张
                  </Badge>
                ) : (
                  <p className="text-sm text-muted-foreground">已隐藏</p>
                )}
              </div>
            </div>
          </div>

          <div className="flex gap-2 w-full">
            <a
              href={`${process.env.NEXT_PUBLIC_APP_URL}/explore/${item.shareId}`}
              rel="noopener noreferrer"
              target="_blank"
              className="w-full"
            >
              <Button
                className={cn("w-full", !item.allowFork && "col-span-2")}
                variant="outline"
              >
                查看分享
              </Button>
            </a>
            {item.allowFork && (
              <Button
                className="w-full"
                onClick={handleFork}
                disabled={!item.allowFork}
              >
                一键复刻
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
