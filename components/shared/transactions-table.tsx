"use client";

import { FC } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import Link from "next/link";

export interface Transaction {
  id: string;
  type: string;
  amount: number;
  description: string;
  exchangeType?: string;
  timestamp: string;
  userId: string;
  note?: string;
}

interface TransactionsTableProps {
  transactions: Transaction[];
  className?: string;
}

export const TransactionsTable: FC<TransactionsTableProps> = ({
  transactions,
  className,
}) => {
  if (!transactions || transactions.length === 0) {
    return (
      <div className={cn("text-center py-4 text-muted-foreground", className)}>
        暂无关联交易记录
      </div>
    );
  }

  const getExchangeTypeBadge = (type: string) => {
    const styles = {
      refund: "bg-blue-100 text-blue-800 hover:bg-blue-200",
      gift: "bg-purple-100 text-purple-800 hover:bg-purple-200",
      award: "bg-green-100 text-green-800 hover:bg-green-200",
      affiliate: "bg-amber-100 text-amber-800 hover:bg-amber-200",
      other: "bg-gray-100 text-gray-800 hover:bg-gray-200",
    };

    const labels = {
      refund: "退款",
      gift: "赠送",
      award: "奖励",
      affiliate: "推广",
      other: "其他",
    };

    return (
      <Badge
        variant="outline"
        className={cn(
          "font-normal",
          styles[type as keyof typeof styles] || styles.other
        )}
      >
        {labels[type as keyof typeof labels] || type}
      </Badge>
    );
  };

  return (
    <div className={cn("rounded-md border overflow-hidden", className)}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>交易ID</TableHead>
            <TableHead>类型</TableHead>
            <TableHead>积分</TableHead>
            <TableHead>描述</TableHead>
            <TableHead>时间</TableHead>
            <TableHead>备注</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction) => (
            <TableRow key={transaction.id}>
              <TableCell className="font-mono text-xs">
                <Link 
                  href={`/settings/orders/${transaction.id}`}
                  className="text-blue-600 hover:underline"
                >
                  {transaction.id}
                </Link>
              </TableCell>
              <TableCell>
                {transaction.exchangeType
                  ? getExchangeTypeBadge(transaction.exchangeType)
                  : transaction.type === "credit"
                  ? "充值"
                  : "消费"}
              </TableCell>
              <TableCell>
                <span
                  className={
                    transaction.type === "credit"
                      ? "text-green-600"
                      : "text-red-600"
                  }
                >
                  {transaction.type === "credit" ? "+" : "-"}
                  {transaction.amount}
                </span>
              </TableCell>
              <TableCell>{transaction.description}</TableCell>
              <TableCell>
                {format(new Date(transaction.timestamp), "yyyy-MM-dd HH:mm:ss")}
              </TableCell>
              <TableCell>
                {transaction.note ? (
                  <span className="text-sm text-muted-foreground">
                    {transaction.note}
                  </span>
                ) : (
                  "-"
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
