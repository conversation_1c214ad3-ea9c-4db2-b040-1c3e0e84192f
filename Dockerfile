FROM node:24-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm install

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Copy and use .env.example as build-time environment variables
COPY .env.example ./.env.local

# Build the application
# RUN npm run compile

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
# Uncomment the following line in case you want to disable telemetry during runtime.
# ENV NEXT_TELEMETRY_DISABLED=1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy package.json and install production dependencies for database tools
COPY package.json package-lock.json* ./
# Install only database-related tools
RUN npm install drizzle-kit tsx --production=false && npm cache clean --force

# Copy database related files
COPY drizzle.config.ts ./drizzle.config.ts
COPY drizzle.tsconfig.json ./drizzle.tsconfig.json
COPY drizzle ./drizzle
COPY lib/db ./lib/db

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
# COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
# COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Copy and setup startup scripts
COPY docker/start.sh /app/start.sh
COPY docker/dev-start.sh /app/dev-start.sh
RUN chmod +x /app/start.sh /app/dev-start.sh
RUN chown nextjs:nodejs /app/start.sh /app/dev-start.sh

USER nextjs

EXPOSE 3000

ENV PORT=3000

# Run startup script
CMD ["/app/start.sh"]
