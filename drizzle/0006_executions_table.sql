CREATE TABLE "executions" (
  "id" text PRIMARY KEY NOT NULL,
  "type" text NOT NULL,
  "status" text DEFAULT 'PENDING' NOT NULL,
  "params" jsonb DEFAULT '{}'::jsonb NOT NULL,
  "summary" jsonb DEFAULT '{}'::jsonb NOT NULL,
  "logs" text DEFAULT '',
  "started_at" timestamp,
  "completed_at" timestamp,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

CREATE INDEX "executions_type_idx" ON "executions" ("type");
CREATE INDEX "executions_status_idx" ON "executions" ("status");
CREATE INDEX "executions_created_at_idx" ON "executions" ("created_at");
