{"id": "6b8c9d7e-5f4a-3e2d-1c0b-9a8b7c6d5e4f", "prevId": "5a4b3c2d-1e0f-9a8b-7c6d-5e4f3a2b1c0d", "version": "7", "dialect": "postgresql", "tables": {"public.executions": {"name": "executions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "params": {"name": "params", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "summary": {"name": "summary", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "logs": {"name": "logs", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"executions_type_idx": {"name": "executions_type_idx", "columns": ["type"], "isUnique": false}, "executions_status_idx": {"name": "executions_status_idx", "columns": ["status"], "isUnique": false}, "executions_created_at_idx": {"name": "executions_created_at_idx", "columns": ["created_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}}