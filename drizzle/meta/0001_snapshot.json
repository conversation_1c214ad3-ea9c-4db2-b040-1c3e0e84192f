{"id": "0170c4ef-1b86-4bd3-9556-f0c2c0feacf8", "prevId": "95123ecd-1393-4f9d-8f23-90f21083cd84", "version": "7", "dialect": "postgresql", "tables": {"public.histories": {"name": "histories", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "boolean", "primaryKey": false, "notNull": true}, "result_url": {"name": "result_url", "type": "text", "primaryKey": false, "notNull": false}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "points_used": {"name": "points_used", "type": "integer", "primaryKey": false, "notNull": true}, "parameters": {"name": "parameters", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "extra": {"name": "extra", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"histories_user_id_users_clerk_id_fk": {"name": "histories_user_id_users_clerk_id_fk", "tableFrom": "histories", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["clerk_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "buyer_id": {"name": "buyer_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": false}, "out_trade_no": {"name": "out_trade_no", "type": "text", "primaryKey": false, "notNull": false}, "trade_no": {"name": "trade_no", "type": "text", "primaryKey": false, "notNull": false}, "qr_code_url": {"name": "qr_code_url", "type": "text", "primaryKey": false, "notNull": false}, "paid_at": {"name": "paid_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refunded_at": {"name": "refunded_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "extra": {"name": "extra", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"orders_user_id_users_clerk_id_fk": {"name": "orders_user_id_users_clerk_id_fk", "tableFrom": "orders", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["clerk_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"orders_out_trade_no_unique": {"name": "orders_out_trade_no_unique", "nullsNotDistinct": false, "columns": ["out_trade_no"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"clerk_id": {"name": "clerk_id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "extra": {"name": "extra", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.wallets": {"name": "wallets", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "permanent_points": {"name": "permanent_points", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "extra": {"name": "extra", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"wallets_user_id_users_clerk_id_fk": {"name": "wallets_user_id_users_clerk_id_fk", "tableFrom": "wallets", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["clerk_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}