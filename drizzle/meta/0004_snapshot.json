{"id": "81795472-e501-4852-9ad7-570eff33b0e4", "prevId": "ddbc98b2-20a6-4af2-b321-acdce719ed2c", "version": "7", "dialect": "postgresql", "tables": {"public.fork_transactions": {"name": "fork_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "from_user_id": {"name": "from_user_id", "type": "text", "primaryKey": false, "notNull": true}, "to_user_id": {"name": "to_user_id", "type": "text", "primaryKey": false, "notNull": true}, "share_id": {"name": "share_id", "type": "text", "primaryKey": false, "notNull": true}, "history_id": {"name": "history_id", "type": "text", "primaryKey": false, "notNull": true}, "tip_points": {"name": "tip_points", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "extra": {"name": "extra", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {"fork_transactions_from_user_id_idx": {"name": "fork_transactions_from_user_id_idx", "columns": [{"expression": "from_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fork_transactions_to_user_id_idx": {"name": "fork_transactions_to_user_id_idx", "columns": [{"expression": "to_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fork_transactions_share_id_idx": {"name": "fork_transactions_share_id_idx", "columns": [{"expression": "share_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"fork_transactions_from_user_id_users_clerk_id_fk": {"name": "fork_transactions_from_user_id_users_clerk_id_fk", "tableFrom": "fork_transactions", "tableTo": "users", "columnsFrom": ["from_user_id"], "columnsTo": ["clerk_id"], "onDelete": "no action", "onUpdate": "no action"}, "fork_transactions_to_user_id_users_clerk_id_fk": {"name": "fork_transactions_to_user_id_users_clerk_id_fk", "tableFrom": "fork_transactions", "tableTo": "users", "columnsFrom": ["to_user_id"], "columnsTo": ["clerk_id"], "onDelete": "no action", "onUpdate": "no action"}, "fork_transactions_share_id_shares_id_fk": {"name": "fork_transactions_share_id_shares_id_fk", "tableFrom": "fork_transactions", "tableTo": "shares", "columnsFrom": ["share_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "fork_transactions_history_id_histories_id_fk": {"name": "fork_transactions_history_id_histories_id_fk", "tableFrom": "fork_transactions", "tableTo": "histories", "columnsFrom": ["history_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.histories": {"name": "histories", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "boolean", "primaryKey": false, "notNull": true}, "result_url": {"name": "result_url", "type": "text", "primaryKey": false, "notNull": false}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "points_used": {"name": "points_used", "type": "integer", "primaryKey": false, "notNull": true}, "parameters": {"name": "parameters", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "forked_from_share_id": {"name": "forked_from_share_id", "type": "text", "primaryKey": false, "notNull": false}, "extra": {"name": "extra", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"histories_user_id_users_clerk_id_fk": {"name": "histories_user_id_users_clerk_id_fk", "tableFrom": "histories", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["clerk_id"], "onDelete": "no action", "onUpdate": "no action"}, "histories_forked_from_share_id_shares_id_fk": {"name": "histories_forked_from_share_id_shares_id_fk", "tableFrom": "histories", "tableTo": "shares", "columnsFrom": ["forked_from_share_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.likes": {"name": "likes", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "share_id": {"name": "share_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"likes_user_id_share_id_unique": {"name": "likes_user_id_share_id_unique", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "share_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "likes_share_id_idx": {"name": "likes_share_id_idx", "columns": [{"expression": "share_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "likes_user_id_idx": {"name": "likes_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"likes_user_id_users_clerk_id_fk": {"name": "likes_user_id_users_clerk_id_fk", "tableFrom": "likes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["clerk_id"], "onDelete": "no action", "onUpdate": "no action"}, "likes_share_id_shares_id_fk": {"name": "likes_share_id_shares_id_fk", "tableFrom": "likes", "tableTo": "shares", "columnsFrom": ["share_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "buyer_id": {"name": "buyer_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": false}, "out_trade_no": {"name": "out_trade_no", "type": "text", "primaryKey": false, "notNull": false}, "trade_no": {"name": "trade_no", "type": "text", "primaryKey": false, "notNull": false}, "qr_code_url": {"name": "qr_code_url", "type": "text", "primaryKey": false, "notNull": false}, "paid_at": {"name": "paid_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refunded_at": {"name": "refunded_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "extra": {"name": "extra", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"orders_user_id_users_clerk_id_fk": {"name": "orders_user_id_users_clerk_id_fk", "tableFrom": "orders", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["clerk_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"orders_out_trade_no_unique": {"name": "orders_out_trade_no_unique", "nullsNotDistinct": false, "columns": ["out_trade_no"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.shares": {"name": "shares", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "share_id": {"name": "share_id", "type": "text", "primaryKey": false, "notNull": true}, "history_id": {"name": "history_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "allow_fork": {"name": "allow_fork", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "fork_tip_points": {"name": "fork_tip_points", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "like_count": {"name": "like_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "fork_count": {"name": "fork_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "fork_earnings": {"name": "fork_earnings", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "forked_from_id": {"name": "forked_from_id", "type": "text", "primaryKey": false, "notNull": false}, "model": {"name": "model", "type": "text", "primaryKey": false, "notNull": false}, "style_id": {"name": "style_id", "type": "text", "primaryKey": false, "notNull": false}, "original_images": {"name": "original_images", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "custom_prompt": {"name": "custom_prompt", "type": "text", "primaryKey": false, "notNull": false}, "shared_at": {"name": "shared_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "extra": {"name": "extra", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {"shares_user_id_idx": {"name": "shares_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shares_history_id_idx": {"name": "shares_history_id_idx", "columns": [{"expression": "history_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shares_is_public_idx": {"name": "shares_is_public_idx", "columns": [{"expression": "is_public", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shares_fork_count_idx": {"name": "shares_fork_count_idx", "columns": [{"expression": "fork_count", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shares_shared_at_idx": {"name": "shares_shared_at_idx", "columns": [{"expression": "shared_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"shares_history_id_histories_id_fk": {"name": "shares_history_id_histories_id_fk", "tableFrom": "shares", "tableTo": "histories", "columnsFrom": ["history_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "shares_user_id_users_clerk_id_fk": {"name": "shares_user_id_users_clerk_id_fk", "tableFrom": "shares", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["clerk_id"], "onDelete": "no action", "onUpdate": "no action"}, "shares_forked_from_id_shares_id_fk": {"name": "shares_forked_from_id_shares_id_fk", "tableFrom": "shares", "tableTo": "shares", "columnsFrom": ["forked_from_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"shares_share_id_unique": {"name": "shares_share_id_unique", "nullsNotDistinct": false, "columns": ["share_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"clerk_id": {"name": "clerk_id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "extra": {"name": "extra", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.wallets": {"name": "wallets", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "permanent_points": {"name": "permanent_points", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "extra": {"name": "extra", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"wallets_user_id_users_clerk_id_fk": {"name": "wallets_user_id_users_clerk_id_fk", "tableFrom": "wallets", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["clerk_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"wallets_user_id_unique": {"name": "wallets_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}