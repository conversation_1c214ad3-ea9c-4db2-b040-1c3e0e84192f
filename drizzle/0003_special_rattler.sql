CREATE TABLE "fork_transactions" (
	"id" text PRIMARY KEY NOT NULL,
	"from_user_id" text NOT NULL,
	"to_user_id" text NOT NULL,
	"share_id" text NOT NULL,
	"history_id" text NOT NULL,
	"tip_points" integer NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"extra" jsonb DEFAULT '{}'::jsonb NOT NULL
);
--> statement-breakpoint
CREATE TABLE "likes" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"share_id" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "shares" (
	"id" text PRIMARY KEY NOT NULL,
	"share_id" text NOT NULL,
	"history_id" text NOT NULL,
	"user_id" text NOT NULL,
	"is_public" boolean DEFAULT true NOT NULL,
	"allow_fork" boolean DEFAULT true NOT NULL,
	"fork_tip_points" integer DEFAULT 0 NOT NULL,
	"image_url" text NOT NULL,
	"view_count" integer DEFAULT 0 NOT NULL,
	"like_count" integer DEFAULT 0 NOT NULL,
	"fork_count" integer DEFAULT 0 NOT NULL,
	"fork_earnings" integer DEFAULT 0 NOT NULL,
	"forked_from_id" text,
	"shared_at" timestamp DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"extra" jsonb DEFAULT '{}'::jsonb NOT NULL,
	CONSTRAINT "shares_share_id_unique" UNIQUE("share_id")
);
--> statement-breakpoint
ALTER TABLE "histories" ADD COLUMN "forked_from_share_id" text;--> statement-breakpoint
ALTER TABLE "fork_transactions" ADD CONSTRAINT "fork_transactions_from_user_id_users_clerk_id_fk" FOREIGN KEY ("from_user_id") REFERENCES "public"."users"("clerk_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "fork_transactions" ADD CONSTRAINT "fork_transactions_to_user_id_users_clerk_id_fk" FOREIGN KEY ("to_user_id") REFERENCES "public"."users"("clerk_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "fork_transactions" ADD CONSTRAINT "fork_transactions_share_id_shares_id_fk" FOREIGN KEY ("share_id") REFERENCES "public"."shares"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "fork_transactions" ADD CONSTRAINT "fork_transactions_history_id_histories_id_fk" FOREIGN KEY ("history_id") REFERENCES "public"."histories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "likes" ADD CONSTRAINT "likes_user_id_users_clerk_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("clerk_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "likes" ADD CONSTRAINT "likes_share_id_shares_id_fk" FOREIGN KEY ("share_id") REFERENCES "public"."shares"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shares" ADD CONSTRAINT "shares_history_id_histories_id_fk" FOREIGN KEY ("history_id") REFERENCES "public"."histories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shares" ADD CONSTRAINT "shares_user_id_users_clerk_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("clerk_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shares" ADD CONSTRAINT "shares_forked_from_id_shares_id_fk" FOREIGN KEY ("forked_from_id") REFERENCES "public"."shares"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "fork_transactions_from_user_id_idx" ON "fork_transactions" USING btree ("from_user_id");--> statement-breakpoint
CREATE INDEX "fork_transactions_to_user_id_idx" ON "fork_transactions" USING btree ("to_user_id");--> statement-breakpoint
CREATE INDEX "fork_transactions_share_id_idx" ON "fork_transactions" USING btree ("share_id");--> statement-breakpoint
CREATE UNIQUE INDEX "likes_user_id_share_id_unique" ON "likes" USING btree ("user_id","share_id");--> statement-breakpoint
CREATE INDEX "likes_share_id_idx" ON "likes" USING btree ("share_id");--> statement-breakpoint
CREATE INDEX "likes_user_id_idx" ON "likes" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "shares_user_id_idx" ON "shares" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "shares_history_id_idx" ON "shares" USING btree ("history_id");--> statement-breakpoint
CREATE INDEX "shares_is_public_idx" ON "shares" USING btree ("is_public");--> statement-breakpoint
CREATE INDEX "shares_fork_count_idx" ON "shares" USING btree ("fork_count");--> statement-breakpoint
CREATE INDEX "shares_shared_at_idx" ON "shares" USING btree ("shared_at");--> statement-breakpoint
ALTER TABLE "histories" ADD CONSTRAINT "histories_forked_from_share_id_shares_id_fk" FOREIGN KEY ("forked_from_share_id") REFERENCES "public"."shares"("id") ON DELETE no action ON UPDATE no action;