ALTER TABLE "orders" ADD COLUMN "status" text DEFAULT 'PENDING' NOT NULL;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "payment_method" text;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "out_trade_no" text;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "trade_no" text;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "qr_code_url" text;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "paid_at" timestamp;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "refunded_at" timestamp;--> statement-breakpoint
ALTER TABLE "orders" ADD CONSTRAINT "orders_out_trade_no_unique" UNIQUE("out_trade_no");