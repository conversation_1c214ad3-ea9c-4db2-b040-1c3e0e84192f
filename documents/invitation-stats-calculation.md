# 邀请奖励统计计算更新

## 概述

本文档描述了邀请奖励统计计算的更新，主要涉及在计算待领取和已领取的积分和现金奖励时，考虑邀请记录的 `extra.redeemType` 字段，确保只计算相应类型的奖励。

## 背景

在当前实现中，邀请奖励统计计算存在一个问题：当用户兑换邀请奖励时，系统会将该邀请记录标记为已完成（`status = 'completed'`），并在 `extra.redeemType` 字段中记录兑换的类型（`'points'` 或 `'cash'`）。然而，在计算总奖励时，系统没有考虑这个字段，导致统计数据不准确。

例如，如果一个邀请记录有 100 积分和 ¥10 现金奖励，用户选择兑换积分，那么在统计中，系统会同时计入 100 积分和 ¥10 现金作为已领取的奖励，而实际上用户只领取了积分奖励。

## 技术实现

### 更新 `getInvitationStats` 函数

在 `lib/invitation/index.ts` 文件中的 `getInvitationStats` 函数需要进行以下更新：

1. 计算已领取的积分奖励时，只统计 `status = 'completed'` 且 `extra.redeemType = 'points'` 的记录
2. 计算已领取的现金奖励时，只统计 `status = 'completed'` 且 `extra.redeemType = 'cash'` 的记录
3. 对于没有 `extra.redeemType` 字段的旧记录（兼容性考虑），根据记录的 `pointsAwarded` 和 `cashAwarded` 字段判断奖励类型

### SQL 查询更新

原始查询：

```sql
-- 获取总积分奖励
SELECT SUM(points_awarded) FROM invitation_usages
WHERE invitation_id IN (?) AND status = 'completed';

-- 获取总现金奖励
SELECT SUM(cash_awarded) FROM invitation_usages
WHERE invitation_id IN (?) AND status = 'completed';
```

更新后的查询：

```sql
-- 获取总积分奖励
SELECT SUM(points_awarded) FROM invitation_usages
WHERE invitation_id IN (?)
  AND status = 'completed'
  AND (
    (extra->>'redeemType' = 'points')
    OR (extra->>'redeemType' IS NULL AND points_awarded > 0)
  );

-- 获取总现金奖励
SELECT SUM(cash_awarded) FROM invitation_usages
WHERE invitation_id IN (?)
  AND status = 'completed'
  AND (
    (extra->>'redeemType' = 'cash')
    OR (extra->>'redeemType' IS NULL AND cash_awarded > 0)
  );
```

## 预期结果

更新后，邀请奖励统计将更加准确：

1. "积分奖励" 统计块：
   - 大字显示：等待领取的积分总和
   - 小字显示：已领取的积分总和（只计算选择兑换积分的记录）

2. "现金奖励" 统计块：
   - 大字显示：等待领取的现金总和
   - 小字显示：已领取的现金总和（只计算选择兑换现金的记录）

这样可以确保统计数据准确反映用户的实际奖励情况，避免重复计算或错误计算。
