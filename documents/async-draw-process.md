# 异步绘图流程设计文档

## 1. 概述

本文档描述了将当前同步绘图流程改造为异步流程的设计方案。异步绘图流程将允许用户提交绘图请求后立即返回，而不需要等待绘图完成，从而提高用户体验和系统性能。

## 2. 系统架构

### 2.1 数据模型扩展

#### 历史记录表 (histories) 扩展

需要在 `histories` 表中添加以下字段：

```typescript
drawStatus: text("draw_status").$type<DrawStatus>().default("PENDING"),
drawResult: text("draw_result"),
```

其中 `DrawStatus` 定义为：

```typescript
export type DrawStatus = "PENDING" | "PROCESSING" | "SUCCESS" | "FAILED";
```

### 2.2 API 接口设计

#### 2.2.1 同步绘图接口 (备份)

- 路径: `/api/draw/sync`
- 方法: POST
- 功能: 保留原有同步绘图功能，作为备份

#### 2.2.2 异步绘图接口

- 路径: `/api/draw`
- 方法: POST
- 功能: 接收绘图请求，创建历史记录，立即返回，后台继续处理绘图请求
- 返回: 包含 `historyId` 的 JSON 响应

### 2.3 处理流程

1. 用户提交绘图请求到 `/api/draw`
2. 服务器验证请求参数和用户积分
3. 检查用户是否有过多的 PENDING 状态绘图请求
4. 创建历史记录，设置 `drawStatus` 为 "PENDING"
5. 立即返回包含 `historyId` 的响应
6. 后台继续处理绘图请求：
   - 更新 `drawStatus` 为 "PROCESSING"
   - 调用 AI 模型生成图片
   - 定期更新 `drawResult` 字段，记录生成进度
   - 生成完成后，更新 `drawStatus` 为 "SUCCESS" 或 "FAILED"
   - 如果成功，更新 `resultUrl` 并扣除积分

## 3. 前端实现

### 3.1 绘图生成页面改造

1. 提交请求后，不再等待流式响应，而是立即显示"已提交"状态
2. 添加一个新的 Tab 页，显示所有 PENDING 和 PROCESSING 状态的绘图请求
3. 定期轮询每个未完成的绘图请求的状态
4. 实时更新界面，显示绘图进度和结果

### 3.2 用户体验优化

1. 显示队列中的请求数量和预计等待时间
2. 提供取消正在处理的绘图请求的功能
3. 当绘图完成时，提供通知提醒

## 4. 安全与限制

1. 限制每个用户的 PENDING 状态绘图请求数量
2. 检查用户的 PENDING 状态绘图请求的总积分消耗，不能超过用户当前积分
3. 对长时间未完成的绘图请求进行超时处理

## 5. 实现计划

1. 数据库模式扩展
2. 复制并修改现有绘图接口
3. 实现异步处理逻辑
4. 添加状态查询接口
5. 前端界面改造
6. 测试与优化

## 6. 注意事项

1. 确保异步处理不会因为服务器重启而丢失
2. 处理并发请求时的资源分配
3. 监控系统性能和用户体验
4. 提供回退到同步模式的选项
