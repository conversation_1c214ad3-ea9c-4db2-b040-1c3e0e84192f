# 数据库索引优化建议

## 概述

为了进一步优化 `/api/public/status` 接口的数据库查询性能，建议为 `histories` 表添加相关索引。

## 当前查询分析

### 查询条件
```sql
WHERE updated_at >= ? 
  AND draw_status NOT IN ('PENDING', 'PROCESSING')
```

### 查询字段
```sql
SELECT status, draw_status, extra FROM histories
```

## 建议的索引优化

### 1. 复合索引：updated_at + draw_status

**索引名称**: `histories_updated_at_draw_status_idx`

**作用**:
- 优化时间范围查询 (`updated_at >= hoursAgo`)
- 优化状态过滤 (`draw_status NOT IN (...)`)
- 支持覆盖索引查询

**SQL**:
```sql
CREATE INDEX histories_updated_at_draw_status_idx 
ON histories (updated_at DESC, draw_status);
```

### 2. 单独索引：draw_status

**索引名称**: `histories_draw_status_idx`

**作用**:
- 优化状态过滤查询
- 支持状态统计查询

**SQL**:
```sql
CREATE INDEX histories_draw_status_idx 
ON histories (draw_status);
```

## 性能预期

### 查询优化效果
- **时间复杂度**: 从 O(n) 降低到 O(log n)
- **扫描行数**: 大幅减少需要扫描的行数
- **查询时间**: 预计减少 70-90% 查询时间

### 适用场景
- 状态接口每分钟自动查询
- 管理后台统计查询
- 其他基于时间和状态的查询

## 实施建议

### 1. 使用 Drizzle ORM 添加索引

在 `lib/db/schema.ts` 中更新 histories 表定义：

```typescript
export const histories = pgTable("histories", {
  // ... 现有字段
}, (table) => [
  // 现有索引...
  index("histories_updated_at_draw_status_idx").on(table.updatedAt.desc(), table.drawStatus),
  index("histories_draw_status_idx").on(table.drawStatus),
]);
```

### 2. 生成迁移文件

```bash
npm run db:generate
npm run db:migrate
```

### 3. 监控索引效果

添加索引后，可以通过以下方式监控效果：

```sql
-- 查看查询执行计划
EXPLAIN ANALYZE 
SELECT status, draw_status, extra 
FROM histories 
WHERE updated_at >= NOW() - INTERVAL '12 hours'
  AND draw_status NOT IN ('PENDING', 'PROCESSING');

-- 查看索引使用情况
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename = 'histories';
```

## 注意事项

### 索引维护成本
- **写入性能**: 每次插入/更新会稍微增加开销
- **存储空间**: 索引会占用额外存储空间
- **维护成本**: 需要定期维护索引统计信息

### 权衡考虑
- 对于读多写少的场景（如状态查询），索引收益远大于成本
- 建议在生产环境的低峰期添加索引
- 可以先在测试环境验证效果

## 总结

通过添加合适的数据库索引，可以进一步优化状态接口的查询性能，配合之前的字段选择优化，整体性能提升将非常显著。

**预期总体优化效果**:
- 数据库查询时间: 减少 70-90%
- 数据传输量: 减少 80%
- 整体响应时间: 显著提升
