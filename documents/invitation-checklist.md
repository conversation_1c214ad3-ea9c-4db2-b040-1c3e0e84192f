# 邀请管理系统实施清单

## 阶段一：数据库设计与迁移

- [x] 创建设计文档
- [x] 在 `lib/db/schema.ts` 中定义 `invitations` 表
- [x] 在 `lib/db/schema.ts` 中定义 `invitation_usages` 表
- [x] 创建数据库迁移文件
- [x] 添加数据库迁移脚本命令
- [x] 创建邀请系统常量文件

## 阶段二：API 实现

### 邀请码管理 API
- [x] 实现 `POST /api/invitations/activate` 激活邀请码
- [x] 实现 `PATCH /api/invitations/{id}` 更新邀请码
- [x] 实现 `GET /api/invitations` 获取邀请码列表
- [x] 实现邀请码验证逻辑（只允许小写字母和数字）

### 邀请使用记录 API
- [x] 实现 `GET /api/invitation_usages` 获取使用记录
- [x] 实现 `POST /api/invitation_usages/{id}/redeem` 兑换奖励
- [x] 实现 `POST /api/register-with-invite` 注册关联邀请
- [x] 实现 `POST /api/invitation_usages/redeem-batch` 批量兑换奖励
- [x] 实现 `POST /api/admin/invitation_usages/redeem-batch` 管理员批量兑换奖励

## 阶段三：前端页面实现

### 设置中心 - 邀请管理页面
- [x] 创建 `/settings/invitation` 路由
- [x] 实现邀请码激活组件
- [x] 实现邀请码更新功能（包括弹出对话框编辑）
- [x] 实现邀请统计组件
- [x] 实现邀请使用记录表格组件
- [x] 实现兑换操作功能
- [ ] 实现批量选择和批量兑换功能

### 邀请落地页
- [x] 创建 `/invite/[inviteCode]` 路由
- [x] 实现邀请码存储逻辑（localStorage）
- [x] 优化为快速跳转页面

## 阶段四：注册流程集成

- [x] 修改注册流程，支持读取邀请码
- [x] 实现注册成功后关联邀请记录
- [x] 实现首次充值后更新邀请记录状态
- [x] 优化充值金额计算（使用元作为单位）

## 阶段五：管理员功能

- [x] 创建 `/admin/invitations` 路由
- [x] 实现管理员邀请码列表组件
- [x] 实现管理员邀请使用记录表格
- [x] 实现管理员兑换功能（积分/返现）
- [x] 在用户详情页显示邀请关系
- [ ] 实现管理员批量兑换功能（积分/返现）

## 阶段六：优化与测试

### 前端优化
- [x] 添加邀请码格式验证（只允许小写字母和数字）
- [x] 优化邀请码编辑体验（在邀请链接旁添加编辑按钮）
- [x] 实现邀请链接一键复制功能
- [x] 实现邀请统计仪表盘

### 后端优化
- [x] 添加邀请码使用限制逻辑
- [x] 添加并发操作安全检查
- [x] 优化货币单位转换（元/分）
- [x] 实现批量兑换功能（积分/现金）
- [x] 优化批量处理性能

### 调试与测试
- [x] 在注册与邀请码关联流程添加调试日志
- [x] 在充值后奖励计算流程添加调试日志
- [x] 在奖励兑换操作流程添加调试日志
- [x] 在批量兑换操作流程添加调试日志
- [x] 全流程测试与问题修复

### 文档
- [x] 更新设计文档，添加调试与排错章节
- [x] 添加常见问题与解决方案
- [x] 文档更新与完善

## 阶段七：部署与监控

- [ ] 部署新功能
  - 执行 `npm run db:migrate:invitation` 创建数据库表
  - 部署代码到生产环境
- [ ] 设置监控与告警
- [ ] 收集用户反馈
- [ ] 迭代优化
