# History 表扩展方案

## 背景

当前系统使用 `histories` 表记录图片生成的历史记录和积分消耗。随着业务扩展，需要支持文字生成、视频生成等多种模态，同时保持统一的积分消耗记录。

## 现有 History 表结构分析

```typescript
export const histories = pgTable("histories", {
  id: text("id").primaryKey(),
  userId: text("user_id").references(() => users.clerkId).notNull(),
  status: boolean("status").notNull(),          // 成功/失败状态
  resultUrl: text("result_url"),                // 结果文件URL
  prompt: text("prompt").notNull(),             // 用户输入
  description: text("description"),             // 描述
  pointsUsed: integer("points_used").notNull(), // 积分消耗
  parameters: jsonb("parameters").default({}).notNull(), // 生成参数
  forkedFromShareId: text("forked_from_share_id").references(shareRef),
  backupStatus: text("backup_status").$type<BackupStatus>().default("PENDING"),
  lastBackupAt: timestamp("last_backup_at"),
  archived: boolean("archived").default(false).notNull(),
  drawStatus: text("draw_status").$type<DrawStatus>().default("SUCCESS"),
  drawResult: text("draw_result"),
  extra: jsonb("extra").default({}).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});
```

## 推荐扩展方案

### 1. 添加 type 字段进行分类

```sql
-- 数据库迁移 - 只添加新字段，保持现有字段名不变
ALTER TABLE histories ADD COLUMN type text NOT NULL DEFAULT 'image';
```

### 2. 更新类型定义（使用别名映射）

```typescript
export type GenerationType = "image" | "text" | "video";
export type GenerationStatus = "PENDING" | "PROCESSING" | "SUCCESS" | "FAILED";

export const histories = pgTable("histories", {
  // ... 现有字段
  type: text("type").notNull().$type<GenerationType>().default("image"),
  
  // 保持原有字段名，但在业务逻辑中作为通用生成状态使用
  // 注意：drawStatus 字段实际用于所有类型的生成状态（图片/文字/视频等）
  drawStatus: text("draw_status").$type<GenerationStatus>().default("SUCCESS"), // 别名：generationStatus
  
  // 保持原有字段名，但在业务逻辑中作为通用生成结果使用  
  // 注意：drawResult 字段实际用于所有类型的生成结果消息
  drawResult: text("draw_result"), // 别名：generationResult
  
  // ... 其他字段
});

// 为了代码可读性，可以创建别名访问器
export const generationStatus = (record: History) => record.drawStatus;
export const generationResult = (record: History) => record.drawResult;
```

### 3. 不同类型的数据存储结构

#### 图片生成
```typescript
// parameters 字段
{
  model: "dall-e-3",
  style: "realistic", 
  dimensions: "1024x1024",
  quality: "hd"
}

// extra 字段
{
  model: "dall-e-3",
  style: "realistic",
  styleId: "style_001",
  originalImages: ["url1", "url2"],
  drawProgress: "completed"
}
```

#### 文字生成
```typescript
// parameters 字段
{
  model: "gpt-4",
  maxTokens: 1000,
  temperature: 0.7,
  systemPrompt: "You are a helpful assistant"
}

// extra 字段
{
  model: "gpt-4",
  tokenCount: 856,
  finishReason: "stop",
  responseTime: 2.3
}
```

#### 视频生成
```typescript
// parameters 字段
{
  model: "runway-gen3",
  duration: 30,
  fps: 24,
  resolution: "1080p",
  style: "cinematic"
}

// extra 字段
{
  model: "runway-gen3",
  processingTime: 120,
  fileSize: "50MB",
  thumbnailUrl: "thumb_url"
}
```

## 实施步骤

### 1. 数据库迁移
```sql
-- 只添加类型字段，保持现有字段名不变
ALTER TABLE histories ADD COLUMN type text NOT NULL DEFAULT 'image';

-- 添加索引
CREATE INDEX histories_type_idx ON histories(type);
CREATE INDEX histories_type_user_idx ON histories(type, userId);
```

### 2. 更新 Schema 定义
```typescript
// 在 lib/db/schema.ts 中更新
export type GenerationType = "image" | "text" | "video";
export type GenerationStatus = "PENDING" | "PROCESSING" | "SUCCESS" | "FAILED";

export const histories = pgTable("histories", {
  // ... 现有字段保持不变
  type: text("type").notNull().$type<GenerationType>().default("image"),
  
  // 保持原字段名，但类型更新为通用的 GenerationStatus
  // 注意：这些字段虽然名为 draw*，但实际用于所有生成类型
  drawStatus: text("draw_status").$type<GenerationStatus>().default("SUCCESS"),
  drawResult: text("draw_result"),
  
  // ... 其他字段
}, (table) => [
  // ... 现有索引
  index("histories_type_idx").on(table.type),
  index("histories_type_user_idx").on(table.type, table.userId),
]);

// 为了业务逻辑清晰，可以创建语义化的访问方法
export const getGenerationStatus = (history: History) => history.drawStatus;
export const getGenerationResult = (history: History) => history.drawResult;
```

### 3. 更新业务逻辑
- 创建记录时指定 `type` 字段
- 根据类型设置不同的 `parameters` 和 `extra` 数据
- 查询时可按类型过滤：`WHERE type = 'text'`

## 优势分析

### 1. 成本极低
- 只需添加一个 `type` 字段，保持现有字段名不变
- 利用现有 `parameters` 和 `extra` jsonb 字段存储类型特有数据
- 无需重命名字段或大幅修改现有结构
- 避免了字段重命名可能导致的数据迁移风险

### 2. 向后兼容
- 现有图片生成数据自动标记为 `type: 'image'`
- 现有查询逻辑无需修改，`drawStatus` 和 `drawResult` 字段继续使用
- 渐进式迁移，风险可控
- 现有代码中的字段引用无需修改

### 3. 灵活扩展
- jsonb 字段可存储任意结构的数据
- 易于添加新的生成类型
- 支持不同类型的特有字段

### 4. 查询效率
- 添加类型索引，按类型查询高效
- 复合索引支持用户+类型的组合查询
- 保持单表结构，避免复杂 JOIN

## 潜在考虑

### 1. 数据一致性
- 需要确保不同类型的数据格式规范
- 建议在应用层添加数据验证

### 2. 查询复杂度
- 不同类型可能需要不同的查询逻辑
- 建议封装类型特定的查询方法

### 3. 未来扩展
- 如果某种类型数据量极大，可考虑分表
- 监控 jsonb 字段的查询性能

## 总结

通过添加 `type` 字段分类 + 利用现有 jsonb 字段存储特有数据的方案，可以以最小成本实现多模态生成记录的统一管理。这个方案既保持了向后兼容性，又为未来扩展提供了足够的灵活性。