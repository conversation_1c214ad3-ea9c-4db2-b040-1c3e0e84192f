# 积分消费逻辑审查报告

## 审查目标

对 `/api/draw` 及相关积分消费逻辑进行全面审查，确保以下关键点：

1. 结果失败时不扣分的设计是否正确实现
2. 积分消费记录是否正确保存
3. 各种错误情况下的积分处理是否符合预期

## 审查结果

经过代码审查，确认积分消费逻辑已正确实现，具体如下：

### 1. 成功生成图片时的积分扣除

```typescript
if (url) {
  const pointsUsed = isWebp ? 0 : creditCost;

  // 生成成功，扣除积分
  const pointsResult = await deductPoints({
    userId,
    points: pointsUsed,
  });

  // 更新历史记录
  const updateitems = {
    historyId: history.id,
    status: true,
    resultUrl: url,
    originalUrl,
    isWebp,
    pointsUsed: isWebp ? pointsUsed : undefined,
    pointsConsumption: {
      oldBalance: pointsResult.oldBalance,
      newBalance: pointsResult.newBalance,
    },
  };

  await updateHistory(updateitems);
}
```

### 2. 生成失败时不扣除积分

```typescript
else {
  // 生成失败，记录错误
  await updateHistory({
    historyId: history.id,
    status: false,
    pointsUsed: 0,
    error: error || 'No image URL in response',
  });
}
```

### 3. WebP图片不扣除积分

- 当生成的图片是WebP格式时，设置 `pointsUsed = 0`
- WebP格式通过URL后缀判断：`isWebp: validateWebPImage(url)`
- 在 `lib/draw/image-validator.ts` 中实现了 `validateWebPImage` 函数

### 4. 错误处理不扣除积分

- 所有错误处理路径都正确设置了 `status: false`
- 没有在错误处理中调用 `deductPoints`
- 历史记录正确更新了错误信息

### 5. 积分扣除的原子性

- 使用数据库事务确保积分扣除的原子性
- 记录旧余额和新余额，用于审计和历史记录

## 积分扣除流程

1. 初始积分检查：在开始生成前检查用户是否有足够积分
2. 创建历史记录：记录用户请求的模型、风格和预期积分消费
3. 图片生成：调用AI模型生成图片
4. 结果处理：
   - 成功：扣除积分并更新历史记录
   - 失败：不扣除积分，记录错误信息
5. 错误处理：所有错误路径都不扣除积分

## 特殊情况处理

### WebP图片处理

系统对WebP格式的图片有特殊处理：
- 通过URL后缀识别WebP格式
- WebP图片生成不消耗积分（设置 `pointsUsed = 0`）
- 在历史记录中标记 `isWebp: true`

### 错误处理

系统对各种错误情况都有正确处理：
- 流错误：记录错误但不扣除积分
- 完成回调错误：记录错误但不扣除积分
- 图片URL提取失败：记录为生成失败，不扣除积分

## 结论

经过全面审查，确认 `/api/draw` 及相关代码中的积分消费逻辑已正确实现：

1. ✅ 只有在成功生成图片时才扣除积分
2. ✅ 生成失败时不扣除积分
3. ✅ WebP格式图片不扣除积分
4. ✅ 所有错误情况都不扣除积分
5. ✅ 积分消费记录正确保存在历史记录中

积分消费逻辑符合设计要求，特别是"结果失败时不扣分"的要求已正确实现。
