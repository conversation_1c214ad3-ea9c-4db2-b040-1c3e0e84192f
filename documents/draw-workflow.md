# Draw Generation Workflow

## 整体流程

### 1. 前端流程 (`@/components/draw/draw-generator.tsx`)
1. 用户选择生成参数：
   - 选择风格 (style)
   - 选择模型 (model)
   - 上传参考图片或输入提示词
2. 前端预校验：
   - 检查必填参数完整性
   - 显示积分消耗提示
3. 发起生成请求：
   - 构建 FormData
   - 发送 POST 请求到 `/api/draw`
4. 处理响应流：
   - 实时显示生成进度
   - 检测图片 URL
   - 显示最终结果
5. 更新用户状态：
   - 生成完成后刷新用户积分
   - 显示成功/失败提示

### 2. 后端流程 (`/api/draw/route.ts`)
1. 请求验证：
   ```typescript
   - 验证用户身份 (auth)
   - 验证必填参数
   - 验证模型是否存在
   ```

2. 积分检查：
   ```typescript
   const creditCost = getModelCreditCost(modelId);
   const hasEnoughPoints = await checkUserPoints({
     userId,
     points: creditCost,
   });
   ```

3. 权限检查：
   ```typescript
   if (requestedModel.paidOnly) {
     // 检查用户是否有付费记录
   }
   ```

4. 创建历史记录：
   ```typescript
   const history = await createDrawHistory({
     userId,
     prompt: finalPrompt,
     style: styleId,
     model: modelId,
     pointsUsed: creditCost,
     // ...
   });
   ```

5. 图片生成：
   - 调用 AI 模型生成图片
   - 流式返回生成进度
   - 提取生成的图片 URL

6. 完成处理：
   ```typescript
   if (imageUrl) {
     // 生成成功，扣除积分
     await deductDrawPoints({
       userId,
       points: creditCost,
       model: modelId,
       description: `Generated ${styleId} style image`,
     });

     // 更新历史记录
     await updateDrawHistory({
       historyId: history.id,
       status: true,
       resultUrl: imageUrl,
     });
   }
   ```

### 3. 积分管理 (`/lib/draw/points.ts`)

#### 积分检查
```typescript
export async function checkUserPoints({ userId, points }: CheckPointsParams): Promise<boolean> {
  const wallet = await db.query.wallets.findFirst({
    where: eq(wallets.userId, userId),
  });

  if (!wallet) {
    return false;
  }

  return wallet.permanentPoints >= points;
}
```

#### 积分扣除
```typescript
export async function deductDrawPoints({ userId, points, description, model }: DeductPointsParams) {
  // 先检查积分是否足够
  const hasEnoughPoints = await checkUserPoints({ userId, points });
  if (!hasEnoughPoints) {
    throw new Error('Insufficient points');
  }

  // 扣除积分（事务操作）
  const { orderId, newBalance } = await updateWalletPoints(
    userId,
    points,
    'debit',
    description || `Generated image using ${model}`,
  );

  return { success: true, orderId, newBalance };
}
```

## 绘图完成处理流程

### 1. 图片生成结果处理
```typescript
// 从响应中提取图片URL
const responseText = typeof response === 'string' ? response : '';
const imageUrlMatch = responseText.match(/https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)/i);
const imageUrl = imageUrlMatch ? imageUrlMatch[0] : null;
```

### 2. 状态更新流程

#### 2.1 生成成功
当 `imageUrl` 存在时：
1. 记录成功日志
2. 扣除积分
3. 更新历史记录：
   - 设置 status = true
   - 保存图片URL
   - 记录积分变动

```typescript
if (imageUrl) {
  // 扣除积分
  const pointsResult = await deductPoints({
    userId,
    points: creditCost,
  });

  // 更新历史记录
  await updateHistory({
    historyId: history.id,
    status: true,
    resultUrl: imageUrl,
    pointsConsumption: {
      oldBalance: pointsResult.oldBalance,
      newBalance: pointsResult.newBalance,
    }
  });
}
```

#### 2.2 生成失败
当 `imageUrl` 不存在时：
1. 记录失败日志
2. 更新历史记录：
   - 设置 status = false
   - 记录错误信息
   - 不扣除积分

```typescript
else {
  await updateHistory({
    historyId: history.id,
    status: false,
    error: "Failed to generate image",
  });
}
```

### 3. 错误处理流程

#### 3.1 完成回调错误
```typescript
catch (error) {
  console.error("[DRAW_COMPLETION_ERROR]", error);
  await updateHistory({
    historyId: history.id,
    status: false,
    error: error instanceof Error ? error.message : "Unknown error occurred",
  });
}
```

#### 3.2 流错误
```typescript
onError: (error) => {
  console.error("[DRAW_STREAM_ERROR]", error);
  updateHistory({
    historyId: history.id,
    status: false,
    error: error instanceof Error ? error.message : "Stream error occurred",
  });
}
```

## 历史记录结构

### 1. 基础信息
```typescript
{
  id: string;
  userId: string;
  prompt: string;
  pointsUsed: number;
  status: boolean;
  resultUrl?: string;
}
```

### 2. 扩展信息 (extra)
```typescript
{
  style: string;
  model: string;
  originalImage?: {
    type: string;
    size: number;
  };
  error?: string;
  pointsConsumption?: {
    oldBalance: number;
    newBalance: number;
    timestamp: string;
  };
}
```

## 检查清单

### 1. 生成成功时
- [x] 检查是否有有效的图片URL
- [x] 扣除积分
- [x] 更新历史记录状态为成功
- [x] 保存图片URL
- [x] 记录积分变动信息
- [x] 记录成功日志

### 2. 生成失败时
- [x] 更新历史记录状态为失败
- [x] 记录错误信息
- [x] 不扣除积分
- [x] 记录失败日志

### 3. 错误处理
- [x] 处理完成回调错误
- [x] 处理流错误
- [x] 记录错误日志
- [x] 更新历史记录

## 优化建议

1. **状态检查优化**
   - [ ] 添加图片URL格式验证
   - [ ] 验证图片URL是否可访问
   - [ ] 添加超时处理

2. **错误处理优化**
   - [ ] 细化错误类型
   - [ ] 添加重试机制
   - [ ] 改进错误提示信息

3. **日志优化**
   - [ ] 添加请求追踪ID
   - [ ] 记录完整的请求-响应周期
   - [ ] 添加性能监控指标

## 错误处理

### 1. 前端错误
- 参数校验错误
- 积分不足提示
- 生成失败提示
- 解析错误提示

### 2. 后端错误
- 401: 未授权访问
- 400: 参数错误
- 403: 积分不足/需要付费订阅
- 500: 服务器错误

## 数据结构

### 1. 积分操作记录
```typescript
interface PointsConsumption {
  amount: number;      // 消费数量
  timestamp: string;   // 消费时间
  orderId: string;     // 关联的订单ID
}
```

### 2. 历史记录
```typescript
interface DrawHistory {
  id: string;
  userId: string;
  prompt: string;
  style: string;
  model: string;
  pointsUsed: number;
  status: boolean;
  resultUrl?: string;
  error?: string;
  originalImage?: {
    type: string;
    size: number;
  };
}
```

## 一键复刻功能

### 1. 复刻来源
系统支持两种复刻来源：
- 分享复刻：通过分享ID（shareId）复刻他人分享的图片
- 历史复刻：通过历史ID（historyId）复刻自己的历史记录

### 2. 复刻流程

#### 2.1 分享复刻
```typescript
// 从URL获取shareId
const shareId = searchParams.get('shareId');

// 获取分享数据
const response = await fetch(`/api/public/shares/${shareId}`);
const data = await response.json();

// 填充表单
setStyle(data.data.styleId);
setPrompt(data.data.customPrompt);
// 仅付费用户可使用分享的模型
if (profile?.isPaid) {
  setModel(data.data.model);
}
```

#### 2.2 历史复刻
```typescript
// 从URL获取historyId
const historyId = searchParams.get('historyId');

// 验证历史记录所有权
const response = await fetch(`/api/history/${historyId}`);
const history = await response.json();

// 填充表单
setStyle(history.extra.style);
setPrompt(history.prompt);
setModel(history.extra.model);
```

### 3. 权限控制
- 分享复刻：需要分享者允许复刻（allowFork = true）
- 历史复刻：只能复刻自己的历史记录

## 注意事项

1. 积分安全：
   - 积分检查和扣除必须在后端完成
   - 使用事务确保积分操作原子性
   - 先检查积分，后生成图片，最后扣除积分

2. 错误处理：
   - 生成失败不扣除积分（已正确实现）
   - 所有错误都要更新到历史记录
   - 提供清晰的错误提示

3. 数据一致性：
   - 使用事务确保积分和历史记录的一致性
   - 定期检查和清理未完成的记录

4. 用户体验：
   - 实时显示生成进度
   - 及时更新用户积分
   - 清晰的成功/失败提示
   - 支持一键复刻自己的历史记录
   - 支持复刻他人分享的图片（如果允许）

## Image Generation API Response Example

```
0:"**0%**\n"
0:"\n\n> "
0:"进度 "
0:"**69%**\n"
0:"\n\n\n![file_00000000bac451f79ad7e54d85db22be](https://sdmntprsouthcentralus.oaiusercontent.com/files/00000000-bac4-51f7-9ad7-e54d85db22be/raw?se=2025-04-04T03%3A43%3A27Z&sp=r&sv=2024-08-04&sr=b&scid=96fdb8da-9d2b-51fd-9fd4-3c53b837562a&skoid=ae70be19-8043-4428-a990-27c58b478304&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skt=2025-04-03T19%3A59%3A27Z&ske=2025-04-04T19%3A59%3A27Z&sks=b&skv=2024-08-04&sig=dCLSsXjKcpEQpq3nCNCKeFboutY/H64V6R39VPJLygg%3D)\n"
0:"[下载⏬](https://sdmntprsouthcentralus.oaiusercontent.com/files/00000000-bac4-51f7-9ad7-e54d85db22be/raw?se=2025-04-04T03%3A43%3A27Z&sp=r&sv=2024-08-04&sr=b&scid=96fdb8da-9d2b-51fd-9fd4-3c53b837562a&skoid=ae70be19-8043-4428-a990-27c58b478304&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skt=2025-04-03T19%3A59%3A27Z&ske=2025-04-04T19%3A59%3A27Z&sks=b&skv=2024-08-04&sig=dCLSsXjKcpEQpq3nCNCKeFboutY/H64V6R39VPJLygg%3D)\n\n"
e:{"finishReason":"stop","usage":{"promptTokens":16,"completionTokens":4637},"isContinued":false}
d:{"finishReason":"stop","usage":{"promptTokens":16,"completionTokens":4637}}
```

## streamText 回调处理流程

### 1. onFinish 回调
根据 [Vercel AI SDK 文档](https://sdk.vercel.ai/docs/ai-sdk-core/generating-text#onfinish-callback)，onFinish 回调会在流结束时触发，包含以下参数：
- text: 生成的完整文本
- finishReason: 结束原因
- usage: 使用情况
- response: 完整响应

```typescript
streamText({
  model: myProvider.languageModel(modelId),
  messages: promptMessages,
  maxSteps: 5,
  maxTokens: 16000,
  experimental_transform: smoothStream({ chunking: "word" }),
  onFinish: async ({ text, finishReason, usage, response }) => {
    try {
      // 1. 提取并验证图片URL
      const { isValid, error, url } = await extractAndValidateImageUrl(text);

      if (url) {
        // 2. 生成成功，扣除积分
        const pointsResult = await deductPoints({
          userId,
          points: creditCost,
        });

        // 3. 更新历史记录
        await updateHistory({
          historyId: history.id,
          status: true,
          resultUrl: url,
          pointsConsumption: {
            oldBalance: pointsResult.oldBalance,
            newBalance: pointsResult.newBalance,
          },
        });

        logger.info('[DRAW_SUCCESS]', {
          historyId: history.id,
          userId,
          imageUrl: url,
          usage,
          finishReason,
        });
      } else {
        // 4. 生成失败，记录错误
        await updateHistory({
          historyId: history.id,
          status: false,
          error: error || 'No image URL in response',
        });

        logger.error('[DRAW_FAILED]', new Error(error || 'No image URL in response'), {
          historyId: history.id,
          userId,
          usage,
          finishReason,
        });
      }
    } catch (error) {
      // 5. 处理错误
      logger.error('[DRAW_COMPLETION_ERROR]', error as Error, {
        historyId: history.id,
        userId,
        usage,
        finishReason,
      });

      await updateHistory({
        historyId: history.id,
        status: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      });
    }
  },
});
```

### 2. 错误处理流程

#### 2.1 onError 回调
处理流式传输过程中的错误：

```typescript
onError: async (error) => {
  logger.error('[DRAW_STREAM_ERROR]', error as Error, {
    historyId: history.id,
    userId,
  });

  await updateHistory({
    historyId: history.id,
    status: false,
    error: error instanceof Error ? error.message : 'Stream error occurred',
  });
}
```

### 3. 状态检查清单

#### 3.1 成功状态
- [x] 成功提取图片URL
- [x] 扣除积分
- [x] 更新历史记录状态为成功
- [x] 记录图片URL
- [x] 记录积分变动
- [x] 记录使用情况和结束原因

#### 3.2 失败状态
- [x] 更新历史记录状态为失败
- [x] 记录具体错误信息
- [x] 不扣除积分
- [x] 记录使用情况和结束原因

### 4. 日志记录规范

#### 4.1 成功日志
```typescript
logger.info('[DRAW_SUCCESS]', {
  historyId: string;
  userId: string;
  imageUrl: string;
  usage: {
    completionTokens: number;
    promptTokens: number;
    totalTokens: number;
  };
  finishReason: string;
});
```

#### 4.2 失败日志
```typescript
logger.error('[DRAW_FAILED]', error, {
  historyId: string;
  userId: string;
  usage?: {
    completionTokens: number;
    promptTokens: number;
    totalTokens: number;
  };
  finishReason?: string;
});
```

### 5. 优化建议

1. **错误分类优化**
   - [ ] 细化错误类型（URL提取失败、验证失败等）
   - [ ] 为不同错误类型设置不同的处理策略

2. **性能监控**
   - [ ] 记录完整的生成时间
   - [ ] 监控token使用情况
   - [ ] 记录重试次数和失败原因

3. **用户体验**
   - [ ] 为不同的失败情况提供清晰的错误提示
   - [ ] 在流式传输过程中提供进度反馈

## Image Validation
The system now supports WebP image format in addition to JPG, JPEG, PNG, and GIF. Image validation includes:
- URL format validation
- Content type verification
- WebP format detection
- Timeout handling (default 5 seconds)

### WebP Support
WebP images are now fully supported and are treated as free (0 points) for generation. The system automatically detects WebP format through URL extension using the `validateWebPImage` function in `lib/draw/image-validator.ts`.

### Error Handling
The system now provides more detailed error messages for:
- Invalid image URLs
- Unreachable images
- Invalid content types
- Timeout errors
- Format validation errors

## Points System
The points system has been enhanced with:
- Atomic transactions for points deduction
- Better balance tracking
- Support for zero-point deductions
- Improved error handling
- Detailed logging

### Points Deduction Process
1. Check user's current balance
2. Validate sufficient points
3. Perform atomic deduction
4. Record old and new balances
5. Log transaction details

### 积分扣除逻辑检查结果

经过代码审查，确认以下积分扣除逻辑已正确实现：

1. **成功生成图片时的积分扣除**：
   ```typescript
   if (url) {
     const pointsUsed = isWebp ? 0 : creditCost;

     // 生成成功，扣除积分
     const pointsResult = await deductPoints({
       userId,
       points: pointsUsed,
     });

     // 更新历史记录
     const updateitems = {
       historyId: history.id,
       status: true,
       resultUrl: url,
       originalUrl,
       isWebp,
       pointsUsed: isWebp ? pointsUsed : undefined,
       pointsConsumption: {
         oldBalance: pointsResult.oldBalance,
         newBalance: pointsResult.newBalance,
       },
     };

     await updateHistory(updateitems);
   }
   ```

2. **生成失败时不扣除积分**：
   ```typescript
   else {
     // 生成失败，记录错误
     await updateHistory({
       historyId: history.id,
       status: false,
       pointsUsed: 0,
       error: error || 'No image URL in response',
     });
   }
   ```

3. **WebP图片不扣除积分**：
   - 当生成的图片是WebP格式时，设置 `pointsUsed = 0`
   - WebP格式通过URL后缀判断：`isWebp: validateWebPImage(url)`

4. **错误处理不扣除积分**：
   - 所有错误处理路径都正确设置了 `status: false`
   - 没有在错误处理中调用 `deductPoints`
   - 历史记录正确更新了错误信息

5. **积分扣除的原子性**：
   - 使用数据库事务确保积分扣除的原子性
   - 记录旧余额和新余额，用于审计和历史记录

