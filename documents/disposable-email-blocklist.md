# 一次性邮箱黑名单功能

## 功能概述

一次性邮箱黑名单功能是黑名单系统的扩展，用于自动识别和限制使用一次性邮箱（临时邮箱）注册的用户。当用户使用一次性邮箱注册时，系统会自动将其标记为黑名单用户，并只给予最低限度的初始积分奖励。

## 实现方式

系统通过两种方式检查邮箱是否为一次性邮箱：

1. **静态文件检查**：系统首先检查邮箱域名是否存在于预定义的一次性邮箱域名列表中（`disposable_email_blocklist.conf`）。
2. **数据库规则检查**：如果邮箱域名不在静态列表中，系统会继续检查数据库中定义的黑名单规则。

## 文件说明

### 一次性邮箱黑名单文件

文件位置：`lib/blocklist/disposable_email_blocklist.conf`

该文件包含已知的一次性邮箱域名列表，每行一个域名。例如：

```
mailinator.com
10minutemail.com
guerrillamail.com
```

系统会在启动时加载此文件，并将域名列表缓存在内存中，以提高检查效率。

## 工作流程

1. 用户注册时，系统调用 `checkEmailBlocklist` 函数检查用户邮箱。
2. 函数首先调用 `isDisposableEmail` 检查邮箱域名是否在一次性邮箱黑名单文件中。
3. 如果邮箱域名在黑名单中，函数立即返回 `true`，表示该邮箱应被限制。
4. 如果邮箱域名不在黑名单中，函数继续检查数据库中的黑名单规则。
5. 如果邮箱匹配任何数据库规则，函数返回 `true`；否则返回 `false`。
6. 如果函数返回 `true`，用户将只获得 1 积分的初始奖励，而不是标准的 300 积分。

## 维护与更新

### 更新黑名单文件

要更新一次性邮箱黑名单，只需编辑 `disposable_email_blocklist.conf` 文件，添加或删除域名。每行一个域名，不需要添加任何额外的格式或标记。

系统会在下次启动时自动加载更新后的列表。如果需要在不重启系统的情况下更新列表，可以通过清除内存缓存来实现：

```typescript
// 清除缓存，强制系统重新读取文件
disposableEmailDomains = null;
```

### 添加数据库规则

对于需要更复杂匹配逻辑的情况，可以通过管理界面添加数据库黑名单规则。这些规则支持正则表达式，可以实现更灵活的匹配。

## 性能考虑

- 静态文件检查使用 `Set` 数据结构，查找操作的时间复杂度为 O(1)。
- 文件内容在首次访问时加载到内存中，并在后续请求中重用，避免重复读取文件。
- 如果黑名单文件很大，初始加载可能会占用一定内存，但通常不会造成明显的性能问题。

## 测试

可以使用以下方法测试一次性邮箱黑名单功能：

1. 使用黑名单中的域名（如 `<EMAIL>`）注册新用户，验证用户只获得 1 积分。
2. 使用正常域名（如 `<EMAIL>`）注册新用户，验证用户获得 300 积分。
3. 添加新的数据库黑名单规则，并测试匹配该规则的邮箱是否被正确识别。

## 日志

系统会记录以下日志信息：

- 黑名单文件加载成功或失败
- 邮箱匹配黑名单的情况
- 正则表达式匹配错误

可以通过查看日志来监控和排查黑名单功能的问题。
