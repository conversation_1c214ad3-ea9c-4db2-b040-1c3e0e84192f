# Exchange Points System

This document outlines the design and implementation of the exchange points system, which allows administrators to add permanent points to user wallets.

## Overview

The exchange points system provides a way for administrators to:
- Add points to a user's wallet
- Specify the type of points addition (refund, gift, award, affiliate, etc.)
- Optionally associate the points with a history or order
- Track all point exchanges in the orders table

## API Endpoint

### POST /api/admin/exchange/points

This endpoint allows administrators to add points to a user's wallet.

#### Request Body

```json
{
  "userId": "string",         // Required: The Clerk ID of the user
  "points": "number",         // Required: The number of points to add
  "type": "string",           // Required: Type of exchange (refund, gift, award, affiliate, etc.)
  "sendBy": "string",         // Optional: Who sent the points (defaults to "system")
  "historyId": "string",      // Optional: Associated history ID
  "orderId": "string",        // Optional: Associated order ID
  "description": "string",    // Optional: Description of the exchange
  "note": "string"            // Optional: Additional notes (stored in extra)
}
```

#### Response

```json
{
  "success": true,
  "order": {
    "id": "string",
    "userId": "string",
    "buyerId": "string",
    "type": "credit",
    "amount": "number",
    "description": "string",
    "status": "SUCCESS",
    "extra": {
      "exchangeType": "string",
      "historyId": "string",
      "orderId": "string",
      "note": "string"
    },
    "createdAt": "date",
    "updatedAt": "date"
  },
  "wallet": {
    "id": "string",
    "userId": "string",
    "permanentPoints": "number",
    "createdAt": "date",
    "updatedAt": "date"
  }
}
```

#### Error Responses

- 400 Bad Request: Missing required fields or invalid data
- 401 Unauthorized: User is not authenticated
- 403 Forbidden: User is not an admin
- 404 Not Found: User not found
- 500 Internal Server Error: Server error

## Admin Interface

The admin interface for exchanging points is located at `/admin/exchange`. It provides a UI for administrators to:

1. Search for users by email or user ID
2. Select the type of exchange
3. Enter the number of points to add
4. Optionally associate with a history or order
5. Add a description and notes
6. Submit the exchange

### User Flow

1. Admin navigates to `/admin/exchange`
2. Admin clicks "Add Points" button
3. A modal opens with a form
4. Admin fills out the form:
   - Selects a user (with search functionality)
   - Selects exchange type
   - Enters points amount
   - Optionally selects associated history or order
   - Adds description and notes
5. Admin clicks "Submit"
6. System creates an order and updates the user's wallet
7. Modal closes and the exchange list refreshes

## Database Changes

No schema changes are required. The feature will use:

- `wallets` table: To update the user's permanent points
- `orders` table: To record the exchange transaction
  - The `extra` field will store additional information:
    - `exchangeType`: Type of exchange
    - `historyId`: Associated history ID (if any)
    - `orderId`: Associated order ID (if any)
    - `note`: Additional notes

## Implementation Details

1. The API endpoint will:
   - Validate the request
   - Create an order record
   - Update the user's wallet
   - Return the updated order and wallet

2. The admin interface will:
   - Provide a form for adding points
   - Allow searching for users
   - Allow selecting exchange type
   - Allow associating with history or order
   - Submit the form to the API endpoint
