# History Deletion Feature

## Overview

This document outlines the design and implementation of the history deletion feature. The feature allows users to delete their generation history records while maintaining data integrity and proper cleanup of associated resources.

## Design Approach

Instead of permanently deleting history records from the database, we use a soft-delete approach:

1. Add an `archived` boolean field to the `histories` table (default: `false`)
2. When a user deletes a history, mark it as `archived = true`
3. Filter out archived histories from all queries
4. Clean up associated resources:
   - Delete image files from Cloudflare R2 storage
   - Update associated shares to be private

## Implementation Details

### 1. Database Schema Update

Add the `archived` field to the `histories` table:

```typescript
export const histories = pgTable("histories", {
  // existing fields...
  archived: boolean("archived").default(false).notNull(),
  // other fields...
});
```

### 2. API Endpoint

Implement a DELETE endpoint at `/api/history/[id]` that:

1. Verifies the user owns the history record
2. Sets `archived = true` on the history record
3. If `backupStatus` is "SUCCESS", deletes the corresponding files from R2
4. If there's an associated share, sets `isPublic = false` and `allowFork = false`
5. Clears the history's `resultUrl` and `originalUrl` fields

### 3. R2 File Deletion

When a history is archived and its `backupStatus` is "SUCCESS", delete the corresponding files from R2:

1. Construct the R2 key path: `${userId}/${historyId}/*`
2. Use the AWS S3 SDK to delete the object

### 4. Query Modifications

Update all queries that fetch histories to include a condition to exclude archived records:

```typescript
// Example query modification
const histories = await db.query.histories.findMany({
  where: and(
    eq(histories.userId, userId),
    eq(histories.archived, false) // Add this condition to all queries
  ),
  // other query options...
});
```

## Security Considerations

- Only allow users to delete their own histories
- Verify authentication for all deletion operations
- Log deletion operations for audit purposes

## Future Enhancements

- Add a "trash" view where users can see their archived histories
- Implement a restore function to unarchive histories
- Add a permanent deletion job that removes very old archived histories
