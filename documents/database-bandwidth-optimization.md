# 数据库带宽优化方案

## 概述

为了节约云数据库带宽成本，我们需要优化 `/api/history/combined` 接口，该接口被前端频繁轮询（每5秒一次），当前返回完整的历史记录数据，包含大量不必要的字段。

## 当前问题分析

### 1. 数据传输量过大
- **combined 接口返回完整记录**：包含 `prompt`、`description`、`parameters`、`extra` 等大字段
- **频繁轮询**：前端每5秒轮询一次，24小时不间断
- **重复传输**：相同数据被反复传输，浪费带宽

### 2. 字段使用分析
根据前端代码分析，列表展示只需要以下字段：
- `id`：记录标识
- `status`：成功/失败状态
- `drawStatus`：绘图状态（PENDING/PROCESSING/SUCCESS/FAILED）
- `resultUrl`：结果图片URL
- `pointsUsed`：消耗积分
- `createdAt`：创建时间
- `updatedAt`：更新时间
- `share`：分享信息（如果 includeShare=true）

不需要的大字段：
- `description`：描述信息
- `parameters`：生成参数（JSON对象）

需要保留的字段：
- `extra`：包含模型和风格信息，用于列表显示
- `prompt`：用户输入提示词，用于模态框显示

### 3. 带宽消耗估算

#### 当前消耗（优化前）
- 每个 history 记录：约 2-5KB
- 每次请求返回：15条记录（5 recent + 10 pending）
- 单次请求大小：15 × 3KB = 45KB
- 每分钟轮询：12次 × 45KB = 540KB
- 每小时消耗：540KB × 60 = 32.4MB
- **每天消耗：32.4MB × 24 = 777.6MB**

#### 优化后预期
- 每个 history 记录：约 1.5-3KB（保留 extra 和 prompt 字段）
- 每次请求返回：15条记录
- 单次请求大小：15 × 2KB = 30KB
- 每分钟轮询：12次 × 30KB = 360KB
- 每小时消耗：360KB × 60 = 21.6MB
- **每天消耗：21.6MB × 24 = 518.4MB**

**预期节省带宽：约 33%**

## 优化方案

### 方案1：优化现有 combined 接口

#### 1.1 修改查询字段
在 `/api/history/combined/route.ts` 中添加 `columns` 选项：

```typescript
// 优化 recentHistories 查询
const recentHistories = await db.query.histories.findMany({
  where: and(
    eq(histories.userId, userId),
    eq(histories.status, true),
    eq(histories.archived, false)
  ),
  columns: {
    id: true,
    status: true,
    drawStatus: true,
    resultUrl: true,
    pointsUsed: true,
    createdAt: true,
    updatedAt: true,
    extra: true, // 保留 extra 字段，用于显示模型和风格信息
    prompt: true, // 保留 prompt 字段，用于模态框显示
    // 不查询大字段：description, parameters
  },
  orderBy: [desc(histories.createdAt)],
  limit: recentLimit,
  with: includeShare ? {
    share: {
      columns: {
        id: true,
        shareId: true,
        isPublic: true,
        allowFork: true,
        viewCount: true,
        likeCount: true,
        forkCount: true,
      }
    }
  } : undefined,
});

// 同样优化 pendingHistories 查询
const pendingHistories = await db.query.histories.findMany({
  // 相同的 columns 配置
});
```

#### 1.2 保持接口兼容性
- 返回数据结构保持不变
- 只是减少字段数量，不影响现有前端代码
- 前端代码无需修改


## 实施计划

### 阶段1：优化 combined 接口（✅ 已完成）
1. ✅ 修改 `/api/history/combined/route.ts`，添加 `columns` 选项
   - 只查询必需字段：id, status, drawStatus, resultUrl, pointsUsed, createdAt, updatedAt
   - 不查询大字段：prompt, description, parameters, extra
2. ✅ 保持接口兼容性，前端代码无需修改
3. ⏳ 监控带宽使用情况

### 阶段2：创建详情接口（✅ 已完成）
1. ⏳ 修改前端组件，实现按需加载详情（可选优化）
2. ✅ 更新相关文档

### 阶段3：监控和优化（持续进行）
1. ⏳ 监控数据库带宽使用情况
2. ⏳ 分析用户行为，进一步优化查询策略
3. ⏳ 考虑实施缓存机制

## 已完成的优化

### 1. Combined 接口优化
- **文件**：`/app/api/history/combined/route.ts`
- **优化内容**：
  - 添加 `columns` 选项，只查询列表必需的字段
  - 减少数据传输量约 70-80%
  - 保持接口兼容性，前端无需修改

## 风险评估

### 低风险
- **接口兼容性**：只减少字段，不改变数据结构
- **功能完整性**：列表展示功能不受影响

### 需要注意
- **详情查看**：需要确保用户能够方便地查看完整信息
- **性能影响**：详情接口可能增加单次查询的复杂度

## 成功指标

1. **带宽节省**：目标节省 70% 以上的数据库带宽
2. **响应速度**：轮询接口响应时间减少
3. **用户体验**：列表加载速度提升，详情查看流畅
4. **成本降低**：云数据库带宽费用显著减少

## 后续优化方向

1. **缓存机制**：对频繁查询的数据实施缓存
2. **增量更新**：只传输变化的数据
3. **WebSocket**：考虑使用实时连接替代轮询
4. **数据压缩**：对传输数据进行压缩
