# Image Backup and Migration System

## Overview

This document outlines the design and implementation of an image backup and migration system for the Next.js + Neon + Drizzle project. The system will:

1. Query the `histories` table within a specified time range
2. Download images from `extra.originalUrl` or `resultUrl`
3. Upload images to Cloudflare R2 bucket
4. Update the `histories` table with the new R2 URLs
5. Update associated `shares` if they exist
6. Log the operation results

## System Components

### 1. R2 Storage Configuration

Environment variables required:
- `R2_ACCOUNT_ID`: Cloudflare account ID
- `R2_ACCESS_KEY_ID`: R2 access key
- `R2_SECRET_ACCESS_KEY`: R2 secret key
- `R2_BUCKET_NAME`: R2 bucket name
- `R2_PUBLIC_URL_PREFIX`: Public URL prefix for accessing stored files

#### How to Obtain R2 Configuration Values

##### 1. Obtaining R2_ACCOUNT_ID

This is your Cloudflare account ID:

1. Log in to the Cloudflare dashboard (https://dash.cloudflare.com/)
2. After logging in, you can see your account ID in the account information area on the right or in the URL
3. The account ID is typically a string of alphanumeric characters, e.g., `a1b2c3d4e5f6g7h8i9j0`
4. You can also find the "Account ID" in the bottom right corner of the Cloudflare dashboard

##### 2. Obtaining R2_ACCESS_KEY_ID and R2_SECRET_ACCESS_KEY

These two values require creating an R2 API token:

1. Log in to the Cloudflare dashboard
2. In the left navigation bar, click "R2"
3. Click "Manage R2 API Tokens"
4. Click "Create API Token"
5. Select "Create S3 Auth Token"
6. Set permissions:
   - Select the buckets you need to access (you can choose specific buckets or all buckets)
   - Set the permission level (read, write, or both)
7. Click "Create Token"
8. After successful creation, the system will display:
   - Access Key ID: This is your `R2_ACCESS_KEY_ID`
   - Secret Access Key: This is your `R2_SECRET_ACCESS_KEY`

**Important Note**: The Secret Access Key will only be displayed once, so save it immediately. If lost, you'll need to create a new token.

##### 3. Obtaining R2_BUCKET_NAME

This is the name of the bucket you create in R2:

1. Log in to the Cloudflare dashboard
2. In the left navigation bar, click "R2"
3. If you already have buckets, use the name of the existing bucket
4. If you need to create a new bucket:
   - Click "Create bucket"
   - Enter a bucket name (this will be your `R2_BUCKET_NAME`)
   - Select the region for the bucket (optional)
   - Click "Create"

##### 4. Setting R2_PUBLIC_URL_PREFIX

This is the public URL prefix for accessing files in your R2 bucket. There are two ways to set it up:

###### Option 1: Using Cloudflare R2 Public Access

1. Log in to the Cloudflare dashboard
2. In the left navigation bar, click "R2"
3. Select your bucket
4. Click "Settings"
5. In the "Public Access" section:
   - Enable "Public Access"
   - The system will generate a public URL in the format `https://pub-[random].r2.dev`
   - This URL is your `R2_PUBLIC_URL_PREFIX`

###### Option 2: Using a Custom Domain (Recommended)

1. Log in to the Cloudflare dashboard
2. In the left navigation bar, click "R2"
3. Select your bucket
4. Click "Settings"
5. In the "Custom Domains" section:
   - Click "Add Custom Domain"
   - Enter the domain you want to use, e.g., `storage.vibany.com`
   - Follow the instructions to complete the DNS configuration
   - After configuration is complete, this custom domain is your `R2_PUBLIC_URL_PREFIX`

Using a custom domain has the advantage of making URLs more concise, professional, and easier to remember and manage.

#### Configuration Example

After completing the above steps, your configuration in the `.env.local` file should look similar to:

```
# Cloudflare R2 Storage
R2_ACCOUNT_ID=a1b2c3d4e5f6g7h8i9j0
R2_ACCESS_KEY_ID=1a2b3c4d5e6f7g8h9i0j
R2_SECRET_ACCESS_KEY=abcdefghijklmnopqrstuvwxyz1234567890ABCDEF
R2_BUCKET_NAME=vibany-images
R2_PUBLIC_URL_PREFIX=https://storage.vibany.com
```

### 2. R2 Client Library

Create a utility library for R2 operations:
- `@lib/storage/r2-client.ts`: Handles connection to R2 and provides methods for upload/download

### 3. Image Backup Service

Create a service to handle the backup process:
- `@lib/backup/image-backup.ts`: Core functionality for backing up images

### 4. API Endpoints

Create API endpoints to trigger the backup process:
- `@app/api/admin/backup/image/route.ts`: Admin endpoint for manual backup
- `@app/api/public/backup/route.ts`: Public endpoint for automated daily backup

### 5. Admin UI

Create an admin page for manual backup:
- `@app/admin/backup/page.tsx`: Admin interface for backup operations

### 6. Cron Job

Configure a cron job to run daily backups:
- Update `vercel.json` to add a cron job that runs at 5:00 AM daily

## Implementation Details

### 1. R2 Client Implementation

The R2 client will:
- Connect to Cloudflare R2 using the AWS S3 SDK
- Provide methods for uploading files to R2
- Generate public URLs for uploaded files

### 2. Image Backup Process

The backup process will:
1. Query histories within the specified time range
2. For each history:
   - Download the image from `extra.originalUrl` or `resultUrl`
   - Upload the image to R2 with path `userId/historyId/<filename>`
   - Update the history record with the new R2 URL
   - If a share exists for the history, update the share's `imageUrl`
3. Log the operation results

### 3. API Endpoints

#### Admin Backup Endpoint
- `POST /api/admin/backup/image`
- Parameters: `startDate`, `endDate`
- Returns: Operation results

#### Public Backup Endpoint
- `GET /api/public/backup`
- No parameters (processes previous day's histories)
- Returns: Operation results

### 4. Admin UI

The admin backup page will:
- Allow selecting a date range
- Trigger the backup process
- Display operation results
- Show logs of previous backup operations

### 5. Cron Job

The cron job will:
- Run daily at 5:00 AM
- Call the public backup endpoint
- Process the previous day's histories

## Data Model Updates

No schema changes are required, but we'll update the following fields:

1. In `histories` table:
   - Store the original URL in `extra.originalUrl` if not already present
   - Replace `resultUrl` with the R2 URL

2. In `shares` table:
   - Replace `imageUrl` with the R2 URL from the associated history

## Error Handling

The system will:
- Handle network errors during download/upload
- Log all errors with details
- Continue processing other images if one fails
- Provide detailed error reports

## Logging

Operation logs will:
- Be stored in the R2 bucket under the `logs` folder
- Follow the naming pattern `backup-yyyy-mm-dd-HH-m-s.log`
- Include details of processed images, successes, and failures

## Todo List

1. Environment Variables
   - [x] Add R2 configuration to `.env.example`

2. R2 Client
   - [x] Create R2 client utility
   - [x] Implement upload functionality
   - [x] Implement URL generation

3. Backup Service
   - [x] Create image backup core functionality
   - [x] Implement history querying
   - [x] Implement image download/upload
   - [x] Implement history/share updates
   - [x] Implement logging

4. API Endpoints
   - [x] Create admin backup endpoint
   - [x] Create public backup endpoint

5. Admin UI
   - [x] Create backup admin page
   - [x] Implement date range selection using DatePickerWithRange component (see [UI Components Documentation](./ui-components.md) for proper usage)
   - [x] Implement backup triggering
   - [x] Display operation results

6. Cron Job
   - [x] Update `vercel.json` with daily cron job

7. Testing
   - [ ] Test R2 client
   - [ ] Test backup process
   - [ ] Test API endpoints
   - [ ] Test admin UI
   - [ ] Test cron job
