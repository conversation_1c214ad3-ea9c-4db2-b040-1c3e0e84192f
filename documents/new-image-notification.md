# 新图片通知功能

## 功能概述

当用户生成新图片后，系统会自动弹出一个模态框通知用户有新作品完成。这个功能通过全局状态管理实现，确保在任何地方都能正确显示通知。

## 实现原理

### 状态管理

在 `@store/draw/index.ts` 中添加了新的状态和方法：

```typescript
// 新图片通知状态
newImageNotificationOpen: boolean
latestGeneratedImageUrl: string | null

// 新图片通知方法
showNewImageNotification: (imageUrl: string) => void
closeNewImageNotification: () => void
```

### 触发点

新图片通知有两个主要触发点：

1. **图片生成完成时**：在 `DrawPreview` 组件中，当从 API 响应中提取到新图片 URL 时触发通知
   ```typescript
   // 显示新图片通知
   const { showNewImageNotification } = useDrawStore.getState();
   showNewImageNotification(imageUrl);
   ```

2. **历史记录更新时**：在 `fetchCombinedHistories` 方法中，当检测到新的历史记录时触发通知
   ```typescript
   // 检查是否有新的成功生成的图片
   if (newSuccessfulHistories.length > 0) {
     const latestHistory = newSuccessfulHistories[0];
     if (latestHistory.resultUrl) {
       // 显示新图片通知
       get().showNewImageNotification(latestHistory.resultUrl);
     }
   }
   ```

### 通知显示

使用 `DrawResultModal` 组件显示通知，该组件支持两种模式：

1. **normal 模式**：用于在生成过程结束后立即显示结果
2. **notification 模式**：用于在检测到新的历史记录时显示通知

```typescript
<DrawResultModal mode="notification" />
```

### 通知关闭

当用户关闭通知时，会执行以下操作：

1. 重置通知状态
   ```typescript
   closeNewImageNotification();
   ```

2. 刷新历史记录列表，确保显示最新的数据
   ```typescript
   fetchCombinedHistories();
   ```

## 使用方法

要在代码中触发新图片通知，只需调用 `showNewImageNotification` 方法：

```typescript
const { showNewImageNotification } = useDrawStore.getState();
showNewImageNotification(imageUrl);
```

## 优势

1. **全局状态管理**：使用 Zustand 进行状态管理，确保在任何地方都能正确显示通知
2. **多触发点**：在多个地方检测新图片，包括 API 轮询和图片提取逻辑，确保不会漏掉通知
3. **统一的通知界面**：使用同一个模态框组件，保持界面一致性
4. **自动刷新**：通知关闭后自动刷新列表，确保显示最新的数据
