# 邀请奖励批量兑换功能

## 概述

本文档描述了邀请奖励批量兑换功能的实现，主要包括以下内容：

1. 用户可以在个人中心（/settings/invitation）批量兑换积分奖励
2. 管理员可以在管理后台（/admin/invitations）批量兑换积分或现金奖励
3. 批量兑换在单个数据库事务中处理，确保数据一致性
4. 批量兑换返回详细的处理结果，包括成功和失败的记录

## 技术实现

### 批量兑换流程

1. 用户/管理员在邀请记录列表中选择多条记录
2. 点击"批量兑换"按钮
3. 系统显示确认对话框，确认要兑换的奖励类型和数量
4. 用户/管理员确认后，系统调用相应的批量兑换 API
5. API 验证请求并处理批量兑换
6. 返回处理结果，包括成功和失败的记录详情

### 批量兑换函数

#### 积分批量兑换

`redeemInvitationPointsBatch` 函数的主要功能：

- 接收多个邀请使用记录 ID 和操作人 ID
- 在单个数据库事务中处理所有记录
- 为每条记录创建相应的订单
- 一次性更新钱包积分（累加所有成功记录的积分）
- 返回详细的处理结果

#### 现金批量兑换（仅管理员）

`redeemInvitationCashBatch` 函数的主要功能：

- 接收多个邀请使用记录 ID、操作人 ID 和可选备注
- 在单个数据库事务中处理所有记录
- 更新所有记录的状态
- 返回详细的处理结果

### 数据处理

1. 批量验证
   - 检查所有记录是否存在
   - 检查记录状态是否为 `ready`
   - 检查操作权限
   - 检查是否有积分/现金可兑换

2. 事务处理
   - 使用单个数据库事务处理所有记录
   - 再次检查记录状态（防止并发操作）
   - 对于积分兑换，累加所有积分并一次性更新钱包
   - 创建相应的订单记录
   - 更新所有邀请记录状态

3. 结果返回
   - 返回成功处理的记录列表
   - 返回失败的记录及原因
   - 返回更新后的钱包信息（积分兑换）

## API 设计

### 用户批量兑换积分 API

**路径**：`POST /api/invitation_usages/redeem-batch`

**权限**：已登录用户

**请求体**：
```json
{
  "usage_ids": ["id1", "id2", "id3", ...]
}
```

**响应**：
```json
{
  "success": true,
  "results": {
    "successful": [
      {
        "id": "id1",
        "pointsAwarded": 100,
        "order": { ... }
      },
      ...
    ],
    "failed": [
      {
        "id": "id2",
        "error": "该记录不可兑换"
      },
      ...
    ]
  },
  "wallet": {
    "permanentPoints": 5000
  },
  "totalPointsAwarded": 300
}
```

### 管理员批量兑换 API

**路径**：`POST /api/admin/invitation_usages/redeem-batch`

**权限**：管理员

**请求体**：
```json
{
  "usage_ids": ["id1", "id2", "id3", ...],
  "redeem_type": "points" | "cash",
  "note": "可选备注"
}
```

**响应**：与用户批量兑换 API 类似，但包含更多管理员相关信息。

## 前端实现

### 用户批量兑换界面

1. 在 `/settings/invitation` 页面添加记录选择功能
2. 添加批量兑换按钮
3. 实现确认对话框
4. 显示处理结果

### 管理员批量兑换界面

1. 在 `/admin/invitations` 页面添加记录选择功能
2. 添加批量兑换按钮，支持选择兑换类型（积分/现金）
3. 实现确认对话框，包含兑换类型选择和备注输入
4. 显示处理结果

## 注意事项

1. 权限控制
   - 普通用户只能兑换自己的邀请记录
   - 普通用户只能兑换积分
   - 管理员可以兑换任何用户的邀请记录
   - 管理员可以选择兑换积分或现金

2. 错误处理
   - 批量处理中的错误不应影响其他记录的处理
   - 详细记录每条记录的处理结果
   - 在事务中处理所有记录，确保数据一致性

3. 性能考虑
   - 限制单次批量处理的记录数量（建议不超过 50 条）
   - 使用单个数据库事务提高性能
   - 对于积分兑换，一次性更新钱包积分，避免多次更新

4. 安全考虑
   - 使用事务隔离防止并发操作导致的数据不一致
   - 在事务内再次检查记录状态，防止并发兑换
   - 按邀请人分组处理记录，减少锁竞争
   - 详细记录操作日志，便于安全审计和问题排查
   - 更多安全考虑请参阅 [邀请奖励系统安全考虑](./invitation-security.md) 文档
