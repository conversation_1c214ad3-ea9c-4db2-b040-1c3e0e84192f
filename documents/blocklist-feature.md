# 黑名单功能设计文档

## 功能概述

黑名单功能允许管理员创建规则来限制特定用户的奖励积分。当用户注册时，系统会检查用户的电子邮件地址是否匹配任何黑名单规则。如果匹配，用户将只获得1积分的奖励，而不是标准的300积分。

## 数据库设计

### 新表：blocklists

```sql
CREATE TABLE "blocklists" (
  "id" text PRIMARY KEY NOT NULL,
  "type" text NOT NULL,
  "pattern" text NOT NULL,
  "enabled" boolean DEFAULT true NOT NULL,
  "description" text,
  "createdBy" text REFERENCES "users"("clerk_id"),
  "extra" jsonb DEFAULT '{}'::jsonb NOT NULL,
  "createdAt" timestamp DEFAULT now() NOT NULL,
  "updatedAt" timestamp DEFAULT now() NOT NULL
);

CREATE INDEX "blocklists_type_idx" ON "blocklists" ("type");
CREATE INDEX "blocklists_enabled_idx" ON "blocklists" ("enabled");
```

### 字段说明

- `id`: 主键，使用 nanoid 生成
- `type`: 黑名单类型，例如 "email"、"ip" 等
- `pattern`: 匹配规则，可以是正则表达式或简单的字符串模式
- `enabled`: 是否启用该规则
- `description`: 规则描述，可选
- `createdBy`: 创建该规则的管理员ID
- `extra`: 额外信息，JSON格式
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

## 常量定义

在 `/constants/blocklist.ts` 中定义相关常量：

```typescript
// 黑名单类型
export const BLOCKLIST_TYPES = {
  EMAIL: "email",
  IP: "ip",
  KEYWORD: "keyword",
} as const;

// 默认新用户奖励积分
export const DEFAULT_NEW_USER_POINTS = 300;

// 黑名单用户奖励积分
export const BLOCKLISTED_USER_POINTS = 1;
```

## API 设计

### 管理员 API

#### 1. 获取黑名单列表

- 路径: `/api/admin/blocklists`
- 方法: `GET`
- 查询参数:
  - `type`: 黑名单类型（可选）
  - `enabled`: 是否启用（可选）
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认10）
- 响应:
  ```json
  {
    "blocklists": [
      {
        "id": "...",
        "type": "email",
        "pattern": ".*@spam\\.com",
        "enabled": true,
        "description": "屏蔽垃圾邮件域名",
        "createdBy": "user_id",
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 10
  }
  ```

#### 2. 创建黑名单规则

- 路径: `/api/admin/blocklists`
- 方法: `POST`
- 请求体:
  ```json
  {
    "type": "email",
    "pattern": ".*@spam\\.com",
    "enabled": true,
    "description": "屏蔽垃圾邮件域名"
  }
  ```
- 响应:
  ```json
  {
    "id": "...",
    "type": "email",
    "pattern": ".*@spam\\.com",
    "enabled": true,
    "description": "屏蔽垃圾邮件域名",
    "createdBy": "user_id",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
  ```

#### 3. 获取单个黑名单规则

- 路径: `/api/admin/blocklists/[id]`
- 方法: `GET`
- 响应:
  ```json
  {
    "id": "...",
    "type": "email",
    "pattern": ".*@spam\\.com",
    "enabled": true,
    "description": "屏蔽垃圾邮件域名",
    "createdBy": "user_id",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
  ```

#### 4. 更新黑名单规则

- 路径: `/api/admin/blocklists/[id]`
- 方法: `PUT`
- 请求体:
  ```json
  {
    "pattern": ".*@newspam\\.com",
    "enabled": false,
    "description": "更新的描述"
  }
  ```
- 响应:
  ```json
  {
    "id": "...",
    "type": "email",
    "pattern": ".*@newspam\\.com",
    "enabled": false,
    "description": "更新的描述",
    "createdBy": "user_id",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-02T00:00:00Z"
  }
  ```

#### 5. 删除黑名单规则

- 路径: `/api/admin/blocklists/[id]`
- 方法: `DELETE`
- 响应:
  ```json
  {
    "success": true
  }
  ```

## 用户注册流程修改

在 `lib/db/user.ts` 中的 `syncUser` 函数中，在初始化用户钱包之前，添加黑名单检查逻辑：

```typescript
// 检查用户邮箱是否在黑名单中
const isBlocklisted = await checkEmailBlocklist(userData.email);
const initialPoints = isBlocklisted ? BLOCKLISTED_USER_POINTS : DEFAULT_NEW_USER_POINTS;

// 初始化用户钱包
const walletId = nanoid();
await db.insert(wallets).values({
  id: walletId,
  userId: clerkUserId,
  permanentPoints: initialPoints, // 根据黑名单状态设置初始积分
});

// 记录赠送操作
await db.insert(orders).values({
  id: nanoid(),
  userId: clerkUserId,
  buyerId: "system",
  type: "credit",
  amount: initialPoints,
  status: "SUCCESS",
  description: isBlocklisted ? "新用户注册受限赠送" : "新用户注册赠送",
  extra: {
    tradeStatus: "success",
    isBlocklisted: isBlocklisted,
  },
});
```

## 黑名单检查函数

在 `lib/blocklist/index.ts` 中实现黑名单检查函数：

```typescript
import { db } from '@/lib/db';
import { blocklists } from '@/lib/db/schema';
import { and, eq } from 'drizzle-orm';
import { BLOCKLIST_TYPES } from '@/constants/blocklist';

/**
 * 检查邮箱是否匹配黑名单规则
 */
export async function checkEmailBlocklist(email: string): Promise<boolean> {
  // 获取所有启用的邮箱黑名单规则
  const rules = await db.query.blocklists.findMany({
    where: and(
      eq(blocklists.type, BLOCKLIST_TYPES.EMAIL),
      eq(blocklists.enabled, true)
    ),
  });

  // 检查邮箱是否匹配任何规则
  for (const rule of rules) {
    try {
      const regex = new RegExp(rule.pattern, 'i'); // 不区分大小写
      if (regex.test(email)) {
        return true;
      }
    } catch (error) {
      console.error(`[BLOCKLIST_ERROR] Invalid regex pattern: ${rule.pattern}`, error);
      // 如果正则表达式无效，跳过该规则
      continue;
    }
  }

  return false;
}

/**
 * 检查文本是否匹配关键词黑名单规则
 */
export async function checkKeywordBlocklist(text: string): Promise<boolean> {
  // 获取所有启用的关键词黑名单规则
  const rules = await db.query.blocklists.findMany({
    where: and(
      eq(blocklists.type, BLOCKLIST_TYPES.KEYWORD),
      eq(blocklists.enabled, true)
    ),
  });

  // 检查文本是否匹配任何规则
  for (const rule of rules) {
    try {
      const regex = new RegExp(rule.pattern, 'i'); // 不区分大小写
      if (regex.test(text)) {
        return true;
      }
    } catch (error) {
      console.error(`[BLOCKLIST_ERROR] Invalid regex pattern: ${rule.pattern}`, error);
      // 如果正则表达式无效，跳过该规则
      continue;
    }
  }

  return false;
}
```

## 管理界面

创建以下页面：

1. `/admin/blocklists` - 黑名单规则列表页面
   - 显示所有规则，包括类型、匹配规则、描述和状态
   - 提供筛选功能（按类型和状态）
   - 启用/禁用规则的开关
   - 编辑和删除按钮

2. `/admin/blocklists/add` - 添加新规则页面
   - 表单包含类型、匹配规则、描述和启用状态字段
   - 提供正则表达式测试功能

3. `/admin/blocklists/edit/[id]` - 编辑规则页面
   - 加载现有规则数据
   - 提供与添加页面相同的表单
   - 类型字段在编辑模式下不可修改

## 实施计划

1. 创建数据库迁移文件，添加 blocklists 表
2. 在 schema.ts 中添加表定义
3. 创建常量文件 constants/blocklist.ts
4. 实现黑名单检查函数 lib/blocklist/index.ts
5. 修改用户注册流程 lib/db/user.ts
6. 实现管理员 API
7. 创建管理界面组件
8. 添加管理界面路由

## 测试计划

1. 单元测试：
   - 测试黑名单检查函数
   - 测试正则表达式匹配逻辑

2. 集成测试：
   - 测试用户注册流程
   - 测试黑名单规则的增删改查

3. 手动测试：
   - 创建黑名单规则
   - 使用匹配规则的邮箱注册新用户
   - 验证用户只获得1积分而不是300积分
