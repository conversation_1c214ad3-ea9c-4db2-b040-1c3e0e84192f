# Stripe 支付集成实现文档

## 概述

本文档详细描述了 Stripe 支付方式在系统中的集成实现。Stripe 作为一种新的支付方法，与现有的支付宝（alipay）和微信支付（wxpay）并行使用，遵循相同的业务流程和数据处理逻辑。

## 技术架构

Stripe 支付集成遵循现有支付系统的架构设计，主要包括以下组件：

1. **前端支付组件**：在 `components/payment/payment-dialog.tsx` 中实现
2. **支付 API**：在 `app/api/purchase/route.ts` 中处理支付请求
3. **支付通知处理**：在 `app/api/public/stripe/notify/route.ts` 中处理 Stripe 的 webhook 回调
4. **积分充值**：复用 `lib/wallet/recharge.ts` 中的 `rechargeUserPoints` 函数
5. **邀请奖励**：复用 `lib/invitation/index.ts` 中的 `updateInvitationUsageAfterRecharge` 函数

## 支付流程

### 1. 创建支付订单

当用户选择 Stripe 支付方式并点击支付按钮时：

1. 前端发送请求到 `/api/purchase` 端点，包含支付方式（`stripe`）和套餐 ID
2. 后端创建订单记录，生成唯一订单号
3. 调用 Stripe API 创建 Checkout 会话，设置成功和取消回调 URL
4. 返回 Stripe Checkout 会话 URL 给前端
5. 前端将用户重定向到 Stripe 支付页面

```typescript
// app/api/purchase/route.ts 中的 Stripe 支付处理
if (paymentMethod === 'stripe') {
  // 创建 Stripe checkout 会话
  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    line_items: [
      {
        price_data: {
          currency: 'cny',
          product_data: {
            name: packageInfo.name,
          },
          unit_amount: Math.round(packageInfo.price * 100), // Stripe 使用分为单位
        },
        quantity: 1,
      },
    ],
    mode: 'payment',
    success_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/success?orderId=${orderId}`,
    cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/cancel?orderId=${orderId}`,
    metadata: {
      orderId: orderId,
      outTradeNo: outTradeNo,
    },
    payment_intent_data: {
      // 确保 Payment Intent 也包含相同的 metadata
      metadata: {
        orderId: orderId,
        outTradeNo: outTradeNo,
      },
    },
  });

  qrCodeUrl = session.url;
}
```

### 2. 处理支付通知

当用户完成支付后：

1. Stripe 发送 webhook 通知到 `/api/public/stripe/notify` 端点
2. 后端验证 webhook 签名，确保通知的真实性
3. 根据通知类型采用不同的处理策略：
   - **对于 `checkout.session.completed` 和 `checkout.session.async_payment_succeeded` 事件**：更新订单状态并处理积分充值（仅当 `payment_status === 'paid'` 时）
   - **对于 `payment_intent.succeeded` 事件**：只记录日志和更新订单状态，不处理积分

这种处理策略符合 Stripe 官方建议，以 Checkout Session 事件作为履约/授权的唯一入口，更易支持 ACH、SEPA 等延迟支付方式，同时避免了同一笔支付产生重复的积分充值记录。

```typescript
// app/api/public/stripe/notify/route.ts 中的 webhook 处理

// 处理 checkout.session.completed 和 checkout.session.async_payment_succeeded 事件
if (event.type === 'checkout.session.completed' || event.type === 'checkout.session.async_payment_succeeded') {
  const session = event.data.object as Stripe.Checkout.Session;
  const orderId = session.metadata?.orderId;

  console.log(`[STRIPE_WEBHOOK] Processing ${event.type} event`);
  console.log('[STRIPE_WEBHOOK] Session ID:', session.id);
  console.log('[STRIPE_WEBHOOK] Payment status:', session.payment_status);

  try {
    const order = await db.query.orders.findFirst({
      where: eq(orders.id, orderId),
    });

    if (!order) {
      console.log('[STRIPE_WEBHOOK] Order not found:', orderId);
      return NextResponse.json({
        code: 400,
        msg: 'Order not found',
        data: { orderId, status: 'ORDER_NOT_FOUND' }
      });
    }

    if (order.status === 'SUCCESS') {
      console.log('[STRIPE_WEBHOOK] Order already processed:', orderId);
      return NextResponse.json({
        code: 0,
        msg: 'success',
        data: { orderId, status: 'ALREADY_PROCESSED', userId: order.userId }
      });
    }

    // 更新订单状态
    const orderExtra = order.extra as OrderExtra;
    await db.update(orders)
      .set({
        status: 'SUCCESS',
        paidAt: new Date(),
        tradeNo: session.id,
        extra: {
          ...orderExtra,
          paymentData: {
            ...orderExtra.paymentData,
            type: event.type,
            id: session.id,
            paymentIntent: session.payment_intent as string,
            paymentStatus: session.payment_status,
            customer: session.customer || undefined,
            customerDetails: session.customer_details || undefined,
            amount: session.amount_total || undefined,
            currency: session.currency || undefined,
            processedAt: new Date().toISOString(),
          },
        },
      })
      .where(eq(orders.id, orderId));

    console.log('[STRIPE_WEBHOOK] Order status updated to SUCCESS:', orderId);

    // 只有当支付状态为 'paid' 时才处理积分充值
    if (session.payment_status === 'paid') {
      const points = orderExtra.points;
      if (points) {
        try {
          console.log('[STRIPE_WEBHOOK] Recharging points for user:', order.userId, 'points:', points);

          const rechargeResult = await rechargeUserPoints({
            userId: order.userId,
            points,
            metadata: {
              orderId,
              paymentMethod: 'stripe',
              sessionId: session.id,
              paymentType: event.type
            }
          });

          console.log('[STRIPE_WEBHOOK] Points recharged successfully:', rechargeResult);

          // 更新邀请使用记录（如果有）
          try {
            const priceInYuan = orderExtra.price || 0;
            await updateInvitationUsageAfterRecharge(
              order.userId,
              points,
              priceInYuan
            );

            console.log('[STRIPE_WEBHOOK] Invitation usage updated successfully');
          } catch (inviteError) {
            console.error('[STRIPE_WEBHOOK] Failed to update invitation usage:', inviteError);
            // 不影响主流程
          }

          return NextResponse.json({
            code: 0,
            msg: 'success',
            data: {
              orderId,
              status: 'POINTS_CREDITED',
              userId: order.userId,
              points,
              walletId: rechargeResult.walletId,
            }
          });
        } catch (rechargeError) {
          console.error('[STRIPE_WEBHOOK] Failed to recharge points:', rechargeError);
          return NextResponse.json({
            code: 400,
            msg: 'Failed to recharge points',
            data: {
              orderId,
              status: 'RECHARGE_FAILED',
              userId: order.userId,
              points,
            }
          });
        }
      }
    } else {
      console.log(`[STRIPE_WEBHOOK] Payment status is '${session.payment_status}', not processing points recharge yet`);
    }

    return NextResponse.json({
      code: 0,
      msg: 'success',
      data: {
        orderId,
        status: 'STATUS_UPDATED',
        userId: order.userId
      }
    });
  } catch (error) {
    console.error('[STRIPE_WEBHOOK] Error processing checkout session:', error);
    return NextResponse.json({
      code: 500,
      msg: 'Internal server error',
      data: {
        orderId,
        status: 'ERROR'
      }
    });
  }
}

// 处理 payment_intent.succeeded 事件 - 只记录日志，不处理积分
else if (event.type === 'payment_intent.succeeded') {
  const paymentIntent = event.data.object as Stripe.PaymentIntent;

  console.log('[STRIPE_WEBHOOK] Received payment_intent.succeeded event');
  console.log('[STRIPE_WEBHOOK] Payment Intent ID:', paymentIntent.id);
  console.log('[STRIPE_WEBHOOK] Payment Intent metadata:', paymentIntent.metadata);

  // 从 metadata 中获取订单 ID
  let orderId = paymentIntent.metadata?.orderId;

  // 如果找到了订单 ID，记录信息但不处理积分
  if (orderId) {
    console.log('[STRIPE_WEBHOOK] Found order:', orderId, 'but not processing points (handled by checkout.session events)');

    // 可以选择更新订单状态，但不处理积分
    try {
      const order = await db.query.orders.findFirst({
        where: eq(orders.id, orderId),
      });

      if (order && order.status !== 'SUCCESS') {
        console.log('[STRIPE_WEBHOOK] Updating order status only (no points processing):', orderId);

        const orderExtra = order.extra as OrderExtra;
        await db.update(orders)
          .set({
            status: 'SUCCESS',
            paidAt: new Date(),
            tradeNo: paymentIntent.id,
            extra: {
              ...orderExtra,
              paymentData: {
                ...orderExtra.paymentData,
                type: 'payment_intent',
                id: paymentIntent.id,
                paymentStatus: paymentIntent.status,
                amount: paymentIntent.amount,
                currency: paymentIntent.currency,
                processedAt: new Date().toISOString(),
              },
            },
          })
          .where(eq(orders.id, orderId));
      }
    } catch (error) {
      console.error('[STRIPE_WEBHOOK] Error updating order status:', error);
    }
  }

  // 返回成功响应，但不处理积分
  return NextResponse.json({
    code: 0,
    msg: 'success',
    data: {
      status: 'PAYMENT_INTENT_LOGGED',
      paymentIntentId: paymentIntent.id,
      orderId: orderId || 'unknown'
    }
  });
}
```

## 安全考虑

### 1. Webhook 签名验证

Stripe webhook 通知使用签名验证机制确保通知的真实性：

```typescript
// 验证 Stripe webhook 签名
const payload = await request.text();
const signature = request.headers.get('stripe-signature') as string;

let event;
try {
  event = stripe.webhooks.constructEvent(payload, signature, endpointSecret!);
} catch (err) {
  console.error('[STRIPE_WEBHOOK] Signature verification failed:', err);
  return new NextResponse('Webhook signature verification failed', { status: 400 });
}
```

### 2. 环境变量配置

所有敏感信息通过环境变量配置，包括：

- `STRIPE_SECRET_KEY`：Stripe API 密钥
- `STRIPE_WEBHOOK_SECRET`：Stripe webhook 签名密钥
- `NEXT_PUBLIC_APP_URL`：应用程序的公共 URL，用于构建回调地址

## 配置说明

### 1. 环境变量

在 `.env.local` 文件中添加以下环境变量：

```
# Stripe 配置
STRIPE_SECRET_KEY=sk_test_...  # 测试环境使用 sk_test_，生产环境使用 sk_live_
STRIPE_WEBHOOK_SECRET=whsec_...  # Stripe webhook 签名密钥
```

### 2. Stripe Dashboard 配置

在 Stripe Dashboard 中需要进行以下配置：

1. **Webhook 端点**：
   - 添加 webhook 端点：`https://your-domain.com/api/public/stripe/notify`
   - 选择事件类型：`checkout.session.completed` 和 `payment_intent.succeeded`
   - 获取 webhook 签名密钥并配置到环境变量 `STRIPE_WEBHOOK_SECRET`

2. **支付设置**：
   - 配置支持的支付方式（信用卡、借记卡等）
   - 设置支持的货币（CNY）
   - 配置商户信息和结算账户

## 测试方法

### 1. 使用 Stripe 测试卡号

Stripe 提供了一系列测试卡号，可用于测试不同的支付场景：

- **成功支付**：4242 4242 4242 4242
- **需要验证**：4000 0000 0000 3220
- **支付失败**：4000 0000 0000 9995

### 2. 使用 Stripe CLI 测试 webhook

使用 Stripe CLI 可以在本地开发环境中测试 webhook：

```bash
# 安装 Stripe CLI
brew install stripe/stripe-cli/stripe

# 登录 Stripe 账户
stripe login

# 转发 webhook 事件到本地开发服务器
stripe listen --forward-to localhost:3000/api/public/stripe/notify
```

## 与现有支付系统的集成

Stripe 支付方式与现有的支付宝和微信支付方式并行使用，共享相同的业务流程：

1. **订单创建**：所有支付方式共用同一个订单创建逻辑，只是支付网关不同
2. **积分充值**：所有支付方式成功后，都调用相同的 `rechargeUserPoints` 函数充值积分
3. **邀请奖励**：所有支付方式都支持邀请奖励计算，使用相同的 `updateInvitationUsageAfterRecharge` 函数

## 故障排除

### 1. 支付失败

如果用户支付失败，可能的原因包括：

- 信用卡余额不足
- 信用卡被拒绝
- 3D Secure 验证失败

系统会将用户重定向到取消页面，用户可以重新尝试支付。

### 2. Webhook 通知失败

如果 webhook 通知失败，可能的原因包括：

- webhook 签名验证失败
- 服务器错误
- 数据库连接问题

系统会记录详细的错误日志，管理员可以通过日志排查问题。

### 3. 订单状态不同步

如果订单状态不同步，管理员可以通过以下方式解决：

1. 在 Stripe Dashboard 中查看支付状态
2. 使用 `lib/stripe/utils.ts` 中的工具函数生成 Stripe Dashboard URL
3. 手动更新订单状态

```typescript
// 生成 Stripe Dashboard URL
import { getStripeDashboardUrl } from '@/lib/stripe/utils';

const dashboardUrl = getStripeDashboardUrl(transactionId);
```

### 4. 重复交易问题

Stripe 会为同一笔支付发送多个不同类型的 webhook 事件（`checkout.session.completed` 和 `payment_intent.succeeded`），如果这两个事件都触发积分充值逻辑，可能导致同一订单被处理两次，产生重复的积分充值记录。

为了解决这个问题，我们采用了差异化处理策略：
- 对于 `checkout.session.completed` 事件：只更新订单状态，不处理积分充值
- 对于 `payment_intent.succeeded` 事件：更新订单状态并处理积分充值

如果发现历史数据中存在重复交易记录，可以通过以下方式处理：

1. 查询钱包交易记录，找出具有相同 `orderId` 但不同 `id` 的记录
2. 确认这些记录是由同一笔支付产生的（检查时间戳和金额）
3. 手动调整用户钱包余额，减去重复充值的积分
4. 在钱包的 `extra.transactions` 数组中标记重复的交易记录

```typescript
// 示例：查找重复交易记录
const wallet = await db.query.wallets.findFirst({
  where: eq(wallets.userId, userId),
});

const transactions = wallet.extra.transactions || [];
const orderIds = new Set();
const duplicates = [];

transactions.forEach(t => {
  if (t.orderId && orderIds.has(t.orderId)) {
    duplicates.push(t);
  } else if (t.orderId) {
    orderIds.add(t.orderId);
  }
});

console.log('Found duplicate transactions:', duplicates);
```

## 结论

Stripe 支付集成遵循现有支付系统的架构设计，共享相同的业务流程和数据处理逻辑。通过环境变量配置和 webhook 签名验证，确保支付过程的安全性。系统支持多种支付方式并行使用，为用户提供更多支付选择。
