# 绘图组件性能优化方案

## 当前问题

目前在 `@components/draw/tabs/generate-tab.tsx` 中的 `fetchCombinedHistories` 函数会直接获取历史记录数据，并通过 `useState` 在组件内部管理状态。这导致以下问题：

1. 每次轮询刷新数据时，都会触发整个组件树的重新渲染，即使数据没有变化
2. 相同的数据在 `@components/draw/recent-generations.tsx` 和 `@components/draw/draw-pending.tsx` 组件中被重复获取和处理
3. 没有对数据进行比对，导致不必要的渲染
4. **数据库带宽浪费**：combined 接口返回完整的 histories 记录，包含 prompt、description、parameters、extra 等大字段，前端每5秒轮询一次，传输大量不必要的数据

## 优化方案

将数据获取和状态管理移至全局状态管理器 `@store/draw/index.ts`，实现以下优化：

1. 在 store 中集中管理历史记录数据，避免重复请求
2. 实现数据比对逻辑，只在关键字段（id/status/drawStatus/share）变化时才更新状态
3. 组件通过 store 订阅数据，只在相关数据变化时才重新渲染
4. **优化数据库查询**：只查询列表必需的字段，减少数据传输量
5. **分离列表和详情**：创建独立的详情接口，点击时才获取完整数据

## 具体实现步骤

### 1. 扩展 DrawStore

在 `@store/draw/index.ts` 中添加历史记录相关的状态和方法：

```typescript
interface DrawState {
  // 现有状态...

  // 新增状态
  recentHistories: HistoryItem[];
  pendingHistories: HistoryItem[];
  historiesLoading: boolean;

  // 新增方法
  fetchCombinedHistories: () => Promise<void>;
  startHistoryPolling: () => () => void;
  stopHistoryPolling: () => void;
}
```

### 2. 实现数据比对逻辑

在 store 的 `fetchCombinedHistories` 方法中实现数据比对：

```typescript
fetchCombinedHistories: async () => {
  set({ historiesLoading: true });
  try {
    const response = await fetch('/api/history/combined?includeShare=true&recentLimit=4&pendingLimit=4');
    if (response.ok) {
      const data = await response.json();

      // 获取当前状态
      const currentRecentHistories = get().recentHistories;
      const currentPendingHistories = get().pendingHistories;

      // 比对数据，只有关键字段变化时才更新
      const recentHasChanged = compareHistories(currentRecentHistories, data.recentHistories || []);
      const pendingHasChanged = compareHistories(currentPendingHistories, data.pendingHistories || []);

      // 只更新变化的部分
      if (recentHasChanged) {
        set({ recentHistories: data.recentHistories || [] });
      }

      if (pendingHasChanged) {
        set({ pendingHistories: data.pendingHistories || [] });
      }
    }
  } catch (error) {
    console.error("Error fetching histories:", error);
  } finally {
    set({ historiesLoading: false });
  }
}
```

### 3. 实现比对函数

创建一个工具函数来比对历史记录数组，只关注关键字段的变化：

```typescript
function compareHistories(oldHistories: HistoryItem[], newHistories: HistoryItem[]): boolean {
  // 长度不同，肯定有变化
  if (oldHistories.length !== newHistories.length) return true;

  // 创建映射以便快速查找
  const oldMap = new Map(oldHistories.map(h => [h.id, h]));

  // 检查每个新历史记录
  for (const newHistory of newHistories) {
    const oldHistory = oldMap.get(newHistory.id);

    // 如果找不到对应的旧记录，说明有变化
    if (!oldHistory) return true;

    // 检查关键字段
    if (
      oldHistory.status !== newHistory.status ||
      oldHistory.drawStatus !== newHistory.drawStatus ||
      JSON.stringify(oldHistory.share) !== JSON.stringify(newHistory.share)
    ) {
      return true;
    }
  }

  // 没有检测到变化
  return false;
}
```

### 4. 修改组件实现

修改 `@components/draw/tabs/generate-tab.tsx`，使用 store 中的数据和方法：

```typescript
export function GenerateTab({ output, renderedContent }: GenerateTabProps) {
  const {
    recentHistories,
    pendingHistories,
    historiesLoading,
    fetchCombinedHistories,
    startHistoryPolling,
    stopHistoryPolling
  } = useDrawStore();

  // 组件挂载时开始轮询
  useEffect(() => {
    const stopPolling = startHistoryPolling();
    return () => stopPolling();
  }, []);

  // 其他逻辑...
}
```

同样修改 `@components/draw/recent-generations.tsx` 和 `@components/draw/draw-pending.tsx` 组件，使它们从 store 获取数据。

### 5. 实现轮询机制

在 store 中实现轮询机制，避免在多个组件中重复设置轮询：

```typescript
// 在 store 中添加
startHistoryPolling: () => {
  const interval = setInterval(() => {
    get().fetchCombinedHistories();
  }, 5000); // 每5秒轮询一次

  // 保存 interval ID 以便清除
  set({ pollingInterval: interval });

  return () => {
    clearInterval(interval);
    set({ pollingInterval: null });
  };
}
```

## 数据库带宽优化详细方案

### 6. 优化 combined 接口查询字段

修改 `/api/history/combined/route.ts`，只查询列表必需的字段：

```typescript
// 优化前：查询所有字段（包含大字段）
const recentHistories = await db.query.histories.findMany({
  // 返回所有字段，包括 prompt、description、parameters、extra 等
});

// 优化后：只查询必需字段
const recentHistories = await db.query.histories.findMany({
  columns: {
    id: true,
    status: true,
    drawStatus: true,
    resultUrl: true,
    pointsUsed: true,
    createdAt: true,
    updatedAt: true,
    // 不查询 prompt、description、parameters、extra 等大字段
  },
  // 其他查询条件保持不变
});
```

### 7. 创建详情接口
这部分暂缓优化

### 8. 更新前端组件
修改前端组件，使用新的 combined 接口。

## 预期效果

1. 减少不必要的组件重新渲染，提高性能
2. 减少重复的数据请求，降低服务器负载
3. 统一数据管理，简化组件逻辑
4. 只在数据真正变化时才触发 DOM 更新
5. **大幅减少数据库带宽消耗**：列表接口数据传输量减少约 70-80%
6. **提高轮询效率**：每5秒的轮询请求更轻量，响应更快

## 带宽优化效果估算

### 优化前
- 每个 history 记录约 2-5KB（包含 prompt、parameters、extra 等）
- 每次轮询返回 15 条记录（5 recent + 10 pending）
- 单次请求：15 × 3KB = 45KB
- 每分钟轮询 12 次：45KB × 12 = 540KB
- 每小时：540KB × 60 = 32.4MB

### 优化后
- 每个 history 记录约 0.5-1KB（只包含基本字段）
- 每次轮询返回 15 条记录
- 单次请求：15 × 0.7KB = 10.5KB
- 每分钟轮询 12 次：10.5KB × 12 = 126KB
- 每小时：126KB × 60 = 7.6MB

**带宽节省：约 76%**

## 后续优化方向

1. 考虑使用 WebSocket 替代轮询，进一步提高实时性和性能
2. 实现更细粒度的组件拆分，减少渲染范围
3. 考虑使用虚拟列表优化长列表渲染性能
4. 实现增量更新机制，只传输变化的数据
