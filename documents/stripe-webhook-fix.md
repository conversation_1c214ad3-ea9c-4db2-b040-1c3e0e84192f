# Stripe Webhook 重复交易问题修复

## 问题描述

在 Stripe 支付集成中，发现同一笔支付可能会产生两条钱包交易记录。这是因为 Stripe 会为同一笔支付发送多个不同类型的 webhook 事件：

1. `checkout.session.completed` - 当用户完成结账流程时
2. `payment_intent.succeeded` - 当支付成功处理时

这两个事件都包含相同的订单 ID，并且之前的实现中都会触发积分充值逻辑，导致同一订单被处理两次，产生重复的积分充值记录。

示例：
```json
{
  "id": "cI1gPHEZL2oPx1-eUowf9",
  "type": "credit",
  "points": 1000,
  "orderId": "DxfMku4PUvdKG2xGalGcT",
  "createdAt": "2025-05-01T01:52:03.676Z",
  "description": "充值1000积分",
  "paymentType": "payment_intent",
  "paymentMethod": "stripe"
},
{
  "id": "i7FX0T2i9zBYcyFffVWSl",
  "type": "credit",
  "points": 1000,
  "orderId": "DxfMku4PUvdKG2xGalGcT",
  "createdAt": "2025-05-01T01:52:04.468Z",
  "description": "充值1000积分",
  "paymentType": "checkout.session",
  "paymentMethod": "stripe"
}
```

## 解决方案

根据 Stripe 官方建议，修改 webhook 处理逻辑，对不同类型的事件采用不同的处理策略：

1. 对于 `checkout.session.completed` 和 `checkout.session.async_payment_succeeded` 事件：**更新订单状态并处理积分充值**（仅当 `payment_status === 'paid'` 时）
2. 对于 `payment_intent.succeeded` 事件：**只记录日志，不处理积分**

这种方式符合 Stripe 官方建议，以 Checkout Session 事件作为履约/授权的唯一入口，更易支持 ACH、SEPA 等延迟支付方式。

## 实现细节

### 1. 修改 `checkout.session.completed` 和 `checkout.session.async_payment_succeeded` 事件处理

```typescript
// 处理 checkout.session.completed 和 checkout.session.async_payment_succeeded 事件
if (event.type === 'checkout.session.completed' || event.type === 'checkout.session.async_payment_succeeded') {
  const session = event.data.object as Stripe.Checkout.Session;
  const orderId = session.metadata?.orderId;

  // 更新订单状态
  const order = await db.query.orders.findFirst({
    where: eq(orders.id, orderId),
  });

  if (order.status === 'SUCCESS') {
    return { success: true, status: 'ALREADY_PROCESSED' };
  }

  // 更新订单状态
  await db.update(orders)
    .set({
      status: 'SUCCESS',
      paidAt: new Date(),
      tradeNo: session.id,
      // 更新其他订单信息...
    })
    .where(eq(orders.id, orderId));

  // 只有当支付状态为 'paid' 时才处理积分充值
  if (session.payment_status === 'paid') {
    const points = orderExtra.points;
    if (points) {
      // 处理积分充值
      const rechargeResult = await rechargeUserPoints({
        userId: order.userId,
        points,
        metadata: {
          orderId,
          paymentMethod: 'stripe',
          sessionId: session.id,
          paymentType: event.type
        }
      });

      // 更新邀请使用记录
      await updateInvitationUsageAfterRecharge(
        order.userId,
        points,
        orderExtra.price || 0
      );

      return { success: true, status: 'POINTS_CREDITED' };
    }
  } else {
    console.log(`Payment status is '${session.payment_status}', not processing points recharge yet`);
  }

  return { success: true, status: 'STATUS_UPDATED' };
}
```

### 2. 修改 `payment_intent.succeeded` 事件处理

```typescript
// 处理 payment_intent.succeeded 事件 - 只记录日志，不处理积分
if (event.type === 'payment_intent.succeeded') {
  const paymentIntent = event.data.object as Stripe.PaymentIntent;
  const orderId = paymentIntent.metadata?.orderId;

  console.log('Received payment_intent.succeeded event');
  console.log('Payment Intent ID:', paymentIntent.id);

  // 如果找到了订单 ID，记录信息但不处理积分
  if (orderId) {
    console.log('Found order:', orderId, 'but not processing points (handled by checkout.session events)');

    // 可以选择更新订单状态，但不处理积分
    const order = await db.query.orders.findFirst({
      where: eq(orders.id, orderId),
    });

    if (order && order.status !== 'SUCCESS') {
      // 只更新订单状态
      await db.update(orders)
        .set({
          status: 'SUCCESS',
          paidAt: new Date(),
          // 更新其他订单信息...
        })
        .where(eq(orders.id, orderId));
    }
  }

  return { success: true, status: 'PAYMENT_INTENT_LOGGED' };
}
```

## 代码优化

1. 删除了 `processSuccessfulPayment` 函数，将逻辑直接集成到事件处理中
2. 删除了不再使用的 `PaymentData` 和 `ProcessPaymentResult` 接口
3. 添加了对 `checkout.session.async_payment_succeeded` 事件的支持，以处理异步支付方式
4. 在 `checkout.session` 事件中添加了对 `payment_status === 'paid'` 的检查，确保只有在支付完成时才处理积分充值

## 测试方法

1. 创建新订单并使用 Stripe 支付
2. 检查用户钱包中是否只有一条与该订单相关的交易记录
3. 查看服务器日志，确认:
   - `checkout.session.completed` 事件处理了积分充值（当 `payment_status === 'paid'` 时）
   - `payment_intent.succeeded` 事件只记录了日志，没有处理积分

## 注意事项

- 此修复不会影响已经产生重复记录的历史订单
- 如需修复历史数据，需要单独编写脚本处理
- 此修复仅针对 Stripe 支付，其他支付方式（如 Epay）已有类似的防重复处理机制
- 此实现符合 Stripe 官方建议，以 Checkout Session 事件作为履约/授权的唯一入口
