# 邀请码格式验证实现文档

## 1. 概述

本文档详细描述了邀请码格式验证的实现方案，包括验证规则、实现方式和相关代码。

**目标**：确保系统中的邀请码格式统一，只允许使用小写字母和数字的组合，提高系统的一致性和安全性。

---

## 2. 验证规则

- **格式要求**：邀请码只能包含小写字母（a-z）和数字（0-9）
- **正则表达式**：`/^[a-z0-9]+$/`
- **长度限制**：3-20个字符（基本长度为8个字符）
- **错误提示**："邀请码只能包含小写字母和数字"

---

## 3. 实现方式

### 3.1 常量定义

在 `constants/invitation.ts` 中定义了邀请码验证相关的常量和函数：

```typescript
// 邀请码正则表达式（只允许小写字母和数字）
export const INVITE_CODE_REGEX = /^[a-z0-9]+$/;

// 邀请码验证错误信息
export const INVITE_CODE_ERROR_MESSAGE = "邀请码只能包含小写字母和数字";

/**
 * 验证邀请码是否有效
 * @param inviteCode 邀请码
 * @returns 是否有效
 */
export function validateInviteCode(inviteCode: string): boolean {
  return INVITE_CODE_REGEX.test(inviteCode);
}
```

### 3.2 随机邀请码生成

为了确保生成的随机邀请码符合格式要求，我们使用了自定义的随机码生成函数，而不是直接使用 `nanoid`：

```typescript
// 生成符合规则的随机邀请码（只包含小写字母和数字）
const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
let result = '';
for (let i = 0; i < INVITE_CODE_LENGTH; i++) {
  result += characters.charAt(Math.floor(Math.random() * characters.length));
}
```

### 3.3 前端验证

在前端组件中，使用 Zod 验证库结合正则表达式进行验证：

```typescript
// 表单验证模式
const formSchema = z.object({
  invite_code: z.string()
    .min(3, "邀请码至少需要3个字符")
    .max(20, "邀请码最多20个字符")
    .refine((value) => !value || INVITE_CODE_REGEX.test(value), {
      message: INVITE_CODE_ERROR_MESSAGE,
    })
    .optional(),
  // 其他字段...
});
```

### 3.4 API 验证

在 API 端点中，同样使用 Zod 验证库进行请求数据验证：

```typescript
// 更新邀请码请求验证
const updateInvitationSchema = z.object({
  invite_code: z.string()
    .min(3, "邀请码至少需要3个字符")
    .max(20, "邀请码最多20个字符")
    .refine((value) => INVITE_CODE_REGEX.test(value), {
      message: INVITE_CODE_ERROR_MESSAGE,
    }),
});
```

---

## 4. 修改的文件

以下是实现邀请码格式验证所修改的文件：

1. **常量定义**：
   - `constants/invitation.ts`

2. **前端组件**：
   - `components/admin/invitation-form.tsx`
   - `components/settings/invitation-activation.tsx`

3. **API 端点**：
   - `app/api/invitations/activate/route.ts`
   - `app/api/invitations/[id]/route.ts`
   - `app/api/admin/invitations/route.ts`

---

## 5. 用户体验优化

为了提升用户体验，我们还实现了以下优化：

1. **编辑按钮**：
   - 在邀请链接旁添加编辑按钮，点击后弹出对话框进行编辑
   - 使用 Modal 对话框，避免用户需要滚动到页面底部进行编辑

2. **实时验证**：
   - 在用户输入邀请码时进行实时验证
   - 如果输入的邀请码不符合格式要求，立即显示错误提示

3. **自动生成**：
   - 提供"生成随机邀请码"按钮，自动生成符合格式要求的邀请码
   - 生成的邀请码保证只包含小写字母和数字

---

## 6. 测试案例

| 测试场景 | 输入 | 预期结果 |
|---------|------|---------|
| 有效邀请码 | `abc123` | 通过验证 |
| 有效邀请码 | `abcdefgh` | 通过验证 |
| 无效邀请码（大写字母） | `ABC123` | 验证失败，显示错误提示 |
| 无效邀请码（特殊字符） | `abc-123` | 验证失败，显示错误提示 |
| 无效邀请码（空格） | `abc 123` | 验证失败，显示错误提示 |
| 无效邀请码（太短） | `ab` | 验证失败，显示错误提示 |
| 无效邀请码（太长） | `abcdefghijklmnopqrstuvwxyz` | 验证失败，显示错误提示 |

---

> 此文档详细记录了邀请码格式验证的实现方案，确保系统中的邀请码格式统一，只允许使用小写字母和数字的组合。
