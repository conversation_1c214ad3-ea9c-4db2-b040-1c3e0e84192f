# 邀请现金奖励兑换功能说明

## 概述

本文档描述了邀请系统中现金奖励兑换功能的实现。与积分奖励不同，现金奖励兑换仅更新邀请使用记录的状态，不执行实际的现金转账操作，需要管理员手动处理后续的现金转账流程。

## 实现方案

### 1. 功能分离

- 将现金奖励兑换功能从原有的 `redeemInvitationReward` 函数中分离出来
- 创建专门的 `redeemInvitationCash` 函数，放置在 `lib/invitation/cash.ts` 文件中
- 保持与 `redeemInvitationPoints` 函数类似的接口设计，便于统一调用

### 2. 功能说明

`redeemInvitationCash` 函数的主要功能：

- 验证邀请使用记录的状态和可兑换性
- 更新邀请使用记录的状态为 `completed`
- 记录兑换操作的相关信息（操作人、时间、备注等）
- 返回更新后的邀请使用记录

### 3. 调用流程

1. 管理员在管理界面选择需要兑换的邀请记录
2. 选择兑换类型为"现金"
3. 系统调用 `redeemInvitationCash` 函数更新记录状态
4. 管理员收到成功提示后，需手动处理实际的现金转账操作

### 4. 数据结构

邀请使用记录在兑换现金奖励后，会更新以下字段：

- `status`: 从 `ready` 更新为 `completed`
- `redeemedAt`: 设置为当前时间
- `operatorId`: 设置为执行兑换操作的管理员ID
- `extra.redeemType`: 设置为 `cash`
- `extra.redeemNote`: 设置为管理员提供的备注信息（可选）

### 5. 未来扩展

未来可能需要实现的功能：

- 集成支付系统，实现自动转账
- 添加转账记录跟踪
- 支持批量兑换操作
- 添加兑换通知功能

## 使用说明

### 管理员操作流程

1. 进入邀请管理页面
2. 查看待兑换的邀请记录列表
3. 点击"兑换"按钮，选择"现金"类型
4. 输入可选的备注信息
5. 确认兑换操作
6. 系统更新记录状态
7. 管理员手动处理实际的现金转账操作

### 注意事项

- 只有管理员可以执行现金奖励兑换操作
- 普通用户只能兑换积分奖励
- 现金奖励兑换后，需要管理员手动处理实际的转账操作
- 系统不会自动执行转账，只会更新记录状态
