# 状态接口带宽优化

## 概述

优化了 `/api/public/status` 接口以节省带宽和流量，该接口用于显示模型状态指示器，每分钟自动刷新一次。

## 优化内容

### 1. 响应格式优化

**优化前（对象格式）：**
```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "models": {
    "draw-model-small": {
      "id": "draw-model-small",
      "name": "4o 基础版",
      "successRate": 85
    },
    "draw-model": {
      "id": "draw-model",
      "name": "4o 专业版",
      "successRate": 92
    }
  }
}
```

**优化后（紧凑数组格式）：**
```json
[
  ["draw-model-small", 85],
  ["draw-model", 92]
]
```

### 2. 带宽节省分析

- **移除冗余字段**：
  - `timestamp`：前端不需要，改为本地时间显示
  - `name`：前端已有 `drawModels` 常量，可通过 `id` 获取
  - `id`：在数组格式中作为第一个元素，无需重复

- **数据结构优化**：
  - 从嵌套对象改为二维数组：`[modelId, successRate]`
  - 减少 JSON 键名重复和嵌套层级

- **预估节省**：
  - 原格式约 200-300 字节
  - 新格式约 50-80 字节
  - **节省约 70-80% 带宽**

### 3. 数据库查询优化

**优化前（查询所有字段）：**
```typescript
const completedHistories = await db.query.histories.findMany({
  where: and(
    gte(histories.updatedAt, hoursAgo),
    not(inArray(histories.drawStatus, ['PENDING', 'PROCESSING'])),
  ),
}) as History[];
```

**优化后（只查询必要字段）：**
```typescript
const completedHistories = await db.query.histories.findMany({
  where: and(
    gte(histories.updatedAt, hoursAgo),
    not(inArray(histories.drawStatus, ['PENDING', 'PROCESSING'])),
  ),
  columns: {
    status: true,        // 用于判断成功/失败
    drawStatus: true,    // 用于判断是否为 SUCCESS
    extra: true,         // 用于获取 model 字段
  },
}) as HistoryStatusData[];
```

**数据库带宽节省分析：**
- **原查询字段**：id, userId, status, resultUrl, prompt, description, pointsUsed, parameters, forkedFromShareId, backupStatus, lastBackupAt, archived, drawStatus, drawResult, extra, createdAt, updatedAt (17个字段)
- **优化后字段**：status, drawStatus, extra (3个字段)
- **节省约 80% 数据库传输量**

### 4. 代码变更

#### 后端 (`app/api/public/status/route.ts`)
- **数据库查询优化**：使用 `columns` 选项只查询必要字段
- **响应格式优化**：改用 `map()` 返回数组格式而非 `reduce()` 返回对象
- **类型定义**：添加 `HistoryStatusData` 类型定义
- 移除 `timestamp` 和 `name` 字段
- 返回格式：`[[modelId, successRate], ...]`

#### 前端 (`components/global/model-status.tsx`)
- 更新类型定义：`type StatusResponse = [string, number][]`
- 使用 `Map` 转换数组为快速查找结构
- 从 `drawModels` 常量获取模型名称
- 使用本地时间显示更新时间

## 兼容性

- ✅ 保持功能完全一致
- ✅ 状态指示器显示正常
- ✅ 详细信息弹窗正常
- ✅ 自动刷新机制不变

## 性能影响

### 数据库层面
- **查询字段减少**：从17个字段减少到3个字段，节省约80%数据库传输量
- **查询性能**：减少数据传输时间，特别是在云数据库环境下效果显著
- **内存使用**：减少数据库连接池内存占用
- **网络延迟**：降低数据库到应用服务器的网络延迟

### 应用层面
- **网络传输**：减少 70-80% HTTP 响应数据量
- **解析性能**：数组解析比对象更快
- **内存使用**：减少 JSON 对象内存占用
- **移动端友好**：显著减少移动网络流量消耗

### 整体优化效果
- **数据库带宽**：节省约 80%
- **HTTP 响应**：节省约 70-80%
- **总体带宽**：在高频访问场景下效果显著

## 注意事项

- 该接口为公开接口，每分钟自动调用
- 优化对高频访问场景效果显著
- 保持了所有原有功能和用户体验
