# Sharing Workflow Design

## Overview
This document outlines the design for the sharing functionality, allowing users to share their image generation histories and manage sharing settings.

## Database Schema
The sharing functionality uses the existing `shares` table in the database schema, which includes:
- `id`: Primary key
- `shareId`: Unique share identifier
- `historyId`: Reference to the history item
- `userId`: Reference to the user who created the share
- `isPublic`: Whether the share is public (default: true)
- `allowFork`: Whether the share can be forked (default: true)
- `imageUrl`: The shared image URL
- Various metadata fields (viewCount, likeCount, forkCount, etc.)

## API Endpoints

### 1. Create Share
- **Endpoint**: `POST /api/shares`
- **Request Body**:
  ```typescript
  {
    historyId: string;
    isPublic?: boolean;  // default: true
    allowFork?: boolean; // default: true
  }
  ```
- **Response**:
  ```typescript
  {
    id: string;  // Share ID for immediate redirection
    shareId: string;
    historyId: string;
    userId: string;
    isPublic: boolean;
    allowFork: boolean;
    imageUrl: string;
    createdAt: string;
  }
  ```

### 2. Update Share
- **Endpoint**: `PATCH /api/shares/[id]`
- **Request Body**:
  ```typescript
  {
    isPublic?: boolean;
    allowFork?: boolean;
  }
  ```
- **Response**: Updated share object

### 3. Get User's Shares
- **Endpoint**: `GET /api/shares/user`
- **Response**:
  ```typescript
  {
    shares: Array<{
      id: string;
      shareId: string;
      historyId: string;
      isPublic: boolean;
      allowFork: boolean;
      imageUrl: string;
      viewCount: number;
      likeCount: number;
      forkCount: number;
      createdAt: string;
    }>;
  }
  ```

### 4. Get Share Details
- **Endpoint**: `GET /api/shares/[id]`
- **Response**: Detailed share object with all fields

### 5. Get History Details with Share Info
- **Endpoint**: `GET /api/history/[id]`
- **Response**:
  ```typescript
  {
    id: string;
    userId: string;
    status: boolean;
    resultUrl: string;
    prompt: string;
    description: string;
    pointsUsed: number;
    parameters: Record<string, any>;
    createdAt: string;
    updatedAt: string;
    share?: {
      id: string;
      shareId: string;
      isPublic: boolean;
      allowFork: boolean;
      viewCount: number;
      likeCount: number;
      forkCount: number;
    };
  }
  ```

## UI Components

### 1. Settings Navigation
- Add "分享管理" (Sharing Management) item to settings navigation
- Link to `/settings/sharing`

### 2. Sharing List Page (`/settings/sharing`)
- List view of user's shares
- Each item shows:
  - Thumbnail image
  - Share ID
  - Public/Fork status
  - View/Like/Fork counts
  - Creation date
  - Link to detail page

### 3. Share Detail Page (`/settings/sharing/[id]`)
- Display all share information
- Toggle switches for:
  - Public/Private status
  - Allow/Disallow forking
- Save button to update settings
- Back to list button

### 4. History Item Share Button
- If history has no share:
  - Show "创建分享" button
  - On click: Show confirmation dialog
  - On confirm: Create share and redirect to `/settings/sharing/[id]`
- If history has share:
  - Show "查看分享" button
  - On click: Redirect to `/settings/sharing/[id]`

## Todo List

### Phase 1: API Implementation
- [x] Create share API endpoint
- [x] Update share API endpoint
- [x] Get user's shares API endpoint
- [x] Get share details API endpoint
- [x] Get history details with share info API endpoint

### Phase 2: UI Implementation
- [x] Add "分享管理" nav item to settings
- [x] Create sharing list page
- [x] Create sharing detail page
- [x] Implement share creation from history with confirmation
- [x] Implement share settings update
- [x] Update history item share button logic

### Phase 3: Testing & Refinement
- [ ] Test all API endpoints
- [ ] Test UI components
- [ ] Add error handling
- [ ] Add loading states
- [ ] Add success/error notifications

## Notes
- All API endpoints require authentication
- Only the share creator can update share settings
- Share creation should copy relevant data from the history item
- UI should provide clear feedback for all actions
- Share creation requires user confirmation before proceeding
- History items should show appropriate share button based on share status
