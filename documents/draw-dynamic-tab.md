# Draw Dynamic Tab Design

## Overview

This document outlines the design and implementation of the "Dynamic" tab in the draw interface, which replaces the previous "Results" tab. The Dynamic tab serves as a central hub for users to view their recent successful generations and latest community shares.

## Design Goals

1. Provide users with continuous access to their recent successful generations and community shares
2. Simplify the user experience by consolidating content into a single, always-accessible tab
3. Ensure users can easily see their most recent generation results
4. Automatically refresh content after each generation

## Tab Structure

The Dynamic tab consists of two main sections:

1. **Recent Successful Generations** (Top)
   - Displays the user's 4 most recent successfully generated images
   - Most recent generation appears first
   - Includes a "View More" link to `/settings/history`

2. **Latest Updates** (Bottom)
   - Displays 8 recent shares from the community
   - Prioritizes shares matching the currently selected style
   - Supplements with shares from other styles when needed (with deduplication)
   - Includes a "View More" link to `/explore`

## User Flow

1. User opens the draw interface
   - The "Dynamic" tab is selected by default
   - Recent successful generations and latest updates are displayed

2. User generates an image
   - During generation, the interface switches to the "Generate" tab to show progress
   - After generation completes, a success/error modal appears

3. User closes the modal
   - The interface automatically switches back to the "Dynamic" tab
   - The content is refreshed to include the newly generated image (if successful)
   - The user's recent successful generations are updated

## Implementation Details

### Component Changes

1. **DrawPreview Component**
   - Rename "Results" tab to "Dynamic"
   - Update tab content to display DrawPlaceholder content
   - Modify tab switching logic to return to "Dynamic" tab after generation

2. **DrawPlaceholder Component**
   - Reorder sections to show recent successful generations at the top
   - Rename "Recent Shares" section to "Latest Updates"
   - Ensure proper data fetching and refresh mechanisms

3. **DrawResultModal Component**
   - Update onAfterClose callback to switch to "Dynamic" tab and refresh data

### Data Flow

1. **Initial Load**
   - Fetch user's recent successful generations
   - Fetch shares matching the current style
   - Fetch additional shares from other styles if needed

2. **After Generation**
   - Refresh user information (including recent histories)
   - Refresh shares data
   - Update the UI to reflect the latest data

3. **Tab Switching**
   - When switching to "Dynamic" tab, ensure data is fresh
   - When switching to "Generate" tab, preserve the state of the "Dynamic" tab

## Benefits

1. **Improved User Experience**
   - Users always have access to their recent generations and community content
   - No need to navigate between tabs to see different types of content
   - Automatic refresh ensures users see the most up-to-date information

2. **Simplified Interface**
   - Consolidates related content into a single tab
   - Reduces the need for users to manually navigate to see their results
   - Provides a more intuitive flow from generation to viewing results

3. **Enhanced Discovery**
   - Encourages users to explore community content
   - Makes it easier for users to find inspiration for their own generations
   - Promotes engagement with the platform's social features
