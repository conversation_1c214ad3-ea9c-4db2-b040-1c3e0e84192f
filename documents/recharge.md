# 积分充值系统重构方案

## 1. 业务流程拆分

### 订单状态流程
- 保持现有的订单状态判定逻辑
- 仅负责处理订单状态的变更（PENDING -> SUCCESS）
- 记录支付相关信息（tradeNo, paidAt 等）

### 积分充值流程
将积分充值逻辑独立为单独的模块，主要职责：
- 接收用户ID和充值积分数量
- 处理钱包积分的增加
- 不涉及订单状态处理

## 2. 积分充值实现方案

### 接口设计
```typescript
interface RechargePoints {
  userId: string;
  points: number;
  description?: string;
  metadata?: Record<string, any>;
}

async function rechargeUserPoints(params: RechargePoints): Promise<{
  success: boolean;
  walletId: string;
  currentPoints: number;
}>
```

### 实现步骤
1. 查询用户钱包
   - 如果不存在，创建新钱包
   - 如果存在，获取当前积分

2. 更新积分
   - 计算新的积分总额
   - 更新钱包记录
   - 添加充值记录到交易历史

### 简化说明
- 不使用事务锁，因为充值是独立流程
- 在 JavaScript 层面处理积分计算
- 使用单一 update 操作更新数据库

## 3. 使用示例

```typescript
// 在支付回调中使用
if (orderSuccess) {
  const result = await rechargeUserPoints({
    userId: order.userId,
    points: orderExtra.points,
    metadata: { orderId: order.id }
  });
}
```
