# 绘图组件性能优化实现记录

## 已完成的优化

我们已经完成了以下优化工作：

1. 在 `@store/draw/index.ts` 中添加了历史记录相关的状态和方法：
   - 添加了 `recentHistories` 和 `pendingHistories` 状态
   - 添加了 `fetchCombinedHistories` 方法，使用原有的 `/api/history/combined` 接口
   - 添加了 `startHistoryPolling` 和 `stopHistoryPolling` 方法
   - 实现了数据比对逻辑，只在关键字段变化时才更新状态

2. 修改了 `@components/draw/tabs/generate-tab.tsx` 组件：
   - 移除了组件内部的数据获取和轮询逻辑
   - 使用 store 中的数据和轮询机制
   - 保留了用户信息刷新逻辑

3. 修改了 `@components/draw/recent-generations.tsx` 组件：
   - 添加了对 store 的支持
   - 保持了向后兼容性，可以同时支持 props 和 store 数据
   - 优化了刷新按钮的处理逻辑

4. 修改了 `@components/draw/draw-pending.tsx` 组件：
   - 添加了对 store 的支持
   - 保持了向后兼容性，可以同时支持 props 和 store 数据
   - 优化了刷新按钮的处理逻辑

## 优化效果

1. 减少了重复的数据请求
   - 之前每个组件都会单独请求数据
   - 现在统一由 store 管理数据请求

2. 减少了不必要的渲染
   - 之前每次轮询都会触发整个组件树的重新渲染
   - 现在只有在数据真正变化时才会触发渲染

3. 简化了组件逻辑
   - 组件不再需要关心数据获取和轮询逻辑
   - 组件只需要关注自己的渲染逻辑

4. 统一了数据管理
   - 所有组件使用同一份数据
   - 避免了数据不一致的问题

## 实现细节

1. 数据比对逻辑
   - 比较历史记录数组的长度
   - 比较历史记录的关键字段（id/status/drawStatus/share）
   - 只有在关键字段变化时才更新状态

2. 轮询机制
   - 使用 `setInterval` 实现轮询
   - 轮询间隔为 5 秒
   - 组件卸载时自动清除轮询

3. 向后兼容性
   - 组件可以同时支持 props 和 store 数据
   - 优先使用 props 中的数据，如果没有传入则使用 store 中的数据

## 后续优化方向

1. 考虑使用 WebSocket 替代轮询，进一步提高实时性和性能
2. 实现更细粒度的组件拆分，减少渲染范围
3. 考虑使用虚拟列表优化长列表渲染性能
4. 添加更多的缓存机制，减少不必要的数据请求
