# Admin Dashboard

This is the admin dashboard for the application. It is only accessible to users who are members of an organization with the slug "root" in Clerk.

## Features

- Protected routes that only allow admin users
- Sidebar navigation with links to different admin sections
- Tabs-based interface for each section
- Responsive design that works on both desktop and mobile
- Real-time statistics and data visualization
- Detailed reports with time range filtering

## How to Access

1. Create an organization in Clerk with the slug "root"
2. Add users to this organization to grant them admin access
3. Navigate to `/admin` to access the dashboard

## Available Pages

- `/admin` - Main dashboard page with overview and detailed reports tabs
- `/admin/users` - User management page with user listing and search functionality
- `/admin/users/[id]` - User detail page showing user information and activity
- `/admin/orders` - Orders management page with filtering and status updates
- `/admin/orders/[id]` - Order detail page showing complete order information
- `/admin/histories` - Image generation history with filtering and search
- `/admin/shares` - Shared content management with filtering and search
- `/admin/exchange` - Points exchange management for adding points to user wallets
- `/admin/analytics` - Detailed analytics page for image generation history
- `/admin/backup` - Image backup management for storing images in Cloudflare R2
- `/admin/invitations` - Invitation code management with creation, editing, and reward redemption
- `/admin/invitations/[id]` - Invitation detail page showing complete invitation information and usage records
- `/admin/blocklists` - 黑名单管理页面，用于控制特定用户的奖励积分

## Blocklist Management

The blocklist management system allows administrators to control which users receive reduced initial points upon registration:

### Blocklist Rules List
- View all blocklist rules in the system
- Filter by rule type (email, IP) and status (enabled, disabled)
- Create new blocklist rules with regular expression patterns
- Edit existing blocklist rules
- Enable/disable rules without deleting them
- Delete rules that are no longer needed

### Blocklist Rule Creation
- Select rule type (email, IP)
- Enter a regular expression pattern for matching
- Add an optional description for the rule
- Set the initial enabled/disabled status

### User Registration Integration
- When a new user registers, their email is checked against all enabled email blocklist rules
- If a match is found, the user receives only 1 point instead of the standard 300 points
- The system logs the blocklist match in the user's registration order

## Invitation Management

The invitation management system allows administrators to manage invitation codes and track their usage:

### Invitation Codes List
- View all invitation codes in the system
- Filter by invitation code, referrer email, and reward type
- Sort by creation date, invitation code, or referrer name
- Create new invitation codes with custom settings
- Edit existing invitation codes
- View detailed statistics for each invitation code

### Invitation Detail Page
- View complete invitation information including:
  - Invitation code and link
  - Reward type and ratio
  - Usage limits and expiration
  - Referrer information
- View usage statistics (total uses, pending, ready for redemption, completed)
- View all usage records with filtering by status
- Redeem rewards for eligible usage records
- Edit invitation code properties

### User Detail Page Integration
- The user detail page includes a new "Invitations" tab
- Shows invitations sent by the user
- Shows invitations received by the user (if any)
- Provides links to view complete invitation details

## Dashboard Overview

The dashboard provides a comprehensive view of key metrics and recent activity with time range filtering:

### Time Range Selector
- Located in the top-right corner of the dashboard
- Allows filtering all dashboard data by different time periods:
  - Today
  - Last 3 Days (default)
  - Last Week
  - Last Month
  - Custom Range (with date picker)

### Summary Cards
- **Total Revenue**: Shows the total payments processed with growth percentage compared to previous period
- **Subscriptions**: Displays new user registrations with growth percentage compared to previous period
- **Generations**: Shows total image generations with growth percentage compared to previous period
- **Active Now**: Displays new shares created with growth percentage since last hour

### Activity Summary
A tabbed interface with separate cards for each tab:
- **Recent Sales** (default tab): List of recent successful self-purchases (excluding system buyers) with user information, transaction price (in currency), and points earned
- **Recent History**: Statistics about image generations with nested tabs for model and style, showing success/failure counts as stacked bar charts with green representing success and red representing failure

## Implementation Details

- Admin authentication is handled by checking if the user belongs to an organization with the slug "root"
- The `AdminCheck` component ensures that only admin users can access the dashboard
- The admin link in the navigation bar is only visible to admin users
- The sidebar uses the shadcn UI sidebar component for a consistent look and feel
- Dashboard components are modular and reusable
- API endpoints provide aggregated statistics and filtered data
- Order status can be modified directly from the orders table
- Invitation rewards can be redeemed directly from the invitation usage table

## UI Components

- **Sidebar**: Provides navigation between different admin sections
- **Tabs**: Organizes content within each section
- **Cards**: Display information and statistics in a structured way
- **Tables**: Display detailed data with sorting, filtering, and pagination
- **Status Selector**: Interactive component for updating order status
- **Time Range Selector**: Filter data by different time periods
- **Date Picker**: For selecting date ranges in filters (see [UI Components Documentation](./ui-components.md) for proper usage)
- **Responsive Layout**: Adapts to different screen sizes

## Data Display Conventions

- User information is displayed with username on top, email in the middle, and clerkId at the bottom
- Order buyer types are displayed as "Self" (when buyerId equals userId) or "System" (when different)
- Order status is color-coded and can be modified if it's in the allowed status list (currently only PENDING)
- Trade-related fields (outTradeNo, tradeNo, qrCodeUrl, paidAt, refundedAt) are displayed in order details
- Invitation codes are validated to only allow lowercase letters and numbers
- Invitation usage status is color-coded (pending, ready, completed, void)
- Invitation rewards can be redeemed if the status is "ready"

