# 黑名单功能实施清单

## 数据库设计

- [x] 创建数据库迁移文件，添加 blocklists 表
- [x] 在 schema.ts 中添加表定义和关系

## 常量和工具函数

- [x] 创建 constants/blocklist.ts 文件，定义相关常量
- [x] 实现 lib/blocklist/index.ts 文件，包含黑名单检查函数
- [x] 添加关键词类型支持
- [x] 实现关键词检查函数

## 用户注册流程修改

- [x] 修改 lib/db/user.ts 中的 syncUser 函数，添加黑名单检查逻辑

## 管理员 API

- [x] 实现 GET /api/admin/blocklists 接口，获取黑名单列表
- [x] 实现 POST /api/admin/blocklists 接口，创建黑名单规则
- [x] 实现 GET /api/admin/blocklists/[id] 接口，获取单个黑名单规则
- [x] 实现 PUT /api/admin/blocklists/[id] 接口，更新黑名单规则
- [x] 实现 DELETE /api/admin/blocklists/[id] 接口，删除黑名单规则

## 管理界面

- [x] 创建 components/admin/blocklists/blocklists-table.tsx 组件，显示黑名单规则列表
- [x] 创建 components/admin/blocklists/blocklist-form.tsx 组件，用于添加和编辑规则
- [x] 创建 components/admin/blocklists/index.tsx 组件，整合所有黑名单管理功能（包含了编辑和删除功能）
- [x] 创建 app/admin/blocklists/page.tsx 页面，使用黑名单管理组件
- [x] 创建 app/admin/blocklists/add/page.tsx 页面，用于添加黑名单规则
- [x] 创建 app/admin/blocklists/edit/[id]/page.tsx 页面，用于编辑黑名单规则
- [x] 更新表格显示，添加匹配规则列

## 测试

- [ ] 编写单元测试，测试黑名单检查函数
- [ ] 手动测试黑名单规则的增删改查
- [ ] 手动测试用户注册流程，验证黑名单功能是否生效

## 文档

- [x] 更新 documents/admin.md，添加黑名单管理页面的说明
- [x] 添加代码注释，说明黑名单功能的实现细节
