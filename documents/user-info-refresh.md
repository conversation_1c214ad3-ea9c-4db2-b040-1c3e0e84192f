# User Information Refresh Implementation

## Current Issues

Currently, there are multiple implementations of user information refresh functionality across different components:

1. `@lib/hooks/use-user.ts` - Contains a `fetchUser()` method to retrieve user profile data
2. `@store/profile/index.ts` - Contains a `fetch()` method to retrieve user profile data
3. `@components/global/user-initializer.tsx` - Implements its own `refreshUserInfo()` method
4. `@components/draw/draw-generator.tsx` - Uses the `refreshUserInfo()` from `user-initializer.tsx`
5. `@components/draw/draw-preview.tsx` - Also uses the `refreshUserInfo()` from `user-initializer.tsx`

This leads to:
- Duplicate code and logic
- Potential inconsistencies in how user data is refreshed
- Multiple API calls to the same endpoint with different query parameters (`?t=11`, `?t=12`, `?t=13`)
- Difficulty in maintaining and updating the refresh logic

## Refactoring Approach

We will centralize the user information refresh functionality in the `@store/profile/index.ts` file:

1. **Enhance the Profile Store**:
   - Rename the `fetch()` method to `refreshUserInfo()` for clarity
   - Ensure it returns the fetched data for components that need immediate access
   - Add a timestamp parameter to prevent caching issues
   - Keep the original `fetch()` method as an alias for backward compatibility

2. **Update User Initializer**:
   - Remove the duplicate `refreshUserInfo` implementation
   - Modify the store to use `useProfileStore.refreshUserInfo()` instead
   - Maintain backward compatibility by exposing the same interface

3. **Update use-user.ts Hook**:
   - Modify the `fetchUser()` method to use `useProfileStore.refreshUserInfo()` instead of making its own API call
   - This ensures all user data fetching goes through a single centralized function

4. **Update Components**:
   - All components continue to use their existing methods, which now indirectly use the centralized function
   - Ensure consistent data access patterns

## Implementation Details

### 1. Profile Store (`@store/profile/index.ts`)

```typescript
// Enhanced profile store with renamed method
export const useProfileStore = create<ProfileState>((set) => ({
  // ... existing state
  refreshUserInfo: async () => {
    try {
      set({ isLoading: true, error: null });
      const timestamp = Date.now(); // Add timestamp to prevent caching
      const res = await fetch(`/api/me?t=${timestamp}`);

      if (!res.ok) {
        throw new Error('Failed to fetch profile');
      }

      const data = await res.json();
      set({
        profile: {
          ...data.user,
          isPaid: data.user.isPaid ?? false,
        },
        wallet: data.wallet,
        transactions: data.recentTransactions,
        isNewUser: data.isNewUser,
        isLoading: false,
      });

      return data; // Return data for immediate access if needed
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to load profile',
        isLoading: false,
      });
      throw error; // Rethrow for error handling in components
    }
  },
  // Keep fetch as an alias for backward compatibility
  fetch: async () => {
    return useProfileStore.getState().refreshUserInfo();
  }
}));
```

### 2. User Initializer (`@components/global/user-initializer.tsx`)

```typescript
// Updated user store to use the profile store
export const useUserStore = create<UserStore>((set) => ({
  credits: 0,
  currentUser: null,
  setCredits: (credits) => set({ credits }),

  // Updated to use the profile store
  refreshUserInfo: async () => {
    try {
      const data = await useProfileStore.getState().refreshUserInfo();

      if (data.wallet) {
        set({ credits: data.wallet.permanentPoints });
      }

      // Set current user
      const user = data.user || null;
      if (user) {
        user.isNewUser = data.isNewUser;
        console.log('[USER_REFRESH] User paid status:', user.isPaid);
      }
      set({ currentUser: user });

      return data;
    } catch (error) {
      console.error('[USER_REFRESH_ERROR]', error);
      throw error;
    }
  },
}));
```

### 3. User Hook (`@lib/hooks/use-user.ts`)

```typescript
// Updated to use the profile store
export const useUserStore = create<UserState>((set) => ({
  // ... existing state
  fetchUser: async () => {
    try {
      set({ isLoading: true, error: null });

      // 使用 ProfileStore 的 refreshUserInfo 方法获取用户数据
      const data = await useProfileStore.getState().refreshUserInfo();

      // 设置 UserStore 的状态
      set({
        user: data.user,
        wallet: data.wallet,
        recentTransactions: data.recentTransactions,
        recentHistories: data.recentHistories,
        isNewUser: data.isNewUser,
        isLoading: false,
      });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'An error occurred',
        isLoading: false,
      });
    }
  },
}));
```

### 4. Component Updates

All components that currently call `refreshUserInfo()` from `useUserStore` or `fetchUser()` from `useUser()` will continue to work with the updated implementation, but they will now indirectly use the centralized function from the profile store.

## Benefits

1. **Single Source of Truth**: All user data refresh operations go through a single function in `@store/profile/index.ts`
2. **Consistency**: All components receive the same data in the same format
3. **Maintainability**: Changes to the refresh logic only need to be made in one place
4. **Performance**: Reduces duplicate API calls to `/api/me`
5. **Backward Compatibility**: Existing components continue to work without changes to their implementation
6. **Simplified Debugging**: When issues occur with user data fetching, there's only one place to check

## Data Flow

After this refactoring, the data flow for user information is as follows:

```
/api/me API endpoint
       ↑
       │
       │ Single API call with timestamp
       │
 useProfileStore.refreshUserInfo()
       │
       ├─────────────────┬─────────────────┐
       │                 │                 │
 useUserStore      useUserStore.      components using
.fetchUser()     refreshUserInfo()    useProfileStore
       │                 │                 │
       ↓                 ↓                 ↓
Components using    Components using   Direct profile
useUser() hook     useUserStore       data access
```

This ensures that all user data comes from a single source, regardless of which store or hook components use to access it.
