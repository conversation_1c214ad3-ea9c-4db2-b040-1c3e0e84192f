# Payment Integration Documentation

This document outlines the payment integration process using the Rainbow Payment API (彩虹易支付) in our Next.js application.

## Overview

The payment system supports multiple payment methods:
- alipay: Alipay (支付宝)
- wxpay: WeChat Pay (微信支付)

## Environment Setup

Required environment variables in `.env`:
```env
PAY_API_URL=https://api.dulupay.com/
PAY_PID=<your_merchant_id>
PAY_PUBLIC_KEY=<platform_public_key>
PAY_MERCHANT_PRIVATE_KEY=<your_private_key>
```

## Implementation Flow

### 1. Payment Initiation
When user selects a payment method and package:

1. Frontend sends request to `/api/purchase` with:
   ```typescript
   {
     type: "alipay" | "wxpay",
     packageId: string,
     userId: string
   }
   ```

2. Backend:
   - Creates order record in `orders` table
   - Generates unique `out_trade_no`
   - Prepares payment request with signature
   - Sends request to payment gateway
   - Returns payment QR code URL and order ID

### 2. QR Code Display
Frontend:
- Receives payment URL and order ID
- Uses QR code library (e.g., `qrcode.react`) to display payment QR code
- Starts polling order status

### 3. Order Status Polling
Frontend polls `/api/orders/[id]/status` endpoint:
```typescript
GET /api/orders/:id/status
Response: {
  code: 0,
  msg: "Success",
  data: {
    status: "PENDING" | "SUCCESS" | "FAILED" | "REFUND",
    trade_status: "PENDING" | "SUCCESS" | "FAILED" | "REFUND",
    trade_no: string | null,
    money: number,
    orderId: string,
    amount: number,
    createdAt: string
  }
}
```

### 4. Payment Notification
Payment gateway sends notification to `/api/public/epay`:
1. Backend:
   - Verifies notification signature
   - Queries payment gateway for order status
   - Updates order status in database
   - If payment successful:
     - Updates order status to "success"
     - Credits user's wallet with package points
     - Triggers any necessary notifications

## Database Schema

The application uses Drizzle ORM with PostgreSQL. The relevant tables are:

### Orders Table
```typescript
export const orders = pgTable("orders", {
  id: text("id").primaryKey(),
  userId: text("user_id").references(() => users.clerkId).notNull(),
  buyerId: text("buyer_id").notNull(), // can be "system" for system gifts
  type: text("type").notNull(), // "credit" or "debit"
  amount: integer("amount").notNull(),
  description: text("description").notNull(),
  extra: jsonb("extra").default({}).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});
```

### Wallets Table
```typescript
export const wallets = pgTable("wallets", {
  id: text("id").primaryKey(),
  userId: text("user_id").references(() => users.clerkId).notNull(),
  permanentPoints: integer("permanent_points").default(0).notNull(),
  extra: jsonb("extra").default({}).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});
```

For payment integration, we'll extend the `orders` table's `extra` field to include:
```typescript
{
  paymentType: "alipay" | "wxpay",
  outTradeNo: string,
  tradeNo?: string,
  tradeStatus?: "pending" | "success" | "failed",
  paymentGatewayResponse?: any
}
```

## API Endpoints

### Create Payment Order
```typescript
POST /api/purchase
Request: {
  type: "alipay" | "wxpay",
  packageId: string,
  userId: string
}
Response: {
  orderId: string,
  qrCodeUrl: string
}
```

### Get Order Status
```typescript
GET /api/orders/:id/status
Response: {
  status: "pending" | "success" | "failed",
  orderId: string,
  amount: number,
  createdAt: string
}
```

### Payment Notification
```typescript
POST /api/public/epay
Request: {
  out_trade_no: string,
  trade_no: string,
  type: string,
  name: string,
  money: string,
  trade_status: string,
  sign: string
}
Response: "success" | "fail"
```

## Payment Notification Endpoints Design

### Common Flow for Both Endpoints

1. **Request Validation**
   - Verify request signature using merchant private key
   - Extract common parameters:
     - `out_trade_no`: Merchant order number
     - `trade_no`: Platform transaction number
     - `type`: Payment method
     - `money`: Payment amount
     - `trade_status`: Payment status
     - `sign`: Request signature
     - `sign_type`: Signature type (default: RSA)

2. **Order Verification**
   - Query local database for order using `out_trade_no`
   - Verify order amount matches payment amount
   - Check if order has already been processed

3. **Payment Status Query**
   - Query payment gateway for order status using `/api/pay/query`
   - Parameters:
     - `pid`: Merchant ID
     - `out_trade_no`: Order number
     - `signature`: Request signature
   - Response status codes:
     - `0`: Unpaid
     - `1`: Paid
     - `2`: Refunded

4. **Order Processing**
   - Update order status in database
   - If payment successful:
     - Update user's wallet balance
     - Record transaction details
   - If payment failed:
     - Mark order as failed
     - Log failure reason

### Notify Endpoint (`/api/public/epay/notify`)

**Purpose**: Handles asynchronous notifications from payment gateway

**Flow**:
1. Receive POST request from payment gateway
2. Follow common validation flow
3. Return response:
   - Success: Return plain text "success"
   - Failure: Return plain text "fail"

**Implementation Details**:
```typescript
POST /api/public/epay/notify
Content-Type: application/x-www-form-urlencoded

Request:
{
  out_trade_no: string;    // ORDER_xxx
  trade_no: string;        // Platform transaction ID
  type: string;           // Payment method
  money: string;          // Amount paid
  name: string;           // Product name
  trade_status: string;   // TRADE_SUCCESS
  sign: string;          // Signature
  sign_type: string;     // RSA
}

Response:
Text: "success" | "fail"
```

### Error Handling

1. **Signature Verification Errors**
   - Log invalid signature details
   - Return failure response
   - Do not process order

2. **Order Verification Errors**
   - Log order mismatch details
   - Return failure response
   - Flag order for review

3. **Payment Status Query Errors**
   - Retry query with exponential backoff
   - Log communication errors
   - Return temporary failure

4. **Database Update Errors**
   - Implement transaction rollback
   - Log error details
   - Return system error

### Security Considerations

1. **Request Validation**
   - Verify all signatures
   - Validate amount matches
   - Check request timestamp
   - Prevent replay attacks

2. **Data Processing**
   - Use database transactions
   - Handle concurrent notifications
   - Prevent double-processing

3. **Error Handling**
   - Log all errors securely
   - Don't expose internal details
   - Implement retry mechanism

4. **Access Control**
   - Rate limit requests
   - Whitelist payment gateway IPs
   - Validate origin headers

### Implementation Structure

```typescript
// Types
interface PaymentNotification {
  out_trade_no: string;
  trade_no: string;
  type: string;
  money: string;
  name: string;
  trade_status: string;
  sign: string;
  sign_type: string;
}

// Common utilities
class PaymentNotificationHandler {
  async validateSignature(data: PaymentNotification): Promise<boolean>;
  async verifyOrder(data: PaymentNotification): Promise<Order>;
  async queryPaymentStatus(orderId: string): Promise<PaymentStatus>;
  async processPayment(order: Order, status: PaymentStatus): Promise<void>;
}

// Endpoints
class NotifyEndpoint {
  async POST(req: Request): Promise<Response>;
}

class ReturnEndpoint {
  async GET(req: Request): Promise<Response>;
}
```

## Security Considerations

1. All API endpoints must verify user authentication
2. Payment notifications must verify signature
3. Order status updates must be atomic
4. Wallet updates must be transactional
5. Implement rate limiting for status polling
6. Log all payment-related activities

## Error Handling

1. Payment Gateway Errors:
   - Network timeouts
   - Invalid signatures
   - Insufficient balance

2. Application Errors:
   - Invalid order data
   - Database transaction failures
   - Wallet update failures

3. User Experience:
   - Clear error messages
   - Retry mechanisms
   - Status notifications

## Testing

1. Test Environment:
   - Use test credentials
   - Mock payment gateway responses
   - Test all payment methods

2. Integration Tests:
   - Complete payment flow
   - Notification handling
   - Wallet updates
   - Error scenarios

3. Production Deployment:
   - SSL/TLS required
   - Proper logging
   - Monitoring setup

## TODO List

### Backend Implementation
- [ ] Create payment utility functions
  - [ ] Implement signature generation
  - [ ] Implement signature verification
  - [ ] Create payment gateway client

- [ ] Implement API endpoints
  - [ ] POST /api/purchase
    - [ ] Create order record
    - [ ] Generate payment request
    - [ ] Return QR code URL
  - [ ] GET /api/orders/:id/status
    - [ ] Query order status
    - [ ] Return formatted response
  - [ ] POST /api/public/epay
    - [ ] Verify notification signature
    - [ ] Update order status
    - [ ] Credit user wallet
    - [ ] Send success response

- [ ] Database operations
  - [ ] Create order with payment details
  - [ ] Update order status
  - [ ] Update user wallet points
  - [ ] Implement transaction handling

### Frontend Implementation
- [ ] Create payment components
  - [ ] Payment method selection
  - [ ] QR code display
  - [ ] Order status display
  - [ ] Error handling UI

- [ ] Implement payment flow
  - [ ] Send payment request
  - [ ] Display QR code
  - [ ] Poll order status
  - [ ] Handle success/failure states

### Security & Monitoring
- [ ] Implement security measures
  - [ ] User authentication
  - [ ] Rate limiting
  - [ ] Input validation
  - [ ] Error logging

- [ ] Set up monitoring
  - [ ] Payment success rate tracking
  - [ ] Error rate monitoring
  - [ ] Transaction logging
  - [ ] Performance metrics

### Testing & Documentation
- [ ] Write tests
  - [ ] Unit tests for payment utilities
  - [ ] API endpoint tests
  - [ ] Integration tests
  - [ ] Frontend component tests

- [ ] Documentation
  - [ ] API documentation
  - [ ] Integration guide
  - [ ] Error handling guide
  - [ ] Deployment guide

### Deployment & Operations
- [ ] Production setup
  - [ ] Environment configuration
  - [ ] SSL/TLS setup
  - [ ] Monitoring setup
  - [ ] Backup procedures

- [ ] Maintenance procedures
  - [ ] Order cleanup
  - [ ] Failed payment handling
  - [ ] System health checks
  - [ ] Performance optimization

## Payment API Documentation

### Create Payment Order API

API Endpoint: `https://api.dulupay.com/api/pay/create`
Method: `POST`
Content-Type: `application/x-www-form-urlencoded`

#### Request Parameters

| Field Name | Parameter | Required | Type | Example | Description |
|------------|-----------|----------|------|---------|-------------|
| 商户ID | pid | Yes | Int | 1001 | |
| 接口类型 | method | Yes | String | web | See Interface Types |
| 设备类型 | device | No | String | pc | See Device Types |
| 支付方式 | type | Yes | String | alipay | See Payment Types |
| 商户订单号 | out_trade_no | Yes | String | 20160806151343349 | |
| 异步通知地址 | notify_url | Yes | String | http://www.pay.com/notify_url.php | Server notification URL |
| 跳转通知地址 | return_url | Yes | String | http://www.pay.com/return_url.php | Page redirect URL |
| 商品名称 | name | Yes | String | VIP会员 | Max 127 bytes |
| 商品金额 | money | Yes | String | 1.00 | Unit: Yuan, max 2 decimals |
| 用户IP地址 | clientip | Yes | String | ************* | User's IP address |
| 业务扩展参数 | param | No | String | | Optional |
| 当前时间戳 | timestamp | Yes | String | 1721206072 | 10-digit seconds |
| 签名字符串 | sign | Yes | String | | See Signature Rules |
| 签名类型 | sign_type | Yes | String | RSA | Default: RSA |

#### Response Parameters

| Field Name | Parameter | Type | Example | Description |
|------------|-----------|------|---------|-------------|
| 返回状态码 | code | Int | 0 | 0 = success, others = failure |
| 错误信息 | msg | String | | Error message on failure |
| 平台订单号 | trade_no | String | 20160806151343349 | Platform order number |
| 发起支付类型 | pay_type | String | qrcode | See Payment Response Types |
| 发起支付参数 | pay_info | String | weixin://wxpay/bizpayurl?pr=04IPMKM | Varies by pay_type |
| 当前时间戳 | timestamp | String | 1721206072 | 10-digit seconds |
| 签名字符串 | sign | String | | See Signature Rules |
| 签名类型 | sign_type | String | RSA | Default: RSA |

#### Interface Types (method)

| Value | Description |
|-------|-------------|
| web | Universal web payment (auto-detects based on device) |
| jump | Redirect payment (returns redirect URL only) |
| jsapi | JSAPI payment (for mini-programs) |
| app | APP payment (for iOS/Android apps) |
| scan | Payment code scanning |
| applet | Mini-program payment |

#### Device Types (device)

| Value | Description |
|-------|-------------|
| pc | Computer browser (default) |
| mobile | Mobile browser |
| qq | Mobile QQ browser |
| wechat | WeChat browser |
| alipay | Alipay client |

#### Payment Response Types (pay_type)

| Value | Description |
|-------|-------------|
| jump | Payment redirect URL |
| html | HTML code for payment redirect |
| qrcode | Payment QR code |
| urlscheme | WeChat/Alipay mini-program URL scheme |
| jsapi | JSAPI payment parameters |
| app | APP payment parameters |
| scan | Payment code order info |
| wxplugin | WeChat mini-program plugin parameters |
| wxapp | WeChat mini-program path and parameters |

#### Response Examples

```json
// QR Code Payment
{
    "code": 0,
    "trade_no": "20160806151343349",
    "pay_type": "qrcode",
    "pay_info": "weixin://wxpay/bizpayurl?pr=04IPMKM"
}

// JSAPI Payment
{
    "code": 0,
    "trade_no": "20160806151343351",
    "pay_type": "jsapi",
    "pay_info": "{\"appId\":\"wx2421b1c4370ec43b\",\"timeStamp\":\"1395712654\",\"nonceStr\":\"e61463f8efa94090b1f366cccfbbb444\",\"package\":\"prepay_id=up_wx21201855730335ac86f8c43d1889123400\",\"signType\":\"RSA\",\"paySign\":\"oR9d8PuhnIc+YZ8cBHFCwfgpaK9gd7vaRvkYD7rthRAZ\"}"
}
```

#### Notes

1. The code should handle different payment types based on the `pay_type` response.
2. For simple integration, use `method: "jump"` to get a direct payment URL.
3. Payment code scanning requires `auth_code`.
4. Mini-program plugin payment varies by platform.
5. APP launching WeChat mini-program requires following WeChat's documentation.

## Order Status Polling Implementation

### Overview
The order status polling system needs to handle the following requirements:
1. Start polling when QR code is displayed
2. Poll at regular intervals (3 seconds)
3. Handle different order statuses properly
4. Clean up resources when component unmounts
5. Update user data on successful payment
6. Close dialog after successful payment

### Implementation Details

#### 1. Polling Hook (useOrderPolling.ts)
```typescript
interface UseOrderPollingProps {
  orderId: string | null;
  onSuccess?: () => void;
  onFailed?: () => void;
}

interface OrderPollingState {
  status: OrderStatus | null;
  error: string | null;
  isPolling: boolean;
}

const useOrderPolling = ({ orderId, onSuccess, onFailed }: UseOrderPollingProps) => {
  const [state, setState] = useState<OrderPollingState>({
    status: null,
    error: null,
    isPolling: false
  });

  useEffect(() => {
    if (!orderId) return;

    let mounted = true;
    let timeoutId: NodeJS.Timeout;

    const pollOrder = async () => {
      try {
        const response = await fetch(`/api/orders/${orderId}`);
        const data = await response.json();

        if (!mounted) return;

        if (data.status === "success") {
          setState(prev => ({ ...prev, status: "success" }));
          onSuccess?.();
          return true; // Stop polling
        }

        if (data.status === "failed") {
          setState(prev => ({ ...prev, status: "failed" }));
          onFailed?.();
          return true; // Stop polling
        }

        return false; // Continue polling
      } catch (error) {
        if (!mounted) return true;
        setState(prev => ({
          ...prev,
          error: error instanceof Error ? error.message : "Unknown error"
        }));
        return true; // Stop polling on error
      }
    };

    const startPolling = async () => {
      setState(prev => ({ ...prev, isPolling: true }));

      const shouldStop = await pollOrder();
      if (shouldStop || !mounted) return;

      timeoutId = setTimeout(startPolling, 3000);
    };

    startPolling();

    return () => {
      mounted = false;
      clearTimeout(timeoutId);
      setState(prev => ({ ...prev, isPolling: false }));
    };
  }, [orderId, onSuccess, onFailed]);

  return state;
};
```

#### 2. Payment Dialog Implementation
```typescript
export function PaymentDialog({ tier, isOpen, onOpenChange }: PaymentDialogProps) {
  const { fetchUser } = useUser();
  const [orderId, setOrderId] = useState<string | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);

  const handleSuccess = useCallback(async () => {
    try {
      await fetchUser();
      // Show success message for 2 seconds before closing
      setTimeout(() => {
        onOpenChange(false);
      }, 2000);
    } catch (error) {
      console.error("Failed to update user data:", error);
    }
  }, [fetchUser, onOpenChange]);

  const handleFailed = useCallback(() => {
    // Handle failed payment
    console.error("Payment failed");
  }, []);

  const { status, error, isPolling } = useOrderPolling({
    orderId,
    onSuccess: handleSuccess,
    onFailed: handleFailed
  });

  // Reset state when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setOrderId(null);
      setQrCodeUrl(null);
    }
  }, [isOpen]);

  // ... rest of the component implementation
}
```

### Key Features

1. **Clean Resource Management**
   - Uses cleanup function to clear timeouts
   - Tracks component mount state
   - Properly resets state on dialog close

2. **Error Handling**
   - Handles network errors
   - Handles API errors
   - Provides error feedback to user

3. **State Management**
   - Centralizes polling logic in a custom hook
   - Maintains clear state transitions
   - Provides status updates to parent component

4. **User Experience**
   - Shows loading states
   - Displays appropriate error messages
   - Smooth transition on success

5. **Performance**
   - Uses setTimeout instead of setInterval for better timing control
   - Stops polling immediately on unmount
   - Prevents memory leaks

### Implementation Steps

1. Create `useOrderPolling` hook
2. Refactor PaymentDialog to use the hook
3. Implement proper error handling
4. Add loading states
5. Test all scenarios:
   - Successful payment
   - Failed payment
   - Network errors
   - Component unmounting
   - Dialog closing
