# 项目文档

本目录包含项目的各种文档，用于帮助开发者理解系统的设计和实现。

## 功能文档

- [管理后台](./admin.md) - 管理后台的设计和实现
- [图片备份系统](./backup-readme.md) - 图片备份系统的设计和实现
- [绘图工作流](./draw-workflow.md) - 绘图功能的工作流程
- [绘图组件优化](./draw-optimization.md) - 绘图组件的性能优化方案
- [新图片通知功能](./new-image-notification.md) - 新图片生成完成通知功能的实现
- [历史记录](./history.md) - 历史记录系统的设计和实现
- [积分消费审查](./points-consumption-review.md) - 积分消费逻辑的审查报告
- [积分充值系统](./recharge.md) - 积分充值系统的设计和实现
- [积分兑换系统](./exchange-points.md) - 积分兑换系统的设计和实现
- [分享功能](./sharing.md) - 分享功能的设计和实现
- [用户信息刷新](./user-refresh.md) - 用户信息刷新机制的设计和实现

## 技术文档

- [UI组件](./ui-components.md) - UI组件的使用说明
- [支付集成](./sdk.md) - 支付系统的集成说明
- [Stripe支付集成](./stripe-implementation.md) - Stripe支付系统的集成实现文档
