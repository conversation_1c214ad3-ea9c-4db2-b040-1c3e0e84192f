# 邀请奖励兑换系统更新

## 概述

本文档描述了邀请奖励兑换系统的更新，主要包括以下变更：

1. 在用户界面（/settings/invitation）中，只允许用户自助兑换积分奖励
2. 现金奖励需要联系管理员进行兑换
3. 防止用户多次点击兑换按钮导致重复兑换
4. 积分和现金奖励只能二选一，不能同时兑换
5. 增强并发控制机制，防止并发兑换导致的数据不一致

## 技术实现

### 积分兑换流程

1. 用户在邀请记录列表中点击"兑换积分"按钮
2. 系统显示确认对话框，确认用户要兑换的积分数量
3. 用户确认后，系统调用 `/api/invitation_usages/{id}/redeem` API
4. API 验证请求并调用 `redeemInvitationPoints` 函数处理积分兑换
5. 积分兑换的本质是系统奖励积分给邀请人，理由是邀请奖励
6. 兑换完成后，更新邀请记录状态为"已兑换"

### 现金奖励处理

1. 用户在邀请记录列表中看到现金奖励信息
2. 用户点击"领取现金奖励"按钮
3. 系统显示提示信息："现金奖励需要联系管理员进行兑换"
4. 管理员在后台可以兑换用户的现金奖励

### 防止重复兑换

1. 前端实现：
   - 在用户点击兑换按钮后，立即禁用按钮并显示"处理中..."状态
   - 只有在请求完成（成功或失败）后才重新启用按钮
   - 使用 `redeemingId` 和 `redeemLoading` 状态变量跟踪当前正在处理的记录

2. 后端实现：
   - 在兑换处理过程中，使用数据库事务确保原子性
   - 在更新邀请记录状态前，再次检查记录状态是否为"待兑换"
   - 如果状态已变更，则拒绝兑换请求
   - 使用事务隔离确保并发操作的安全性

> **注意**：关于并发控制和安全考虑的详细信息，请参阅 [邀请奖励系统安全考虑](./invitation-security.md) 文档。

## 数据结构

### 积分兑换记录

积分兑换时，系统会创建一个新的订单记录，包含以下信息：

```typescript
{
  id: string,              // 订单ID
  userId: string,          // 用户ID（邀请人）
  buyerId: "system",       // 购买者ID（系统）
  type: "credit",          // 交易类型（增加积分）
  amount: number,          // 积分数量
  description: string,     // 描述（例如："邀请奖励：100积分"）
  status: "SUCCESS",       // 订单状态
  extra: {                 // 额外信息
    exchangeType: "affiliate",           // 交换类型（邀请奖励）
    invitationUsageId: string,           // 邀请使用记录ID
    refereeId: string,                   // 被邀请人ID
    rechargeAmount: number,              // 充值金额（分）
    pointsExchange: {                    // 积分交换信息
      oldBalance: number,                // 旧余额
      newBalance: number,                // 新余额
      timestamp: string                  // 时间戳
    }
  }
}
```

## API 变更

### `/api/invitation_usages/{id}/redeem`

**请求参数变更**：
- `redeem_type`: 只接受 "points"，不再接受 "cash"

**响应变更**：
- 如果尝试兑换现金奖励，返回错误信息："只能自助兑换积分，现金奖励的兑换需要联系管理员"

## UI 变更

1. 邀请记录列表中，兑换按钮文本从"兑换奖励"改为"兑换积分"
2. 对于现金奖励，显示"联系管理员领取"按钮
3. 点击"联系管理员领取"按钮时，显示提示信息："现金奖励需要联系管理员后发放"

## 安全考虑

1. **并发控制**：
   - 使用数据库事务和状态二次检查防止并发兑换
   - 前端防重复提交机制减少重复请求
   - 详细实现请参考 [邀请奖励系统安全考虑](./invitation-security.md) 文档

2. **权限控制**：
   - 用户只能兑换自己的邀请记录
   - 用户只能兑换积分，不能兑换现金
   - 管理员可以兑换任何用户的邀请记录，包括积分和现金

3. **数据一致性**：
   - 使用事务确保钱包余额更新和邀请记录状态更新的原子性
   - 记录详细的操作日志，便于审计和问题排查
