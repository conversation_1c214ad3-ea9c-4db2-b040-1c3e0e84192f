# Asynchronous Backup System Design

## Overview

This document outlines the design for the new asynchronous backup system. The current backup process runs synchronously, which can lead to timeouts and poor user experience for large backup operations. The new system will:

1. Keep the core backup logic unchanged
2. Convert the interface and API to an asynchronous model
3. Create an `executions` table to track operation status
4. Provide detailed logs and summaries for each execution

## Database Schema

### New Executions Table

We will create a new `executions` table to track asynchronous operations:

```typescript
export type ExecutionStatus = "PENDING" | "RUNNING" | "SUCCESS" | "FAILED";
export type ExecutionType = "BACKUP_IMAGES" | "OTHER_FUTURE_OPERATIONS";

export const executions = pgTable("executions", {
  id: text("id").primaryKey(),
  type: text("type").notNull().$type<ExecutionType>(),
  status: text("status").notNull().$type<ExecutionStatus>().default("PENDING"),
  params: jsonb("params").default({}).notNull(),
  summary: jsonb("summary").default({}).notNull(),
  logs: text("logs").default(""),
  startedAt: timestamp("started_at"),
  completedAt: timestamp("completed_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});
```

Fields explanation:
- `id`: Unique identifier for the execution
- `type`: Type of operation (e.g., "BACKUP_IMAGES")
- `status`: Current status of the execution
- `params`: JSON object containing operation parameters
- `summary`: JSON object containing operation results summary
- `logs`: Text field containing detailed operation logs
- `startedAt`: Timestamp when the operation started
- `completedAt`: Timestamp when the operation completed
- `createdAt`: Timestamp when the execution record was created
- `updatedAt`: Timestamp when the execution record was last updated

## API Endpoints

### Create Backup Execution

- **URL**: `/api/admin/backup/image`
- **Method**: `POST`
- **Body**:
  ```json
  {
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2023-01-31T23:59:59.999Z",
    "dryRun": true,
    "batchSize": 10,
    "skipBackupCheck": false
  }
  ```
- **Response**:
  ```json
  {
    "message": "Backup execution created",
    "executionId": "exec_123456789"
  }
  ```

### Get Execution Status

- **URL**: `/api/admin/executions/[id]`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "execution": {
      "id": "exec_123456789",
      "type": "BACKUP_IMAGES",
      "status": "RUNNING",
      "params": {
        "startDate": "2023-01-01T00:00:00.000Z",
        "endDate": "2023-01-31T23:59:59.999Z",
        "dryRun": true,
        "batchSize": 10,
        "skipBackupCheck": false
      },
      "summary": {
        "totalProcessed": 10,
        "succeeded": 5,
        "failed": 0,
        "skipped": 0,
        "inProgress": 5
      },
      "startedAt": "2023-01-01T12:00:00.000Z",
      "createdAt": "2023-01-01T11:59:00.000Z",
      "updatedAt": "2023-01-01T12:01:00.000Z"
    }
  }
  ```

### List Executions

- **URL**: `/api/admin/executions`
- **Method**: `GET`
- **Query Parameters**:
  - `type`: Filter by execution type
  - `status`: Filter by execution status
  - `page`: Page number
  - `limit`: Items per page
- **Response**:
  ```json
  {
    "executions": [
      {
        "id": "exec_123456789",
        "type": "BACKUP_IMAGES",
        "status": "SUCCESS",
        "summary": {
          "totalProcessed": 100,
          "succeeded": 95,
          "failed": 5,
          "skipped": 0
        },
        "startedAt": "2023-01-01T12:00:00.000Z",
        "completedAt": "2023-01-01T12:10:00.000Z",
        "createdAt": "2023-01-01T11:59:00.000Z",
        "updatedAt": "2023-01-01T12:10:00.000Z"
      },
      // More executions...
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "totalCount": 50,
      "totalPages": 5
    }
  }
  ```

## Admin UI

### Backup Page (`/admin/backup`)

The backup page will be updated to:
1. Allow selecting a date range and backup options
2. Create a new backup execution
3. Show a list of recent backup executions
4. Provide links to execution detail pages

### Execution Detail Page (`/admin/backup/[id]`)

A new execution detail page will be created to:
1. Show execution details (type, status, parameters)
2. Display execution summary (total processed, succeeded, failed, skipped)
3. Show execution logs
4. Allow downloading logs
5. Provide a link back to the backup page

## Implementation Plan

1. Create the executions table schema
2. Modify the image-backup.ts file to support asynchronous operations
3. Create/update API endpoints for the new asynchronous backup process
4. Implement the admin UI for the new backup system

## Execution Flow

1. User initiates a backup operation from the admin UI
2. System creates a new execution record with status "PENDING"
3. System returns the execution ID to the client
4. Client redirects to the execution detail page
5. System starts the backup operation in the background
6. System updates the execution record status to "RUNNING"
7. System periodically updates the execution record with progress information
8. When the operation completes, system updates the execution record status to "SUCCESS" or "FAILED"
9. Client polls the execution status and updates the UI accordingly

## Cron Job

The daily backup cron job will be updated to use the new asynchronous backup system:
1. Create a new execution record
2. Start the backup operation in the background
3. Update the execution record with progress and results
