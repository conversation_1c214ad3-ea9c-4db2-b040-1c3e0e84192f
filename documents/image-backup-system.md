# 图片备份系统完整文档

本文档全面梳理了图片备份系统的实现，包括主动备份和自动备份功能，以及最近的改进。

## 1. 系统概述

图片备份系统用于将 `histories` 表中的图片自动备份到 Cloudflare R2 存储服务中。系统支持两种备份模式：

1. **主动备份**：通过管理界面手动触发，可以指定日期范围和其他参数
2. **自动备份**：通过 Vercel Cron Jobs 定时触发，包括每日备份和每小时备份

备份过程会：

1. 查询指定时间范围内的历史记录（仅处理 `status` 为 `true`、`drawStatus` 不为 `FAILED` 且 `backupStatus` 不为 `FAILED` 的记录，除非启用了 `skipBackupCheck`）
2. 从 `extra.originalUrl` 或 `resultUrl` 下载图片
3. 上传图片到 Cloudflare R2 存储桶，路径为 `userId/historyId/<filename>`
4. 更新历史记录的 `resultUrl` 和备份状态
5. 如果存在关联的分享记录，同时更新分享记录的 `imageUrl`
6. 记录操作结果和日志

## 2. 系统架构

### 2.1 数据模型

#### 历史记录表 (histories)

```typescript
export const histories = pgTable("histories", {
  // 其他字段...
  resultUrl: text("result_url"),
  backupStatus: text("backup_status").$type<BackupStatus>().default("PENDING"),
  lastBackupAt: timestamp("last_backup_at"),
  extra: jsonb("extra").default({}).notNull(),
  // 其他字段...
});
```

#### 执行记录表 (executions)

```typescript
export type ExecutionStatus = "PENDING" | "RUNNING" | "SUCCESS" | "FAILED";
export type ExecutionType = "BACKUP_IMAGES" | "OTHER_FUTURE_OPERATIONS";

export const executions = pgTable("executions", {
  id: text("id").primaryKey(),
  type: text("type").notNull().$type<ExecutionType>(),
  status: text("status").notNull().$type<ExecutionStatus>().default("PENDING"),
  params: jsonb("params").default({}).notNull(),
  summary: jsonb("summary").default({}).notNull(),
  logs: text("logs").default(""),
  startedAt: timestamp("started_at"),
  completedAt: timestamp("completed_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});
```

### 2.2 核心组件

1. **R2 客户端**：`lib/storage/r2-client.ts` - 处理与 Cloudflare R2 的连接和文件操作
2. **执行管理器**：`lib/execution/execution-manager.ts` - 管理异步执行记录
3. **备份服务**：`lib/backup/async-image-backup.ts` - 实现备份核心逻辑
4. **API 端点**：
   - `app/api/admin/backup/image/route.ts` - 管理员手动触发备份
   - `app/api/public/backup/route.ts` - 自动备份（由 cron 触发）
   - `app/api/public/backup/fix/route.ts` - 修复卡住的备份执行
5. **管理界面**：
   - `app/admin/backup/page.tsx` - 备份管理页面
   - `app/admin/backup/[id]/page.tsx` - 备份执行详情页面

### 2.3 配置

系统需要以下环境变量：

```
# Cloudflare R2 Storage
R2_ACCOUNT_ID=your_account_id
R2_ACCESS_KEY_ID=your_access_key_id
R2_SECRET_ACCESS_KEY=your_secret_access_key
R2_BUCKET_NAME=your_bucket_name
R2_PUBLIC_URL_PREFIX=https://storage.example.com

# Cron Secret
CRON_SECRET=your_cron_secret
```

## 3. 备份流程详解

### 3.1 异步备份流程

备份过程采用异步执行模式，主要流程如下：

1. **创建执行记录**：
   ```typescript
   const executionId = await createExecution('BACKUP_IMAGES', options);
   ```

2. **初始化执行状态**：
   ```typescript
   await startExecution(executionId);
   await appendExecutionLog(executionId, `Starting backup process...`);
   await updateExecutionSummary(executionId, { ... });
   ```

3. **异步执行备份**：
   ```typescript
   setTimeout(() => {
     runBackupProcess(executionId, options).catch(error => { ... });
   }, 0);
   ```

4. **批量处理图片**：
   - 将要处理的图片分成多个批次
   - 每个批次并发处理多张图片
   - 每个批次完成后更新执行摘要

5. **完成执行**：
   ```typescript
   await completeExecution(executionId, 'SUCCESS', result);
   ```

### 3.2 备份过程优化

为解决在 Vercel 无服务器环境中备份执行卡在 "RUNNING" 状态的问题，我们实施了以下优化：

1. **初始化同步化**：确保执行状态在 API 响应前更新为 "RUNNING"
2. **处理过程异步化**：使用 `setTimeout` 确保实际处理在后台异步进行
3. **状态检查**：在 `runBackupProcess` 开始时检查执行状态
4. **超时处理**：设置 TIMEOUT_SECONDS 秒超时（接近 Vercel 800 秒限制）
5. **卡住执行修复**：添加 `************************` 函数定期检查和修复卡住的执行

### 3.3 自动备份类型

系统支持两种自动备份：

1. **每日备份**：备份前一天的所有历史记录
   ```typescript
   export async function createPreviousDayBackupExecution(
     batchSize = 10,
     skipBackupCheck = false
   ): Promise<string | null> {
     // 设置时间范围为昨天的 00:00:00 到 23:59:59
     // ...
   }
   ```

2. **每小时备份**：备份最近 80 分钟的历史记录
   ```typescript
   export async function createHourlyBackupExecution(
     batchSize = 10,
     skipBackupCheck = false
   ): Promise<string | null> {
     // 设置时间范围为当前时间往前 80 分钟
     // ...
   }
   ```

## 4. API 端点

### 4.1 管理员备份端点

- **URL**: `/api/admin/backup/image`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2023-01-31T23:59:59.999Z",
    "dryRun": true,
    "batchSize": 10,
    "skipBackupCheck": false
  }
  ```
- **响应**:
  ```json
  {
    "message": "Backup execution created",
    "executionId": "exec_123456789"
  }
  ```

### 4.2 自动备份端点

- **URL**: `/api/public/backup`
- **方法**: `GET`
- **查询参数**:
  - `secret`: CRON_SECRET 环境变量值（可选，如果设置了 CRON_SECRET）
  - `type`: 备份类型，可选值为 "daily"（默认）或 "hourly"
  - `batchSize`: 每批处理的图片数量（可选，默认为 10）
  - `skipBackupCheck`: 是否跳过备份状态检查（可选，默认为 false）
- **响应**:
  ```json
  {
    "message": "Daily backup execution created",
    "executionId": "exec_123456789",
    "type": "daily"
  }
  ```

### 4.3 修复卡住执行端点

- **URL**: `/api/public/backup/fix`
- **方法**: `GET`
- **查询参数**:
  - `secret`: CRON_SECRET 环境变量值（可选，如果设置了 CRON_SECRET）
  - `maxAgeMinutes`: 执行被视为卡住的最大时间（分钟）（可选，默认为 30）
- **响应**:
  ```json
  {
    "message": "Fixed 2 stuck backup executions",
    "fixedCount": 2
  }
  ```

## 5. 定时任务配置

系统使用 Vercel Cron Jobs 配置了以下定时任务：

```json
{
  "crons": [
    {
      "path": "/api/public/backup",
      "schedule": "0 5 * * *"
    },
    {
      "path": "/api/public/backup?type=hourly",
      "schedule": "0 * * * *"
    },
    {
      "path": "/api/public/backup/fix",
      "schedule": "15,45 * * * *"
    }
  ]
}
```

- **每日备份**：每天 5:00 UTC 运行，备份前一天的历史记录
- **每小时备份**：每小时整点运行，备份最近 80 分钟的历史记录
- **修复卡住执行**：每小时的第 15 分钟和第 45 分钟运行，检查并修复卡住的备份执行

## 6. 管理界面

### 6.1 备份管理页面

备份管理页面 (`/admin/backup`) 提供以下功能：

1. **创建备份任务**：
   - 选择日期范围
   - 设置备份选项（模拟运行、批处理大小、跳过备份状态检查）
   - 触发备份任务

2. **查看备份历史**：
   - 显示所有备份执行记录
   - 查看执行状态、时间范围和完成时间
   - 访问执行详情页面

### 6.2 执行详情页面

执行详情页面 (`/admin/backup/[id]`) 提供以下功能：

1. **执行概览**：
   - 显示执行状态、创建时间、开始时间和完成时间
   - 显示备份参数（日期范围、批处理大小等）

2. **执行摘要**：
   - 显示处理总数、成功数、失败数和跳过数
   - 显示执行进度（对于正在运行的执行）

3. **执行日志**：
   - 显示详细的执行日志
   - 提供下载日志功能

4. **详细信息**：
   - 显示处理的每个历史记录的详细信息
   - 显示原始图片和备份后的图片预览
   - 提供链接到历史记录详情页面

## 7. 最近改进

### 7.1 跳过失败的记录

为了避免备份失败的记录，我们在备份过程中添加了以下检查：

1. **跳过失败的绘图结果**：
   - 在 `runBackupProcess` 函数中添加条件 `not(eq(histories.drawStatus, "FAILED"))`
   - 这确保只有成功的绘图结果才会被备份

2. **跳过备份失败的记录**：
   - 添加条件 `not(eq(histories.backupStatus, "FAILED"))`（当 `skipBackupCheck` 为 `false` 时）
   - 避免重复尝试已知备份失败的记录，除非明确要求重试

3. **优化备份效率**：
   - 减少不必要的备份尝试
   - 避免备份可能无效或不完整的图片
   - 减少对已知问题记录的重复处理

4. **文档更新**：
   - 更新系统概述部分，明确说明备份条件
   - 在性能优化部分添加相关说明

### 7.2 解决卡住执行问题

为解决备份执行卡在 "RUNNING" 状态的问题，我们实施了以下改进：

1. **修改 `createBackupExecution` 函数**：
   - 在返回 `executionId` 前同步更新执行状态为 "RUNNING"
   - 初始化结果对象并更新执行摘要
   - 使用 `setTimeout` 在后台异步运行备份过程

2. **改进 `runBackupProcess` 函数**：
   - 在开始处理前检查执行状态
   - 如果执行不存在或已完成，则提前返回
   - 如果执行处于 "PENDING" 状态，则将其更新为 "RUNNING"
   - 使用现有摘要（如果有）或创建新摘要

3. **添加 `************************` 函数**：
   - 查找长时间处于 "RUNNING" 状态的执行
   - 将这些执行标记为 "FAILED"
   - 添加错误消息说明执行被卡住

4. **创建新的 API 端点**：
   - 添加 `/api/public/backup/fix` 端点
   - 定期运行以检查和修复卡住的备份执行

5. **更新 Vercel 的 cron 配置**：
   - 添加每小时两次运行的 cron 任务，用于检查和修复卡住的备份执行

### 7.3 性能优化

1. **批量处理**：
   - 将要处理的图片分成多个批次
   - 每个批次并发处理多张图片
   - 控制并发数量以避免资源耗尽

2. **超时处理**：
   - 设置 790 秒超时（接近 Vercel 800 秒限制）
   - 在接近超时时停止处理新批次
   - 将部分完成的执行标记为成功

3. **跳过已备份项目**：
   - 默认跳过已成功备份的历史记录（`backupStatus` 为 `SUCCESS`）
   - 默认跳过备份失败的历史记录（`backupStatus` 为 `FAILED`）
   - 提供 `skipBackupCheck` 选项强制重新备份所有记录

4. **跳过失败的绘图**：
   - 跳过 `drawStatus` 为 `FAILED` 的历史记录
   - 避免备份失败的绘图结果

## 8. 故障排除

### 8.1 常见问题

1. **执行卡在 "RUNNING" 状态**：
   - 可能原因：Vercel 无服务器环境中的函数执行被挂起
   - 解决方案：使用 `/api/public/backup/fix` 端点修复卡住的执行

2. **备份失败**：
   - 可能原因：源图片 URL 不可访问、R2 配置错误、网络问题
   - 解决方案：检查执行日志，确认 R2 配置正确，确保源图片可访问

3. **重复备份**：
   - 可能原因：多个 cron 任务同时触发
   - 解决方案：系统已实现检查，避免对同一时间范围重复备份

### 8.2 日志和监控

1. **执行日志**：
   - 存储在 `executions` 表的 `logs` 字段中
   - 可在执行详情页面查看

2. **备份日志文件**：
   - 存储在 R2 存储桶的 `logs` 文件夹中
   - 命名格式为 `backup-yyyy-MM-dd-HH-m-s.log`
   - 包含处理的每个历史记录的详细信息

## 9. 安全考虑

1. **API 安全**：
   - 公共备份端点受 `CRON_SECRET` 环境变量保护
   - 管理员备份端点需要管理员权限

2. **数据安全**：
   - 原始图片 URL 保存在 `extra.originalUrl` 字段中
   - 敏感信息不会暴露在日志或响应中

3. **错误处理**：
   - 所有错误都会被记录并妥善处理
   - 系统会尝试继续处理其他图片，即使某些图片处理失败

## 10. 未来改进计划

1. **队列系统**：
   - 考虑使用外部队列系统（如 AWS SQS、RabbitMQ 等）管理备份任务
   - 这样即使 Vercel 函数执行环境被挂起，任务也能在队列中保持

2. **更细粒度的控制**：
   - 添加按用户 ID 或历史记录 ID 备份的功能
   - 添加优先级控制，允许某些备份任务优先执行

3. **更好的监控**：
   - 添加备份统计信息仪表板
   - 添加备份失败告警机制

4. **性能进一步优化**：
   - 优化图片下载和上传过程
   - 考虑使用流式处理减少内存使用
