# 邀请奖励兑换确认功能

## 概述

本文档描述了邀请奖励兑换确认功能的实现，主要包括以下内容：

1. 管理员在兑换邀请奖励时，需要先选择奖励类型（积分或现金）
2. 选择奖励类型后，需要点击确认按钮才会发送兑换请求
3. 整个流程通过确认对话框进行，确保管理员有足够的时间确认兑换操作

## 技术实现

### 兑换流程

1. 管理员在邀请使用记录列表中点击"兑换奖励"按钮
2. 系统显示确认对话框
3. 如果邀请类型是"both"（同时支持积分和现金）：
   - 管理员需要先选择要兑换的奖励类型（积分或现金）
   - 选择后，确认按钮才会启用
   - 点击确认按钮后，系统才会发送兑换请求
4. 如果邀请类型是"points"或"cash"（只支持一种奖励类型）：
   - 系统直接显示要兑换的奖励类型和数量
   - 管理员点击确认按钮后，系统才会发送兑换请求
5. 兑换完成后，更新邀请记录状态为"已兑换"

### 状态管理

1. 使用 `selectedRedeemType` 状态变量跟踪选择的奖励类型
2. 当打开兑换对话框时，重置 `selectedRedeemType` 为 null
3. 当选择奖励类型时，更新 `selectedRedeemType` 状态
4. 当点击确认按钮时，检查 `selectedRedeemType` 是否已设置，如果未设置则显示错误提示
5. 当兑换请求完成后，重置 `selectedRedeemType` 为 null

### 防止重复兑换

1. 前端实现：
   - 在管理员点击兑换按钮后，立即禁用按钮并显示"处理中..."状态
   - 只有在请求完成（成功或失败）后才重新启用按钮
   - 兑换成功后，刷新数据列表

2. 后端实现：
   - 在兑换处理过程中，使用数据库事务确保原子性
   - 在更新邀请记录状态前，再次检查记录状态是否为"待兑换"
   - 如果状态已变更，则拒绝兑换请求

## UI 变更

1. 兑换对话框中的奖励类型选择按钮：
   - 选中状态使用 `variant="default"` 样式
   - 未选中状态使用 `variant="outline"` 样式
   - 按钮文本简化为"积分"和"现金"，后面显示具体数量
2. 确认按钮：
   - 对于"both"类型的邀请，只有在选择了奖励类型后才启用
   - 对于"points"或"cash"类型的邀请，始终启用
3. 取消按钮：
   - 点击取消按钮时，重置 `selectedRedeemType` 为 null

## 代码变更

主要修改了 `components/admin/invitation-usages-table.tsx` 文件：

1. 添加 `selectedRedeemType` 状态变量
2. 添加 `selectRedeemType` 函数用于选择奖励类型
3. 修改 `handleRedeemReward` 函数，只有在确认按钮点击后才发送请求
4. 更新奖励类型选择按钮的样式和行为
5. 更新确认按钮的行为，根据邀请类型和选择状态决定是否启用
