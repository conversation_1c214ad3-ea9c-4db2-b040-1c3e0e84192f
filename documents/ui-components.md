# UI Components Documentation

This document provides information about the UI components used in the application, their props, and usage examples.

## Date Pickers

### DatePickerWithRange

The `DatePickerWithRange` component is used to select a date range. It uses the AirDatepicker library with Chinese localization.

#### Props

| Prop | Type | Description |
|------|------|-------------|
| `date` | `DateRange \| undefined` | The currently selected date range |
| `onSelect` | `(date: DateRange \| undefined) => void` | Callback function when a date range is selected |
| `className` | `string \| undefined` | Optional CSS class name |

#### Usage Example

```tsx
import { DatePickerWithRange } from "@/components/ui/date-picker-with-range";
import { DateRange } from "react-day-picker";
import { useState } from "react";

function MyComponent() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().setDate(new Date().getDate() - 7)),
    to: new Date(),
  });

  return (
    <div className="space-y-2">
      <Label>Date Range</Label>
      <DatePickerWithRange
        date={dateRange}
        onSelect={setDateRange}
      />
    </div>
  );
}
```

#### Common Mistakes

❌ **Incorrect usage with `value` and `onChange` props:**

```tsx
<DatePickerWithRange
  value={dateRange}
  onChange={setDateRange}
/>
```

✅ **Correct usage with `date` and `onSelect` props:**

```tsx
<DatePickerWithRange
  date={dateRange}
  onSelect={setDateRange}
/>
```

### DatePicker

The `DatePicker` component is used to select a single date. It uses the shadcn UI Calendar component.

#### Props

| Prop | Type | Description |
|------|------|-------------|
| `date` | `Date \| undefined` | The currently selected date |
| `setDate` | `(date: Date \| undefined) => void` | Callback function when a date is selected |
| `placeholder` | `string \| undefined` | Optional placeholder text (default: "Pick a date") |
| `className` | `string \| undefined` | Optional CSS class name |

#### Usage Example

```tsx
import { DatePicker } from "@/components/ui/date-picker";
import { useState } from "react";

function MyComponent() {
  const [date, setDate] = useState<Date | undefined>(new Date());

  return (
    <div className="space-y-2">
      <Label>Date</Label>
      <DatePicker
        date={date}
        setDate={setDate}
        placeholder="Select a date"
      />
    </div>
  );
}
```
