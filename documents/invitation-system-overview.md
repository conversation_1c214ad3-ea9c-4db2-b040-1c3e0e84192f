# 邀请系统概述与安全考虑

## 系统概述

邀请系统是一个允许用户邀请新用户并获得奖励的功能。系统主要包括以下组件：

1. **邀请码生成与管理**：用户可以获得唯一的邀请码，用于邀请新用户
2. **邀请关系记录**：记录邀请人与被邀请人的关系
3. **奖励计算**：根据被邀请人的充值金额计算邀请人的奖励
4. **奖励兑换**：邀请人可以兑换积分奖励，管理员可以处理现金奖励

## 核心功能

### 1. 邀请码管理

- 每个用户只能有一个激活的邀请码
- 邀请码格式为小写字母和数字的组合
- 邀请码可以设置使用次数限制和过期时间
- 邀请码可以关联不同的奖励比例和渠道信息

### 2. 邀请关系记录

- 记录邀请人和被邀请人的关系
- 记录被邀请人的注册时间和首次充值时间
- 记录充值金额和计算的奖励金额
- 记录奖励状态（待处理、待兑换、已兑换、作废）

### 3. 奖励计算

- 积分奖励基于用户实际获得的积分数量乘以奖励比例计算
- 现金奖励基于充值金额乘以奖励比例计算
- 奖励计算在被邀请人首次充值时进行
- 现金奖励金额与充值金额成正比
- 所有金额计算均使用元为单位，存储时转换为分

### 4. 奖励兑换

- 用户可以自助兑换积分奖励
- 现金奖励需要管理员处理
- 支持单条和批量兑换
- 兑换过程在数据库事务中执行，确保数据一致性

## 安全考虑

### 1. 并发控制

当前系统已实现以下并发控制机制：

- **数据库事务**：所有兑换操作在事务中执行，确保原子性
- **状态二次检查**：在事务内再次检查记录状态，防止并发操作
- **前端防重复提交**：禁用按钮和状态跟踪防止重复点击
- **批量处理优化**：按邀请人分组处理，减少锁竞争

潜在改进：

- 提高事务隔离级别（如 SERIALIZABLE）
- 使用行级锁定（SELECT FOR UPDATE）
- 使用数据库原子操作更新钱包余额
- 实现 API 幂等性，防止重复处理

### 2. 权限控制

- 用户只能兑换自己的邀请记录
- 用户只能兑换积分，不能兑换现金
- 管理员可以兑换任何用户的邀请记录
- API 请求验证用户身份和权限

### 3. 数据验证

- 验证邀请码格式（小写字母和数字）
- 验证兑换请求参数
- 验证记录状态是否可兑换
- 验证操作权限

### 4. 日志记录

- 记录关键操作的详细日志
- 包含操作人、操作时间、操作类型等信息
- 便于安全审计和问题排查

## 相关文档

- [邀请奖励兑换系统更新](./invitation-redemption.md)
- [邀请奖励批量兑换功能](./invitation-batch-redemption.md)
- [邀请奖励系统安全考虑](./invitation-security.md)

## 安全建议总结

1. **提高事务隔离级别**：对于涉及金融操作的事务，使用更高的事务隔离级别
2. **使用行级锁定**：在检查记录状态时使用 SELECT FOR UPDATE 锁定行
3. **使用数据库原子操作**：对于钱包余额更新，使用原子操作而不是读取-修改-写入模式
4. **实现 API 幂等性**：为关键 API 添加幂等性支持，防止重复处理
5. **添加请求限流**：对敏感 API 实施请求限流，防止恶意攻击
6. **增强日志记录**：记录更详细的操作日志，便于安全审计
