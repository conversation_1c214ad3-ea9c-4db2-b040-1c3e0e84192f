# Database Schema Design Document

## Current Schema Overview

### Users Table
- Primary table for user management
- Key fields:
  - `clerkId`: Primary key, linked to Clerk authentication
  - `email`: User's email address
  - `username`: User's display name
  - `avatarUrl`: Profile picture URL
  - `extra`: JSON field for additional user metadata
  - Timestamps for creation and updates

### Wallets Table
- Manages user points/credits
- Key fields:
  - `id`: Primary key
  - `userId`: Foreign key to users table
  - `permanentPoints`: Integer field for user's points balance
  - `extra`: JSON field for additional wallet metadata
  - Timestamps for creation and updates

### Orders Table
- Handles payment transactions
- Key fields:
  - `id`: Primary key
  - `userId`: Foreign key to users table
  - `buyerId`: Can be user ID or "system" for system gifts
  - `type`: Transaction type ("credit" or "debit")
  - `amount`: Transaction amount
  - `status`: Order status (PENDING, SUCCESS, FAILED, REFUND)
  - `paymentMethod`: Payment provider (alipay, wxpay)
  - Payment tracking fields (outTradeNo, tradeNo, qrCodeUrl)
  - Payment timestamps (paidAt, refundedAt)
  - `extra`: JSON field for additional order metadata
  - Timestamps for creation and updates

### Histories Table
- Tracks user generation history
- Key fields:
  - `id`: Primary key
  - `userId`: Foreign key to users table
  - `status`: Success/failure status
  - `resultUrl`: URL to generated content
  - `prompt`: User's input prompt
  - `description`: Additional description
  - `pointsUsed`: Points consumed in generation
  - `parameters`: JSON field for generation parameters
  - `extra`: JSON field for additional history metadata
  - Timestamps for creation and updates

## New Schema Additions

### Sharing System

#### Histories Table Updates
- Added `forkedFromShareId`: References `shares.id`, tracks if history was forked from a shared item

#### Shares Table
- Core table for sharing functionality
- Key fields:
  - `id`: Primary key
  - `shareId`: Unique identifier for short URLs
  - `historyId`: References `histories.id`
  - `userId`: References `users.clerkId`
  - `isPublic`: Controls visibility
  - `allowFork`: Controls fork permissions
  - `forkTipPoints`: Points earned from forks
  - `imageUrl`: Cloudflare R2 image URL
  - Engagement metrics (viewCount, likeCount, forkCount)
  - `forkEarnings`: Total points earned from forks
  - `forkedFromId`: References `shares.id` for tracking original content
  - Timestamps for sharing and updates
  - `extra`: JSON field for additional metadata

#### Likes Table
- Tracks user likes on shared content
- Key fields:
  - `id`: Primary key
  - `userId`: References `users.clerkId`
  - `shareId`: References `shares.id`
  - `createdAt`: Timestamp
  - Unique constraint on (userId, shareId)

#### Fork Transactions Table
- Records fork operations and point transfers
- Key fields:
  - `id`: Primary key
  - `fromUserId`: References `users.clerkId` (forker)
  - `toUserId`: References `users.clerkId` (creator)
  - `shareId`: References `shares.id`
  - `historyId`: References `histories.id`
  - `tipPoints`: Points transferred
  - `createdAt`: Timestamp
  - `extra`: JSON field for additional metadata

### Indexes

#### Shares Table Indexes
- `userId`: For user's shares query
- `historyId`: For history-to-share lookup
- `isPublic`: For public content filtering
- `forkCount`: For popular content sorting
- `sharedAt`: For chronological sorting

#### Likes Table Indexes
- `shareId`: For like count aggregation
- `userId`: For user's likes query

#### Fork Transactions Table Indexes
- `fromUserId`: For user's fork history
- `toUserId`: For creator's earnings
- `shareId`: For share's fork history

## Potential Extension Areas

### 1. User Management
- User preferences and settings
- User roles and permissions
- User activity tracking
- User subscription status

### 2. Wallet System
- Transaction history
- Point expiration rules
- Multiple currency support
- Promotional codes and discounts

### 3. Order System
- Subscription management
- Recurring payments
- Multiple payment methods
- Refund policies and tracking
- Order status notifications

### 4. Generation History
- Generation templates
- Favorite generations
- Generation categories/tags
- Generation sharing capabilities
- Generation versioning

### 5. System Features
- Rate limiting
- Usage analytics
- System notifications
- API key management
- Audit logging

## Design Considerations

### Scalability
- Current schema supports basic operations
- JSON fields provide flexibility for future extensions
- Consider partitioning for large tables (histories)

### Performance
- Indexes needed for frequently queried fields
- Consider caching strategies for user data
- Optimize for read-heavy operations

### Security
- Proper access control implementation
- Data encryption for sensitive fields
- Audit trail for critical operations

### Data Integrity
- Foreign key constraints in place
- Consider adding check constraints
- Implement soft delete where appropriate

### Sharing System Design
- Content visibility control through `isPublic`
- Fork permission management via `allowFork`
- Point economy integration with fork tips
- Engagement tracking through views, likes, and forks
- Content attribution through fork chain tracking

### Performance Optimization
- Strategic indexes for common queries
- Efficient aggregation of engagement metrics
- Optimized sorting for discovery features

### Data Integrity
- Proper foreign key relationships
- Unique constraints for likes
- Transaction tracking for point transfers

### Security
- Access control for private shares
- Point transfer validation
- User activity tracking

## Next Steps
1. Prioritize extension areas based on business needs
2. Create detailed specifications for each extension
3. Plan migration strategy for schema changes
4. Implement monitoring and analytics
5. Document API changes and new features
6. Implement schema changes in Drizzle
7. Create migration scripts
8. Update API endpoints for sharing features
9. Implement sharing UI components
10. Add sharing analytics
11. Document new API endpoints
