# 图片压缩功能设计文档

## 1. 概述

本文档描述了使用 jSquash 库实现浏览器端图片压缩功能的设计和实现方案。该功能将允许用户在上传图片时，当图片总大小超过限制时，通过点击"立即压缩"按钮将图片压缩为 WebP 格式，以减小文件大小并提高上传效率。

## 2. 技术选型

- **压缩库**: [jSquash](https://github.com/jamsinclair/jSquash) - 一个基于 WebAssembly 的浏览器端图片压缩库
- **压缩格式**: WebP，压缩质量设置为 80%
- **实现方式**: 客户端 JavaScript，在浏览器中直接处理图片

## 3. 功能需求

1. 当用户上传的图片总大小超过 4MB 限制时，在警告提示后显示"立即压缩"按钮
2. 点击"立即压缩"按钮后，对所有已上传的图片进行压缩
3. 压缩后的图片替换原始图片，使用 Data URL 格式
4. 更新 UI 显示压缩后的总大小
5. 在压缩过程中显示加载状态

## 4. 实现步骤

### 4.1 创建图片压缩工具函数

在 `lib/utils/image-compressor.ts` 中实现图片压缩功能：

```typescript
/**
 * 图片压缩工具
 * 使用 jSquash 库将图片压缩为 WebP 格式
 */

import { encode } from '@jsquash/webp';

/**
 * 将 File 对象转换为 Data URL
 * @param file 图片文件
 * @returns Promise<string> Data URL
 */
export const fileToDataUrl = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

/**
 * 将 Data URL 转换为 Image 对象
 * @param dataUrl Data URL
 * @returns Promise<HTMLImageElement> Image 对象
 */
export const dataUrlToImage = (dataUrl: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = dataUrl;
  });
};

/**
 * 使用 Canvas 压缩图片
 * @param image 图片对象
 * @param quality 压缩质量 (0-1)
 * @param type 输出类型 (默认为 'image/webp')
 * @returns Promise<string> 压缩后的 Data URL
 */
export const compressWithCanvas = (
  image: HTMLImageElement,
  quality: number = 0.8,
  type: string = 'image/webp'
): string => {
  const canvas = document.createElement('canvas');
  canvas.width = image.width;
  canvas.height = image.height;

  const ctx = canvas.getContext('2d');
  if (!ctx) {
    throw new Error('无法创建 Canvas 上下文');
  }

  // 绘制图片到 Canvas
  ctx.drawImage(image, 0, 0);

  // 返回压缩后的 Data URL
  return canvas.toDataURL(type, quality);
};

/**
 * 将 Data URL 转换为 File 对象
 * @param dataUrl Data URL
 * @param filename 文件名
 * @param type MIME 类型
 * @returns File 对象
 */
export const dataUrlToFile = (
  dataUrl: string,
  filename: string,
  type: string = 'image/webp'
): File => {
  // 从 Data URL 中提取 base64 数据
  const arr = dataUrl.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || type;
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  // 创建 File 对象
  return new File([u8arr], filename, { type: mime });
};

/**
 * 压缩单个图片文件
 * @param file 原始图片文件
 * @param quality 压缩质量 (0-100)
 * @returns Promise<File> 压缩后的文件
 */
export const compressImage = async (
  file: File,
  quality: number = 80
): Promise<File> => {
  try {
    // 创建图片对象
    const image = new Image();
    const blobUrl = URL.createObjectURL(file);

    // 等待图片加载
    await new Promise((resolve) => {
      image.onload = resolve;
      image.src = blobUrl;
    });

    // 创建 canvas 并绘制图片
    const canvas = document.createElement('canvas');
    canvas.width = image.width;
    canvas.height = image.height;
    const ctx = canvas.getContext('2d');
    ctx?.drawImage(image, 0, 0);

    // 释放 Blob URL
    URL.revokeObjectURL(blobUrl);

    // 从 canvas 获取图像数据
    const imageData = ctx?.getImageData(0, 0, canvas.width, canvas.height);

    if (!imageData) {
      throw new Error('无法获取图像数据');
    }

    // 使用 jSquash 压缩为 WebP
    const compressedData = await encode(imageData, {
      quality
    });
  } catch (error) {
    console.error('图片压缩失败:', error);
    throw error;
  }
};

/**
 * 批量压缩多个图片
 * @param files 图片文件数组
 * @param quality 压缩质量 (0-1)
 * @returns Promise<File[]> 压缩后的文件数组
 */
export const compressImages = async (
  files: File[],
  quality: number = 80
): Promise<File[]> => {
  const compressPromises = files.map(file => compressImage(file, quality));
  return Promise.all(compressPromises);
};

/**
 * 计算文件大小 (MB)
 * @param file 文件
 * @returns 文件大小 (MB)
 */
export const getFileSizeMB = (file: File): number => {
  return file.size / (1024 * 1024);
};

/**
 * 计算多个文件的总大小 (MB)
 * @param files 文件数组
 * @returns 总大小 (MB)
 */
export const getTotalSizeMB = (files: File[]): number => {
  const totalBytes = files.reduce((sum, file) => sum + file.size, 0);
  return totalBytes / (1024 * 1024);
};
```

### 4.3 修改 DrawOptions 组件

在 `components/draw/draw-options.tsx` 中添加压缩功能：

1. 添加压缩状态和处理函数
2. 在警告提示后添加"立即压缩"按钮
3. 实现压缩处理逻辑

### 4.4 更新 UI 显示

1. 添加压缩中的加载状态
2. 更新压缩后的图片预览
3. 更新压缩后的总大小显示

## 5. 用户界面

### 5.1 压缩按钮

在图片大小超过限制时，在警告提示后显示"立即压缩"按钮：

```
图片和提示词总大小不能超过4MB [立即压缩]
```

### 5.2 压缩状态

在压缩过程中，显示加载状态：

```
正在压缩图片...
```

### 5.3 压缩结果

压缩完成后，更新总大小显示，并保持原有的图片预览界面。

## 6. 实现检查清单

- [x] 安装 jSquash 依赖
- [x] 创建图片压缩工具函数 (lib/utils/image-compressor.ts)
- [x] 修改 DrawOptions 组件，添加压缩功能
- [x] 实现压缩状态和 UI 更新
- [ ] 测试不同大小和类型的图片压缩效果
- [x] 处理可能的错误情况和边缘情况
- [x] 优化用户体验，确保压缩过程流畅

## 7. 注意事项

1. 确保 jSquash 库的 WASM 文件能够正确加载
2. 处理不同浏览器对 WebP 格式的兼容性
3. 考虑大图片压缩时的性能问题
4. 确保压缩后的图片质量满足需求
5. 处理压缩过程中可能出现的错误
