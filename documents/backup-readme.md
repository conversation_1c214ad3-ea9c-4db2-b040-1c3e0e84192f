# Image Backup System

This document provides instructions for setting up and using the image backup system.

## Overview

The image backup system automatically backs up images from the `histories` table to Cloudflare R2 storage. It:

1. Queries the `histories` table within a specified time range
2. Downloads images from `extra.originalUrl` or `resultUrl`
3. Uploads images to Cloudflare R2 bucket with path `userId/historyId/<filename>`
4. Updates the `histories` table with the new R2 URLs
5. Updates associated `shares` if they exist
6. Logs the operation results

## Setup

### 1. Environment Variables

Add the following environment variables to your `.env.local` file:

```
# Cloudflare R2 Storage
R2_ACCOUNT_ID=your_account_id
R2_ACCESS_KEY_ID=your_access_key_id
R2_SECRET_ACCESS_KEY=your_secret_access_key
R2_BUCKET_NAME=your_bucket_name
R2_PUBLIC_URL_PREFIX=https://storage.vibany.com
```

### 2. Cloudflare R2 Setup

#### Obtaining R2 Configuration Values

##### 1. Obtaining R2_ACCOUNT_ID

This is your Cloudflare account ID:

1. Log in to the Cloudflare dashboard (https://dash.cloudflare.com/)
2. After logging in, you can see your account ID in the account information area on the right or in the URL
3. The account ID is typically a string of alphanumeric characters, e.g., `a1b2c3d4e5f6g7h8i9j0`
4. You can also find the "Account ID" in the bottom right corner of the Cloudflare dashboard

##### 2. Obtaining R2_ACCESS_KEY_ID and R2_SECRET_ACCESS_KEY

These two values require creating an R2 API token:

1. Log in to the Cloudflare dashboard
2. In the left navigation bar, click "R2"
3. Click "Manage R2 API Tokens"
4. Click "Create API Token"
5. Select "Create S3 Auth Token"
6. Set permissions:
   - Select the buckets you need to access (you can choose specific buckets or all buckets)
   - Set the permission level (read, write, or both)
7. Click "Create Token"
8. After successful creation, the system will display:
   - Access Key ID: This is your `R2_ACCESS_KEY_ID`
   - Secret Access Key: This is your `R2_SECRET_ACCESS_KEY`

**Important Note**: The Secret Access Key will only be displayed once, so save it immediately. If lost, you'll need to create a new token.

##### 3. Obtaining R2_BUCKET_NAME

This is the name of the bucket you create in R2:

1. Log in to the Cloudflare dashboard
2. In the left navigation bar, click "R2"
3. If you already have buckets, use the name of the existing bucket
4. If you need to create a new bucket:
   - Click "Create bucket"
   - Enter a bucket name (this will be your `R2_BUCKET_NAME`)
   - Select the region for the bucket (optional)
   - Click "Create"

##### 4. Setting R2_PUBLIC_URL_PREFIX

This is the public URL prefix for accessing files in your R2 bucket. There are two ways to set it up:

###### Option 1: Using Cloudflare R2 Public Access

1. Log in to the Cloudflare dashboard
2. In the left navigation bar, click "R2"
3. Select your bucket
4. Click "Settings"
5. In the "Public Access" section:
   - Enable "Public Access"
   - The system will generate a public URL in the format `https://pub-[random].r2.dev`
   - This URL is your `R2_PUBLIC_URL_PREFIX`

###### Option 2: Using a Custom Domain (Recommended)

1. Log in to the Cloudflare dashboard
2. In the left navigation bar, click "R2"
3. Select your bucket
4. Click "Settings"
5. In the "Custom Domains" section:
   - Click "Add Custom Domain"
   - Enter the domain you want to use, e.g., `storage.vibany.com`
   - Follow the instructions to complete the DNS configuration
   - After configuration is complete, this custom domain is your `R2_PUBLIC_URL_PREFIX`

Using a custom domain has the advantage of making URLs more concise, professional, and easier to remember and manage.

## Usage

### Admin Interface

The admin interface for backup operations is available at `/admin/backup`. It allows you to:

1. Select a date range for backup
2. Choose whether to run in "dry run" mode (no actual changes)
3. Set batch size for concurrent processing
4. Choose whether to skip backup status check
5. View backup results including successes, failures, and logs

### API Endpoints

#### Admin Backup Endpoint

- **URL**: `/api/admin/backup/image`
- **Method**: `POST`
- **Body**:
  ```json
  {
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2023-01-31T23:59:59.999Z",
    "dryRun": true,
    "batchSize": 10,
    "skipBackupCheck": false
  }
  ```
- **Response**:
  ```json
  {
    "message": "Backup completed",
    "result": {
      "totalProcessed": 10,
      "succeeded": 8,
      "failed": 1,
      "skipped": 1,
      "errors": [...],
      "processed": [...],
      "logUrl": "https://storage.vibany.com/logs/backup-2023-01-01-12-30-45.log"
    }
  }
  ```

#### Public Backup Endpoint

- **URL**: `/api/public/backup`
- **Method**: `GET`
- **Query Parameters**:
  - `secret` (optional, if `CRON_SECRET` is set)
  - `batchSize` (optional, default: 10) - Number of images to process concurrently in each batch
  - `skipBackupCheck` (optional, default: false) - Whether to skip checking if images are already backed up
- **Response**: Same as admin endpoint

### Automated Backup

The system is configured to run a daily backup at 5:00 AM UTC using Vercel Cron Jobs. This backup processes all histories from the previous day.

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**: Ensure all R2 environment variables are set correctly.
2. **Permission Issues**: Verify that the R2 API keys have the correct permissions.
3. **Network Errors**: Check if the source image URLs are accessible.

### Logs

Backup logs are stored in the R2 bucket under the `logs` folder with the naming pattern `backup-yyyy-MM-dd-HH-m-s.log`. These logs contain detailed information about each backup operation.

## Security Considerations

1. The public backup endpoint is protected by the `CRON_SECRET` environment variable if set.
2. All API endpoints use proper authentication and authorization.
3. Sensitive information is not exposed in logs or responses.
