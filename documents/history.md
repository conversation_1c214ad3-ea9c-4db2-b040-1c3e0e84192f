# AI 图片生成历史记录和积分系统实现清单

## 功能概述

系统需要完整记录每次图片生成的过程，包括开始、结果和积分消费，并确保用户信息及时更新。

## 实现清单

### 1. 历史记录创建和更新 ✅

- [x] 创建 `lib/draw/history.ts` 独立模块
- [x] 实现 `createDrawHistory` 方法
  - [x] 记录用户ID、提示词、风格、模型等基本信息
  - [x] 记录积分消费数量
  - [x] 记录原始图片信息（如果有）
- [x] 实现 `updateDrawHistory` 方法
  - [x] 更新生成状态
  - [x] 保存生成的图片URL
  - [x] 记录错误信息（如果有）

### 2. 积分消费流程 ✅

- [x] 在 `updateDrawHistory` 中集成积分消费
  - [x] 使用事务确保原子性
  - [x] 只在生成成功时扣除积分
  - [x] 在 history.extra 中记录积分消费信息
- [x] 使用 `updateWalletPoints` 进行积分扣除

### 3. API 集成 ✅

- [x] 修改 `/api/draw/route.ts`
  - [x] 生成开始时创建历史记录
  - [x] 生成完成时更新历史记录
  - [x] 处理错误情况
  - [x] 提取生成的图片URL

### 4. 前端自动刷新 ⚠️

- [ ] 修改 `DrawGenerator.tsx`
  - [x] 在生成完成后延迟3秒刷新用户信息
  - [x] 在生成失败后延迟3秒刷新用户信息
  - [ ] 确保使用正确的刷新方法（需要确认是用 UserInitializer 还是当前的 fetchProfile）

## 待确认事项

1. 前端刷新方法
   - 目前使用 `fetchProfile`
   - 需要确认是否应该改用 `UserInitializer` 中的刷新方法

## 下一步行动

1. 确认前端刷新方法的正确实现
2. 添加更多错误处理和日志记录
3. 考虑添加历史记录查询接口
4. 考虑添加积分消费记录查询接口

## 技术细节

### History Extra 字段结构
```typescript
interface DrawHistoryExtra {
  style: string;
  model: string;
  originalImage?: {
    type: string;
    size: number;
  } | null;
  error?: string;
  pointsConsumption?: {
    amount: number;
    timestamp: string;
    orderId: string;
  };
}
```

### 积分消费记录
```typescript
// 在 history.extra 中记录
{
  pointsConsumption: {
    amount: number;      // 消费数量
    timestamp: string;   // 消费时间
    orderId: string;     // 关联的订单ID
  }
}
```

### 用户信息刷新时机
1. 生成成功后 3 秒
2. 生成失败后 3 秒
3. 任何错误发生后 3 秒

## History Record Structure
The history system now includes more detailed tracking:

### Basic Information
- User ID
- Prompt
- Style
- Model
- Points Used
- Status
- Result URL
- Timestamps

### Extended Information
- Model details
- Style details
- Image count
- Custom prompt
- Original images metadata
- Points consumption history
- Error information

### Points Consumption Tracking
Each history record now includes:
- Old balance
- New balance
- Timestamp
- Transaction details

### Error Tracking
Error information is now stored with:
- Error message
- Error type
- Timestamp
- Context information
