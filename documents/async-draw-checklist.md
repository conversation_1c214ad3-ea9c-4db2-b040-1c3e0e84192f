# 异步绘图流程实现清单

## 数据库扩展
- [x] 创建数据库迁移文件，添加 `drawStatus` 和 `drawResult` 字段到 `histories` 表
- [x] 更新 `schema.ts` 中的类型定义，添加 `DrawStatus` 类型

## API 接口实现
- [x] 复制 `/api/draw` 到 `/api/draw/sync` 作为同步接口备份
- [x] 修改 `/api/draw` 实现异步处理逻辑
  - [x] 检查用户 PENDING 状态的历史记录数量和积分消耗
  - [x] 创建历史记录后立即返回 historyId
  - [x] 实现后台继续处理绘图请求的逻辑
  - [x] 定期更新 drawResult 字段

## 历史记录模块扩展
- [x] 扩展 `createHistory` 函数，支持设置 `drawStatus` 字段
- [x] 扩展 `updateHistory` 函数，支持更新 `drawStatus` 和 `drawResult` 字段
- [x] 实现查询用户 PENDING 状态历史记录的函数

## 前端实现
- [x] 修改 `draw-generator.tsx`，支持异步绘图流程
  - [x] 提交请求后立即显示"已提交"状态
  - [x] 添加轮询逻辑，定期查询绘图状态
- [x] 创建新的 Tab 页，显示进行中的绘图请求
  - [x] 显示每个请求的状态、进度和预计完成时间
  - [x] 支持查看实时更新的绘图结果

## 测试与优化
- [ ] 编写测试用例，验证异步绘图流程
- [ ] 测试并发请求处理
- [ ] 测试服务器重启后的恢复机制
- [ ] 性能优化和资源分配

## 部署与监控
- [x] 部署数据库迁移
- [ ] 监控系统性能和用户体验
- [x] 添加日志记录，跟踪异步处理流程
