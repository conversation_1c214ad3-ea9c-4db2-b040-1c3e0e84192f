# Stripe 支付集成设计文档

## 概述

本文档描述了在现有支付系统中集成 Stripe 支付方式的设计和实现方案。Stripe 将作为一种新的支付方法，与现有的支付宝（alipay）和微信支付（wxpay）并行使用。

## 环境配置

系统将使用以下环境变量：
- `STRIPE_SECRET_KEY`: Stripe API 密钥
- `NEXT_PUBLIC_APP_URL`: 应用程序的公共 URL，用于构建 webhook 回调地址

## 实现方案

### 1. 安装依赖

首先需要安装 Stripe 官方 Node.js 库：

```bash
npm install stripe
```

### 2. 数据库适配

现有的数据库模式已经支持多种支付方式，需要在 `PaymentMethod` 类型中添加 `stripe` 选项：

```typescript
// lib/db/schema.ts
export type PaymentMethod = "alipay" | "wxpay" | "stripe";
```

### 3. 购买 API 扩展

在 `/api/purchase` 端点中，需要添加对 Stripe 支付方式的处理：

```typescript
// app/api/purchase/route.ts
import Stripe from 'stripe';

// 初始化 Stripe 客户端
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

// 在现有的 POST 处理函数中添加 Stripe 支付处理
if (paymentMethod === 'stripe') {
  // 创建 Stripe 支付会话
  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    line_items: [
      {
        price_data: {
          currency: 'usd',
          product_data: {
            name: packageInfo.name,
          },
          unit_amount: packageInfo.price * 100, // Stripe 使用分为单位
        },
        quantity: 1,
      },
    ],
    mode: 'payment',
    success_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/success?orderId=${orderId}`,
    cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/cancel?orderId=${orderId}`,
    metadata: {
      orderId: orderId,
      outTradeNo: outTradeNo,
    },
  });

  // 更新订单记录
  await db
    .update(orders)
    .set({
      qrCodeUrl: session.url, // Stripe 支付链接
    })
    .where(eq(orders.id, orderId));

  return NextResponse.json({
    orderId: order.id,
    qrCodeUrl: session.url, // 返回支付链接
  });
}
```

### 4. Stripe Webhook 处理

创建一个新的 API 端点来处理 Stripe 的 webhook 回调：

```typescript
// app/api/public/stripe/notify/route.ts
import { NextResponse } from 'next/server';
import Stripe from 'stripe';
import { db } from '@/lib/db';
import { orders } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { rechargeUserPoints } from '@/lib/wallet/recharge';
import { updateInvitationUsageAfterRecharge } from '@/lib/invitation';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);
const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request: Request) {
  const payload = await request.text();
  const signature = request.headers.get('stripe-signature') as string;
  
  let event;
  
  try {
    event = stripe.webhooks.constructEvent(payload, signature, endpointSecret!);
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return new NextResponse('Webhook signature verification failed', { status: 400 });
  }
  
  if (event.type === 'checkout.session.completed') {
    const session = event.data.object as Stripe.Checkout.Session;
    const orderId = session.metadata?.orderId;
    
    if (!orderId) {
      console.error('Missing orderId in session metadata');
      return new NextResponse('Missing orderId', { status: 400 });
    }
    
    // 处理支付成功逻辑，与 epay 通知处理器类似
    try {
      const result = await db.transaction(async (tx) => {
        // 检查订单是否存在且未处理
        const order = await tx.query.orders.findFirst({
          where: eq(orders.id, orderId),
        });
        
        if (!order) {
          console.log('Order not found:', orderId);
          return {
            success: false,
            error: 'Order not found',
          };
        }
        
        if (order.status === 'SUCCESS') {
          console.log('Order already processed:', orderId);
          return {
            success: true,
            data: {
              orderId,
              status: 'ALREADY_PROCESSED',
            },
          };
        }
        
        // 更新订单状态
        const orderExtra = order.extra as any;
        await tx
          .update(orders)
          .set({
            status: 'SUCCESS',
            paidAt: new Date(),
            tradeNo: session.id,
            extra: {
              ...orderExtra,
              paymentData: {
                sessionId: session.id,
                paymentIntent: session.payment_intent,
              },
            },
          })
          .where(eq(orders.id, orderId));
        
        // 如果订单包含积分，处理充值
        const points = orderExtra.points;
        if (points) {
          try {
            const rechargeResult = await rechargeUserPoints({
              userId: order.userId,
              points,
              metadata: { orderId },
            });
            
            // 更新邀请使用记录（如果有）
            try {
              const priceInYuan = orderExtra.price || 0;
              await updateInvitationUsageAfterRecharge(
                order.userId,
                points,
                priceInYuan
              );
            } catch (inviteError) {
              console.error('Failed to update invitation usage:', inviteError);
              // 不影响主流程
            }
            
            return {
              success: true,
              data: {
                orderId,
                status: 'POINTS_CREDITED',
                userId: order.userId,
                points,
                walletId: rechargeResult.walletId,
              },
            };
          } catch (rechargeError) {
            console.error('Failed to recharge points:', rechargeError);
            return {
              success: false,
              error: 'Failed to recharge points',
              data: {
                orderId,
                status: 'RECHARGE_FAILED',
                userId: order.userId,
                points,
              },
            };
          }
        }
        
        return {
          success: true,
          data: {
            orderId,
            status: 'SUCCESS_NO_POINTS',
            userId: order.userId,
          },
        };
      });
      
      return NextResponse.json({
        code: result.success ? 0 : 400,
        msg: result.success ? 'success' : (result.error || 'Unknown error'),
        data: result.data,
      });
    } catch (error) {
      console.error('Stripe webhook error:', error);
      return new NextResponse('Internal server error', { status: 500 });
    }
  }
  
  return new NextResponse('Received', { status: 200 });
}
```

### 5. 前端支付对话框修改

现有的 `PaymentDialog` 组件已经支持 Stripe 支付方式的 UI，但需要修改处理逻辑：

```typescript
// components/payment/payment-dialog.tsx
// 在 handlePayment 函数中添加对 Stripe 的处理
const handlePayment = async (method: 'wechat' | 'alipay' | 'stripe') => {
  try {
    setIsLoading(true);
    setError(null);
    setPaymentMethod(method);

    const response = await fetch('/api/purchase', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tierId: tier.id,
        paymentMethod: method,
      }),
    });

    if (!response.ok) {
      throw new Error("创建订单失败");
    }

    const data = await response.json();
    console.log('API Response:', data);

    if (!data.orderId || !data.qrCodeUrl) {
      throw new Error("无效的支付信息");
    }

    setOrderId(data.orderId);
    setQrCodeUrl(data.qrCodeUrl);

    // 如果是 Stripe 支付，直接跳转到支付页面
    if (method === 'stripe') {
      window.location.href = data.qrCodeUrl;
    }
  } catch (error) {
    console.error("Payment error:", error);
    setError(error instanceof Error ? error.message : "创建订单失败");
  } finally {
    setIsLoading(false);
  }
};
```

## 测试计划

1. 安装 Stripe 依赖
2. 实现 Stripe 支付处理逻辑
3. 创建 Stripe webhook 处理端点
4. 修改前端支付对话框
5. 使用 Stripe 测试卡号进行支付测试
6. 验证订单状态更新和积分充值

## 注意事项

1. Stripe 使用美元作为默认货币，需要考虑汇率转换
2. Stripe webhook 需要配置正确的端点 URL
3. 在生产环境中，需要使用 HTTPS
4. 需要处理支付失败和取消的情况
5. 考虑添加 Stripe webhook 密钥作为环境变量，以增强安全性
