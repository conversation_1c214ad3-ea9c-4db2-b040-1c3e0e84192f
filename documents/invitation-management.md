# 邀请管理系统设计文档（更新版）

## 1. 概述

本文档详细描述了 AI 绘图工具的邀请奖励（Referral）系统的实现方案，包括数据库表结构、前后端流程、接口定义、权限与错误处理，以及后续扩展方案。

**目标**：实现用户激活个人邀请码，其他用户通过该码注册并完成首次充值后，邀请人可手动兑换积分或返现奖励。

**更新说明**：根据新的需求，每个用户只能创建一个邀请码，并且只能修改邀请码本身。奖励类型和比例使用固定值，由系统设置。管理员可以管理所有用户的邀请码。

---

## 2. 架构与模块划分

- **数据库层**：PostgreSQL（Neon），使用 Drizzle‑ORM 定义表结构
- **业务层**：Next.js API Routes 处理邀请码生成、关联、充值回调、兑换等
- **认证与权限**：Clerk 管理用户身份与角色（`member` / `admin`）
- **前端层**：Next.js + Clerk SDK 实现「设置中心」「邀请页」「注册页」等

---

## 3. 数据库表设计

> 注意：邀请码格式限制为小写字母和数字的组合，使用正则表达式 `/^[a-z0-9]+$/` 进行验证。

### 3.1 invitations 表（邀请码管理）

| 字段         | 类型     | 说明                                           |
|------------|--------|----------------------------------------------|
| id         | TEXT   | 主键，使用 nanoid 生成                            |
| invite_code| TEXT   | 唯一短码（nanoid 生成）                            |
| referrer_id| TEXT   | 外键 → users.clerkId，邀请人                      |
| invite_type| TEXT   | 奖励类型：`points` / `cash` / `both`              |
| ref_ratio  | NUMERIC| 奖励比例（百分比格式，如 0.1 表示 10%）                  |
| channel    | TEXT   | 渠道标记（可选），如 `wechat_share`                   |
| max_uses   | INTEGER| 最大使用次数（0 表示无限）                             |
| expires_at | TIMESTAMP| null：永不过期；不 null：过期时间                     |
| extra      | JSONB  | 预留字段                                         |
| created_at | TIMESTAMP| 生成时间                                       |
| updated_at | TIMESTAMP| 更新时间                                       |

### 3.2 invitation_usages 表（邀请使用与奖励）

| 字段               | 类型      | 说明                                                        |
|------------------|---------|-----------------------------------------------------------|
| id               | TEXT    | 主键，使用 nanoid 生成                                         |
| invitation_id    | TEXT    | 外键 → invitations.id                                       |
| referee_id       | TEXT    | 外键 → users.clerkId，被邀请者                                 |
| registered_at    | TIMESTAMP| 注册时间                                                    |
| first_recharge_at| TIMESTAMP| null：未首次充值；不 null：首次充值时间                            |
| recharge_amount  | INTEGER | 首次充值金额（分）                                              |
| points_awarded   | INTEGER | 邀请人应获积分（基于用户实际获得的积分数量和 ref_ratio 计算）       |
| cash_awarded     | INTEGER | 邀请人应获返现（分）                                             |
| status           | TEXT    | `pending` / `ready` / `completed` / `void`                 |
| redeemed_at      | TIMESTAMP| null：未兑换；不 null：兑换时间                                  |
| operator_id      | TEXT    | 兑换操作人（仅管理员可填）                                         |
| extra            | JSONB   | 预留字段                                                     |
| created_at       | TIMESTAMP| 记录创建时间                                                 |
| updated_at       | TIMESTAMP| 更新时间                                                    |

---

## 4. 业务流程

### 阶段 1：表结构 & 接口准备
1. 使用 Drizzle‑ORM 定义并迁移 `invitations` 与 `invitation_usages` 表
2. 编写 CRUD API：
   - `POST /api/invitations`
   - `GET  /api/invitations`
   - `GET  /api/invitation_usages`
   - `POST /api/invitation_usages/{id}/redeem`

### 阶段 2：邀请码激活流程
1. 用户访问 `Settings / Invitation` 页
2. 如果用户没有邀请码，显示激活按钮
3. 用户点击激活按钮，前端调用 `POST /api/invitations/activate` 生成邀请码
4. 系统自动生成邀请码，并设置默认的奖励类型和比例
5. 如果用户已有邀请码，显示邀请码信息和修改邀请码的选项

### 阶段 3：分享 & 存储
1. 被邀请者访问 `/invite/{inviteCode}`
2. 前端将 `inviteCode` 存入 `localStorage`（避免重复覆盖）
3. 跳转到注册页

### 阶段 4：注册并写入使用记录
1. 注册完成后，前端将 `inviteCode` 随注册请求一并提交
2. 后端通过 `invite_code` 查 `invitations.id`
3. 插入 `invitation_usages`，`status = 'pending'`

### 阶段 5：首次充值回调 & 状态更新
1. 支付回调时，查找 `invitation_usages` 中对应 `referee_id` 且 `status = 'pending'`
2. 更新 `first_recharge_at`、`recharge_amount`
3. 计算 `points_awarded` / `cash_awarded`
4. 更新 `status = 'ready'`
5. 通知邀请人有奖励可兑换

### 阶段 6：兑换操作
1. 用户或管理员在 `Settings / Invitation` 查看所有 usages
2. 普通用户只能调用积分兑换接口
3. 管理员可选择返现，并填写审核备注
4. 更新对应 `invitation_usages` 的 `status = 'completed'`、`redeemed_at`、`operator_id`
5. 发放积分或触发返现流程
6. 支持批量选择记录进行兑换，提高操作效率

---

## 5. API 设计

### 5.1 邀请码管理

> 所有邀请码相关的 API 都会验证邀请码格式，只允许小写字母和数字的组合。

#### 激活邀请码
- **路径**：`POST /api/invitations/activate`
- **权限**：已登录用户
- **请求体**：
  ```json
  {
    "invite_code": "string?" // 可选，不提供则自动生成
  }
  ```
- **响应**：
  ```json
  {
    "id": "string",
    "invite_code": "string",
    "referrer_id": "string",
    "invite_type": "both", // 固定值，从常量中获取
    "ref_ratio": 0.1, // 固定值，从常量中获取
    "channel": null,
    "max_uses": 0,
    "expires_at": null,
    "created_at": "2023-01-01T00:00:00Z"
  }
  ```

#### 更新邀请码
- **路径**：`PATCH /api/invitations/{id}`
- **权限**：已登录用户（普通用户只能修改自己的邀请码，管理员可以修改任何邀请码）
- **请求体**：
  ```json
  {
    "invite_code": "string" // 普通用户只能修改这一项
  }
  ```
- **响应**：
  ```json
  {
    "id": "string",
    "invite_code": "string",
    "referrer_id": "string",
    "invite_type": "both",
    "ref_ratio": 0.1,
    "channel": null,
    "max_uses": 0,
    "expires_at": null,
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-02T00:00:00Z"
  }
  ```

#### 获取邀请码列表
- **路径**：`GET /api/invitations`
- **权限**：已登录用户
- **查询参数**：
  - `?limit=10` - 限制返回数量
  - `?offset=0` - 分页偏移量
- **响应**：
  ```json
  {
    "invitations": [
      {
        "id": "string",
        "invite_code": "string",
        "invite_type": "string",
        "ref_ratio": 0.1,
        "channel": "string",
        "max_uses": 0,
        "expires_at": "2023-12-31T23:59:59Z",
        "created_at": "2023-01-01T00:00:00Z",
        "usage_count": 5, // 已使用次数
        "ready_count": 2, // 待兑换次数
        "completed_count": 3 // 已兑换次数
      }
    ],
    "total": 15,
    "limit": 10,
    "offset": 0
  }
  ```

### 5.2 邀请使用记录

#### 获取邀请使用记录
- **路径**：`GET /api/invitation_usages`
- **权限**：已登录用户
- **查询参数**：
  - `?invitation_id=string` - 按邀请码ID筛选
  - `?status=pending|ready|completed|void` - 按状态筛选
  - `?limit=10` - 限制返回数量
  - `?offset=0` - 分页偏移量
- **响应**：
  ```json
  {
    "usages": [
      {
        "id": "string",
        "invitation_id": "string",
        "invite_code": "string", // 关联的邀请码
        "referee_id": "string",
        "referee_username": "string", // 被邀请人用户名
        "registered_at": "2023-01-01T00:00:00Z",
        "first_recharge_at": "2023-01-02T00:00:00Z",
        "recharge_amount": 10000, // 100元
        "points_awarded": 1000,
        "cash_awarded": 1000, // 10元
        "status": "ready",
        "redeemed_at": null,
        "created_at": "2023-01-01T00:00:00Z"
      }
    ],
    "total": 25,
    "limit": 10,
    "offset": 0
  }
  ```

#### 兑换奖励
- **路径**：`POST /api/invitation_usages/{id}/redeem`
- **权限**：已登录用户（普通用户只能兑换积分，管理员可兑换积分或返现）
- **请求体**：
  ```json
  {
    "redeem_type": "points|cash", // 兑换类型
    "note": "string?" // 可选，管理员备注
  }
  ```
- **响应**：
  ```json
  {
    "success": true,
    "usage": {
      "id": "string",
      "status": "completed",
      "redeemed_at": "2023-01-03T00:00:00Z",
      "operator_id": "string"
    },
    "wallet": {
      "permanentPoints": 5000 // 更新后的积分余额
    }
  }
  ```

#### 批量兑换奖励
- **路径**：`POST /api/invitation_usages/redeem-batch`
- **权限**：已登录用户（普通用户只能兑换积分，管理员可兑换积分或返现）
- **请求体**：
  ```json
  {
    "usage_ids": ["id1", "id2", "id3", ...]
  }
  ```
- **响应**：
  ```json
  {
    "success": true,
    "results": {
      "successful": [
        {
          "id": "id1",
          "pointsAwarded": 100,
          "order": { ... }
        },
        ...
      ],
      "failed": [
        {
          "id": "id2",
          "error": "该记录不可兑换"
        },
        ...
      ]
    },
    "wallet": {
      "permanentPoints": 5000
    },
    "totalPointsAwarded": 300
  }
  ```

#### 管理员批量兑换奖励
- **路径**：`POST /api/admin/invitation_usages/redeem-batch`
- **权限**：管理员
- **请求体**：
  ```json
  {
    "usage_ids": ["id1", "id2", "id3", ...],
    "redeem_type": "points|cash",
    "note": "string?" // 可选，管理员备注
  }
  ```
- **响应**：与用户批量兑换 API 类似，但包含更多管理员相关信息。

### 5.3 邀请注册

#### 邀请落地页
- **路径**：`GET /invite/{inviteCode}`
- **权限**：公开访问
- **功能**：
  1. 验证邀请码有效性
  2. 将邀请码存入 localStorage
  3. 展示品牌介绍
  4. 提供注册按钮

#### 注册时关联邀请
- **路径**：`POST /api/register-with-invite`
- **权限**：公开访问
- **请求体**：
  ```json
  {
    "invite_code": "string",
    "user_id": "string" // Clerk 用户 ID
  }
  ```
- **响应**：
  ```json
  {
    "success": true,
    "message": "邀请关联成功"
  }
  ```

---

## 6. 前端页面 & 交互

### 6.1 设置中心 - 邀请管理页面
- **路径**：`/settings/invitation`
- **组件**：
  - 邀请码状态显示（未激活/已激活）
  - 激活按钮（未激活时显示）
  - 邀请码修改表单（已激活时显示）
  - 邀请统计信息
  - 邀请使用记录表格
  - 兑换操作按钮
  - 批量选择和批量兑换功能

### 6.2 邀请落地页
- **路径**：`/invite/{inviteCode}`
- **组件**：
  - 品牌介绍
  - 产品特性展示
  - 注册按钮

### 6.3 管理员邀请管理
- **路径**：`/admin/invitations`
- **组件**：
  - 所有邀请码列表
  - 所有邀请使用记录
  - 高级筛选与搜索
  - 返现兑换操作
  - 批量选择和批量兑换功能（支持积分和现金）

---

## 7. 权限 & 安全

### 7.1 用户权限
- **普通用户**：
  - 激活自己的邀请码（每个用户只能有一个邀请码）
  - 修改自己的邀请码（仅限邀请码本身）
  - 查看自己的邀请码和使用记录
  - 兑换积分奖励（单条或批量）

### 7.2 管理员权限
- **管理员**：
  - 查看所有邀请码和使用记录
  - 为任何用户创建或修改邀请码
  - 编辑邀请码的所有参数（包括奖励类型、比例等）
  - 兑换积分或返现奖励（单条或批量）
  - 作废邀请记录

### 7.3 安全措施
- 所有 API 请求需验证用户身份
- 普通用户只能操作自己的资源
- 敏感操作（如返现）需管理员权限
- 防止邀请码滥用的限制措施

---

## 8. 扩展与优化

### 8.0 已完成的优化
- 邀请码格式验证：只允许小写字母和数字的组合
- 邀请码编辑体验优化：在邀请链接旁添加编辑按钮，点击后弹出对话框
- 管理员邀请码管理界面：实现了全功能的管理界面，包括创建、编辑、查看和兑换邀请奖励
- 用户详情页邀请关系：在用户详情页显示用户的邀请和被邀请关系
- 添加调试日志：在关键节点添加详细的调试日志，便于跟踪邀请流程和排查问题
- 货币单位转换注释：在所有金额转换处添加清晰的单位注释（元/分），防止单位错误
- 批量兑换功能：实现了批量兑换邀请奖励功能，提高操作效率

### 8.1 短期优化
- 邀请链接分享功能（生成图片、一键复制）
- 邀请统计仪表盘
- 邀请活动管理（限时奖励翻倍等）

### 8.2 长期规划
- 多级邀请体系
- 邀请任务系统
- 会员等级与邀请特权
- 渠道效果分析

---

## 9. 调试与排错

### 9.1 关键调试点

为了方便跟踪和排查邀请系统的问题，在以下关键节点添加了详细的调试日志：

1. **注册与邀请码关联**
   - `lib/db/user.ts` 中的 `syncUser` 函数，记录邀请码处理过程
   - `store/profile/index.ts` 中的 `refreshUserInfo` 函数，记录 localStorage 中邀请码的获取和清除

2. **充值后奖励计算**
   - `lib/invitation/index.ts` 中的 `updateInvitationUsageAfterRecharge` 函数，记录充值金额、奖励计算过程和单位转换
   - 清晰标记元和分的转换关系，防止单位错误

3. **奖励兑换操作**
   - `lib/invitation/points.ts` 中的 `redeemInvitationPoints` 函数，记录积分兑换全过程
   - `lib/invitation/points.ts` 中的 `redeemInvitationPointsBatch` 函数，记录批量积分兑换全过程
   - `lib/invitation/cash.ts` 中的 `redeemInvitationCash` 函数，记录现金兑换全过程
   - `lib/invitation/cash.ts` 中的 `redeemInvitationCashBatch` 函数，记录批量现金兑换全过程
   - 事务处理中的并发检查和状态更新

### 9.2 常见问题与解决方案

1. **邀请码不被正确关联**
   - 检查 localStorage 中的 inviteCode 是否正确存储
   - 检查用户注册时是否正确传递邀请码
   - 查看日志中 `[INVITATION_DEBUG]` 开头的记录

2. **奖励计算错误**
   - 检查积分奖励是否基于用户实际获得的积分数量
   - 检查充值金额的单位是否正确（元还是分）
   - 检查 `refRatio` 的转换是否正确（字符串转数字）
   - 查看日志中 `[INVITATION_RECHARGE_DEBUG]` 开头的记录

3. **兑换操作失败**
   - 检查记录状态是否为 `ready`
   - 检查操作人是否有权限进行兑换
   - 检查是否有并发操作导致状态冲突
   - 查看日志中 `[INVITATION_POINTS_DEBUG]` 或 `[INVITATION_CASH_DEBUG]` 开头的记录

### 9.3 性能优化建议

1. **数据库查询优化**
   - 在高频查询的字段上添加索引
   - 使用批量查询而非多次单条查询

2. **并发处理优化**
   - 使用事务和乐观锁确保并发安全
   - 在兑换操作中再次检查记录状态

3. **前端优化**
   - 防止重复点击兑换按钮
   - 在兑换操作前显示确认对话框

## 10. 实施计划（更新版）

### 阶段一：基础设施（1-2天）
- [x] 数据库表设计与迁移
- [x] 创建邀请系统常量文件
- [x] 邀请码激活与更新 API 实现
- [x] 邀请码验证逻辑优化

### 阶段二：用户界面优化（1-2天）
- [x] 简化邀请管理页面，只显示激活按钮或已有邀请码
- [x] 实现邀请码修改功能（仅限邀请码本身）
- [x] 优化邀请落地页为快速跳转页面
- [x] 兑换流程优化

### 阶段三：管理员功能（1-2天）
- [x] 创建管理员邀请管理界面
- [x] 实现管理员创建和编辑任何用户邀请码的功能
- [x] 实现管理员编辑奖励类型和比例的功能
- [x] 实现返现兑换流程

### 阶段四：调试与测试（1天）
- [x] 在关键节点添加调试日志
- [x] 全流程测试与修复
- [x] 文档更新

### 阶段五：部署与监控（1天）
- [ ] 部署上线
- [ ] 设置监控与告警
- [ ] 收集用户反馈

---

> 此设计文档作为开发与实施的蓝图，后续可根据业务增长与反馈进行迭代和优化。
