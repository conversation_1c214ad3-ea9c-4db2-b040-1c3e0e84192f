# User Points Refresh Mechanism

## 功能概述

用户积分刷新机制分为两种模式：
1. 单次刷新：用于用户手动点击刷新按钮时
2. 自动重试刷新：用于绘图完成后自动检查积分更新

## Store 设计

### UserStore Interface
```typescript
interface UserStore {
  credits: number;
  setCredits: (credits: number) => void;

  // 单次刷新用户积分
  refreshUserInfo: () => Promise<void>;

  // 自动重试刷新（用于绘图完成后）
  autoRefreshUserInfo: () => void;
}
```

### Store 实现
```typescript
export const useUserStore = create<UserStore>((set) => ({
  credits: 0,
  setCredits: (credits) => set({ credits }),

  // 单次刷新实现
  refreshUserInfo: async () => {
    try {
      const response = await fetch('/api/me');
      if (response.ok) {
        const data = await response.json();
        if (data.wallet) {
          set({ credits: data.wallet.permanentPoints });
        }
      }
    } catch (error) {
      console.error('[USER_REFRESH_ERROR]', error);
    }
  },

  // 自动重试刷新实现
  autoRefreshUserInfo: () => {
    let count = 0;
    const maxTries = 3;  // 最多刷新3次
    const interval = 1000;  // 每次间隔1秒

    const refresh = async () => {
      if (count >= maxTries) return;

      try {
        const response = await fetch('/api/me');
        if (response.ok) {
          const data = await response.json();
          if (data.wallet) {
            set({ credits: data.wallet.permanentPoints });
          }
        }
      } catch (error) {
        console.error('[USER_REFRESH_ERROR]', error);
      }

      count++;
      if (count < maxTries) {
        setTimeout(refresh, interval);
      }
    };

    refresh();
  },
}));
```

## 使用场景

### 1. 手动刷新积分
用于用户点击刷新按钮时：
```typescript
<button onClick={() => refreshUserInfo()}>
  剩余积分 {credits}，点击刷新
</button>
```

### 2. 绘图完成后自动刷新
用于绘图生成完成后检查积分更新：
```typescript
// 在 DrawGenerator 组件中
const imageUrlMatch = accumulatedContent.match(/https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)/i);
if (imageUrlMatch) {
  // 生成成功后触发自动刷新
  autoRefreshUserInfo();
}
```

## 实现要点

1. **职责分离**
   - `refreshUserInfo`: 单次刷新，立即返回结果
   - `autoRefreshUserInfo`: 多次重试，确保积分更新成功

2. **状态管理**
   - 所有积分更新都通过 Store 统一管理
   - 确保 UI 上的积分显示与 Store 中的值同步

3. **错误处理**
   - 记录刷新失败的错误日志
   - 在多次重试失败后停止尝试

4. **性能优化**
   - 单次刷新避免重复请求
   - 自动刷新限制最大重试次数和间隔时间

## 注意事项

1. **请求频率控制**
   - 单次刷新不应过于频繁
   - 自动刷新要有合理的间隔时间

2. **状态同步**
   - 确保所有使用积分的组件都订阅了 Store 的更新
   - 避免本地状态与 Store 状态不同步

3. **用户体验**
   - 手动刷新时可以显示加载状态
   - 自动刷新应该在后台进行，不影响用户操作

4. **错误提示**
   - 手动刷新失败时应该提示用户
   - 自动刷新失败可以在控制台记录日志

## 后续优化

1. **防抖处理**
   - [ ] 为手动刷新添加防抖处理
   - [ ] 避免短时间内多次触发刷新

2. **重试策略优化**
   - [ ] 根据错误类型调整重试策略
   - [ ] 实现指数退避算法

3. **状态指示**
   - [ ] 添加刷新中的加载状态
   - [ ] 显示最后更新时间

4. **监控与统计**
   - [ ] 记录刷新成功率
   - [ ] 统计平均刷新延迟
