# AI 辅助提示词创作功能设计

## 1. 功能概述

在 `@components/draw/draw-options.tsx` 的提示词输入区域添加一个 AI 辅助创作功能，用户可以通过弹窗根据当前选择的风格和已输入的提示词，获取 AI 辅助生成的提示词建议。

## 2. 技术架构

### 2.1 API 设计

#### 2.1.1 接口定义

- **路径**: `/api/chat/prompts`
- **方法**: POST
- **请求参数**:
  ```typescript
  interface PromptRequest {
    model: string;           // 使用的 AI 模型
    styleId: string;         // 当前选择的风格 ID
    customPrompt: string;    // 用户已输入的提示词
    requirements: string;    // 用户的额外要求
    type: string;            // 操作类型：扩写、缩写、创作等
  }
  ```
- **响应**:
  ```typescript
  interface PromptResponse {
    prompt: string;          // 生成的提示词
  }
  ```

#### 2.1.2 流式响应

- 接口默认开启 stream 模式，实时返回生成的提示词
- 使用 AI SDK 的流式响应功能，提供更好的用户体验

#### 2.1.3 提示词处理逻辑

- 根据 `styleId` 获取 `@constants/draw/index.ts` 中对应风格的信息
- 根据不同的 `type` 使用不同的提示词模板
- 在 `/lib` 目录下创建专门的模块管理提示词模板和处理逻辑

### 2.2 UI 设计

#### 2.2.1 组件结构

- 在 `@components/draw/draw-options.tsx` 中添加 AI 辅助按钮
- 创建新的 `PromptAssistModal` 组件，用于 AI 辅助提示词生成
- 组件位置: `@components/draw/prompt-assist-modal.tsx`

#### 2.2.2 模态框内容

- 模型选择下拉菜单（使用 `@constants/chat/models.ts` 中定义的模型）
- 操作类型选择（扩写、缩写、创作等）
- 额外要求输入框
- 生成结果预览区域（带有加载状态指示）
- 操作按钮（生成、替换、取消）

#### 2.2.3 状态管理

- 使用 React 状态管理生成过程
- 实现请求取消功能，当模态框关闭时取消正在进行的生成
- 防止用户滥用（如重复点击）的限制机制

## 3. 实现细节

### 3.1 API 实现

1. 创建 `/app/api/chat/prompts/route.ts` 文件
2. 实现 POST 处理函数，支持流式响应
3. 创建 `/lib/chat/prompt-templates.ts` 管理不同类型的提示词模板
4. 实现积分计算和扣除逻辑（每次使用扣除 0 分，为后续收费做准备）

### 3.2 UI 实现

1. 在 `draw-options.tsx` 的提示词区域添加 AI 辅助按钮
2. 创建 `prompt-assist-modal.tsx` 组件
3. 实现模型选择、类型选择和额外要求输入
4. 实现生成状态管理和结果预览
5. 实现替换功能，将生成的提示词替换到主界面

### 3.3 模型配置

1. 创建 `@constants/chat/models.ts` 文件
2. 参考 `@constants/draw/models.ts` 的结构定义聊天模型
3. 定义模型的可见性、积分消耗等属性

## 4. 用户体验优化

1. 生成过程中显示加载状态，避免用户误操作
2. 实现取消生成功能，优化资源使用
3. 提供清晰的错误提示和引导
4. 支持一键替换和再次生成功能

## 5. 安全与性能考虑

1. 实现请求频率限制，防止滥用
2. 优化流式响应性能，减少延迟
3. 确保用户输入的安全处理
4. 实现错误处理和日志记录

## 6. 后续扩展计划

1. 支持更多的提示词操作类型
2. 添加提示词历史记录和收藏功能
3. 实现提示词评分和推荐系统
4. 根据用户反馈优化提示词模板
