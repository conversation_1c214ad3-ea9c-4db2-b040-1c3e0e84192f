# 邀请奖励系统安全考虑

## 概述

本文档描述了邀请奖励系统的安全考虑，特别关注并发兑换问题和潜在的安全漏洞。文档旨在分析当前实现中的安全机制，并提供改进建议。

## 当前安全机制

### 1. 并发控制机制

当前系统已实现以下并发控制机制：

#### 1.1 数据库事务

所有兑换操作（单条和批量）都在数据库事务中执行，确保操作的原子性：

```typescript
return await db.transaction(async (tx) => {
  // 事务内的所有操作要么全部成功，要么全部失败
});
```

#### 1.2 状态二次检查

在事务内部，系统会再次检查记录状态，防止并发操作导致的状态不一致：

```typescript
// 再次检查记录状态（防止并发操作）
const currentUsage = await tx.query.invitationUsages.findFirst({
  where: eq(invitationUsages.id, usageId),
});

if (!currentUsage) {
  throw new Error('该记录已被处理或不可兑换');
}

if (currentUsage.status !== 'ready') {
  throw new Error('该记录已被处理或不可兑换');
}
```

#### 1.3 前端防重复提交

前端实现了防止重复点击的机制：

```typescript
// 防止重复点击
if (redeemLoading) return;

setRedeemingId(usageId);
setRedeemLoading(true);
```

#### 1.4 批量处理优化

批量兑换功能将多条记录在单个事务中处理，减少并发操作的可能性：

```typescript
// 对于积分兑换，累加所有积分并一次性更新钱包
await tx.update(wallets)
  .set({
    permanentPoints: newBalance,
    updatedAt: new Date(),
  })
  .where(eq(wallets.id, wallet.id));
```

## 潜在安全漏洞

尽管已有上述安全机制，系统仍存在以下潜在安全漏洞：

### 1. 事务隔离级别未明确设置

当前代码未明确设置事务隔离级别，可能导致在高并发场景下出现问题：

```typescript
// 当前实现
return await db.transaction(async (tx) => {
  // 事务内操作
});

// 建议实现
return await db.transaction(async (tx) => {
  // 设置事务隔离级别为 SERIALIZABLE
  await tx.execute({ sql: 'SET TRANSACTION ISOLATION LEVEL SERIALIZABLE' });
  // 事务内操作
});
```

### 2. 缺少行级锁定

在检查记录状态时，没有使用行级锁定，可能导致并发事务读取到相同的"ready"状态：

```typescript
// 当前实现
const currentUsage = await tx.query.invitationUsages.findFirst({
  where: eq(invitationUsages.id, usageId),
});

// 建议实现（使用 FOR UPDATE 锁定行）
const currentUsage = await tx.execute({
  sql: 'SELECT * FROM invitation_usages WHERE id = $1 FOR UPDATE',
  args: [usageId],
});
```

### 3. 钱包更新的并发问题

当多个兑换操作同时针对同一用户的钱包进行更新时，可能出现并发问题：

```typescript
// 当前实现
const wallet = await tx.query.wallets.findFirst({
  where: eq(wallets.userId, referrerId),
});
const oldBalance = wallet.permanentPoints;
const newBalance = oldBalance + pointsAwarded;
await tx.update(wallets)
  .set({
    permanentPoints: newBalance,
    updatedAt: new Date(),
  })
  .where(eq(wallets.id, wallet.id));

// 建议实现（使用数据库原子操作）
await tx.execute({
  sql: 'UPDATE wallets SET permanent_points = permanent_points + $1, updated_at = NOW() WHERE user_id = $2',
  args: [pointsAwarded, referrerId],
});
```

### 4. API 重放攻击

当前系统可能容易受到 API 重放攻击，特别是在网络不稳定的情况下，客户端可能会重复发送相同的请求：

```typescript
// 建议实现（添加幂等性标识符）
const idempotencyKey = req.headers.get('X-Idempotency-Key');
if (!idempotencyKey) {
  return NextResponse.json(
    { error: "Missing idempotency key" },
    { status: 400 }
  );
}

// 检查该幂等性标识符是否已被使用
const existingRequest = await db.query.idempotencyRequests.findFirst({
  where: eq(idempotencyRequests.key, idempotencyKey),
});

if (existingRequest) {
  // 返回之前的处理结果
  return NextResponse.json(existingRequest.response);
}
```

## 改进建议

### 1. 提高事务隔离级别

对于涉及金融操作的事务，建议使用更高的事务隔离级别（如 SERIALIZABLE）：

```typescript
return await db.transaction(async (tx) => {
  // 设置事务隔离级别
  await tx.execute({ sql: 'SET TRANSACTION ISOLATION LEVEL SERIALIZABLE' });
  // 事务内操作
});
```

### 2. 使用行级锁定

在检查记录状态时，使用 SELECT FOR UPDATE 语句锁定行，防止并发修改：

```typescript
const [currentUsage] = await tx.execute({
  sql: 'SELECT * FROM invitation_usages WHERE id = $1 FOR UPDATE',
  args: [usageId],
});
```

### 3. 使用数据库原子操作

对于钱包余额更新，使用数据库原子操作而不是读取-修改-写入模式：

```typescript
await tx.execute({
  sql: 'UPDATE wallets SET permanent_points = permanent_points + $1, updated_at = NOW() WHERE id = $2 RETURNING *',
  args: [pointsAwarded, walletId],
});
```

### 4. 实现 API 幂等性

为关键 API 添加幂等性支持，防止重复处理相同的请求：

1. 客户端生成唯一的幂等性标识符（如 UUID）
2. 在请求头中包含该标识符
3. 服务器检查该标识符是否已被处理
4. 如果已处理，返回之前的结果；否则处理请求并记录标识符

### 5. 添加请求限流

对敏感 API 实施请求限流，防止恶意攻击：

```typescript
// 使用 Redis 或内存缓存实现简单的限流
const requestCount = await redis.incr(`rate_limit:${userId}`);
await redis.expire(`rate_limit:${userId}`, 60); // 60秒过期

if (requestCount > 10) { // 每分钟最多 10 个请求
  return NextResponse.json(
    { error: "Too many requests" },
    { status: 429 }
  );
}
```

### 6. 增强日志记录

增强关键操作的日志记录，包括请求 IP、用户 ID、操作类型等信息，便于安全审计和问题排查：

```typescript
logger.info('兑换邀请奖励', {
  userId,
  usageId,
  clientIp: req.headers.get('x-forwarded-for') || 'unknown',
  userAgent: req.headers.get('user-agent') || 'unknown',
  timestamp: new Date().toISOString()
});
```

## 结论

当前邀请奖励系统已实现基本的并发控制机制，但在高并发场景下仍存在潜在安全漏洞。通过实施上述改进建议，可以显著提高系统的安全性和可靠性，防止并发兑换问题和其他安全风险。

特别是对于积分兑换功能，建议优先实施行级锁定和数据库原子操作，以防止并发兑换导致的数据不一致问题。
