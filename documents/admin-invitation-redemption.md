# 管理员邀请奖励兑换功能

## 概述

本文档描述了管理员邀请奖励兑换功能的实现，主要包括以下内容：

1. 管理员可以在后台为用户兑换邀请奖励（积分或现金）
2. 兑换操作通过确认对话框进行，显示兑换类型和数量
3. 兑换操作人为当前管理员，接收对象是邀请码所有者，发送人为系统
4. 兑换后更新邀请使用记录状态

## 技术实现

### 兑换流程

1. 管理员在邀请使用记录列表中点击"兑换奖励"按钮
2. 系统显示确认对话框，确认管理员要兑换的奖励类型和数量
3. 管理员确认后，系统调用 `/api/admin/invitation_usages/{id}/redeem` API
4. API 验证请求并根据奖励类型调用相应的函数处理兑换：
   - 积分奖励：调用 `redeemInvitationPoints` 函数
   - 现金奖励：调用 `redeemInvitationReward` 函数
5. 兑换完成后，更新邀请记录状态为"已兑换"

### 防止重复兑换

1. 前端实现：
   - 在管理员点击兑换按钮后，立即禁用按钮并显示"处理中..."状态
   - 只有在请求完成（成功或失败）后才重新启用按钮

2. 后端实现：
   - 在兑换处理过程中，使用数据库事务确保原子性
   - 在更新邀请记录状态前，再次检查记录状态是否为"待兑换"
   - 如果状态已变更，则拒绝兑换请求

## 数据结构

### 积分兑换记录

积分兑换时，系统会创建一个新的订单记录，包含以下信息：

```typescript
{
  id: string,              // 订单ID
  userId: string,          // 用户ID（邀请人）
  buyerId: "system",       // 购买者ID（系统）
  type: "credit",          // 交易类型（增加积分）
  amount: number,          // 积分数量
  description: string,     // 描述（例如："邀请奖励：100积分"）
  status: "SUCCESS",       // 订单状态
  extra: {                 // 额外信息
    exchangeType: "affiliate",           // 交换类型（邀请奖励）
    invitationUsageId: string,           // 邀请使用记录ID
    refereeId: string,                   // 被邀请人ID
    rechargeAmount: number,              // 充值金额（分）
    pointsExchange: {                    // 积分交换信息
      oldBalance: number,                // 旧余额
      newBalance: number,                // 新余额
      timestamp: string                  // 时间戳
    }
  }
}
```

### 现金兑换记录

现金兑换时，系统会更新邀请使用记录的状态和额外信息：

```typescript
{
  status: "completed",       // 状态（已完成）
  redeemedAt: Date,          // 兑换时间
  operatorId: string,        // 操作人ID（管理员）
  extra: {                   // 额外信息
    redeemType: "cash",      // 兑换类型（现金）
    redeemNote: string,      // 兑换备注（可选）
  }
}
```

## UI 实现

1. 在管理员邀请使用记录列表中，为状态为"待兑换"的记录显示"兑换奖励"按钮
2. 点击按钮后，显示确认对话框，包含以下内容：
   - 如果邀请类型为"both"，显示两个选项：兑换积分和兑换现金
   - 如果邀请类型为"points"或"cash"，直接显示对应的奖励信息
3. 确认对话框中显示兑换的奖励类型和数量
4. 兑换成功后，刷新列表并显示成功提示
