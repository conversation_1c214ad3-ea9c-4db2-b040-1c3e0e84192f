.air-datepicker-custom {
  --adp-font-family: var(--font-sans);
  --adp-font-size: 0.875rem;
  --adp-padding: 1rem;
  --adp-color: hsl(var(--foreground));
  --adp-border-color: hsl(var(--border));
  --adp-border-radius: calc(var(--radius) + 4px);
  --adp-border-radius-small: var(--radius);
  --adp-background-color: hsl(var(--background));
  --adp-background-color-hover: hsl(var(--accent));
  --adp-background-color-active: hsl(var(--accent));
  --adp-background-color-in-range: hsl(var(--accent));
  --adp-background-color-in-range-focused: hsl(var(--accent));
  --adp-background-color-selected: hsl(var(--primary));
  --adp-background-color-selected-hover: hsl(var(--primary));
  --adp-color-selected: hsl(var(--primary-foreground));
  --adp-color-in-range: hsl(var(--accent-foreground));
  --adp-nav-height: 2rem;
  --adp-nav-arrow-color: hsl(var(--foreground));
  --adp-day-name-color: hsl(var(--muted-foreground));
  --adp-popper-width: auto;
  --adp-z-index: 1000; /* 确保在模态框内可见 */
}

.air-datepicker-custom .air-datepicker-body--day-name {
  font-weight: 400;
  font-size: 0.8rem;
}

.air-datepicker-custom .air-datepicker-nav {
  border-bottom: 1px solid hsl(var(--border));
  padding-bottom: 0.5rem;
  margin-bottom: 0.5rem;
}

.air-datepicker-custom .air-datepicker-nav--title {
  font-weight: 500;
}

.air-datepicker-custom.-inline- {
  border: 1px solid hsl(var(--border));
}

.air-datepicker-custom .air-datepicker-cell.-selected-,
.air-datepicker-custom .air-datepicker-cell.-selected-.-current- {
  color: hsl(var(--primary-foreground));
  background: hsl(var(--primary));
}

.air-datepicker-custom .air-datepicker-cell.-range-from-,
.air-datepicker-custom .air-datepicker-cell.-range-to- {
  border: none;
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.air-datepicker-custom .air-datepicker-cell.-in-range- {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.air-datepicker-custom .air-datepicker-cell.-current- {
  color: hsl(var(--primary));
  border-color: hsl(var(--primary));
}

.air-datepicker-custom .air-datepicker-cell:hover {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* 确保日期选择器在模态框中正确显示 */
.air-datepicker {
  z-index: 1000 !important;
}

/* 禁用遮罩层 */
.air-datepicker-overlay {
  display: none !important;
}
