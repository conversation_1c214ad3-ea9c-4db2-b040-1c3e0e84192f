import { Suspense } from "react";
import { Metada<PERSON> } from "next";
import { Navbar } from "@/components/global/navbar";
import { Footer } from "@/components/global/footer";
import { getPageMetadata } from "@/constants";

import { Draw } from "@/components/draw";

export const metadata: Metadata = getPageMetadata("创造图片", "使用AI创造精美图片，支持多种风格和模型");

export default function DashboardPage() {
  return (
    <>
      <main className="max-w-[75rem] w-full mx-auto px-2 sm:px-4 md:px-6 lg:px-8">
        <div className="grid gap-4 pb-4">
          <div>
            <Navbar />
            <Suspense fallback={<div>Loading...</div>}>
              <Draw />
            </Suspense>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
