.bg-gradient-text {
  @apply bg-clip-text text-transparent bg-[length:400%_400%];
  background-image: linear-gradient(
    135deg,
    #6366f1,
    #ec4899,
    #3b82f6,
    #10b981,
    #6366f1
  );
}

@keyframes gradient-normal {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradient-reverse {
  0% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

.animate-gradient-normal {
  animation: gradient-normal 30s ease infinite;
}

.animate-gradient-reverse {
  animation: gradient-reverse 30s ease infinite;
}

@keyframes gradient1 {
  0% {
    background-position: 0% 50%;
    background-size: 200% 200%;
    opacity: 0.92;
  }
  25% {
    background-position: 50% 50%;
    background-size: 250% 250%;
    opacity: 1;
  }
  50% {
    background-position: 100% 50%;
    background-size: 200% 200%;
    opacity: 0.92;
  }
  75% {
    background-position: 50% 50%;
    background-size: 250% 250%;
    opacity: 1;
  }
  100% {
    background-position: 0% 50%;
    background-size: 200% 200%;
    opacity: 0.92;
  }
}

@keyframes gradient2 {
  0% {
    background-position: 100% 50%;
    background-size: 200% 200%;
    opacity: 0.92;
  }
  25% {
    background-position: 50% 50%;
    background-size: 250% 250%;
    opacity: 1;
  }
  50% {
    background-position: 0% 50%;
    background-size: 200% 200%;
    opacity: 0.92;
  }
  75% {
    background-position: 50% 50%;
    background-size: 250% 250%;
    opacity: 1;
  }
  100% {
    background-position: 100% 50%;
    background-size: 200% 200%;
    opacity: 0.92;
  }
}

@keyframes gradient3 {
  0% {
    background-position: 50% 0%;
    background-size: 200% 200%;
    opacity: 0.92;
  }
  25% {
    background-position: 50% 25%;
    background-size: 250% 250%;
    opacity: 1;
  }
  50% {
    background-position: 50% 100%;
    background-size: 200% 200%;
    opacity: 0.92;
  }
  75% {
    background-position: 50% 75%;
    background-size: 250% 250%;
    opacity: 1;
  }
  100% {
    background-position: 50% 0%;
    background-size: 200% 200%;
    opacity: 0.92;
  }
}

.gradient-text-1 {
  @apply bg-clip-text text-transparent bg-[length:400%_400%];
  background-image: linear-gradient(
    135deg,
    #3b82f6,    /* 亮蓝 */
    #10b981,    /* 翠绿 */
    #2563eb,    /* 深蓝 */
    #059669,    /* 深绿 */
    #3b82f6     /* 回到起始色 */
  );
  animation: gradient-normal 30s ease infinite;
}

.gradient-text-2 {
  @apply bg-clip-text text-transparent bg-[length:400%_400%];
  background-image: linear-gradient(
    135deg,
    #8b5cf6,    /* 亮紫 */
    #facc15,    /* 明亮金 */
    #7c3aed,    /* 深紫 */
    #fbbf24,    /* 金色 */
    #8b5cf6     /* 回到起始色 */
  );
  animation: gradient-reverse 30s ease infinite;
}

.gradient-button {
  @apply bg-[length:400%_400%];
  background-image: linear-gradient(
    135deg,
    #f59e0b,    /* 琥珀金 */
    #7c3aed,    /* 深紫 */
    #eab308,    /* 明黄 */
    #6d28d9,    /* 靛紫 */
    #f59e0b     /* 回到起始色 */
  );
  animation: gradient-normal 30s ease infinite;
  transition: all 0.4s cubic-bezier(0.45, 0.05, 0.55, 0.95);
}

.gradient-button:hover {
  transform: translateY(-2px) scale(1.02);
  animation: gradient-reverse 30s ease infinite;
  filter: brightness(1.1) saturate(1.2);
}
