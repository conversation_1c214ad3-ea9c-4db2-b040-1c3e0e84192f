import "./home.css"
import { Metadata } from "next"
import { SignInButton, SignedIn, SignedOut } from "@clerk/nextjs"

import { Navbar } from "@/components/global/navbar"
import { Footer } from "@/components/global/footer"
import { ScatteredImages } from "@/components/landing/scattered-images"
import { HOME_PAGE, getBaseMetadata } from "@/constants"

import Link from "next/link"

export const metadata: Metadata = getBaseMetadata()

export default function Home() {
  return (
    <>
      <main className="relative">
        <div className="w-full max-w-[75rem] mx-auto">
          <Navbar />
        </div>

        {/* Hero Section - Compact */}
        <div className="w-full max-w-[75rem] mx-auto px-4">
          <div className="h-[130px] flex items-center justify-center">
            <div className="flex items-center gap-3 sm:gap-12">
              {/* Brand */}
              <h1 className="text-xl sm:text-[2.5rem] leading-normal font-bold gradient-text-1 whitespace-nowrap">
                {HOME_PAGE.heroTitle}
              </h1>

              {/* Slogan */}
              <span className="text-lg sm:text-[1.8rem] font-bold gradient-text-2 whitespace-nowrap">
                {HOME_PAGE.heroSlogan}
              </span>

              {/* Button */}
              <div>
                <SignedIn>
                  <Link
                    href="/draw"
                    className="px-4 sm:px-8 py-1.5 sm:py-2.5 rounded-full text-white text-sm sm:text-base font-medium gradient-button shadow-lg whitespace-nowrap"
                  >
                    {HOME_PAGE.ctaButton}
                  </Link>
                </SignedIn>
                <SignedOut>
                  <SignInButton>
                    <button className="px-4 sm:px-8 py-1.5 sm:py-2.5 rounded-full text-white text-sm sm:text-base font-medium gradient-button shadow-lg whitespace-nowrap">
                      {HOME_PAGE.ctaButton}
                    </button>
                  </SignInButton>
                </SignedOut>
              </div>
            </div>
          </div>
        </div>

        {/* Image Gallery Section */}
        <div className="w-full max-w-[75rem] mx-auto px-4 sm:px-6 lg:px-8">
          <ScatteredImages />
        </div>
        <Footer />
      </main>
    </>
  )
}
