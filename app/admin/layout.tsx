"use client";

import {
  <PERSON><PERSON>,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarProvider,
  SidebarRail,
  SidebarFooter,
  useSidebar
} from "@/components/ui/sidebar";
import { Home, Users, RefreshCw, LogOut, CreditCard, History, Share2, Wallet, BarChart, PanelLeftClose, PanelLeftOpen, ChevronRight, Link as LinkIcon, LockIcon, ShieldBanIcon } from "lucide-react";
import Link from "next/link";
import { AdminCheck } from "@/components/admin/admin-check";
import { usePathname } from "next/navigation";
import { useEffect } from "react";

// Custom sidebar toggle buttons component
function SidebarToggleButtons() {
  const { state, toggleSidebar } = useSidebar();

  return (
    <>
      {/* Custom toggle button on the rail - half hidden and aligned to the edge */}
      <div className="fixed left-0 top-1/2 z-30 -translate-y-1/2 hidden md:block" style={{ transform: 'translateX(-50%)' }}>
        <button
          onClick={toggleSidebar}
          className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-primary-foreground shadow-md hover:bg-primary/90"
        >
          {state === "expanded" ? <PanelLeftClose className="h-5 w-5" /> : <PanelLeftOpen className="h-5 w-5" />}
        </button>
      </div>
    </>
  );
}

// Mobile-specific sidebar toggle button that's always visible
function MobileSidebarToggle() {
  const { toggleSidebar } = useSidebar();

  return (
    <div className="fixed left-0 top-1/2 -translate-y-1/2 z-50 block md:hidden" style={{ transform: 'translateX(-50%)' }}>
      <button
        onClick={toggleSidebar}
        className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-primary-foreground shadow-md hover:bg-primary/90"
      >
        <ChevronRight className="h-5 w-5" />
      </button>
    </div>
  );
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  const isActive = (href: string) => {
    if (href === "/admin" && pathname === "/admin") {
      return true;
    }
    return href !== "/admin" && pathname?.startsWith(href);
  };

  // Add a class to the body element when the component mounts
  useEffect(() => {
    document.body.classList.add('body-admin');

    // Clean up when the component unmounts
    return () => {
      document.body.classList.remove("body-admin");
    };
  }, []);

  return (
    <AdminCheck>
      <div className="bg-background">
        <SidebarProvider defaultOpen={true}>
          <MobileSidebarToggle />
          <div className="grid min-h-screen w-full md:grid-cols-[auto_1fr]">
            <Sidebar className="border-r">
              <SidebarRail className="relative" />
              <SidebarToggleButtons />
              <SidebarHeader className="flex h-14 items-center border-b p-4">
                <div className="flex items-center gap-2 font-semibold">
                  <span className="text-lg">Admin Dashboard</span>
                </div>
                {/* Original sidebar trigger removed as we have custom buttons now */}
              </SidebarHeader>
              <SidebarContent>
                <div className="p-4">
                  <SidebarMenu>
                    <Link href="/admin" passHref>
                      <SidebarMenuItem>
                        <SidebarMenuButton isActive={isActive("/admin")}>
                          <Home className="mr-2 h-4 w-4" />
                          <span>Dashboard</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </Link>
                    <Link href="/admin/users" passHref>
                      <SidebarMenuItem>
                        <SidebarMenuButton isActive={isActive("/admin/users")}>
                          <Users className="mr-2 h-4 w-4" />
                          <span>Users</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </Link>
                    <Link href="/admin/orders" passHref>
                      <SidebarMenuItem>
                        <SidebarMenuButton isActive={isActive("/admin/orders")}>
                          <CreditCard className="mr-2 h-4 w-4" />
                          <span>Orders</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </Link>
                    <Link href="/admin/histories" passHref>
                      <SidebarMenuItem>
                        <SidebarMenuButton
                          isActive={isActive("/admin/histories")}
                        >
                          <History className="mr-2 h-4 w-4" />
                          <span>Histories</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </Link>
                    <Link href="/admin/shares" passHref>
                      <SidebarMenuItem>
                        <SidebarMenuButton isActive={isActive("/admin/shares")}>
                          <Share2 className="mr-2 h-4 w-4" />
                          <span>Shares</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </Link>
                    <Link href="/admin/exchange" passHref>
                      <SidebarMenuItem>
                        <SidebarMenuButton
                          isActive={isActive("/admin/exchange")}
                        >
                          <Wallet className="mr-2 h-4 w-4" />
                          <span>Exchange</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </Link>
                    <Link href="/admin/invitations" passHref>
                      <SidebarMenuItem>
                        <SidebarMenuButton
                          isActive={isActive("/admin/invitations")}
                        >
                          <LinkIcon className="mr-2 h-4 w-4" />
                          <span>Invitations</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </Link>
                    <Link href="/admin/blocklists" passHref>
                      <SidebarMenuItem>
                        <SidebarMenuButton
                          isActive={isActive("/admin/blocklists")}
                        >
                          <ShieldBanIcon className="mr-2 h-4 w-4" />
                          <span>Blocklists</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </Link>
                    <Link href="/admin/analytics" passHref>
                      <SidebarMenuItem>
                        <SidebarMenuButton
                          isActive={isActive("/admin/analytics")}
                        >
                          <BarChart className="mr-2 h-4 w-4" />
                          <span>Analytics</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </Link>
                    <Link href="/admin/backup" passHref>
                      <SidebarMenuItem>
                        <SidebarMenuButton isActive={isActive("/admin/backup")}>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          <span>Backup</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </Link>
                  </SidebarMenu>
                </div>
              </SidebarContent>
              <SidebarFooter className="border-t p-4">
                <Link href="/" className="w-full">
                  <SidebarMenuButton
                    variant="outline"
                    className="w-full justify-start"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Back to Site</span>
                  </SidebarMenuButton>
                </Link>
              </SidebarFooter>
            </Sidebar>
            <main className="flex-1 overflow-auto p-6">{children}</main>
          </div>
        </SidebarProvider>
      </div>
    </AdminCheck>
  );
}
