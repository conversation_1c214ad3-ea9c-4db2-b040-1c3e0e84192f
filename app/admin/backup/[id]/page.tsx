"use client";

import { useParams, useRouter } from "next/navigation";
import { BackupExecutionDetail } from "@/components/admin/backup/backup-execution-detail";

export default function ExecutionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const executionId = params.id as string;

  return (
    <div className="py-4 px-2 md:px-4 relative">
      <div className="p-4 sm:p-8 rounded-xl bg-white shadow-[0_5px_15px_rgba(0,0,0,0.08),0_15px_35px_-5px_rgba(25,28,33,0.2)] ring-1 ring-gray-950/5 w-full">
        <BackupExecutionDetail
          executionId={executionId}
          onBack={() => router.push("/admin/backup")}
        />
      </div>
    </div>
  );
}
