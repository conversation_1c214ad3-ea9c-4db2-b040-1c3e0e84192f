"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { ChevronLeft, ExternalLink, Eye, ThumbsUp, GitFork, History, Share2, Trash2, Globe, Lock } from "lucide-react";
import { useToast } from "@/lib/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialog<PERSON>ontent,
  AlertDialogDes<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  AlertDialog<PERSON>it<PERSON>,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

interface ShareDetail {
  share: {
    id: string;
    shareId: string;
    historyId: string;
    userId: string;
    isPublic: boolean;
    allowFork: boolean;
    forkTipPoints: number;
    imageUrl: string;
    viewCount: number;
    likeCount: number;
    forkCount: number;
    forkEarnings: number;
    model?: string;
    styleId?: string;
    originalImages?: string[];
    customPrompt?: string;
    sharedAt: string;
    createdAt: string;
    updatedAt: string;
    extra: any;
    user?: {
      clerkId: string;
      username: string;
      email: string;
      avatarUrl?: string;
      createdAt: string;
    };
    history?: {
      id: string;
      userId: string;
      status: boolean;
      resultUrl?: string;
      prompt: string;
      description?: string;
      pointsUsed: number;
      parameters: any;
      createdAt: string;
    };
  };
  recentLikes: Array<{
    id: string;
    userId: string;
    shareId: string;
    createdAt: string;
    user?: {
      clerkId: string;
      username: string;
      email: string;
      avatarUrl?: string;
    };
  }>;
  forkedHistories: Array<{
    id: string;
    userId: string;
    status: boolean;
    resultUrl?: string;
    prompt: string;
    pointsUsed: number;
    createdAt: string;
    user?: {
      clerkId: string;
      username: string;
      email: string;
      avatarUrl?: string;
    };
  }>;
}

export default function ShareDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [shareDetail, setShareDetail] = useState<ShareDetail | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    const fetchShareDetail = async () => {
      try {
        const response = await fetch(`/api/admin/shares/${resolvedParams.id}`);

        if (!response.ok) {
          throw new Error("Failed to fetch share details");
        }

        const data = await response.json();
        setShareDetail(data);
      } catch (error) {
        console.error("Error fetching share details:", error);
        toast({
          title: "Error",
          description: "Failed to load share details. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchShareDetail();
  }, [resolvedParams.id, toast]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!shareDetail) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">Share Not Found</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center py-8">The requested share could not be found.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { share, recentLikes, forkedHistories } = shareDetail;
  const extra = share.extra || {};

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">Share Details</h1>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="visibility-mode"
              checked={share.isPublic}
              disabled={isUpdating}
              onCheckedChange={async (checked) => {
                setIsUpdating(true);
                try {
                  const response = await fetch(`/api/admin/shares/${share.id}`, {
                    method: "PUT",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ isPublic: checked }),
                  });

                  if (!response.ok) {
                    throw new Error("Failed to update share visibility");
                  }

                  // Update the local state
                  setShareDetail(prev => {
                    if (!prev) return null;
                    return {
                      ...prev,
                      share: {
                        ...prev.share,
                        isPublic: checked,
                      }
                    };
                  });

                  toast({
                    title: "Success",
                    description: `Share is now ${checked ? "public" : "private"}.`,
                  });
                } catch (error) {
                  console.error("Error updating share visibility:", error);
                  toast({
                    title: "Error",
                    description: "Failed to update share visibility. Please try again.",
                    variant: "destructive",
                  });
                } finally {
                  setIsUpdating(false);
                }
              }}
            />
            <Label htmlFor="visibility-mode" className="flex items-center cursor-pointer">
              {share.isPublic ? (
                <>
                  <Globe className="h-4 w-4 mr-1 text-green-500" />
                  Public
                </>
              ) : (
                <>
                  <Lock className="h-4 w-4 mr-1 text-yellow-500" />
                  Private
                </>
              )}
            </Label>
          </div>

          <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" size="sm" disabled={isDeleting}>
                <Trash2 className="h-4 w-4 mr-2" />
                {isDeleting ? "Deleting..." : "Delete Share"}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action will permanently delete this share record and all associated likes.
                  This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={async (e) => {
                    e.preventDefault();
                    setIsDeleting(true);
                    try {
                      const response = await fetch(`/api/admin/shares/${share.id}`, {
                        method: "DELETE",
                      });

                      if (!response.ok) {
                        throw new Error("Failed to delete share");
                      }

                      toast({
                        title: "Success",
                        description: "Share deleted successfully.",
                      });

                      // Redirect back to shares list
                      router.push("/admin/shares");
                    } catch (error) {
                      console.error("Error deleting share:", error);
                      toast({
                        title: "Error",
                        description: "Failed to delete share. Please try again.",
                        variant: "destructive",
                      });
                      setIsDeleting(false);
                    }
                  }}
                  className="bg-red-600 hover:bg-red-700"
                >
                  {isDeleting ? "Deleting..." : "Delete"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Image Preview */}
        <Card>
          <CardHeader>
            <CardTitle>Image Preview</CardTitle>
            <CardDescription>Shared image</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center">
            <div className="relative w-full max-w-md h-64 mb-4 overflow-hidden rounded-md">
              <img
                src={share.imageUrl}
                alt="Shared image"
                className="object-contain w-full h-full"
              />
            </div>
            <a
              href={share.imageUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:underline flex items-center"
            >
              View Full Size <ExternalLink className="ml-1 h-4 w-4" />
            </a>
          </CardContent>
          <CardFooter className="flex justify-center">
            <div className="flex gap-6 text-center">
              <div className="flex flex-col items-center">
                <Eye className="h-5 w-5 mb-1 text-blue-500" />
                <span className="text-xl font-bold">{share.viewCount}</span>
                <span className="text-sm text-muted-foreground">Views</span>
              </div>
              <div className="flex flex-col items-center">
                <ThumbsUp className="h-5 w-5 mb-1 text-green-500" />
                <span className="text-xl font-bold">{share.likeCount}</span>
                <span className="text-sm text-muted-foreground">Likes</span>
              </div>
              <div className="flex flex-col items-center">
                <GitFork className="h-5 w-5 mb-1 text-purple-500" />
                <span className="text-xl font-bold">{share.forkCount}</span>
                <span className="text-sm text-muted-foreground">Forks</span>
              </div>
            </div>
          </CardFooter>
        </Card>

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Share Information</CardTitle>
            <CardDescription>Details about this shared content</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Share ID:</span>
                <span className="text-muted-foreground">{share.shareId}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Visibility:</span>
                <Badge className={share.isPublic ? "bg-green-500" : "bg-yellow-500"}>
                  {share.isPublic ? "Public" : "Private"}
                </Badge>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Allow Fork:</span>
                <Badge className={share.allowFork ? "bg-green-500" : "bg-red-500"}>
                  {share.allowFork ? "Allowed" : "Not Allowed"}
                </Badge>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Fork Tip Points:</span>
                <span className="text-muted-foreground">{share.forkTipPoints}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Fork Earnings:</span>
                <span className="text-muted-foreground">{share.forkEarnings} points</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Shared At:</span>
                <span className="text-muted-foreground">{formatDate(share.sharedAt)}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Created At:</span>
                <span className="text-muted-foreground">{formatDate(share.createdAt)}</span>
              </div>
              {share.model && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">Model:</span>
                  <span className="text-muted-foreground">{share.model}</span>
                </div>
              )}
              {share.styleId && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">Style ID:</span>
                  <span className="text-muted-foreground">{share.styleId}</span>
                </div>
              )}
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">History:</span>
                <a
                  href={`/admin/histories/${share.historyId}`}
                  className="text-blue-500 hover:underline flex items-center"
                >
                  View History <ExternalLink className="ml-1 h-3 w-3" />
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* User Information */}
      <Card>
        <CardHeader>
          <CardTitle>User Information</CardTitle>
          <CardDescription>User who shared this content</CardDescription>
        </CardHeader>
        <CardContent>
          {share.user ? (
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={share.user.avatarUrl} />
                <AvatarFallback className="text-lg">
                  {getInitials(share.user.username)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-xl font-semibold">{share.user.username}</h2>
                <p className="text-muted-foreground">{share.user.email}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  asChild
                >
                  <a href={`/admin/users/${share.user.clerkId}`}>View User Profile</a>
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">User information not available</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Prompt Information */}
      {share.history && (
        <Card>
          <CardHeader>
            <CardTitle>Prompt Information</CardTitle>
            <CardDescription>Generation prompt used for this content</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Original Prompt</h3>
                <div className="bg-muted p-4 rounded-md whitespace-pre-wrap">
                  {share.history.prompt}
                </div>
              </div>

              {share.customPrompt && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Custom Prompt (For Sharing)</h3>
                  <div className="bg-muted p-4 rounded-md whitespace-pre-wrap">
                    {share.customPrompt}
                  </div>
                </div>
              )}

              {share.history.description && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Description</h3>
                  <div className="bg-muted p-4 rounded-md whitespace-pre-wrap">
                    {share.history.description}
                  </div>
                </div>
              )}

              <div className="flex justify-between items-center">
                <div>
                  <span className="font-medium">Points Used: </span>
                  <span className="text-muted-foreground">{share.history.pointsUsed}</span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                >
                  <a href={`/admin/histories/${share.historyId}`}>
                    <History className="h-4 w-4 mr-2" />
                    View Full History
                  </a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Activity Tabs */}
      <Card>
        <CardHeader>
          <CardTitle>Activity</CardTitle>
          <CardDescription>Recent activity related to this share</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="likes" className="space-y-4">
            <TabsList>
              <TabsTrigger value="likes">Recent Likes</TabsTrigger>
              <TabsTrigger value="forks">Forked Histories</TabsTrigger>
            </TabsList>

            <TabsContent value="likes">
              {recentLikes.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No likes yet</p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>User</TableHead>
                        <TableHead>Liked At</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {recentLikes.map((like) => (
                        <TableRow key={like.id}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={like.user?.avatarUrl} />
                                <AvatarFallback>
                                  {like.user ? getInitials(like.user.username) : "?"}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div>{like.user?.username || "Unknown"}</div>
                                <div className="text-xs text-muted-foreground">
                                  {like.user?.email || "No email"}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{formatDate(like.createdAt)}</TableCell>
                          <TableCell className="text-right">
                            {like.user && (
                              <Button
                                variant="outline"
                                size="sm"
                                asChild
                              >
                                <a href={`/admin/users/${like.user.clerkId}`}>View User</a>
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </TabsContent>

            <TabsContent value="forks">
              {forkedHistories.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No forks yet</p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>User</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Points Used</TableHead>
                        <TableHead>Created At</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {forkedHistories.map((history) => (
                        <TableRow key={history.id}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={history.user?.avatarUrl} />
                                <AvatarFallback>
                                  {history.user ? getInitials(history.user.username) : "?"}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div>{history.user?.username || "Unknown"}</div>
                                <div className="text-xs text-muted-foreground">
                                  {history.user?.email || "No email"}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={history.status ? "bg-green-500" : "bg-red-500"}>
                              {history.status ? "Success" : "Failed"}
                            </Badge>
                          </TableCell>
                          <TableCell>{history.pointsUsed}</TableCell>
                          <TableCell>{formatDate(history.createdAt)}</TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="outline"
                              size="sm"
                              asChild
                            >
                              <a href={`/admin/histories/${history.id}`}>View History</a>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Extra Information */}
      {Object.keys(extra).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
            <CardDescription>Extra metadata about this share</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Field</TableHead>
                    <TableHead>Value</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(extra).map(([key, value]) => (
                    <TableRow key={key}>
                      <TableCell className="font-medium">{key}</TableCell>
                      <TableCell>
                        {typeof value === 'object'
                          ? JSON.stringify(value)
                          : String(value)
                        }
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
