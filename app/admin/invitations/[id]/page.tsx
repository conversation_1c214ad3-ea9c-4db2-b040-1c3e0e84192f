"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft, Users, Edit2, ExternalLinkIcon, RefreshCw, CheckSquare } from "lucide-react";
import { useToast } from "@/lib/hooks/use-toast";
import { InvitationUsagesAdminTable } from "@/components/admin/invitation/invitation-usages-table";
import { InvitationForm } from "@/components/admin/invitation/invitation-form";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { generateInviteLink } from "@/constants/invitation";
import { useInvitationStore } from "@/store/invitation";
import { BatchRedeemDialog } from "@/components/invitation/batch-redeem-dialog";

interface InvitationDetail {
  invitation: {
    id: string;
    inviteCode: string;
    referrerId: string;
    inviteType: "points" | "cash" | "both";
    refRatio: number;
    channel?: string;
    maxUses: number;
    expiresAt?: string;
    createdAt: string;
    updatedAt: string;
  };
  referrer: {
    clerkId: string;
    email: string;
    username: string;
    avatarUrl?: string;
  };
  stats: {
    usageCount: number;
    pendingCount: number;
    readyCount: number;
    completedCount: number;
    voidCount: number;
  };
}

export default function InvitationDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);

  const router = useRouter();
  const { toast } = useToast();
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [batchRedeemDialogOpen, setBatchRedeemDialogOpen] = useState(false);

  // 使用邀请store
  const {
    invitationDetail,
    loadingDetail,
    usages,
    selectedUsageIds,
    fetchInvitationDetail,
    fetchInvitationUsages,
    refreshAll,
    clearSelectedUsages
  } = useInvitationStore();

  // 初始化加载数据
  useEffect(() => {
    const loadData = async () => {
      try {
        await fetchInvitationDetail(resolvedParams.id);
        await fetchInvitationUsages({ invitationId: resolvedParams.id, isAdmin: true });
      } catch (error) {
        console.error("Error loading invitation data:", error);
        toast({
          title: "错误",
          description: "加载邀请码详情失败，请重试。",
          variant: "destructive",
        });
      }
    };

    loadData();
  }, [resolvedParams.id, fetchInvitationDetail, fetchInvitationUsages, toast]);

  const formatDate = (dateString?: string) => {
    if (!dateString) return "无";
    return new Date(dateString).toLocaleString();
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const handleUpdateSuccess = () => {
    toast({
      title: "更新成功",
      description: "邀请码已成功更新",
      className: "bg-green-500 text-white border-green-600",
    });
    refreshAll(resolvedParams.id);
  };

  // 处理批量兑换成功
  const handleBatchRedeemSuccess = () => {
    toast({
      title: "兑换成功",
      description: "批量兑换操作已完成",
      className: "bg-green-500 text-white border-green-600",
    });
    refreshAll(resolvedParams.id);
    clearSelectedUsages();
  };

  const getInviteTypeText = (type: "points" | "cash" | "both") => {
    switch (type) {
      case "points": return "积分奖励";
      case "cash": return "现金奖励";
      case "both": return "积分或现金";
      default: return type;
    }
  };

  if (loadingDetail) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <div className="animate-spin rounded-full h-8 w-16 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!invitationDetail) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">邀请码不存在</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center py-8">请求的邀请码不存在。</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { invitation, referrer, stats } = invitationDetail;
  const inviteLink = generateInviteLink(invitation.inviteCode);

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          返回
        </Button>
        <h1 className="text-3xl font-bold tracking-tight">邀请码详情</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* 邀请码信息卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>邀请码信息</CardTitle>
            <CardDescription>基本邀请码信息</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">邀请码:</span>
                <div className="flex items-center gap-2">
                  <span className="font-mono">{invitation.inviteCode}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setEditDialogOpen(true)}
                    className="h-6 w-6"
                  >
                    <Edit2 className="h-3.5 w-3.5" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">邀请链接:</span>
                <a
                  href={inviteLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline font-mono truncate max-w-[300px]"
                >
                  <ExternalLinkIcon className="h-3.5 w-3.5 mr-1" />
                </a>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">奖励类型:</span>
                <span>{getInviteTypeText(invitation.inviteType)}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">奖励比例:</span>
                <span>{(invitation.refRatio * 100).toFixed(0)}%</span>
              </div>
              {invitation.channel && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">渠道:</span>
                  <span>{invitation.channel}</span>
                </div>
              )}
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">最大使用次数:</span>
                <span>{invitation.maxUses > 0 ? invitation.maxUses : "不限"}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">过期时间:</span>
                <span>{invitation.expiresAt ? formatDate(invitation.expiresAt) : "永不过期"}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">创建时间:</span>
                <span>{formatDate(invitation.createdAt)}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">更新时间:</span>
                <span>{formatDate(invitation.updatedAt)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 邀请人信息卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>邀请人信息</CardTitle>
            <CardDescription>创建此邀请码的用户</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center gap-4 mb-6">
              <Avatar className="h-24 w-24">
                <AvatarImage src={referrer.avatarUrl} />
                <AvatarFallback className="text-lg">
                  {getInitials(referrer.username)}
                </AvatarFallback>
              </Avatar>
              <div className="text-center">
                <h2 className="text-xl font-semibold">{referrer.username}</h2>
                <p className="text-muted-foreground">{referrer.email}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                asChild
              >
                <a href={`/admin/users/${referrer.clerkId}`}>查看用户资料</a>
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-6">
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <Users className="h-8 w-16 mb-2 text-primary" />
                <span className="text-2xl font-bold">{stats.usageCount}</span>
                <span className="text-sm text-muted-foreground">总使用次数</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <div className="h-8 w-16 mb-2 flex items-center justify-center">
                  <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
                    已完成
                  </Badge>
                </div>
                <span className="text-2xl font-bold">{stats.completedCount}</span>
                <span className="text-sm text-muted-foreground">已完成</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <div className="h-8 w-16 mb-2 flex items-center justify-center">
                  <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300">
                    待兑换
                  </Badge>
                </div>
                <span className="text-2xl font-bold">{stats.readyCount}</span>
                <span className="text-sm text-muted-foreground">待兑换</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <div className="h-8 w-16 mb-2 flex items-center justify-center">
                  <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-300">
                    待充值
                  </Badge>
                </div>
                <span className="text-2xl font-bold">{stats.pendingCount}</span>
                <span className="text-sm text-muted-foreground">待充值</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>邀请使用记录</CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={selectedUsageIds.length <= 0}
                onClick={() => setBatchRedeemDialogOpen(true)}
              >
                <CheckSquare className="h-4 w-4 mr-2" />
                批量兑换 ({selectedUsageIds.length})
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refreshAll(resolvedParams.id)}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <InvitationUsagesAdminTable
            invitationId={invitation.id}
            useStore={true}
          />
        </CardContent>
      </Card>

      {/* 编辑邀请码对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑邀请码</DialogTitle>
          </DialogHeader>
          <InvitationForm
            initialData={invitation}
            onSuccess={() => {
              handleUpdateSuccess();
              setEditDialogOpen(false);
            }}
            isEdit={true}
          />
        </DialogContent>
      </Dialog>

      {/* 批量兑换对话框 */}
      <BatchRedeemDialog
        open={batchRedeemDialogOpen}
        onOpenChange={setBatchRedeemDialogOpen}
        onSuccess={handleBatchRedeemSuccess}
        isAdmin={true}
      />
    </div>
  );
}
