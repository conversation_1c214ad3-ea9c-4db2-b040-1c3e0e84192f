"use client";

import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BlocklistForm } from "@/components/admin/blocklists/blocklist-form";

import { useToast } from "@/lib/hooks/use-toast";

type FormValues = {
  type: string;
  pattern: string;
  description?: string;
  enabled: boolean;
};

export default function AddBlocklistPage() {
  const router = useRouter();
  const { toast } = useToast();

  // 创建新规则
  const handleCreateRule = async (values: FormValues) => {
    try {
      const response = await fetch("/api/admin/blocklists", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create blocklist rule");
      }

      toast({
        title: "创建成功",
        description: "新的黑名单规则已创建",
      });

      router.push("/admin/blocklists");
    } catch (error) {
      console.error("Error creating blocklist rule:", error);
      throw error;
    }
  };

  // 返回列表页
  const handleCancel = () => {
    router.push("/admin/blocklists");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm" onClick={handleCancel}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回列表
        </Button>
        <h1 className="text-3xl font-bold tracking-tight">添加黑名单规则</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>添加黑名单规则</CardTitle>
          <CardDescription>
            创建新的黑名单规则，匹配的用户将只获得1积分而不是标准的300积分
          </CardDescription>
        </CardHeader>
        <CardContent>
          <BlocklistForm
            onSubmit={handleCreateRule}
            onCancel={handleCancel}
            isEdit={false}
          />
        </CardContent>
      </Card>
    </div>
  );
}
