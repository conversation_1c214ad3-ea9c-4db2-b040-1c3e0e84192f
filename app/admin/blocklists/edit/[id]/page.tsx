"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { ArrowLeft } from "lucide-react";

import { BlocklistForm } from "@/components/admin/blocklists/blocklist-form";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

import { useToast } from "@/lib/hooks/use-toast";

type BlocklistRule = {
  id: string;
  type: string;
  pattern: string;
  enabled: boolean;
  description?: string;
  createdAt: string;
  updatedAt: string;
  creator?: {
    username: string;
    email: string;
  };
};

type FormValues = {
  type: string;
  pattern: string;
  description?: string;
  enabled: boolean;
};

export default function EditBlocklistPage() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const [rule, setRule] = useState<BlocklistRule | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const id = params.id as string;

  // 加载黑名单规则
  useEffect(() => {
    const loadRule = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/admin/blocklists/${id}`);

        if (!response.ok) {
          throw new Error("Failed to load blocklist rule");
        }

        const data = await response.json();
        setRule(data);
      } catch (error) {
        console.error("Error loading blocklist rule:", error);
        toast({
          title: "加载失败",
          description: "无法加载黑名单规则",
          variant: "destructive",
        });
        router.push("/admin/blocklists");
      } finally {
        setIsLoading(false);
      }
    };

    loadRule();
  }, [id, router, toast]);

  // 更新规则
  const handleUpdateRule = async (values: FormValues) => {
    try {
      const response = await fetch(`/api/admin/blocklists/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update blocklist rule");
      }

      toast({
        title: "更新成功",
        description: "黑名单规则已更新",
      });

      router.push("/admin/blocklists");
    } catch (error) {
      console.error("Error updating blocklist rule:", error);
      throw error;
    }
  };

  // 返回列表页
  const handleCancel = () => {
    router.push("/admin/blocklists");
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={handleCancel}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回列表
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">编辑黑名单规则</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-center items-center h-40">
              <p>加载中...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!rule) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={handleCancel}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回列表
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">编辑黑名单规则</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-center items-center h-40">
              <p>未找到规则</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm" onClick={handleCancel}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回列表
        </Button>
        <h1 className="text-3xl font-bold tracking-tight">编辑黑名单规则</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>编辑黑名单规则</CardTitle>
          <CardDescription>
            修改黑名单规则的匹配模式、描述和状态
          </CardDescription>
        </CardHeader>
        <CardContent>
          <BlocklistForm
            initialData={rule}
            onSubmit={handleUpdateRule}
            onCancel={handleCancel}
            isEdit={true}
          />
        </CardContent>
      </Card>
    </div>
  );
}
