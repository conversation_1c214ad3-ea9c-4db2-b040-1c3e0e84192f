"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import { TransactionsTable } from "@/components/admin/transactions-table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { ChevronLeft, CreditCard, User, Calendar, Tag, ExternalLink } from "lucide-react";
import Link from "next/link";
import { OrderStatusSelector } from "@/components/admin/order/order-status-selector";
import { useToast } from "@/lib/hooks/use-toast";

interface OrderDetail {
  order: {
    id: string;
    userId: string;
    buyerId: string;
    type: string;
    status: string;
    amount: number;
    description: string;
    paymentMethod?: string;
    outTradeNo?: string; // 商户订单号
    tradeNo?: string; // 支付平台订单号
    qrCodeUrl?: string; // 支付二维码URL
    paidAt?: string; // 支付完成时间
    refundedAt?: string; // 退款时间
    extra: {
      price?: number;
      [key: string]: any;
    };
    createdAt: string;
    updatedAt: string;
    user?: {
      clerkId: string;
      username: string;
      email: string;
      avatarUrl?: string;
      createdAt: string;
    };
  };
}

export default function OrderDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [orderDetail, setOrderDetail] = useState<OrderDetail | null>(null);

  useEffect(() => {
    const fetchOrderDetail = async () => {
      try {
        const response = await fetch(`/api/admin/orders/${resolvedParams.id}`);

        if (!response.ok) {
          throw new Error("Failed to fetch order details");
        }

        const data = await response.json();
        setOrderDetail(data);
      } catch (error) {
        console.error("Error fetching order details:", error);
        toast({
          title: "Error",
          description: "Failed to load order details. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetail();
  }, [resolvedParams.id, toast]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "credit":
        return <Badge className="bg-green-500">Credit</Badge>;
      case "debit":
        return <Badge className="bg-blue-500">Debit</Badge>;
      default:
        return <Badge className="bg-gray-500">{type}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!orderDetail) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">Order Not Found</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center py-8">The requested order could not be found.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { order } = orderDetail;
  const extra = order.extra || {};

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <h1 className="text-3xl font-bold tracking-tight">Order Details</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Order Information</CardTitle>
            <CardDescription>Details about this order</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Order ID:</span>
                <span className="text-muted-foreground">{order.id}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Type:</span>
                <span>{getTypeBadge(order.type)}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Status:</span>
                <OrderStatusSelector
                  orderId={order.id}
                  initialStatus={order.status}
                  onStatusChange={(newStatus) => {
                    // Update the local state
                    setOrderDetail(prev => {
                      if (!prev) return prev;
                      return {
                        ...prev,
                        order: {
                          ...prev.order,
                          status: newStatus
                        }
                      };
                    });
                  }}
                />
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Amount:</span>
                <span className="text-muted-foreground">{order.amount} points</span>
              </div>
              {extra.price !== undefined && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">Price:</span>
                  <span className="text-muted-foreground">${extra.price.toFixed(2)}</span>
                </div>
              )}
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Description:</span>
                <span className="text-muted-foreground">{order.description}</span>
              </div>
              {order.paymentMethod && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">Payment Method:</span>
                  <span className="text-muted-foreground">{order.paymentMethod}</span>
                </div>
              )}
              {order.outTradeNo && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">Merchant Order No:</span>
                  <span className="text-muted-foreground">{order.outTradeNo}</span>
                </div>
              )}
              {order.tradeNo && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">Platform Order No:</span>
                  <span className="text-muted-foreground">{order.tradeNo}</span>
                </div>
              )}
              {order.qrCodeUrl && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">QR Code URL:</span>
                  <a
                    href={order.qrCodeUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline"
                  >
                    View QR Code
                  </a>
                </div>
              )}
              {order.paidAt && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">Paid At:</span>
                  <span className="text-muted-foreground">{formatDate(order.paidAt)}</span>
                </div>
              )}
              {order.refundedAt && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">Refunded At:</span>
                  <span className="text-muted-foreground">{formatDate(order.refundedAt)}</span>
                </div>
              )}
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Created At:</span>
                <span className="text-muted-foreground">{formatDate(order.createdAt)}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Updated At:</span>
                <span className="text-muted-foreground">{formatDate(order.updatedAt)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* User Information */}
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
            <CardDescription>User who placed this order</CardDescription>
          </CardHeader>
          <CardContent>
            {order.user ? (
              <div className="flex flex-col items-center gap-4 mb-6">
                <Avatar className="h-24 w-24">
                  <AvatarImage src={order.user.avatarUrl} />
                  <AvatarFallback className="text-lg">
                    {getInitials(order.user.username)}
                  </AvatarFallback>
                </Avatar>
                <div className="text-center">
                  <h2 className="text-xl font-semibold">{order.user.username}</h2>
                  <p className="text-muted-foreground">{order.user.email}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  asChild
                >
                  <a href={`/admin/users/${order.user.clerkId}`}>View User Profile</a>
                </Button>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">User information not available</p>
              </div>
            )}

            <div className="mt-6 pt-6 border-t">
              <h3 className="text-lg font-medium mb-4">Buyer Information</h3>
              {order.buyerId === order.userId ? (
                <div className="text-center py-2 bg-muted rounded-md">
                  <p>Same as order user (self-purchase)</p>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Buyer ID:</span>
                    <span className="text-muted-foreground">{order.buyerId}</span>
                  </div>
                  <div className="flex justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      asChild
                    >
                      <a href={`/admin/users/${order.buyerId}`}>View Buyer Profile</a>
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Transactions */}
      {extra.transactions && extra.transactions.length > 0 && (
        <TransactionsTable
          transactions={extra.transactions}
          title="Points Transactions"
          description="Points transactions associated with this order"
        />
      )}

      {/* Extra Information */}
      {Object.keys(extra).length > 0 && Object.keys(extra).some(key => key !== 'price' && key !== 'transactions') && (
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
            <CardDescription>Extra metadata about this order</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Field</TableHead>
                    <TableHead>Value</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(extra)
                    .filter(([key]) => key !== 'price' && key !== 'transactions') // Skip price and transactions as they're shown elsewhere
                    .map(([key, value]) => (
                      <TableRow key={key}>
                        <TableCell className="font-medium">{key}</TableCell>
                        <TableCell>
                          {key === 'historyId' ? (
                            <Link href={`/admin/histories/${value}`} className="text-blue-600 hover:underline flex items-center">
                              <span>查看历史记录:</span>
                              <span className="ml-1 font-mono">{String(value).substring(0, 8)}...</span>
                              <ExternalLink className="h-3 w-3 ml-1" />
                            </Link>
                          ) : key === 'relatedOrderId' ? (
                            <Link href={`/admin/orders/${value}`} className="text-blue-600 hover:underline flex items-center">
                              <span>查看关联订单:</span>
                              <span className="ml-1 font-mono">{String(value).substring(0, 8)}...</span>
                              <ExternalLink className="h-3 w-3 ml-1" />
                            </Link>
                          ) : typeof value === 'object' ? (
                            JSON.stringify(value)
                          ) : (
                            String(value)
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Order Summary</CardTitle>
          <CardDescription>Overview of this transaction</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4">
            <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
              <CreditCard className="h-8 w-8 mb-2 text-primary" />
              <span className="text-2xl font-bold">{order.amount}</span>
              <span className="text-sm text-muted-foreground">Points</span>
            </div>
            <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
              <User className="h-8 w-8 mb-2 text-primary" />
              <span className="text-2xl font-bold">{order.user?.username || "Unknown"}</span>
              <span className="text-sm text-muted-foreground">User</span>
            </div>
            <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
              <Tag className="h-8 w-8 mb-2 text-primary" />
              <span className="text-2xl font-bold">{order.type}</span>
              <span className="text-sm text-muted-foreground">Type</span>
            </div>
            <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
              <Calendar className="h-8 w-8 mb-2 text-primary" />
              <span className="text-2xl font-bold">{new Date(order.createdAt).toLocaleDateString()}</span>
              <span className="text-sm text-muted-foreground">Date</span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">
              {order.status === "SUCCESS" ? (
                <>This order was successfully processed {order.paymentMethod && <>via {order.paymentMethod}</>} {order.paidAt && <>on {formatDate(order.paidAt)}</>}</>
              ) : order.status === "PENDING" ? (
                <>This order is pending payment {order.paymentMethod && <>via {order.paymentMethod}</>} since {formatDate(order.createdAt)}</>
              ) : order.status === "FAILED" ? (
                <>This order failed processing {order.paymentMethod && <>via {order.paymentMethod}</>} on {formatDate(order.updatedAt)}</>
              ) : order.status === "REFUND" ? (
                <>This order was refunded {order.refundedAt && <>on {formatDate(order.refundedAt)}</>}</>
              ) : (
                <>This order was created on {formatDate(order.createdAt)}</>
              )}
            </p>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
