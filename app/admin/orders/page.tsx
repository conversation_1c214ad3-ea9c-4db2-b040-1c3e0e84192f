import { OrdersTable } from "@/components/admin/order/orders-table";
import { Button } from "@/components/ui/button";
import { Wallet } from "lucide-react";
import Link from "next/link";

export default function AdminOrdersPage() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Orders Management</h1>
          <p className="text-muted-foreground">
            View and manage payment transactions in the system.
          </p>
        </div>
      </div>

      <OrdersTable />
    </div>
  );
}
