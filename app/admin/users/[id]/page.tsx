"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft, Wallet, History, CreditCard, Share2, Users, AlertTriangle } from "lucide-react";
import { useToast } from "@/lib/hooks/use-toast";
import { OrdersTable } from "@/components/admin/order/orders-table";
import { HistoriesTable } from "@/components/admin/history/histories-table";
import { SharesTable } from "@/components/admin/shares-table";
import { InvitationUsagesAdminTable } from "@/components/admin/invitation/invitation-usages-table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface UserDetail {
  user: {
    clerkId: string;
    email: string;
    username: string;
    avatarUrl?: string;
    createdAt: string;
    updatedAt: string;
    extra?: {
      isSuspended?: boolean;
      [key: string]: any;
    };
  };
  wallet: {
    id: string;
    userId: string;
    permanentPoints: number;
    createdAt: string;
    updatedAt: string;
  } | null;
  stats: {
    ordersCount: number;
    historiesCount: number;
    sharesCount: number;
    sentInvitationsCount: number;
    receivedInvitationsCount: number;
  };
  recentActivity: {
    orders: any[];
    histories: any[];
    shares: any[];
  };
}

export default function UserDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);

  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [userDetail, setUserDetail] = useState<UserDetail | null>(null);
  const [isSuspended, setIsSuspended] = useState(false);
  const [suspendDialogOpen, setSuspendDialogOpen] = useState(false);
  const [activateDialogOpen, setActivateDialogOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    const fetchUserDetail = async () => {
      try {
        const response = await fetch(`/api/admin/users/${resolvedParams.id}`);

        if (!response.ok) {
          throw new Error("Failed to fetch user details");
        }

        const data = await response.json();
        setUserDetail(data);

        // Set the suspension status based on the user data
        const suspendedStatus = data.user.extra?.isSuspended || false;
        setIsSuspended(suspendedStatus);
      } catch (error) {
        console.error("Error fetching user details:", error);
        toast({
          title: "Error",
          description: "Failed to load user details. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchUserDetail();
  }, [resolvedParams.id, toast]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  // Function to suspend a user
  const handleSuspendUser = async () => {
    if (!userDetail) return;

    setActionLoading(true);
    try {
      const response = await fetch('/api/admin/users/suspend', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userDetail.user.clerkId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to suspend user");
      }

      // Update the local state
      setIsSuspended(true);

      // Update the userDetail state
      setUserDetail(prev => {
        if (!prev) return null;
        return {
          ...prev,
          user: {
            ...prev.user,
            extra: {
              ...prev.user.extra,
              isSuspended: true,
            }
          }
        };
      });

      toast({
        title: "Success",
        description: "User has been suspended successfully.",
      });
    } catch (error) {
      console.error("Error suspending user:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to suspend user. Please try again.",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
      setSuspendDialogOpen(false);
    }
  };

  // Function to activate a user
  const handleActivateUser = async () => {
    if (!userDetail) return;

    setActionLoading(true);
    try {
      const response = await fetch(`/api/admin/users/suspend?userId=${userDetail.user.clerkId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to activate user");
      }

      // Update the local state
      setIsSuspended(false);

      // Update the userDetail state
      setUserDetail(prev => {
        if (!prev) return null;
        return {
          ...prev,
          user: {
            ...prev.user,
            extra: {
              ...prev.user.extra,
              isSuspended: false,
            }
          }
        };
      });

      toast({
        title: "Success",
        description: "User has been activated successfully.",
      });
    } catch (error) {
      console.error("Error activating user:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to activate user. Please try again.",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
      setActivateDialogOpen(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!userDetail) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">User Not Found</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center py-8">The requested user could not be found.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { user, wallet, stats, recentActivity } = userDetail;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <h1 className="text-3xl font-bold tracking-tight">User Details</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* User Profile Card */}
        <Card>
          <CardHeader>
            <CardTitle>User Profile</CardTitle>
            <CardDescription>Basic user information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center gap-4 mb-6">
              <Avatar className="h-24 w-24">
                <AvatarImage src={user.avatarUrl} />
                <AvatarFallback className="text-lg">
                  {getInitials(user.username)}
                </AvatarFallback>
              </Avatar>
              <div className="text-center">
                <h2 className="text-xl font-semibold">{user.username}</h2>
                <p className="text-muted-foreground">{user.email}</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">User ID:</span>
                <span className="text-muted-foreground">{user.clerkId}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Created At:</span>
                <span className="text-muted-foreground">{formatDate(user.createdAt)}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Last Updated:</span>
                <span className="text-muted-foreground">{formatDate(user.updatedAt)}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium">Status:</span>
                <div className="flex items-center">
                  {isSuspended ? (
                    <span className="text-destructive flex items-center">
                      <AlertTriangle className="h-4 w-4 mr-1" />
                      已冻结
                    </span>
                  ) : (
                    <span className="text-green-500">正常</span>
                  )}
                </div>
              </div>
              <div className="pt-4">
                {isSuspended ? (
                  <Button
                    variant="outline"
                    className="w-full text-green-500 border-green-500 hover:bg-green-50"
                    onClick={() => setActivateDialogOpen(true)}
                    disabled={actionLoading}
                  >
                    {actionLoading ? "处理中..." : "激活用户"}
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    className="w-full text-destructive border-destructive hover:bg-red-50"
                    onClick={() => setSuspendDialogOpen(true)}
                    disabled={actionLoading}
                  >
                    {actionLoading ? "处理中..." : "冻结用户"}
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* User Stats Card */}
        <Card>
          <CardHeader>
            <CardTitle>User Statistics</CardTitle>
            <CardDescription>Overview of user activity</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-4">
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <Wallet className="h-8 w-8 mb-2 text-primary" />
                <span className="text-2xl font-bold">{wallet?.permanentPoints || 0}</span>
                <span className="text-sm text-muted-foreground">Points</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <CreditCard className="h-8 w-8 mb-2 text-primary" />
                <span className="text-2xl font-bold">{stats.ordersCount}</span>
                <span className="text-sm text-muted-foreground">Orders</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <History className="h-8 w-8 mb-2 text-primary" />
                <span className="text-2xl font-bold">{stats.historiesCount}</span>
                <span className="text-sm text-muted-foreground">Histories</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <Share2 className="h-8 w-8 mb-2 text-primary" />
                <span className="text-2xl font-bold">{stats.sharesCount}</span>
                <span className="text-sm text-muted-foreground">Shares</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <Users className="h-8 w-8 mb-2 text-primary" />
                <span className="text-2xl font-bold">{stats.sentInvitationsCount}</span>
                <span className="text-sm text-muted-foreground">Invitations</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity Tabs */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>User's recent activity in the system</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="orders" className="space-y-4">
            <TabsList>
              <TabsTrigger value="orders">Recent Orders</TabsTrigger>
              <TabsTrigger value="histories">Recent Histories</TabsTrigger>
              <TabsTrigger value="shares">Recent Shares</TabsTrigger>
              <TabsTrigger value="invitations">Invitations</TabsTrigger>
            </TabsList>

            <TabsContent value="orders">
              {recentActivity.orders.length === 0 ? (
                <p className="text-center py-4 text-muted-foreground">No recent orders found</p>
              ) : (
                <OrdersTable
                  initialOrders={recentActivity.orders}
                  fixedUserId={user.clerkId}
                  hideSearch={true}
                  hideFilters={true}
                  hidePagination={true}
                  hideUserColumn={true}
                />
              )}
            </TabsContent>

            <TabsContent value="histories">
              {recentActivity.histories.length === 0 ? (
                <p className="text-center py-4 text-muted-foreground">No recent histories found</p>
              ) : (
                <HistoriesTable
                  initialHistories={recentActivity.histories}
                  fixedUserId={user.clerkId}
                  hideSearch={true}
                  hideFilters={true}
                  hidePagination={true}
                  hideUserColumn={true}
                />
              )}
            </TabsContent>

            <TabsContent value="shares">
              {recentActivity.shares.length === 0 ? (
                <p className="text-center py-4 text-muted-foreground">No recent shares found</p>
              ) : (
                <SharesTable
                  initialShares={recentActivity.shares}
                  fixedUserId={user.clerkId}
                  hideSearch={true}
                  hideFilters={true}
                  hidePagination={true}
                  hideUserColumn={true}
                />
              )}
            </TabsContent>

            <TabsContent value="invitations">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Sent Invitations</h3>
                  {stats.sentInvitationsCount === 0 ? (
                    <p className="text-center py-4 text-muted-foreground">No invitations sent by this user</p>
                  ) : (
                    <div className="rounded-md border">
                      <Button variant="outline" size="sm" className="m-4" asChild>
                        <a href={`/admin/invitations?email=${encodeURIComponent(user.email)}`}>View All Sent Invitations</a>
                      </Button>
                    </div>
                  )}
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Received Invitations</h3>
                  {stats.receivedInvitationsCount === 0 ? (
                    <p className="text-center py-4 text-muted-foreground">No invitations received by this user</p>
                  ) : (
                    <InvitationUsagesAdminTable refereeId={user.clerkId} hideSearch={true} hideFilters={true} />
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Suspend User Confirmation Dialog */}
      <AlertDialog open={suspendDialogOpen} onOpenChange={setSuspendDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认冻结用户</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要冻结此用户吗？冻结后，用户将无法登录和使用系统功能。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={actionLoading}>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleSuspendUser();
              }}
              disabled={actionLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {actionLoading ? "处理中..." : "确认冻结"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Activate User Confirmation Dialog */}
      <AlertDialog open={activateDialogOpen} onOpenChange={setActivateDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认激活用户</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要激活此用户吗？激活后，用户将能够正常登录和使用系统功能。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={actionLoading}>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleActivateUser();
              }}
              disabled={actionLoading}
              className="bg-green-600 text-white hover:bg-green-700"
            >
              {actionLoading ? "处理中..." : "确认激活"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
