@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}


@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shadow-pulse {
  0% {
    box-shadow: 0 0 15px rgba(56, 114, 255, 0.5);
  }
  25% {
    box-shadow: 0 0 15px rgba(129, 56, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 15px rgba(236, 72, 153, 0.7);
  }
  75% {
    box-shadow: 0 0 15px rgba(255, 120, 56, 0.6);
  }
  100% {
    box-shadow: 0 0 15px rgba(56, 114, 255, 0.5);
  }
}

@keyframes magical-gradient {
  0% {
    background-position: 0% 50%;
    filter: hue-rotate(0deg) brightness(1);
  }
  25% {
    background-position: 50% 100%;
    filter: hue-rotate(45deg) brightness(1.1);
  }
  50% {
    background-position: 100% 50%;
    filter: hue-rotate(90deg) brightness(1);
  }
  75% {
    background-position: 50% 0%;
    filter: hue-rotate(135deg) brightness(1.1);
  }
  100% {
    background-position: 0% 50%;
    filter: hue-rotate(0deg) brightness(1);
  }
}

@keyframes magical-text {
  0% {
    background-position: 0% 50%;
    filter: hue-rotate(0deg);
    text-shadow: 0 0 1px rgba(255, 105, 180, 0.2);
  }
  20% {
    background-position: 100% 50%;
    filter: hue-rotate(72deg);
    text-shadow: 0 0 1px rgba(111, 66, 193, 0.25);
  }
  40% {
    background-position: 0% 50%;
    filter: hue-rotate(144deg);
    text-shadow: 0 0 1px rgba(59, 130, 246, 0.2);
  }
  60% {
    background-position: 100% 50%;
    filter: hue-rotate(216deg);
    text-shadow: 0 0 1px rgba(236, 72, 153, 0.25);
  }
  80% {
    background-position: 0% 50%;
    filter: hue-rotate(288deg);
    text-shadow: 0 0 1px rgba(167, 139, 250, 0.2);
  }
  100% {
    background-position: 0% 50%;
    filter: hue-rotate(360deg);
    text-shadow: 0 0 1px rgba(255, 105, 180, 0.2);
  }
}

.animate-magical-gradient {
  animation: magical-gradient 10s infinite alternate;
  background-size: 300% 300%;
}

.animate-magical-text {
  animation: magical-text 8s infinite;
  background-size: 300% 300%;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* 为SVG图标应用渐变效果 */
.magical-icon {
  fill: url(#magical-gradient);
  animation: magical-text 15s infinite;
  filter: drop-shadow(0 0 2px rgba(236, 72, 153, 0.4));
}

.animate-magical-button {
  animation:
    magical-gradient 10s infinite alternate,
    shadow-pulse 4s infinite;
  background-size: 300% 300%;
}

.animate-shadow-pulse {
  animation: shadow-pulse 4s infinite;
}

.bg-gradient-text {
  background: linear-gradient(90deg, #3872ff, #ec4899, #f97316);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.animate-gradient-normal {
  animation: gradient 8s ease infinite;
}

.animate-gradient-reverse {
  animation: gradient 8s ease infinite reverse;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 8s ease infinite;
}

.background {
  overflow: hidden;
  background: #f8f8f8;
  background-image: repeating-linear-gradient(0deg, transparent, transparent 11px, #f2f2f2 11px, #f2f2f2 12px),
    repeating-linear-gradient(90deg, transparent, transparent 11px, #f2f2f2 11px, #f2f2f2 12px);
}

.mask {
  mask-image: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 1) 40px,
    rgba(0, 0, 0, 1) calc(100% - 20px),
    rgba(0, 0, 0, 0) 100%
  );
}

/* Admin layout overrides */
.pt-0 {
  padding-top: 0 !important;
}



@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  }

  /* 当页面被锁定时，main 元素的 margin-top 为 4rem */
[data-scroll-locked] main {
    @apply sm:pt-16;
  }

  /* 设置主要内容区域的最小宽度 */
  main {
    @apply min-w-[320px];
  }
}

.body-admin {
  padding-top: 0 !important;
}

.body-admin main {
  padding-top: 1.5rem !important;
}

/* Markdown content styles */
.markdown-content {
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.markdown-content h1 {
  font-size: 1.5em;
}

.markdown-content h2 {
  font-size: 1.3em;
}

.markdown-content h3 {
  font-size: 1.1em;
}

.markdown-content p {
  margin-bottom: 0.75em;
}

.markdown-content ul,
.markdown-content ol {
  margin-left: 1.5em;
  margin-bottom: 0.75em;
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

.markdown-content a {
  color: #3b82f6;
  text-decoration: underline;
}

.markdown-content code {
  font-family: monospace;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.1em 0.3em;
  border-radius: 3px;
  font-size: 0.9em;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.markdown-content pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.75em;
  border-radius: 4px;
  overflow-x: auto;
  margin-bottom: 0.75em;
  /* Inherit white-space and word-wrap from parent */
  white-space: inherit;
  word-wrap: inherit;
  word-break: break-all;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  white-space: inherit;
  word-wrap: inherit;
  word-break: break-all;
}

.markdown-content blockquote {
  border-left: 3px solid #e5e7eb;
  padding-left: 1em;
  margin-left: 0;
  margin-bottom: 0.75em;
  color: #6b7280;
  white-space: pre-wrap;
  word-wrap: break-word;
}
