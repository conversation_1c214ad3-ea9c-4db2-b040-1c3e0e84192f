"use client";

import { use } from "react";
import { SettingsNav } from "@/components/settings/nav";
import { Navbar } from "@/components/global/navbar";
import { Footer } from "@/components/global/footer";
import { ShareDetails } from "@/components/settings/share/share-details";

export default function SharingDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = use(params);
  const { id } = resolvedParams;

  return (
    <>
      <main className="max-w-[75rem] w-full mx-auto px-2 px-4 md:px-6 lg:px-8">
        <div className="grid gap-10 pb-10">
          <div>
            <Navbar />
            <div className="container mx-auto lg:py-10">
              <div className="flex flex-col md:flex-row lg:gap-8">
                <SettingsNav />
                <div className="flex-1">
                  <ShareDetails id={id} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
