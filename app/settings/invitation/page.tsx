import { Navbar } from "@/components/global/navbar";
import { Footer } from "@/components/global/footer";
import { SettingsNav } from "@/components/settings/nav";
import { InvitationManagement } from "@/components/settings/invitation/invitation-management";

export default async function InvitationPage() {
  return (
    <>
      <main className="max-w-[75rem] w-full mx-auto px-2 px-4 md:px-6 lg:px-8">
        <div className="grid gap-10 pb-10">
          <div>
            <Navbar />
            <div className="container mx-auto lg:py-10">
              <div className="flex flex-col md:flex-row lg:gap-8">
                <SettingsNav />
                <div className="flex-1">
                  <div className="py-4 px-2 md:px-4 relative">
                    <div className="p-4 sm:p-8 rounded-xl bg-white shadow-[0_5px_15px_rgba(0,0,0,0.08),0_15px_35px_-5px_rgba(25,28,33,0.2)] ring-1 ring-gray-950/5 w-full">
                      <h1 className="text-2xl font-bold mb-6">邀请管理</h1>
                      <InvitationManagement />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
