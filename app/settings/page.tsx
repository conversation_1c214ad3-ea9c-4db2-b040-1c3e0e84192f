import { Navbar } from "@/components/global/navbar";
import { Footer } from "@/components/global/footer";
import { UserInfo } from "@/components/settings/profile/profile";
import { SettingsNav } from "@/components/settings/nav";

export default async function SettingsPage() {
  return (
    <>
      <main className="max-w-[75rem] w-full mx-auto px-2 px-4 md:px-6 lg:px-8">
        <div className="grid gap-4 pb-4">
          <div>
            <Navbar />
            <div className="container mx-auto py-4 sm:py-6 lg:py-10">
              <div className="flex flex-col md:flex-row gap-4 sm:gap-6 lg:gap-8">
                <SettingsNav />
                <div className="flex-1">
                  <UserInfo />
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
