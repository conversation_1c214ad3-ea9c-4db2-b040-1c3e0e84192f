import { use } from "react";
import { Navbar } from "@/components/global/navbar";
import { Footer } from "@/components/global/footer";
import { HistoryDetails } from "@/components/settings/history/history-details";
import { SettingsNav } from "@/components/settings/nav";

export default function HistoryDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = use(params);
  const { id } = resolvedParams;

  return (
    <>
      <main className="max-w-[75rem] w-full mx-auto">
        <div className="grid gap-10 pb-10">
          <div>
            <Navbar />
            <div className="container mx-auto lg:py-10">
              <div className="flex flex-col md:flex-row lg:gap-8">
                <SettingsNav />
                <div className="flex-1">
                  <HistoryDetails id={id} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
