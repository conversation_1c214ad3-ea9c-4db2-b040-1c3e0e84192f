import { Navbar } from "@/components/global/navbar";
import { Footer } from "@/components/global/footer";

export default async function PrivacyPage() {
  return (
    <>
      <main className="max-w-[75rem] w-full mx-auto">
        <div className="grid gap-10 pb-10">
          <div>
            <Navbar />
            <div className="p-16 rounded-lg border border-[#EDEDED] bg-[#F1F1F2] background">
              <div className="p-8 rounded-xl bg-white shadow-[0_5px_15px_rgba(0,0,0,0.08),0_15px_35px_-5px_rgba(25,28,33,0.2)] ring-1 ring-gray-950/5">
                <h1 className="text-3xl font-bold mb-8">隐私协议</h1>
                <div className="space-y-6">
                  <section>
                    <h2 className="text-2xl font-semibold mb-4">信息收集与使用</h2>
                    <div className="px-2.5 py-4 bg-[#FAFAFB] rounded-lg space-y-4">
                      <p className="mb-4">我们非常重视您的隐私保护。本隐私协议旨在向您说明我们如何收集、使用和保护您的个人信息。</p>
                      <p className="mb-4">我们承诺：</p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>仅收集必要的用户信息用于提供服务</li>
                        <li>不会将用户内容用于其他商业用途</li>
                        <li>严格保护用户数据安全</li>
                        <li>未经用户同意，不会向第三方披露用户信息</li>
                      </ul>
                    </div>
                  </section>

                  <section>
                    <h2 className="text-2xl font-semibold mb-4">数据安全</h2>
                    <div className="px-2.5 py-4 bg-[#FAFAFB] rounded-lg space-y-4">
                      <p className="mb-4">我们采用行业标准的安全措施来保护您的个人信息：</p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>使用加密技术保护数据传输</li>
                        <li>定期进行安全审计和更新</li>
                        <li>限制员工访问用户数据的权限</li>
                      </ul>
                    </div>
                  </section>

                  <section>
                    <h2 className="text-2xl font-semibold mb-4">信息共享</h2>
                    <div className="px-2.5 py-4 bg-[#FAFAFB] rounded-lg space-y-4">
                      <p className="mb-4">我们不会在未经您同意的情况下与第三方共享您的个人信息，除非：</p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>法律法规要求</li>
                        <li>保护平台和其他用户的安全</li>
                        <li>配合执法机构调查</li>
                      </ul>
                    </div>
                  </section>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
