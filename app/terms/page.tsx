import { Navbar } from "@/components/global/navbar";
import { Footer } from "@/components/global/footer";
import Link from "next/link";

export default async function TermsPage() {
  return (
    <>
      <main className="max-w-[75rem] w-full mx-auto">
        <div className="grid gap-10 pb-10">
          <div>
            <Navbar />
            <div className="p-16 rounded-lg border border-[#EDEDED] bg-[#F1F1F2] background">
              <div className="p-8 rounded-xl bg-white shadow-[0_5px_15px_rgba(0,0,0,0.08),0_15px_35px_-5px_rgba(25,28,33,0.2)] ring-1 ring-gray-950/5">
                <h1 className="text-3xl font-bold mb-8">协议与条款</h1>
                <div className="grid gap-6">
                  <Link
                    href="/terms/privacy"
                    className="p-6 bg-[#FAFAFB] rounded-lg border border-[#EEEEF0] hover:bg-gray-50 transition-colors"
                  >
                    <h2 className="text-2xl font-semibold mb-2">隐私协议</h2>
                    <p className="text-gray-600">了解我们如何保护您的隐私和数据安全</p>
                  </Link>

                  <Link
                    href="/terms/user-agreement"
                    className="p-6 bg-[#FAFAFB] rounded-lg border border-[#EEEEF0] hover:bg-gray-50 transition-colors"
                  >
                    <h2 className="text-2xl font-semibold mb-2">用户协议</h2>
                    <p className="text-gray-600">了解平台使用规则和用户责任</p>
                  </Link>

                  <Link
                    target="_blank"
                    href="/terms/invitation"
                    className="p-6 bg-[#FAFAFB] rounded-lg border border-[#EEEEF0] hover:bg-gray-50 transition-colors"
                  >
                    <h2 className="text-2xl font-semibold mb-2">邀请奖励计划条款</h2>
                    <p className="text-gray-600">了解邀请奖励计划的目的、规则和条款</p>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
