"use client";

import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useEffect, useState, useCallback } from "react";

import { CheckCircle2, Loader2, XCircle, AlertCircle, Calendar, CreditCard, Copy } from "lucide-react";

import { Navbar } from "@/components/global/navbar";
import { Footer } from "@/components/global/footer";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useOrderPolling } from "@/lib/hooks/use-order-polling";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import dynamic from "next/dynamic";
import { useCopy } from "@/lib/hooks/use-copy";

/**
 * 订单详情接口
 *
 * 定义从 API 返回的订单数据结构
 */
interface OrderDetails {
  /** 订单ID */
  id: string;
  /** 用户ID */
  userId: string;
  /** 购买者ID（可能是用户ID或"system"） */
  buyerId: string;
  /** 订单类型（"credit"或"debit"） */
  type: string;
  /** 积分数量 */
  amount: number;
  /** 订单描述 */
  description: string;
  /** 订单状态 */
  status: "PENDING" | "SUCCESS" | "FAILED" | "REFUND";
  /** 支付方式（可选） */
  paymentMethod?: string;
  /** 商户订单号（可选） */
  outTradeNo?: string;
  /** 支付平台订单号（可选） */
  tradeNo?: string;
  /** 支付二维码URL（可选） */
  qrCodeUrl?: string;
  /** 支付完成时间（可选） */
  paidAt?: string;
  /** 退款时间（可选） */
  refundedAt?: string;
  /** 额外信息 */
  extra: {
    /** 积分数量（可选） */
    points?: number;
    /** 价格（单位：元）（可选） */
    price?: number;
    /** 套餐信息（可选） */
    package?: {
      /** 套餐名称 */
      name: string;
      /** 套餐描述 */
      description: string;
    };
    /** 其他可能的字段 */
    [key: string]: any;
  };
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}

// 定义组件
function PaymentSuccessPageContent() {
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());
  const orderId = params.get("orderId") || "Unknown";

  const [order, setOrder] = useState<OrderDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { copyToClipboard } = useCopy();

  /**
   * 获取订单详情
   *
   * 使用 /api/orders/[id] 接口获取订单信息
   * 设置 loading 状态来显示加载中的 UI
   * 处理可能的错误情况
   */
  const fetchOrderDetails = useCallback(async () => {
    if (!orderId || orderId === "Unknown") return;

    console.log("开始获取订单详情:", orderId);

    try {
      setLoading(true);
      const response = await fetch(`/api/orders/${orderId}`);
      console.log("订单详情请求状态:", response.status);

      if (!response.ok) {
        throw new Error("获取订单详情失败");
      }

      const data = await response.json();
      console.log("获取到订单详情:", data);

      // 更新订单数据
      setOrder(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取订单详情失败");
      console.error("Error fetching order details:", err);
    } finally {
      setLoading(false);
    }
  }, [orderId]);

  // 回调函数引用保持稳定，避免不必要的轮询重启
  const onSuccessCallback = useCallback(async () => {
    console.log("订单支付成功，重新获取订单详情");
    await fetchOrderDetails();
  }, [fetchOrderDetails]);

  const onFailedCallback = useCallback(() => {
    console.log("订单支付失败，重新获取订单详情");
    fetchOrderDetails();
  }, [fetchOrderDetails]);

  // 只有当订单状态为 PENDING 时才进行轮询
  const shouldPoll = !order || order.status === "PENDING";

  // 使用订单轮询钩子检查订单状态
  const { status: pollingStatus } = useOrderPolling({
    orderId: shouldPoll ? orderId : null, // 只有需要轮询时才传递 orderId
    onSuccess: onSuccessCallback,
    onFailed: onFailedCallback,
  });

  // 当轮询状态变化时，更新订单状态
  useEffect(() => {
    if (pollingStatus && order) {
      console.log("轮询状态变化:", pollingStatus);
      setOrder(prev => {
        if (prev && prev.status !== pollingStatus) {
          return { ...prev, status: pollingStatus };
        }
        return prev;
      });
    }
  }, [pollingStatus, order]);



  /**
   * 初始加载时获取订单详情
   *
   * 当组件挂载时，自动获取订单详情
   */
  useEffect(() => {
    console.log("组件挂载，orderId:", orderId);

    // 立即获取一次订单详情
    fetchOrderDetails();

  }, [fetchOrderDetails]);

  /**
   * 渲染订单状态徽章
   *
   * 根据订单状态显示不同颜色和文本的徽章
   * SUCCESS: 绿色 - 支付成功
   * FAILED: 红色 - 支付失败
   * REFUND: 黄色 - 已退款
   * PENDING: 蓝色 - 处理中
   */
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "SUCCESS":
        return (
          <Badge className="bg-green-500 hover:bg-green-600">
            支付成功
          </Badge>
        );
      case "FAILED":
        return (
          <Badge variant="destructive">
            支付失败
          </Badge>
        );
      case "REFUND":
        return (
          <Badge variant="outline" className="text-yellow-500 border-yellow-500">
            已退款
          </Badge>
        );
      case "PENDING":
      default:
        return (
          <Badge variant="outline" className="text-blue-500 border-blue-500">
            处理中
          </Badge>
        );
    }
  };

  /**
   * 渲染状态图标
   *
   * 根据不同的状态显示不同的图标：
   * - 加载中：显示加载动画
   * - 错误：显示警告图标
   * - 订单不存在：显示警告图标
   * - 成功订单：显示成功图标
   * - 失败订单：显示失败图标
   * - 退款订单：显示警告图标
   * - 处理中订单：显示加载动画
   */
  const renderStatusIcon = () => {
    if (loading) {
      return <Loader2 className="h-16 w-16 text-blue-500 animate-spin" />;
    }

    if (error) {
      return <AlertCircle className="h-16 w-16 text-yellow-500" />;
    }

    if (!order) {
      return <AlertCircle className="h-16 w-16 text-yellow-500" />;
    }

    switch (order.status) {
      case "SUCCESS":
        return <CheckCircle2 className="h-16 w-16 text-green-500" />;
      case "FAILED":
        return <XCircle className="h-16 w-16 text-red-500" />;
      case "REFUND":
        return <AlertCircle className="h-16 w-16 text-yellow-500" />;
      case "PENDING":
      default:
        return <Loader2 className="h-16 w-16 text-blue-500 animate-spin" />;
    }
  };

  /**
   * 渲染标题和描述
   *
   * 根据不同的状态显示不同的标题和描述：
   * - 加载中：显示加载中的提示
   * - 错误：显示错误信息
   * - 订单不存在：显示订单不存在的提示
   * - 成功订单：显示支付成功的提示
   * - 失败订单：显示支付失败的提示
   * - 退款订单：显示订单已退款的提示
   * - 处理中订单：显示处理中的提示
   */
  const renderTitleAndDescription = () => {
    if (loading) {
      return (
        <>
          <CardTitle className="text-xl text-center">处理中...</CardTitle>
          <CardDescription className="text-center">
            正在获取订单信息
          </CardDescription>
        </>
      );
    }

    if (error) {
      return (
        <>
          <CardTitle className="text-xl text-center">获取订单失败</CardTitle>
          <CardDescription className="text-center">
            {error}
          </CardDescription>
        </>
      );
    }

    if (!order) {
      return (
        <>
          <CardTitle className="text-xl text-center">订单不存在</CardTitle>
          <CardDescription className="text-center">
            未找到该订单信息
          </CardDescription>
        </>
      );
    }

    switch (order.status) {
      case "SUCCESS":
        return (
          <>
            <CardTitle className="text-xl text-center">支付成功</CardTitle>
            <CardDescription className="text-center">
              您的支付已成功处理
            </CardDescription>
          </>
        );
      case "FAILED":
        return (
          <>
            <CardTitle className="text-xl text-center">支付失败</CardTitle>
            <CardDescription className="text-center">
              您的支付处理失败
            </CardDescription>
          </>
        );
      case "REFUND":
        return (
          <>
            <CardTitle className="text-xl text-center">订单已退款</CardTitle>
            <CardDescription className="text-center">
              您的订单已退款
            </CardDescription>
          </>
        );
      case "PENDING":
      default:
        return (
          <>
            <CardTitle className="text-xl text-center">处理中</CardTitle>
            <CardDescription className="text-center">
              您的支付正在处理中，请稍候
            </CardDescription>
          </>
        );
    }
  };

  return (
    <>
      <main className="max-w-[75rem] w-full mx-auto">
        <div className="grid gap-10 pb-10">
          <div>
            <Navbar />
            <div className="container py-10">
              <Card className="max-w-md mx-auto">
                <CardHeader>
                  <div className="flex justify-center mb-4">
                    {renderStatusIcon()}
                  </div>
                  {renderTitleAndDescription()}
                </CardHeader>
                <CardContent className="space-y-4">
                  <Alert
                    className={cn(
                      "border",
                      order?.status === "SUCCESS"
                        ? "bg-green-50 border-green-200"
                        : order?.status === "FAILED"
                        ? "bg-red-50 border-red-200"
                        : order?.status === "REFUND"
                        ? "bg-yellow-50 border-yellow-200"
                        : "bg-blue-50 border-blue-200"
                    )}
                  >
                    <div className="flex items-center justify-between">
                      <AlertTitle>订单编号</AlertTitle>
                      <AlertDescription>
                        <div
                          className="flex gap-1 cursor-pointer hover:text-primary transition-colors"
                          onClick={() => copyToClipboard(orderId, "订单编号已复制到剪贴板", "复制订单编号失败，请重试")}
                          title="点击复制订单编号"
                        >
                          <span className="font-semibold">{orderId}</span>
                          <Copy className="h-4 w-4 mt-0.5" />
                        </div>
                      </AlertDescription>
                    </div>
                  </Alert>

                  {loading ? (
                    <div className="space-y-4">
                      <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground mt-4">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span>正在获取订单详情...</span>
                      </div>
                      <div className="space-y-2">
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-10 w-full" />
                      </div>
                    </div>
                  ) : order ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                          <CreditCard className="h-8 w-8 mb-2 text-primary" />
                          <span className="text-2xl font-bold">
                            {order.amount}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            积分
                          </span>
                        </div>
                        <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                          <Calendar className="h-8 w-8 mb-2 text-primary" />
                          <span className="text-2xl font-bold">
                            {order.createdAt
                              ? format(new Date(order.createdAt), "MM-dd")
                              : "未知"}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            创建日期
                          </span>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between py-2 border-b">
                          <span className="font-medium">订单状态:</span>
                          <span>{renderStatusBadge(order.status)}</span>
                        </div>
                        <div className="flex justify-between py-2 border-b">
                          <span className="font-medium">订单描述:</span>
                          <span>{order.description}</span>
                        </div>
                        {order.paymentMethod && (
                          <div className="flex justify-between py-2 border-b">
                            <span className="font-medium">支付方式:</span>
                            <span>
                              {order.paymentMethod === "alipay"
                                ? "支付宝"
                                : order.paymentMethod === "wxpay"
                                ? "微信支付"
                                : order.paymentMethod === "stripe"
                                ? "Stripe"
                                : order.paymentMethod}
                            </span>
                          </div>
                        )}
                        {order.extra?.price && (
                          <div className="flex justify-between py-2 border-b">
                            <span className="font-medium">支付金额:</span>
                            <span>¥{order.extra.price.toFixed(2)}</span>
                          </div>
                        )}
                        {order.paidAt && (
                          <div className="flex justify-between py-2 border-b">
                            <span className="font-medium">支付时间:</span>
                            <span>
                              {format(
                                new Date(order.paidAt),
                                "yyyy-MM-dd HH:mm:ss"
                              )}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : null}

                  {order?.status === "PENDING" && (
                    <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground mt-4">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>订单处理中，系统正在自动检查支付状态...</span>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-center gap-4">
                  <Button asChild>
                    <Link href="/draw">开始创作</Link>
                  </Button>
                  {order?.status === "SUCCESS" && (
                    <Button asChild variant="outline">
                      <Link href={`/settings/orders/${orderId}`}>查看订单详情</Link>
                    </Button>
                  )}
                </CardFooter>
              </Card>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}

// 使用 dynamic 导入确保组件只在客户端渲染，避免水合不匹配
const PaymentSuccessPage = dynamic(() => Promise.resolve(PaymentSuccessPageContent), {
  ssr: false,
});

export default PaymentSuccessPage;
