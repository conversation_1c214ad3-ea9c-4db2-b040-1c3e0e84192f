import { Suspense } from "react";
import { Navbar } from "@/components/global/navbar";
import { Footer } from "@/components/global/footer";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { PaymentErrorCard } from "@/components/payment/payment-error-card";

// Main page component (Server Component)
export default function PaymentErrorPage() {
  return (
    <>
      <main className="max-w-[75rem] w-full mx-auto">
        <div className="grid gap-10 pb-10">
          <div>
            <Navbar />
            <div className="container py-10">
              <Suspense fallback={<PaymentErrorCardSkeleton />}>
                <PaymentErrorCard />
              </Suspense>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}

// Skeleton component for loading state
function PaymentErrorCardSkeleton() {
  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <div className="flex justify-center mb-4">
          <Skeleton className="h-16 w-16 rounded-full" />
        </div>
        <CardTitle className="text-xl text-center">
          <Skeleton className="h-6 w-32 mx-auto" />
        </CardTitle>
        <CardDescription className="text-center">
          <Skeleton className="h-4 w-64 mx-auto mt-2" />
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Skeleton className="h-24 w-full" />
      </CardContent>
      <CardFooter className="flex justify-center gap-4">
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-24" />
      </CardFooter>
    </Card>
  );
}
