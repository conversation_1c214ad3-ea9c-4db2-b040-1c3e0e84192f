import { Metadata } from "next";
import { Navbar } from "@/components/global/navbar";
import { Footer } from "@/components/global/footer";
import { PricingContent } from "@/components/pricing/pricing-content";
import { getPageMetadata } from "@/constants";

export const metadata: Metadata = getPageMetadata("价格", "查看我们的价格套餐和积分购买选项");

export default async function PricingPage() {
  return (
    <>
      <main className="max-w-[75rem] w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid gap-4 pb-4">
          <div>
            <Navbar />
            <PricingContent />
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
