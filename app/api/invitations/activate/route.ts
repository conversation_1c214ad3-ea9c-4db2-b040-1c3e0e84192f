import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { invitations } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { nanoid } from "nanoid";
import { createLogger } from "@/lib/draw/logger";
import { z } from "zod";
import {
  DEFAULT_INVITE_TYPE,
  DEFAULT_REF_RATIO,
  DEFAULT_MAX_USES,
  INVITE_CODE_LENGTH,
  INVITE_CODE_REGEX,
  INVITE_CODE_ERROR_MESSAGE
} from "@/constants/invitation";

const logger = createLogger('api-invitations-activate');

// 激活邀请码请求验证
const activateInvitationSchema = z.object({
  invite_code: z.string()
    .min(3, "邀请码至少需要3个字符")
    .max(20, "邀请码最多20个字符")
    .refine((value) => !value || INVITE_CODE_REGEX.test(value), {
      message: INVITE_CODE_ERROR_MESSAGE,
    })
    .optional(),
});

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // 检查用户是否已有邀请码
    const existingInvitation = await db.query.invitations.findFirst({
      where: eq(invitations.referrerId, userId),
    });

    if (existingInvitation) {
      return NextResponse.json(
        { error: "您已经有一个邀请码，无法再次激活" },
        { status: 400 }
      );
    }

    // 解析请求数据
    let inviteCode: string;
    try {
      const body = await req.json();
      const validationResult = activateInvitationSchema.safeParse(body);

      if (validationResult.success && validationResult.data.invite_code) {
        inviteCode = validationResult.data.invite_code;
      } else {
        // 自动生成邀请码（只包含小写字母和数字）
        const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < INVITE_CODE_LENGTH; i++) {
          result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        inviteCode = result;
      }
    } catch (error) {
      // 如果请求体为空或解析失败，自动生成邀请码（只包含小写字母和数字）
      const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      for (let i = 0; i < INVITE_CODE_LENGTH; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
      }
      inviteCode = result;
    }

    // 检查邀请码是否已存在
    const codeExists = await db.query.invitations.findFirst({
      where: eq(invitations.inviteCode, inviteCode),
    });

    if (codeExists) {
      return NextResponse.json(
        { error: "邀请码已存在，请尝试其他邀请码或留空自动生成" },
        { status: 400 }
      );
    }

    // 创建新邀请码
    const id = nanoid();
    const [newInvitation] = await db
      .insert(invitations)
      .values({
        id: id,
        inviteCode,
        referrerId: userId,
        inviteType: DEFAULT_INVITE_TYPE,
        refRatio: DEFAULT_REF_RATIO.toString(),
        maxUses: DEFAULT_MAX_USES,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return NextResponse.json(newInvitation);
  } catch (error: any) {
    logger.error("激活邀请码失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
