import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { createInvitation, getUserInvitations, getInvitationStats } from "@/lib/invitation";
import { createLogger } from "@/lib/draw/logger";
import { z } from "zod";

const logger = createLogger('api-invitations');

// 创建邀请码请求验证
const createInvitationSchema = z.object({
  invite_code: z.string().min(3).max(20).optional(),
  invite_type: z.enum(["points", "cash", "both"]),
  ref_ratio: z.number().min(0.01).max(1),
  channel: z.string().optional(),
  max_uses: z.number().int().min(0).optional(),
  expires_at: z.string().optional(),
});

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();

    // 验证请求数据
    const validationResult = createInvitationSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { invite_code, invite_type, ref_ratio, channel, max_uses, expires_at } = validationResult.data;

    // 创建邀请码
    const invitation = await createInvitation({
      referrerId: userId,
      inviteCode: invite_code,
      inviteType: invite_type,
      refRatio: ref_ratio,
      channel,
      maxUses: max_uses || 0,
      expiresAt: expires_at ? new Date(expires_at) : undefined,
    });

    return NextResponse.json(invitation);
  } catch (error: any) {
    logger.error("创建邀请码失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: error.message === "邀请码已存在" ? 400 : 500 }
    );
  }
}

export async function GET(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");
    const includeStats = searchParams.get("includeStats") === "true";
    const includePendingRewards = searchParams.get("includePendingRewards") === "true";

    // 获取邀请码列表
    const invitationsResult = await getUserInvitations(userId, limit, offset);

    // 如果需要统计信息，一并返回
    if (includeStats) {
      const stats = await getInvitationStats(userId, includePendingRewards);
      return NextResponse.json({
        ...invitationsResult,
        stats,
      });
    }

    return NextResponse.json(invitationsResult);
  } catch (error: any) {
    logger.error("获取邀请码列表失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
