import { NextResponse } from "next/server";
import { validateInviteCode } from "@/lib/invitation";
import { createLogger } from "@/lib/draw/logger";

const logger = createLogger('api-invitation-validate');

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const code = searchParams.get("code");
    
    if (!code) {
      return NextResponse.json(
        { valid: false, message: "邀请码不能为空" },
        { status: 400 }
      );
    }
    
    // 验证邀请码
    const result = await validateInviteCode(code);
    
    return NextResponse.json(result);
  } catch (error: any) {
    logger.error("验证邀请码失败", error);
    return NextResponse.json(
      { valid: false, error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
