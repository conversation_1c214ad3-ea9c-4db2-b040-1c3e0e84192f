import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { invitations } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { createLogger } from "@/lib/draw/logger";
import { z } from "zod";
import { INVITE_CODE_REGEX, INVITE_CODE_ERROR_MESSAGE } from "@/constants/invitation";

const logger = createLogger('api-invitations-update');

// 更新邀请码请求验证
const updateInvitationSchema = z.object({
  invite_code: z.string()
    .min(3, "邀请码至少需要3个字符")
    .max(20, "邀请码最多20个字符")
    .refine((value) => INVITE_CODE_REGEX.test(value), {
      message: INVITE_CODE_ERROR_MESSAGE,
    }),
});

// 管理员更新邀请码请求验证
const adminUpdateInvitationSchema = z.object({
  invite_code: z.string()
    .min(3, "邀请码至少需要3个字符")
    .max(20, "邀请码最多20个字符")
    .refine((value) => !value || INVITE_CODE_REGEX.test(value), {
      message: INVITE_CODE_ERROR_MESSAGE,
    })
    .optional(),
  invite_type: z.enum(["points", "cash", "both"]).optional(),
  ref_ratio: z.number().min(0.01).max(1).optional(),
  channel: z.string().optional().nullable(),
  max_uses: z.number().int().min(0).optional(),
  expires_at: z.string().optional().nullable(),
});

export async function PATCH(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { id } = await params;
    if (!id) {
      return NextResponse.json(
        { error: "Missing invitation ID" },
        { status: 400 }
      );
    }

    // 获取邀请码信息
    const invitation = await db.query.invitations.findFirst({
      where: eq(invitations.id, id),
    });

    if (!invitation) {
      return NextResponse.json(
        { error: "邀请码不存在" },
        { status: 404 }
      );
    }

    // 检查权限
    const isAdmin = true; // 这里应该调用实际的管理员检查逻辑
    const isOwner = invitation.referrerId === userId;

    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        { error: "无权修改此邀请码" },
        { status: 403 }
      );
    }

    const body = await req.json();

    // 根据用户角色使用不同的验证模式
    let updateData: any = {};

    if (isAdmin) {
      // 管理员可以更新所有字段
      const validationResult = adminUpdateInvitationSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          { error: "Invalid request data", details: validationResult.error.format() },
          { status: 400 }
        );
      }

      const {
        invite_code,
        invite_type,
        ref_ratio,
        channel,
        max_uses,
        expires_at
      } = validationResult.data;

      if (invite_code !== undefined) updateData.inviteCode = invite_code;
      if (invite_type !== undefined) updateData.inviteType = invite_type;
      if (ref_ratio !== undefined) updateData.refRatio = String(ref_ratio);
      if (channel !== undefined) updateData.channel = channel;
      if (max_uses !== undefined) updateData.maxUses = max_uses;
      if (expires_at !== undefined) {
        updateData.expiresAt = expires_at ? new Date(expires_at) : null;
      }
    } else {
      // 普通用户只能更新邀请码
      const validationResult = updateInvitationSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          { error: "Invalid request data", details: validationResult.error.format() },
          { status: 400 }
        );
      }

      const { invite_code } = validationResult.data;
      updateData.inviteCode = invite_code;
    }

    // 检查邀请码是否已存在（如果要更新邀请码）
    if (updateData.inviteCode && updateData.inviteCode !== invitation.inviteCode) {
      const codeExists = await db.query.invitations.findFirst({
        where: eq(invitations.inviteCode, updateData.inviteCode),
      });

      if (codeExists) {
        return NextResponse.json(
          { error: "邀请码已存在，请尝试其他邀请码" },
          { status: 400 }
        );
      }
    }

    // 更新邀请码
    updateData.updatedAt = new Date();

    const [updatedInvitation] = await db
      .update(invitations)
      .set(updateData)
      .where(eq(invitations.id, id))
      .returning();

    return NextResponse.json(updatedInvitation);
  } catch (error: any) {
    logger.error("更新邀请码失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
