import Stripe from 'stripe';

import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

import { stripeEnabled } from '@/constants/payment';

// Initialize Stripe client
const stripe = stripeEnabled ? new Stripe(process.env.STRIPE_SECRET_KEY!) : null;

/**
 * API endpoint to retrieve Stripe session details
 * This endpoint is protected and requires authentication
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    if (!stripe) {
      return new NextResponse('Stripe is not enabled', { status: 500 });
    }

    // Check if user is authenticated
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const { id } = await params;
    if (!id) {
      return new NextResponse('Session ID is required', { status: 400 });
    }

    // Determine if this is a session ID or payment intent ID
    let sessionData;
    let paymentIntentData;

    try {
      if (id.startsWith('cs_')) {
        // This is a checkout session ID
        sessionData = await stripe.checkout.sessions.retrieve(id, {
          expand: ['payment_intent', 'line_items', 'customer']
        });
      } else if (id.startsWith('pi_')) {
        // This is a payment intent ID
        paymentIntentData = await stripe.paymentIntents.retrieve(id, {
          expand: ['customer', 'latest_charge', 'payment_method']
        });

        // Try to find the associated session
        const sessions = await stripe.checkout.sessions.list({
          payment_intent: id,
          limit: 1
        });

        if (sessions.data.length > 0) {
          sessionData = sessions.data[0];
        }
      } else {
        // Unknown ID format
        return new NextResponse('Invalid Stripe ID format', { status: 400 });
      }
    } catch (error) {
      console.error('Error retrieving Stripe data:', error);
      return new NextResponse('Failed to retrieve Stripe data', { status: 500 });
    }

    // Format the response data
    const responseData = {
      session: sessionData,
      paymentIntent: paymentIntentData || (sessionData?.payment_intent || null),
      dashboardUrl: id.startsWith('cs_')
        ? `https://dashboard.stripe.com/${id.startsWith('cs_test_') ? 'test/' : ''}checkout/sessions/${id}`
        : `https://dashboard.stripe.com/${id.startsWith('pi_test_') ? 'test/' : ''}payments/${id}`
    };

    return NextResponse.json(responseData);
  } catch (error) {
    console.error('Error in Stripe session API:', error);
    return new NextResponse('Internal server error', { status: 500 });
  }
}
