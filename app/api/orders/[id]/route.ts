import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { orders } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";

// This endpoint serves two purposes:
// 1. For payment callback verification (original implementation)
// 2. For order details in the user interface (new implementation)

export async function GET(
  request: NextRequest,
  {
    params,
  }: {
    params: Promise<{ id: string }>;
  }
) {
  console.log("=== Request Received ===");
  console.log("Request URL:", request.url);

  const { id } = await params;
  console.log("Order ID:", id);

  try {
    if (!id) {
      return new Response(
        JSON.stringify({ code: -1, msg: "Order ID is required" }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    console.log("Querying order with ID:", id);
    const order = await db.query.orders.findFirst({
      where: eq(orders.id, id),
    });

    if (!order) {
      return new Response(
        JSON.stringify({ code: -1, msg: "Order not found" }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    console.log("=== Order Found ===");
    console.log("Order status:", order.status);
    console.log("Order amount:", order.amount);
    console.log("Order created at:", order.createdAt);

    // Check if this is a payment verification request or a UI request
    // Payment verification requests typically come from payment gateways and don't have auth
    // UI requests come from the frontend with auth
    const { userId } = await auth();

    // If this is a UI request (has userId), verify ownership and return full order details
    if (userId) {
      // Ensure the user can only access their own orders
      if (order.userId !== userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      // Return the full order for UI display
      return NextResponse.json(order);
    }

    // Otherwise, this is a payment verification request, return the original response format
    const extra = order.extra as Record<string, any>;
    const responseData = {
      code: 0,
      msg: "Success",
      data: {
        status: order.status,
        trade_status: order.status,
        trade_no: order.tradeNo,
        money: extra.price,
      },
    };

    return new Response(JSON.stringify(responseData), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("=== Error Details ===");
    console.error(
      "Error type:",
      error instanceof Error ? error.constructor.name : "Unknown"
    );
    console.error(
      "Error message:",
      error instanceof Error ? error.message : "Unknown error"
    );
    console.error(
      "Error stack:",
      error instanceof Error ? error.stack : "No stack trace"
    );

    return new Response(
      JSON.stringify({
        code: -1,
        msg:
          error instanceof Error
            ? error.message
            : "Failed to query order status",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  } finally {
    console.log("=== Order Query End ===");
  }
}
