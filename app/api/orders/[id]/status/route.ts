import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { orders } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";

export async function GET(
  request: NextRequest,
  {
    params,
  }: {
    params: Promise<{ id: string }>;
  }
) {
  console.log("=== Status Request Received ===");
  console.log("Request URL:", request.url);

  const { id } = await params;
  console.log("Order ID:", id);

  try {
    if (!id) {
      return new Response(
        JSON.stringify({ code: -1, msg: "Order ID is required" }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    console.log("Querying order status with ID:", id);
    const order = await db.query.orders.findFirst({
      where: eq(orders.id, id),
    });

    if (!order) {
      return new Response(
        JSON.stringify({ code: -1, msg: "Order not found" }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    console.log("=== Order Status Found ===");
    console.log("Order status:", order.status);
    console.log("Order amount:", order.amount);
    console.log("Order created at:", order.createdAt);

    // Get the extra data for price information
    const extra = order.extra as Record<string, any>;

    // Return the response in the expected format for order status polling
    const responseData = {
      code: 0,
      msg: "Success",
      data: {
        status: order.status,
        trade_status: order.status,
        trade_no: order.tradeNo,
        money: extra.price,
        // Include these fields for the useOrderPolling hook
        orderId: order.id,
        amount: order.amount,
        createdAt: order.createdAt.toISOString(),
      },
    };

    return new Response(JSON.stringify(responseData), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("=== Error Details ===");
    console.error(
      "Error type:",
      error instanceof Error ? error.constructor.name : "Unknown"
    );
    console.error(
      "Error message:",
      error instanceof Error ? error.message : "Unknown error"
    );
    console.error(
      "Error stack:",
      error instanceof Error ? error.stack : "No stack trace"
    );

    return new Response(
      JSON.stringify({
        code: -1,
        msg:
          error instanceof Error
            ? error.message
            : "Failed to query order status",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  } finally {
    console.log("=== Order Status Query End ===");
  }
}
