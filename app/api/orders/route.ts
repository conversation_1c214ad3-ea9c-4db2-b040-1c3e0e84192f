import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { orders } from "@/lib/db/schema";
import { and, eq, gte, lte } from "drizzle-orm";

export async function GET(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const paymentMethod = searchParams.get("paymentMethod");
    const buyerType = searchParams.get("buyerType");
    const status = searchParams.get("status");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    const conditions = [eq(orders.userId, userId)];

    if (buyerType === "system") {
      conditions.push(eq(orders.buyerId, "system"));
    } else if (buyerType === "self") {
      conditions.push(eq(orders.buyerId, userId));
    }

    if (status && status !== "all") {
      conditions.push(eq(orders.status, status as any));
    }

    if (paymentMethod && paymentMethod !== "all") {
      conditions.push(eq(orders.paymentMethod, paymentMethod as any));
    }

    if (startDate) {
      conditions.push(gte(orders.createdAt, new Date(startDate)));
    }

    if (endDate) {
      conditions.push(lte(orders.createdAt, new Date(endDate)));
    }

    const results = await db.query.orders.findMany({
      where: and(...conditions),
      orderBy: (orders, { desc }) => [desc(orders.createdAt)],
    });

    return NextResponse.json(results);
  } catch (error) {
    console.error("Error fetching orders:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
