import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { histories, DrawStatus } from "@/lib/db/schema";
import { eq, desc, and, gte, lte, asc, sql, inArray } from "drizzle-orm";
import { createLogger } from "@/lib/draw/logger";

const logger = createLogger('api-histories');

export async function GET(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const status = searchParams.get("status");
    const drawStatus = searchParams.get("drawStatus");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const includeShare = searchParams.get("includeShare") === "true";
    const orderBy = searchParams.get("orderBy") || "createdAt";
    const order = searchParams.get("order") || "desc";

    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const offset = (page - 1) * limit;

    let conditions = [
      eq(histories.userId, userId),
      eq(histories.archived, false), // Filter out archived records
    ];

    if (status && status !== "all") {
      conditions.push(eq(histories.status, status === "true"));
    }

    // 处理绘图状态筛选
    if (drawStatus) {
      // 支持多个状态，用逗号分隔
      const statusArray = drawStatus.split(',') as DrawStatus[];
      if (statusArray.length > 0) {
        conditions.push(inArray(histories.drawStatus, statusArray));
      }
    }

    if (startDate) {
      conditions.push(gte(histories.createdAt, new Date(startDate)));
    }

    if (endDate) {
      conditions.push(lte(histories.createdAt, new Date(endDate)));
    }

    // 构建排序条件
    const orderDirection = order === "asc" ? asc : desc;
    const orderByColumn = orderBy === "createdAt" ? histories.createdAt : histories.updatedAt;

    const items = await db.query.histories.findMany({
      where: and(...conditions),
      orderBy: [orderDirection(orderByColumn)],
      limit,
      offset,
      with: includeShare ? {
        share: {
          columns: {
            id: true,
            shareId: true,
            isPublic: true,
            allowFork: true,
            viewCount: true,
            likeCount: true,
            forkCount: true,
          }
        }
      } : undefined,
    });

    // Get total count for pagination
    const countResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(histories)
      .where(and(...conditions));

    const totalCount = Number(countResult[0]?.count || 0);
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      histories: items,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    logger.error("Error fetching histories", error instanceof Error ? error : new Error(String(error)));
    return new NextResponse("Internal Error", { status: 500 });
  }
}
