import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { histories, shares } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { deleteObjectsWithPrefix } from "@/lib/storage/r2-client";
import { createLogger } from "@/lib/draw/logger";

const logger = createLogger('api-history');

export async function GET(
  _request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { id } = await params;
    const history = await db.query.histories.findFirst({
      where: eq(histories.id, id),
    });

    if (!history) {
      return new NextResponse("History not found", { status: 404 });
    }

    if (history.userId !== userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (history.archived) {
      return new NextResponse("History has been deleted", { status: 410 });
    }

    // Get associated share if exists
    const share = await db.query.shares.findFirst({
      where: eq(shares.historyId, id),
    });

    return NextResponse.json({
      ...history,
      share: share
        ? {
            id: share.id,
            shareId: share.shareId,
            isPublic: share.isPublic,
            allowFork: share.allowFork,
            viewCount: share.viewCount,
            likeCount: share.likeCount,
            forkCount: share.forkCount,
          }
        : undefined,
    });
  } catch (error) {
    logger.error("Error fetching history", error instanceof Error ? error : new Error(String(error)), { historyId: params.then(p => p.id) });
    return new NextResponse("Internal Error", { status: 500 });
  }
}

export async function DELETE(
  _request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { id } = await params;

    // Get the history record
    const history = await db.query.histories.findFirst({
      where: eq(histories.id, id),
    });

    if (!history) {
      return new NextResponse("History not found", { status: 404 });
    }

    if (history.userId !== userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // 1. Mark history as archived
    await db.update(histories)
      .set({
        archived: true,
        updatedAt: new Date(),
      })
      .where(eq(histories.id, id));

    // 2. If backupStatus is SUCCESS, delete R2 files
    let deletedFiles = 0;
    if (history.backupStatus === "SUCCESS") {
      // Delete all files in the user's history directory
      const prefix = `${userId}/${id}/`;
      deletedFiles = await deleteObjectsWithPrefix(prefix);
      logger.info(`Deleted ${deletedFiles} files from R2 for history ${id}`, { historyId: id, userId, deletedFiles });
    }

    // 3. Check for associated share and delete it
    const share = await db.query.shares.findFirst({
      where: eq(shares.historyId, id),
    });

    if (share) {
      // Delete the share record completely
      await db.delete(shares)
        .where(eq(shares.id, share.id));

      logger.info(`Deleted share ${share.id} for history ${id}`, { historyId: id, shareId: share.id });
    }

    // 4. Clear resultUrl and originalUrl
    const historyExtra = history.extra as Record<string, any>;
    await db.update(histories)
      .set({
        resultUrl: "",
        extra: {
          ...historyExtra,
          originalUrl: "",
        },
        updatedAt: new Date(),
      })
      .where(eq(histories.id, id));

    return NextResponse.json({
      message: "History deleted successfully",
      deletedFiles,
      shareDeleted: !!share,
    });
  } catch (error) {
    logger.error("Error deleting history", error instanceof Error ? error : new Error(String(error)), { historyId: params.then(p => p.id) });
    return new NextResponse("Internal Error", { status: 500 });
  }
}
