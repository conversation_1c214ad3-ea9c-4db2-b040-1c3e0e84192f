import Stripe from 'stripe';
import { eq } from 'drizzle-orm';
import { nanoid } from 'nanoid';

import { NextResponse } from 'next/server';
import { auth, currentUser } from '@clerk/nextjs/server';

import { db } from '@/lib/db';
import { orders } from '@/lib/db/schema';
import { PaymentClient } from '@/lib/payment/client';
import { PRICING_TIERS } from '@/constants/draw/pricing';
import { PaymentMethod } from '@/lib/db/schema';

import { stripeEnabled, wxpayEnabled, alipayEnabled } from '@/constants/payment';

// Initialize Stripe client
const stripe = stripeEnabled ? new Stripe(process.env.STRIPE_SECRET_KEY!) : null;

// Initialize payment client
const paymentClient = alipayEnabled || wxpayEnabled ? new PaymentClient({
  apiUrl: process.env.PAY_API_URL!,
  pid: process.env.PAY_PID!,
  publicKey: process.env.PAY_PUBLIC_KEY!,
  privateKey: process.env.PAY_MERCHANT_PRIVATE_KEY!,
}) : null;

export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get the current user to access their email
    const user = await currentUser();
    const userEmail = user?.emailAddresses[0]?.emailAddress;

    const body = await request.json();
    const { paymentMethod, tierId } = body;

    if (!paymentMethod || !tierId) {
      return new NextResponse('Missing required fields', { status: 400 });
    }

    const packageInfo = PRICING_TIERS.find((pkg) => pkg.id === tierId);
    if (!packageInfo) {
      return new NextResponse('Invalid package', { status: 400 });
    }

    if (!packageInfo.points) {
      return new NextResponse('Invalid package points', { status: 400 });
    }

    const orderId = nanoid();
    const outTradeNo = `ORDER_${orderId}`;

    // Create order record
    const [order] = await db
      .insert(orders)
      .values({
        id: orderId,
        userId: userId,
        buyerId: userId,
        type: 'credit',
        amount: packageInfo.points,
        description: `购买${packageInfo.name}`,
        status: 'PENDING' as const,
        paymentMethod: (paymentMethod === 'wechat' ? 'wxpay' : paymentMethod) as PaymentMethod,
        outTradeNo,
        extra: {
          points: packageInfo.points, // 积分数量
          package: packageInfo,
          price: packageInfo.price, // 价格（单位：元）
        },
      })
      .returning();
    // Handle different payment methods
    let qrCodeUrl;

    if (paymentMethod === 'stripe') {
      if (!stripe) {
        return new NextResponse('Stripe is not enabled', { status: 500 });
      }

      // Create Stripe checkout session
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        customer_email: userEmail, // Pre-fill the user's email in the checkout form
        line_items: [
          {
            price_data: {
              currency: 'cny',
              product_data: {
                name: packageInfo.name,
              },
              unit_amount: Math.round(packageInfo.price * 100), // Stripe uses cents as unit
            },
            quantity: 1,
          },
        ],
        mode: 'payment',
        success_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/success?orderId=${orderId}`,
        cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/cancel?orderId=${orderId}`,
        metadata: {
          orderId: orderId,
          outTradeNo: outTradeNo,
        },
        payment_intent_data: {
          // 确保 Payment Intent 也包含相同的 metadata
          metadata: {
            orderId: orderId,
            outTradeNo: outTradeNo,
          },
        },
      });

      qrCodeUrl = session.url;
    } else {
      if (!paymentClient) {
        return new NextResponse('Payment client is not enabled', { status: 500 });
      }

      // Use existing payment client for alipay and wxpay
      const paymentRequest = await paymentClient.createPaymentRequest({
        type: paymentMethod === "wechat" ? "wxpay" : paymentMethod,
        out_trade_no: outTradeNo,
        amount: packageInfo.price, // 价格（单位：元）
        name: packageInfo.name,
        notify_url: `${process.env.NEXT_PUBLIC_APP_URL}/api/public/epay/notify`,
        return_url: `${process.env.NEXT_PUBLIC_APP_URL}/api/public/epay/notify`,
      });

      qrCodeUrl = paymentRequest.qrCodeUrl;
    }

    // Update order with payment URL
    await db
      .update(orders)
      .set({
        qrCodeUrl: qrCodeUrl,
      })
      .where(eq(orders.id, orderId));

    return NextResponse.json({
      orderId: order.id,
      qrCodeUrl: qrCodeUrl,
    });
  } catch (error) {
    console.error('[PURCHASE_ERROR]', error);
    return new NextResponse('Internal error', { status: 500 });
  }
}
