import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { getInvitationUsages } from "@/lib/invitation";
import { createLogger } from "@/lib/draw/logger";
import { InvitationUsageStatus } from "@/lib/db/schema";

const logger = createLogger('api-invitation-usages');

export async function GET(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }
    
    const { searchParams } = new URL(req.url);
    const invitationId = searchParams.get("invitation_id") || undefined;
    const status = searchParams.get("status") as InvitationUsageStatus | undefined;
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");
    
    // 获取邀请使用记录
    const usagesResult = await getInvitationUsages(userId, {
      invitationId,
      status,
      limit,
      offset,
    });
    
    return NextResponse.json(usagesResult);
  } catch (error: any) {
    logger.error("获取邀请使用记录失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
