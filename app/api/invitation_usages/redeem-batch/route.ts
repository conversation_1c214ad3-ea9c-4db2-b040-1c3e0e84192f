import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { createLogger } from "@/lib/draw/logger";
import { redeemInvitationPointsBatch } from "@/lib/invitation/points";
import { z } from "zod";

const logger = createLogger('api-invitation-batch-redeem');

// 批量兑换请求验证
const batchRedeemSchema = z.object({
  usage_ids: z.array(z.string()).min(1).max(50),
});

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();

    // 验证请求数据
    const validationResult = batchRedeemSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { usage_ids } = validationResult.data;

    // 普通用户只能批量兑换积分
    const result = await redeemInvitationPointsBatch(usage_ids, userId);

    return NextResponse.json(result);
  } catch (error: any) {
    logger.error("批量兑换邀请奖励失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
