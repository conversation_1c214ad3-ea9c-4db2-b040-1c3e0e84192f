import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { createLogger } from "@/lib/draw/logger";
import { redeemInvitationPoints } from "@/lib/invitation/points";
import { z } from "zod";

const logger = createLogger('api-invitation-redeem');

// 兑换请求验证
const redeemSchema = z.object({
  redeem_type: z.enum(["points"]),
  note: z.string().optional(),
});

export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { id } = await params;
    if (!id) {
      return NextResponse.json(
        { error: "Missing usage ID" },
        { status: 400 }
      );
    }

    const body = await req.json();

    // 验证请求数据
    const validationResult = redeemSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { redeem_type } = validationResult.data;

    // 只允许兑换积分，不允许兑换现金
    if (redeem_type !== "points") {
      return NextResponse.json(
        { error: "只能自助兑换积分，现金奖励的兑换需要联系管理员" },
        { status: 400 }
      );
    }

    // 兑换积分奖励
    const result = await redeemInvitationPoints(id, userId);

    return NextResponse.json(result);
  } catch (error: any) {
    logger.error("兑换邀请奖励失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
