import { auth, currentUser } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';
import { syncUser, getUserTransactions, getUserHistories } from '@/lib/db/user';
import { checkUserPaid } from "@/lib/db/user";

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const transactionLimit = parseInt(
      searchParams.get("transactionLimit") || "0"
    );
    const historyLimit = parseInt(searchParams.get("historyLimit") || "0");
    const historyIncludeShare =
      searchParams.get("historyIncludeShare") !== "false"; // Default to true if not specified
    const inviteCode = searchParams.get("inviteCode") || "";

    const session = await auth();
    const user = await currentUser();
    const clerkUserId = session.userId;

    if (!clerkUserId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // 同步用户信息并获取钱包信息
    const userData = {
      email: user?.emailAddresses[0]?.emailAddress || "",
      username: user?.username || "user",
      avatarUrl: user?.imageUrl,
      inviteCode: inviteCode || undefined,
    };

    const { permanentPoints, isNewUser, isBlocklisted } = await syncUser(
      clerkUserId,
      userData
    );

    // 检查用户是否有付费记录
    const { isPaid } = await checkUserPaid(clerkUserId);

    // 获取交易记录，如果 limit 为 0 则返回空数组
    const recentTransactions =
      transactionLimit > 0
        ? await getUserTransactions(clerkUserId, transactionLimit)
        : [];

    // 获取使用记录，如果 limit 为 0 则返回空数组
    const recentHistories =
      historyLimit > 0
        ? await getUserHistories(clerkUserId, historyLimit, historyIncludeShare)
        : [];

    return NextResponse.json({
      user: {
        id: clerkUserId,
        email: userData.email,
        username: userData.username,
        avatarUrl: userData.avatarUrl,
        isPaid,
        isBlocklisted,
      },
      wallet: {
        permanentPoints,
      },
      isNewUser,
      recentTransactions,
      recentHistories,
    });
  } catch (error) {
    console.error("[ME_GET]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
