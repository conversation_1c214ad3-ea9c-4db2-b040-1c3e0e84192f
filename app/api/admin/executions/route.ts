import { NextResponse } from "next/server";
import { adminAuth } from "@/lib/admin/auth";
import { listExecutions } from "@/lib/execution/execution-manager";
import { createLogger } from "@/lib/draw/logger";
import { z } from "zod";

const logger = createLogger('api-admin-executions');

// Validation schema for query parameters
const ListExecutionsSchema = z.object({
  type: z.string().optional(),
  status: z.string().optional(),
  page: z.coerce.number().int().positive().optional(),
  limit: z.coerce.number().int().positive().max(100).optional(),
});

export async function GET(req: Request) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const type = searchParams.get('type') || undefined;
    const status = searchParams.get('status') || undefined;
    const page = searchParams.get('page') ? parseInt(searchParams.get('page') || '1', 10) : 1;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit') || '10', 10) : 10;

    // Validate query parameters
    const validationResult = ListExecutionsSchema.safeParse({
      type,
      status,
      page,
      limit,
    });

    if (!validationResult.success) {
      const validationError = new Error('Validation failed');
      logger.error('Validation error', validationError, { details: validationResult.error.format() });
      return NextResponse.json(
        { error: "Invalid request", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // List executions
    const { executions, pagination } = await listExecutions({
      type: type as any,
      status: status as any,
      page,
      limit,
    });

    return NextResponse.json({
      executions,
      pagination,
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Unknown error');
    logger.error('Error listing executions', err, { details: error });
    return NextResponse.json(
      { error: "Internal server error", message: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
