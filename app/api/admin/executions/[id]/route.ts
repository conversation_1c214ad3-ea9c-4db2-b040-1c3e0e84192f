import { NextResponse } from "next/server";
import { adminAuth } from "@/lib/admin/auth";
import { getExecution } from "@/lib/execution/execution-manager";
import { createLogger } from "@/lib/draw/logger";

const logger = createLogger('api-admin-executions-id');

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    const { id } = await params;

    // Get execution
    const execution = await getExecution(id);

    if (!execution) {
      return NextResponse.json(
        { error: "Execution not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      execution,
    });
  } catch (error) {
    const { id } = await params;
    const err = error instanceof Error ? error : new Error('Unknown error');
    logger.error(`Error getting execution ${id}`, err, { executionId: id, details: error });
    return NextResponse.json(
      { error: "Internal server error", message: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
