import { NextResponse } from "next/server";
import { adminAuth } from "@/lib/admin/auth";
import { z } from "zod";
import { createBlocklistRule, getBlocklistRules } from "@/lib/blocklist";
import { BLOCKLIST_TYPES } from "@/constants/blocklist";
import { createLogger } from "@/lib/draw/logger";

const logger = createLogger('api-admin-blocklists');

// 验证创建黑名单规则的请求
const CreateBlocklistSchema = z.object({
  type: z.enum([BLOCKLIST_TYPES.EMAIL, BLOCKLIST_TYPES.IP, BLOCKLIST_TYPES.KEYWORD]),
  pattern: z.string().min(1).max(255),
  description: z.string().optional(),
  enabled: z.boolean().default(true),
});

// 验证获取黑名单规则列表的请求
const GetBlocklistsSchema = z.object({
  type: z.enum([BLOCKLIST_TYPES.EMAIL, BLOCKLIST_TYPES.IP, BLOCKLIST_TYPES.KEYWORD]).optional(),
  enabled: z.boolean().optional(),
  page: z.coerce.number().int().positive().optional(),
  limit: z.coerce.number().int().positive().max(100).optional(),
});

/**
 * 获取黑名单规则列表
 */
export async function GET(req: Request) {
  try {
    // 检查管理员权限
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse;
    }

    // 解析查询参数
    const { searchParams } = new URL(req.url);
    const type = searchParams.get('type') || undefined;
    const enabled = searchParams.has('enabled')
      ? searchParams.get('enabled') === 'true'
      : undefined;
    const page = searchParams.get('page') ? parseInt(searchParams.get('page') || '1', 10) : 1;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit') || '10', 10) : 10;

    // 验证查询参数
    const validationResult = GetBlocklistsSchema.safeParse({
      type,
      enabled,
      page,
      limit,
    });

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // 获取黑名单规则列表
    const result = await getBlocklistRules({
      type,
      enabled,
      page,
      limit,
    });

    return NextResponse.json(result);
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[ADMIN_BLOCKLISTS_GET]', errorObj);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

/**
 * 创建黑名单规则
 */
export async function POST(req: Request) {
  try {
    // 检查管理员权限
    const authResult = await adminAuth();
    if (authResult) {
      return authResult;
    }

    // 获取管理员ID
    const { userId } = await isAdmin();

    // 解析请求体
    const body = await req.json();

    // 验证请求体
    const validationResult = CreateBlocklistSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request body", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { type, pattern, description, enabled } = validationResult.data;

    // 创建黑名单规则
    const rule = await createBlocklistRule({
      type,
      pattern,
      description,
      enabled,
      createdBy: userId,
    });

    return NextResponse.json(rule, { status: 201 });
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[ADMIN_BLOCKLISTS_POST]', errorObj);

    // 处理特定错误
    if (error instanceof Error) {
      if (error.message.includes('无效的正则表达式')) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

// 导入 isAdmin 函数
async function isAdmin() {
  const result = await adminAuth();
  if (result) {
    throw new Error("Unauthorized");
  }

  const session = await auth();
  const userId = session.userId;

  if (!userId) {
    throw new Error("Unauthorized");
  }

  return { userId };
}

import { auth } from "@clerk/nextjs/server";
