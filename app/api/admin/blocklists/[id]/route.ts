import { NextResponse } from "next/server";
import { adminAuth } from "@/lib/admin/auth";
import { z } from "zod";
import { getBlocklistRule, updateBlocklistRule, deleteBlocklistRule } from "@/lib/blocklist";
import { createLogger } from "@/lib/draw/logger";

const logger = createLogger('api-admin-blocklist-detail');

// 验证更新黑名单规则的请求
const UpdateBlocklistSchema = z.object({
  pattern: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  enabled: z.boolean().optional(),
});

/**
 * 获取单个黑名单规则
 */
export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse;
    }

    const { id } = await params;

    // 获取黑名单规则
    const rule = await getBlocklistRule(id);

    if (!rule) {
      return NextResponse.json(
        { error: "Blocklist rule not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(rule);
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[ADMIN_BLOCKLIST_GET]', errorObj);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

/**
 * 更新黑名单规则
 */
export async function PUT(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse;
    }

    const { id } = await params;

    // 检查规则是否存在
    const existingRule = await getBlocklistRule(id);
    if (!existingRule) {
      return NextResponse.json(
        { error: "Blocklist rule not found" },
        { status: 404 }
      );
    }

    // 解析请求体
    const body = await req.json();

    // 验证请求体
    const validationResult = UpdateBlocklistSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request body", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { pattern, description, enabled } = validationResult.data;

    // 更新黑名单规则
    const updatedRule = await updateBlocklistRule({
      id,
      pattern,
      description,
      enabled,
    });

    return NextResponse.json(updatedRule);
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[ADMIN_BLOCKLIST_PUT]', errorObj);

    // 处理特定错误
    if (error instanceof Error) {
      if (error.message.includes('无效的正则表达式')) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

/**
 * 删除黑名单规则
 */
export async function DELETE(
  _req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse;
    }

    const { id } = await params;

    // 检查规则是否存在
    const existingRule = await getBlocklistRule(id);
    if (!existingRule) {
      return NextResponse.json(
        { error: "Blocklist rule not found" },
        { status: 404 }
      );
    }

    // 删除黑名单规则
    await deleteBlocklistRule(id);

    return NextResponse.json({ success: true });
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[ADMIN_BLOCKLIST_DELETE]', errorObj);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
