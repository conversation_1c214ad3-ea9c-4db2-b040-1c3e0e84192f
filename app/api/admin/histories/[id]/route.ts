import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { histories, users, shares } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { adminAuth } from "@/lib/admin/auth";
import { createLogger } from "@/lib/draw/logger";
import { deleteObjectsWithPrefix } from "@/lib/storage/r2-client";

const logger = createLogger('api-admin-history-detail');

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    const { id } = await params;

    // Get history details with user and share information
    const history = await db.query.histories.findFirst({
      where: eq(histories.id, id),
      with: {
        user: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
            avatarUrl: true,
            createdAt: true,
          },
        },
      },
    });

    if (!history) {
      return NextResponse.json(
        { error: "History not found" },
        { status: 404 }
      );
    }

    // Get share information if this history has been shared
    const share = await db.query.shares.findFirst({
      where: eq(shares.historyId, id),
    });

    return NextResponse.json({
      history,
      share,
    });
  } catch (error) {
    logger.error("Error fetching admin history detail", error instanceof Error ? error : new Error(String(error)), { historyId: params.then(p => p.id) });
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    const { id } = await params;

    // Get the history record
    const history = await db.query.histories.findFirst({
      where: eq(histories.id, id),
    });

    if (!history) {
      return NextResponse.json(
        { error: "History not found" },
        { status: 404 }
      );
    }

    // 1. Mark history as archived
    await db.update(histories)
      .set({
        archived: true,
        updatedAt: new Date(),
      })
      .where(eq(histories.id, id));

    // 2. If backupStatus is SUCCESS, delete R2 files
    let deletedFiles = 0;
    if (history.backupStatus === "SUCCESS") {
      // Delete all files in the user's history directory
      const prefix = `${history.userId}/${id}/`;
      deletedFiles = await deleteObjectsWithPrefix(prefix);
      logger.info(`Deleted ${deletedFiles} files from R2 for history ${id}`, { historyId: id, userId: history.userId, deletedFiles });
    }

    // 3. Check for associated share and update it
    const share = await db.query.shares.findFirst({
      where: eq(shares.historyId, id),
    });

    if (share) {
      // Update the share to be private and non-forkable
      await db.update(shares)
        .set({
          isPublic: false,
          allowFork: false,
          updatedAt: new Date(),
        })
        .where(eq(shares.id, share.id));

      logger.info(`Updated share ${share.id} for history ${id} to be private`, { historyId: id, shareId: share.id });
    }

    // 4. Clear resultUrl and originalUrl
    const historyExtra = history.extra as Record<string, any>;
    await db.update(histories)
      .set({
        resultUrl: "",
        extra: {
          ...historyExtra,
          originalUrl: "",
        },
        updatedAt: new Date(),
      })
      .where(eq(histories.id, id));

    return NextResponse.json({
      message: "History archived successfully",
      deletedFiles,
      shareUpdated: !!share,
    });
  } catch (error) {
    logger.error("Error archiving history", error instanceof Error ? error : new Error(String(error)), { historyId: params.then(p => p.id) });
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
