import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { histories, users, DrawStatus } from "@/lib/db/schema";
import { and, eq, like, desc, asc, sql, gte, lte } from "drizzle-orm";
import { adminAuth } from "@/lib/admin/auth";
import { createLogger } from "@/lib/draw/logger";

const logger = createLogger('api-admin-histories');

export async function GET(request: Request) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);

    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;

    // Sorting parameters
    const orderBy = searchParams.get("orderBy") || "createdAt";
    const order = searchParams.get("order") || "desc";

    // Filter parameters
    const id = searchParams.get("id");
    const userId = searchParams.get("userId");
    const status = searchParams.get("status");
    const drawStatus = searchParams.get("drawStatus");
    const prompt = searchParams.get("prompt");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const minPoints = searchParams.get("minPoints");
    const maxPoints = searchParams.get("maxPoints");
    const showArchived = searchParams.get("showArchived") === "true";

    // Build query conditions
    const conditions = [];

    if (id) {
      conditions.push(eq(histories.id, id));
    }

    if (userId) {
      conditions.push(eq(histories.userId, userId));
    }

    if (status !== null && status !== undefined) {
      conditions.push(eq(histories.status, status === "true"));
    }

    if (drawStatus) {
      conditions.push(eq(histories.drawStatus, drawStatus as DrawStatus));
    }

    if (prompt) {
      conditions.push(like(histories.prompt, `%${prompt}%`));
    }

    if (startDate) {
      conditions.push(gte(histories.createdAt, new Date(startDate)));
    }

    if (endDate) {
      conditions.push(lte(histories.createdAt, new Date(endDate)));
    }

    if (minPoints) {
      conditions.push(gte(histories.pointsUsed, parseInt(minPoints)));
    }

    if (maxPoints) {
      conditions.push(lte(histories.pointsUsed, parseInt(maxPoints)));
    }

    // Filter archived records unless explicitly requested
    if (!showArchived) {
      conditions.push(eq(histories.archived, false));
    }

    // Determine sort column
    let sortColumn;
    switch (orderBy) {
      case "pointsUsed":
        sortColumn = histories.pointsUsed;
        break;
      case "status":
        sortColumn = histories.status;
        break;
      case "updatedAt":
        sortColumn = histories.updatedAt;
        break;
      case "createdAt":
      default:
        sortColumn = histories.createdAt;
        break;
    }

    // Determine sort direction
    const sortDirection = order === "asc" ? asc : desc;

    // Execute query with pagination
    const historiesList = await db.query.histories.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
      orderBy: [sortDirection(sortColumn)],
      limit,
      offset,
      with: {
        user: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
    });

    // Get total count for pagination
    const countResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(histories)
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const totalCount = countResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      histories: historiesList,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    logger.error("Error fetching admin histories", error instanceof Error ? error : new Error(String(error)));
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
