import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, orders, histories, shares, History } from "@/lib/db/schema";
import { and, eq, gte, lt, sql, desc } from "drizzle-orm";
import { adminAuth } from "@/lib/admin/auth";

export async function GET(request: Request) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    // Parse date range from query parameters
    const { searchParams } = new URL(request.url);
    const fromParam = searchParams.get('from');
    const toParam = searchParams.get('to');

    // Set default date range if not provided (3 days)
    let from: Date;
    let to = new Date();

    if (fromParam && toParam) {
      from = new Date(fromParam);
      to = new Date(toParam);
    } else {
      from = new Date();
      from.setDate(from.getDate() - 3); // Default to 3 days
    }

    // For comparison periods (to calculate growth)
    const timeDiff = to.getTime() - from.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

    // Previous period for comparison (same duration)
    const prevPeriodTo = new Date(from);
    const prevPeriodFrom = new Date(from);
    prevPeriodFrom.setDate(prevPeriodFrom.getDate() - daysDiff);

    // Get last hour for active now metric
    const lastHour = new Date();
    lastHour.setHours(lastHour.getHours() - 1);

    // Count users registered in the selected period
    const newUsersResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(users)
      .where(and(
        gte(users.createdAt, from),
        lt(users.createdAt, to)
      ));

    const newUsers = newUsersResult[0]?.count || 0;

    // Count users registered in the previous period
    const prevPeriodUsersResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(users)
      .where(and(
        gte(users.createdAt, prevPeriodFrom),
        lt(users.createdAt, prevPeriodTo)
      ));

    const prevPeriodUsers = prevPeriodUsersResult[0]?.count || 0;
    const userGrowth = prevPeriodUsers > 0 ? Math.round((newUsers / prevPeriodUsers) * 100) : 0;

    // Calculate total payments in the selected period
    const paymentsResult = await db
      .select({
        totalAmount: sql<number>`COALESCE(SUM((${orders.extra}->>'price')::numeric), 0)`,
      })
      .from(orders)
      .where(and(
        gte(orders.createdAt, from),
        lt(orders.createdAt, to),
        eq(orders.status, "SUCCESS")
      ));

    const totalPayments = paymentsResult[0]?.totalAmount || 0;

    // Calculate total payments in the previous period
    const prevPeriodPaymentsResult = await db
      .select({
        totalAmount: sql<number>`COALESCE(SUM((${orders.extra}->>'price')::numeric), 0)`,
      })
      .from(orders)
      .where(and(
        gte(orders.createdAt, prevPeriodFrom),
        lt(orders.createdAt, prevPeriodTo),
        eq(orders.status, "SUCCESS")
      ));

    const prevPeriodPayments = prevPeriodPaymentsResult[0]?.totalAmount || 0;
    const paymentGrowth = prevPeriodPayments > 0 ? Math.round((totalPayments / prevPeriodPayments) * 100) : 0;

    // Count histories created in the selected period
    const historiesResult = await db
      .select({
        total: sql<number>`count(*)`,
        success: sql<number>`count(*) FILTER (WHERE ${histories.status} = true)`,
        failed: sql<number>`count(*) FILTER (WHERE ${histories.status} = false)`,
      })
      .from(histories)
      .where(and(
        gte(histories.createdAt, from),
        lt(histories.createdAt, to)
      ));

    const historiesTotal = historiesResult[0]?.total || 0;
    const historiesSuccess = historiesResult[0]?.success || 0;
    const historiesFailed = historiesResult[0]?.failed || 0;
    const successRate = historiesTotal > 0 ? historiesSuccess / historiesTotal : 0;

    // Get recent histories with model and style information
    const recentHistories = await db.query.histories.findMany({
      where: and(
        gte(histories.createdAt, from),
        lt(histories.createdAt, to)
      ),
      orderBy: [desc(histories.createdAt)],
      limit: 100, // Get enough data for aggregation
    }) as History[];

    // Aggregate history data by model
    const modelStats: Record<string, { total: number; success: number; failed: number; successRate: number }> = {};
    const styleStats: Record<string, { total: number; success: number; failed: number; successRate: number }> = {};

    recentHistories.forEach(history => {
      // Process model stats
      const model = history.extra?.model || 'unknown';
      if (!modelStats[model]) {
        modelStats[model] = { total: 0, success: 0, failed: 0, successRate: 0 };
      }
      modelStats[model].total += 1;
      if (history.status) {
        modelStats[model].success += 1;
      } else {
        modelStats[model].failed += 1;
      }

      // Process style stats
      const style = history.extra?.style || 'unknown';
      if (!styleStats[style]) {
        styleStats[style] = { total: 0, success: 0, failed: 0, successRate: 0 };
      }
      styleStats[style].total += 1;
      if (history.status) {
        styleStats[style].success += 1;
      } else {
        styleStats[style].failed += 1;
      }
    });

    // Calculate success rates
    Object.keys(modelStats).forEach(model => {
      const stats = modelStats[model];
      stats.successRate = stats.total > 0 ? stats.success / stats.total : 0;
    });

    Object.keys(styleStats).forEach(style => {
      const stats = styleStats[style];
      stats.successRate = stats.total > 0 ? stats.success / stats.total : 0;
    });

    // Count histories created in the previous period
    const prevPeriodHistoriesResult = await db
      .select({
        total: sql<number>`count(*)`,
      })
      .from(histories)
      .where(and(
        gte(histories.createdAt, prevPeriodFrom),
        lt(histories.createdAt, prevPeriodTo)
      ));

    const prevPeriodHistoriesTotal = prevPeriodHistoriesResult[0]?.total || 0;
    const historiesGrowth = prevPeriodHistoriesTotal > 0 ? Math.round((historiesTotal / prevPeriodHistoriesTotal) * 100) : 0;

    // Count shares created in the selected period
    const newSharesResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(shares)
      .where(and(
        gte(shares.createdAt, from),
        lt(shares.createdAt, to)
      ));

    const newShares = newSharesResult[0]?.count || 0;

    // Count shares created in the last hour
    const lastHourSharesResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(shares)
      .where(gte(shares.createdAt, lastHour));

    const lastHourShares = lastHourSharesResult[0]?.count || 0;
    const shareGrowth = lastHourShares > 0 ? Math.round((newShares / lastHourShares) * 100) : 0;

    // Get recent orders with user information (excluding system buyers) within the date range
    const recentOrders = await db.query.orders.findMany({
      where: and(
        eq(orders.status, "SUCCESS"),
        sql`${orders.buyerId} = ${orders.userId}`, // Only include self-purchases (exclude system buyers)
        gte(orders.createdAt, from),
        lt(orders.createdAt, to)
      ),
      with: {
        user: {
          columns: {
            username: true,
            clerkId: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
      columns: {
        id: true,
        userId: true,
        amount: true,
        status: true,
        createdAt: true,
        extra: true,
      },
      orderBy: [desc(orders.createdAt)],
      limit: 5,
    });

    return NextResponse.json({
      newUsers,
      totalPayments,
      userGrowth,
      paymentGrowth,
      histories: {
        total: historiesTotal,
        success: historiesSuccess,
        failed: historiesFailed,
        successRate,
        growth: historiesGrowth,
        byModel: modelStats,
        byStyle: styleStats
      },
      newShares,
      shareGrowth,
      recentOrders,
      recentHistories: recentHistories.slice(0, 10), // Only return the 10 most recent for display
    });
  } catch (error) {
    console.error("[ADMIN_DASHBOARD_STATS]", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
