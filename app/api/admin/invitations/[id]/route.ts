import { NextResponse } from "next/server";
import { adminAuth } from "@/lib/admin/auth";
import { db } from "@/lib/db";
import { invitations, invitationUsages, users } from "@/lib/db/schema";
import { and, eq, count } from "drizzle-orm";
import { createLogger } from "@/lib/draw/logger";
import { z } from "zod";

const logger = createLogger('api-admin-invitation-detail');

// 更新邀请码请求验证
const updateInvitationSchema = z.object({
  invite_code: z.string().min(3).max(20).optional(),
  invite_type: z.enum(["points", "cash", "both"]).optional(),
  ref_ratio: z.number().min(0.01).max(1).optional(),
  channel: z.string().optional().nullable(),
  max_uses: z.number().int().min(0).optional(),
  expires_at: z.string().optional().nullable(),
});

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse;
    }

    const { id } = await params;

    // 获取邀请码详情
    const invitation = await db.query.invitations.findFirst({
      where: eq(invitations.id, id),
    });

    if (!invitation) {
      return NextResponse.json(
        { error: "Invitation not found" },
        { status: 404 }
      );
    }

    // 获取邀请人信息
    const referrer = await db.query.users.findFirst({
      where: eq(users.clerkId, invitation.referrerId),
      columns: {
        clerkId: true,
        username: true,
        email: true,
        avatarUrl: true,
      },
    });

    // 获取使用统计
    const usageCount = await db
      .select({ count: count() })
      .from(invitationUsages)
      .where(eq(invitationUsages.invitationId, id));

    const pendingCount = await db
      .select({ count: count() })
      .from(invitationUsages)
      .where(
        and(
          eq(invitationUsages.invitationId, id),
          eq(invitationUsages.status, "pending")
        )
      );

    const readyCount = await db
      .select({ count: count() })
      .from(invitationUsages)
      .where(
        and(
          eq(invitationUsages.invitationId, id),
          eq(invitationUsages.status, "ready")
        )
      );

    const completedCount = await db
      .select({ count: count() })
      .from(invitationUsages)
      .where(
        and(
          eq(invitationUsages.invitationId, id),
          eq(invitationUsages.status, "completed")
        )
      );

    const voidCount = await db
      .select({ count: count() })
      .from(invitationUsages)
      .where(
        and(
          eq(invitationUsages.invitationId, id),
          eq(invitationUsages.status, "void")
        )
      );

    return NextResponse.json({
      invitation,
      referrer,
      stats: {
        usageCount: usageCount[0]?.count || 0,
        pendingCount: pendingCount[0]?.count || 0,
        readyCount: readyCount[0]?.count || 0,
        completedCount: completedCount[0]?.count || 0,
        voidCount: voidCount[0]?.count || 0,
      },
    });
  } catch (error: any) {
    logger.error("获取邀请码详情失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse;
    }

    const { id } = await params;
    const body = await request.json();

    // 验证请求数据
    const validationResult = updateInvitationSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // 检查邀请码是否存在
    const existingInvitation = await db.query.invitations.findFirst({
      where: eq(invitations.id, id),
    });

    if (!existingInvitation) {
      return NextResponse.json(
        { error: "Invitation not found" },
        { status: 404 }
      );
    }

    // 如果更新邀请码，检查是否已存在
    if (body.invite_code && body.invite_code !== existingInvitation.inviteCode) {
      const duplicateCode = await db.query.invitations.findFirst({
        where: eq(invitations.inviteCode, body.invite_code),
      });

      if (duplicateCode) {
        return NextResponse.json(
          { error: "Invite code already exists" },
          { status: 400 }
        );
      }
    }

    // 准备更新数据
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (body.invite_code) updateData.inviteCode = body.invite_code;
    if (body.invite_type) updateData.inviteType = body.invite_type;
    if (body.ref_ratio !== undefined) updateData.refRatio = String(body.ref_ratio);
    if (body.channel !== undefined) updateData.channel = body.channel;
    if (body.max_uses !== undefined) updateData.maxUses = body.max_uses;
    if (body.expires_at !== undefined) {
      updateData.expiresAt = body.expires_at ? new Date(body.expires_at) : null;
    }

    // 更新邀请码
    const [updatedInvitation] = await db
      .update(invitations)
      .set(updateData)
      .where(eq(invitations.id, id))
      .returning();

    return NextResponse.json(updatedInvitation);
  } catch (error: any) {
    logger.error("更新邀请码失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse;
    }

    const { id } = await params;

    // 检查邀请码是否存在
    const existingInvitation = await db.query.invitations.findFirst({
      where: eq(invitations.id, id),
    });

    if (!existingInvitation) {
      return NextResponse.json(
        { error: "Invitation not found" },
        { status: 404 }
      );
    }

    // 检查是否有使用记录
    const usageCount = await db
      .select({ count: count() })
      .from(invitationUsages)
      .where(eq(invitationUsages.invitationId, id));

    if (usageCount[0]?.count > 0) {
      return NextResponse.json(
        { error: "Cannot delete invitation with usage records" },
        { status: 400 }
      );
    }

    // 删除邀请码
    await db
      .delete(invitations)
      .where(eq(invitations.id, id));

    return NextResponse.json({ success: true });
  } catch (error: any) {
    logger.error("删除邀请码失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
