import { NextResponse } from "next/server";
import { adminAuth, isAdmin } from "@/lib/admin/auth";
import { db } from "@/lib/db";
import { invitations, users } from "@/lib/db/schema";
import { eq, like, desc, asc, count, and, sql } from "drizzle-orm";
import { createInvitation } from "@/lib/invitation";
import { createLogger } from "@/lib/draw/logger";
import { z } from "zod";
import { INVITE_CODE_REGEX, INVITE_CODE_ERROR_MESSAGE } from "@/constants/invitation";

const logger = createLogger('api-admin-invitations');

// 创建邀请码请求验证
const createInvitationSchema = z.object({
  invite_code: z.string()
    .min(3, "邀请码至少需要3个字符")
    .max(20, "邀请码最多20个字符")
    .refine((value) => !value || INVITE_CODE_REGEX.test(value), {
      message: INVITE_CODE_ERROR_MESSAGE,
    })
    .optional(),
  invite_type: z.enum(["points", "cash", "both"]),
  ref_ratio: z.number().min(0.01).max(1),
  channel: z.string().optional(),
  max_uses: z.number().int().min(0).optional(),
  expires_at: z.string().optional(),
  referrer_id: z.string().optional(),
});

export async function GET(req: Request) {
  try {
    // 检查管理员权限
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse;
    }

    // 解析查询参数
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;
    const orderBy = searchParams.get("orderBy") || "createdAt";
    const order = searchParams.get("order") || "desc";
    const code = searchParams.get("code") || "";
    const email = searchParams.get("email") || "";
    const type = searchParams.get("type") || "all";

    // 构建查询条件
    let whereConditions = [];

    if (code) {
      whereConditions.push(like(invitations.inviteCode, `%${code}%`));
    }

    if (type !== "all") {
      whereConditions.push(eq(invitations.inviteType, type as any));
    }

    // 查询邀请码列表
    // 构建查询条件
    let whereClause;
    if (email) {
      whereClause = like(users.email, `%${email}%`);
    } else if (whereConditions.length > 0) {
      whereClause = and(...whereConditions);
    }

    // 构建排序条件
    let orderByClause;
    if (orderBy === "inviteCode") {
      orderByClause = order === "asc" ? asc(invitations.inviteCode) : desc(invitations.inviteCode);
    } else if (orderBy === "referrer.username") {
      orderByClause = order === "asc" ? asc(users.username) : desc(users.username);
    } else {
      orderByClause = order === "asc" ? asc(invitations.createdAt) : desc(invitations.createdAt);
    }

    // 执行查询
    const invitationsList = await db.select({
      id: invitations.id,
      inviteCode: invitations.inviteCode,
      referrerId: invitations.referrerId,
      inviteType: invitations.inviteType,
      refRatio: invitations.refRatio,
      channel: invitations.channel,
      maxUses: invitations.maxUses,
      expiresAt: invitations.expiresAt,
      createdAt: invitations.createdAt,
      updatedAt: invitations.updatedAt,
    })
    .from(invitations)
    .innerJoin(users, eq(invitations.referrerId, users.clerkId))
    .where(whereClause)
    .orderBy(orderByClause)
    .limit(limit)
    .offset(offset);



    // 获取邀请码的使用统计
    const invitationsWithStats = await Promise.all(
      invitationsList.map(async (invitation) => {
        // 获取邀请人信息
        const referrer = await db.query.users.findFirst({
          where: eq(users.clerkId, invitation.referrerId),
          columns: {
            clerkId: true,
            username: true,
            email: true,
            avatarUrl: true,
          },
        });

        // 获取使用统计
        const usageCount = await db.execute(sql`
          SELECT COUNT(*) as count FROM invitation_usages
          WHERE invitation_id = ${invitation.id}
        `);

        const readyCount = await db.execute(sql`
          SELECT COUNT(*) as count FROM invitation_usages
          WHERE invitation_id = ${invitation.id} AND status = 'ready'
        `);

        const completedCount = await db.execute(sql`
          SELECT COUNT(*) as count FROM invitation_usages
          WHERE invitation_id = ${invitation.id} AND status = 'completed'
        `);

        return {
          ...invitation,
          referrer,
          usageCount: usageCount.rows[0]?.count || 0,
          readyCount: readyCount.rows[0]?.count || 0,
          completedCount: completedCount.rows[0]?.count || 0,
        };
      })
    );

    // 获取总数
    const totalCount = await db
      .select({ count: count() })
      .from(invitations)
      .innerJoin(users, eq(invitations.referrerId, users.clerkId))
      .where(whereClause);
    const total = totalCount[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      invitations: invitationsWithStats,
      pagination: {
        page,
        limit,
        totalCount: total,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error: any) {
    logger.error("获取邀请码列表失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    // 检查管理员权限
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse;
    }

    const body = await req.json();

    // 验证请求数据
    const validationResult = createInvitationSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const {
      invite_code,
      invite_type,
      ref_ratio,
      channel,
      max_uses,
      expires_at,
      referrer_id
    } = validationResult.data;

    // 如果指定了referrer_id，检查用户是否存在
    let userId = referrer_id;
    if (userId) {
      const user = await db.query.users.findFirst({
        where: eq(users.clerkId, userId),
      });

      if (!user) {
        return NextResponse.json(
          { error: "Specified referrer user not found" },
          { status: 404 }
        );
      }
    } else {
      // 如果没有指定referrer_id，使用管理员ID
      const adminResult = await isAdmin();
      if (!adminResult.isAdmin) {
        return NextResponse.json(
          { error: "Admin access required" },
          { status: 403 }
        );
      }
      userId = adminResult.userId;
    }

    // 创建邀请码
    if (!userId) {
      return NextResponse.json(
        { error: "Referrer ID is required" },
        { status: 400 }
      );
    }

    const invitation = await createInvitation({
      referrerId: userId,
      inviteCode: invite_code,
      inviteType: invite_type,
      refRatio: ref_ratio,
      channel,
      maxUses: max_uses || 0,
      expiresAt: expires_at ? new Date(expires_at) : undefined,
    });

    return NextResponse.json(invitation);
  } catch (error: any) {
    logger.error("创建邀请码失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
