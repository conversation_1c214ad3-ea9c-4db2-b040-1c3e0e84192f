import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { shares, users, histories, likes } from "@/lib/db/schema";
import { eq, and, count } from "drizzle-orm";
import { adminAuth } from "@/lib/admin/auth";
import { z } from "zod";
import { createLogger } from "@/lib/draw/logger";

const logger = createLogger('api-admin-shares');

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    const { id } = await params;

    // Get share details with user and history information
    const share = await db.query.shares.findFirst({
      where: eq(shares.id, id),
      with: {
        user: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
            avatarUrl: true,
            createdAt: true,
          },
        },
        history: true,
      },
    });

    if (!share) {
      return NextResponse.json(
        { error: "Share not found" },
        { status: 404 }
      );
    }

    // Get recent likes
    const recentLikes = await db.query.likes.findMany({
      where: eq(likes.shareId, id),
      limit: 10,
      orderBy: [likes.createdAt],
      with: {
        user: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
    });

    // Get forked histories
    const forkedHistories = await db.query.histories.findMany({
      where: eq(histories.forkedFromShareId, id),
      limit: 10,
      orderBy: [histories.createdAt],
      with: {
        user: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
    });

    return NextResponse.json({
      share,
      recentLikes,
      forkedHistories,
    });
  } catch (error) {
    logger.error("Error fetching admin share detail", error instanceof Error ? error : new Error(String(error)), { shareId: params.then(p => p.id) });
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

// Define a schema for validating the request body
const updateShareSchema = z.object({
  isPublic: z.boolean(),
});

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    const { id } = await params;

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = updateShareSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { isPublic } = validationResult.data;

    // Check if share exists
    const existingShare = await db.query.shares.findFirst({
      where: eq(shares.id, id),
    });

    if (!existingShare) {
      return NextResponse.json(
        { error: "Share not found" },
        { status: 404 }
      );
    }

    // Update the share
    await db
      .update(shares)
      .set({
        isPublic,
        updatedAt: new Date(),
      })
      .where(eq(shares.id, id));

    return NextResponse.json({
      message: "Share updated successfully",
      isPublic,
    });
  } catch (error) {
    logger.error("Error updating share", error instanceof Error ? error : new Error(String(error)), { shareId: params.then(p => p.id) });
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    const { id } = await params;

    // Check if share exists
    const existingShare = await db.query.shares.findFirst({
      where: eq(shares.id, id),
    });

    if (!existingShare) {
      return NextResponse.json(
        { error: "Share not found" },
        { status: 404 }
      );
    }

    // Delete the share
    await db
      .delete(shares)
      .where(eq(shares.id, id));

    // Also delete any likes associated with this share
    await db
      .delete(likes)
      .where(eq(likes.shareId, id));

    return NextResponse.json({
      message: "Share deleted successfully",
    });
  } catch (error) {
    logger.error("Error deleting share", error instanceof Error ? error : new Error(String(error)), { shareId: params.then(p => p.id) });
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
