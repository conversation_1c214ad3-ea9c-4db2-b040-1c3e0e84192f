import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { shares, users } from "@/lib/db/schema";
import { and, eq, like, desc, asc, sql, gte, lte } from "drizzle-orm";
import { adminAuth } from "@/lib/admin/auth";

export async function GET(request: Request) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);

    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;

    // Sorting parameters
    const orderBy = searchParams.get("orderBy") || "sharedAt";
    const order = searchParams.get("order") || "desc";

    // Filter parameters
    const id = searchParams.get("id");
    const shareId = searchParams.get("shareId");
    const userId = searchParams.get("userId");
    const isPublic = searchParams.get("isPublic");
    const allowFork = searchParams.get("allowFork");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const minViews = searchParams.get("minViews");
    const maxViews = searchParams.get("maxViews");
    const minLikes = searchParams.get("minLikes");
    const maxLikes = searchParams.get("maxLikes");
    const minForks = searchParams.get("minForks");
    const maxForks = searchParams.get("maxForks");

    // Build query conditions
    const conditions = [];

    if (id) {
      conditions.push(eq(shares.id, id));
    }

    if (shareId) {
      conditions.push(eq(shares.shareId, shareId));
    }

    if (userId) {
      conditions.push(eq(shares.userId, userId));
    }

    if (isPublic !== null && isPublic !== undefined) {
      conditions.push(eq(shares.isPublic, isPublic === "true"));
    }

    if (allowFork !== null && allowFork !== undefined) {
      conditions.push(eq(shares.allowFork, allowFork === "true"));
    }

    if (startDate) {
      conditions.push(gte(shares.sharedAt, new Date(startDate)));
    }

    if (endDate) {
      conditions.push(lte(shares.sharedAt, new Date(endDate)));
    }

    if (minViews) {
      conditions.push(gte(shares.viewCount, parseInt(minViews)));
    }

    if (maxViews) {
      conditions.push(lte(shares.viewCount, parseInt(maxViews)));
    }

    if (minLikes) {
      conditions.push(gte(shares.likeCount, parseInt(minLikes)));
    }

    if (maxLikes) {
      conditions.push(lte(shares.likeCount, parseInt(maxLikes)));
    }

    if (minForks) {
      conditions.push(gte(shares.forkCount, parseInt(minForks)));
    }

    if (maxForks) {
      conditions.push(lte(shares.forkCount, parseInt(maxForks)));
    }

    // Determine sort column
    let sortColumn;
    switch (orderBy) {
      case "viewCount":
        sortColumn = shares.viewCount;
        break;
      case "likeCount":
        sortColumn = shares.likeCount;
        break;
      case "forkCount":
        sortColumn = shares.forkCount;
        break;
      case "forkEarnings":
        sortColumn = shares.forkEarnings;
        break;
      case "createdAt":
        sortColumn = shares.createdAt;
        break;
      case "sharedAt":
      default:
        sortColumn = shares.sharedAt;
        break;
    }

    // Determine sort direction
    const sortDirection = order === "asc" ? asc : desc;

    // Execute query with pagination
    const sharesList = await db.query.shares.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
      orderBy: [sortDirection(sortColumn)],
      limit,
      offset,
      with: {
        user: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
            avatarUrl: true,
          },
        },
        history: true,
      },
    });

    // Get total count for pagination
    const countResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(shares)
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const totalCount = countResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      shares: sharesList,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    console.error("[ADMIN_SHARES_GET]", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
