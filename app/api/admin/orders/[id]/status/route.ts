import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { orders } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { adminAuth } from "@/lib/admin/auth";
import { z } from "zod";
import { ALLOWED_STATUS_LIST } from "@/constants/order";

// Define a schema for validating the request body
const updateStatusSchema = z.object({
  status: z.enum(["PENDING", "SUCCESS", "FAILED", "REFUND"] as const),
});

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    const { id } = await params;

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = updateStatusSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid status value", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { status } = validationResult.data;

    // Check if order exists
    const existingOrder = await db.query.orders.findFirst({
      where: eq(orders.id, id),
    });

    if (!existingOrder) {
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      );
    }

    // Check if the current status is allowed to be modified
    if (!ALLOWED_STATUS_LIST.includes(existingOrder.status)) {
      return NextResponse.json(
        { error: "This order status cannot be modified" },
        { status: 403 }
      );
    }

    // Update the order status
    await db
      .update(orders)
      .set({
        status,
        updatedAt: new Date(),
      })
      .where(eq(orders.id, id));

    // Get the updated order
    const updatedOrder = await db.query.orders.findFirst({
      where: eq(orders.id, id),
      with: {
        user: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "Order status updated successfully",
      order: updatedOrder,
    });
  } catch (error) {
    console.error("[ADMIN_ORDER_STATUS_UPDATE]", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
