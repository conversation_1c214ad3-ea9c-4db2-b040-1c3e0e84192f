import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { orders, OrderStatus } from "@/lib/db/schema";
import { and, eq, desc, asc, sql, gte, lte } from "drizzle-orm";
import { adminAuth } from "@/lib/admin/auth";

export async function GET(request: Request) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);

    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;

    // Sorting parameters
    const orderBy = searchParams.get("orderBy") || "createdAt";
    const order = searchParams.get("order") || "desc";

    // Filter parameters
    const id = searchParams.get("id");
    const userId = searchParams.get("userId");
    const type = searchParams.get("type");
    const status = searchParams.get("status");
    const buyerType = searchParams.get("buyerType");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const minAmount = searchParams.get("minAmount");
    const maxAmount = searchParams.get("maxAmount");

    // Build query conditions
    const conditions = [];

    if (id) {
      conditions.push(eq(orders.id, id));
    }

    if (userId) {
      conditions.push(eq(orders.userId, userId));
    }

    if (type) {
      conditions.push(eq(orders.type, type));
    }

    if (status) {
      // Cast the status string to OrderStatus type
      conditions.push(eq(orders.status, status as OrderStatus));
    }

    if (buyerType) {
      if (buyerType === 'self') {
        // Self orders: buyerId equals userId
        conditions.push(sql`${orders.buyerId} = ${orders.userId}`);
      } else if (buyerType === 'system') {
        // System orders: buyerId does not equal userId
        conditions.push(sql`${orders.buyerId} <> ${orders.userId}`);
      }
    }

    if (startDate) {
      conditions.push(gte(orders.createdAt, new Date(startDate)));
    }

    if (endDate) {
      conditions.push(lte(orders.createdAt, new Date(endDate)));
    }

    if (minAmount) {
      conditions.push(gte(orders.amount, parseInt(minAmount)));
    }

    if (maxAmount) {
      conditions.push(lte(orders.amount, parseInt(maxAmount)));
    }

    // Determine sort column
    let sortColumn;
    switch (orderBy) {
      case "amount":
        sortColumn = orders.amount;
        break;
      case "status":
        sortColumn = orders.status;
        break;
      case "type":
        sortColumn = orders.type;
        break;
      case "updatedAt":
        sortColumn = orders.updatedAt;
        break;
      case "createdAt":
      default:
        sortColumn = orders.createdAt;
        break;
    }

    // Determine sort direction
    const sortDirection = order === "asc" ? asc : desc;

    // Execute query with pagination
    const ordersList = await db.query.orders.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined as any,
      orderBy: [sortDirection(sortColumn)],
      limit,
      offset,
      with: {
        user: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
    });

    // Get total count for pagination
    const countResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(orders)
      .where(conditions.length > 0 ? and(...conditions) : undefined as any);

    const totalCount = countResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      orders: ordersList,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    console.error("[ADMIN_ORDERS_GET]", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
