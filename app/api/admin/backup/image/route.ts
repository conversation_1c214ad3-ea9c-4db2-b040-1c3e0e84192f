import { NextResponse } from "next/server";
import { adminAuth } from "@/lib/admin/auth";
import { createBackupExecution } from "@/lib/backup/async-image-backup";
import { z } from "zod";
import { createLogger } from "@/lib/draw/logger";

import { TIMEOUT_SECONDS } from "@/constants/system";

const logger = createLogger('api-admin-backup-image');

// Validation schema for request body
const BackupRequestSchema = z.object({
  startDate: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "startDate must be a valid date string",
  }),
  endDate: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "endDate must be a valid date string",
  }),
  dryRun: z.boolean().optional(),
  batchSize: z.number().int().positive().optional(),
  skipBackupCheck: z.boolean().optional(),
  timeoutSeconds: z.number().int().min(0).optional(),
});

export async function POST(req: Request) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = BackupRequestSchema.safeParse(body);

    if (!validationResult.success) {
      const validationError = new Error('Validation failed');
      logger.error('Validation error', validationError, { details: validationResult.error.format() });
      return NextResponse.json(
        { error: "Invalid request", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const {
      startDate,
      endDate,
      dryRun = false,
      batchSize = 10,
      skipBackupCheck = false,
      timeoutSeconds = TIMEOUT_SECONDS,
    } = validationResult.data;

    // Parse dates
    const parsedStartDate = new Date(startDate);
    const parsedEndDate = new Date(endDate);

    // Validate date range
    if (parsedStartDate > parsedEndDate) {
      return NextResponse.json(
        { error: "startDate must be before endDate" },
        { status: 400 }
      );
    }

    // Create backup execution
    logger.info('Creating backup execution', { startDate, endDate, dryRun, batchSize, skipBackupCheck, timeoutSeconds });
    const executionId = await createBackupExecution({
      startDate: parsedStartDate,
      endDate: parsedEndDate,
      dryRun,
      batchSize,
      skipBackupCheck,
      timeoutSeconds,
    });

    return NextResponse.json({
      message: "Backup execution created",
      executionId,
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Unknown error');
    logger.error('Error creating backup execution', err, { details: error });
    return NextResponse.json(
      { error: "Internal server error", message: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
