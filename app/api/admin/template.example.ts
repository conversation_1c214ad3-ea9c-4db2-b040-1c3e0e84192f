import { NextResponse } from "next/server";
import { db } from "@/lib/db";
// Import your schema tables as needed
import { users, orders } from "@/lib/db/schema";
// Import drizzle-orm functions for database operations
import { and, eq, desc, asc, sql, gte, lte } from "drizzle-orm";
import { adminAuth } from "@/lib/admin/auth";
// Optional: Import zod for request validation
import { z } from "zod";

/**
 * This is a template for admin API routes
 * Copy this file and modify it for your specific admin API needs
 * All admin routes should use the adminAuth middleware to ensure only admins can access them
 *
 * This template includes examples for:
 * 1. Collection endpoints (GET with pagination, filtering, sorting)
 * 2. Detail endpoints (GET, PUT, DELETE for a specific resource)
 * 3. Request validation using zod
 * 4. Error handling and logging
 *
 * HOW TO USE THIS TEMPLATE:
 * 1. For collection endpoints (e.g., /api/admin/users):
 *    - Copy the entire file to your desired location
 *    - Uncomment and modify the GET and POST handlers as needed
 *    - Update the database queries to match your schema
 *
 * 2. For detail endpoints (e.g., /api/admin/users/[id]):
 *    - Create a new folder with [id] as the name
 *    - Create a route.ts file inside that folder
 *    - Copy the commented detail endpoint handlers (GET, PUT, DELETE)
 *    - Uncomment and modify them as needed
 *
 * 3. For specialized endpoints (e.g., /api/admin/orders/[id]/status):
 *    - Create the appropriate nested folder structure
 *    - Create a route.ts file in the deepest folder
 *    - Implement the specific functionality needed
 */

// ===== COLLECTION ENDPOINT EXAMPLE =====

/**
 * GET handler for collection endpoints
 * Supports pagination, filtering, and sorting
 */
export async function GET(request: Request) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);

    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;

    // Sorting parameters
    const orderBy = searchParams.get("orderBy") || "createdAt";
    const order = searchParams.get("order") || "desc";

    // Filter parameters (customize based on your needs)
    const userId = searchParams.get("userId");
    const status = searchParams.get("status");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    // Build query conditions
    const conditions = [];

    if (userId) {
      conditions.push(eq(users.clerkId, userId));
    }

    if (status) {
      // Cast the status string to the appropriate type (e.g., OrderStatus)
      conditions.push(eq(orders.status, status as any));
    }

    if (startDate) {
      conditions.push(gte(orders.createdAt, new Date(startDate)));
    }

    if (endDate) {
      conditions.push(lte(orders.createdAt, new Date(endDate)));
    }

    // Determine sort column (customize based on your schema)
    let sortColumn;
    switch (orderBy) {
      case "amount":
        sortColumn = orders.amount;
        break;
      case "status":
        sortColumn = orders.status;
        break;
      case "updatedAt":
        sortColumn = orders.updatedAt;
        break;
      case "createdAt":
      default:
        sortColumn = orders.createdAt;
        break;
    }

    // Determine sort direction
    const sortDirection = order === "asc" ? asc : desc;

    // Execute query with pagination
    const items = await db.query.orders.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
      orderBy: [sortDirection(sortColumn)],
      limit,
      offset,
      with: {
        user: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
    });

    // Get total count for pagination
    const countResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(orders)
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const totalCount = countResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      items, // Replace with your actual data key (e.g., orders, users, etc.)
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    console.error("[ADMIN_COLLECTION_GET]", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating new resources
 * Includes request validation with zod
 */
export async function POST(request: Request) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    // Parse request body
    const body = await request.json();

    // Validate request body with zod (customize based on your needs)
    const schema = z.object({
      // Define your schema based on the resource you're creating
      userId: z.string().min(1),
      amount: z.number().positive(),
      description: z.string().min(1),
      // Add more fields as needed
    });

    const validationResult = schema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const validData = validationResult.data;

    // Create the resource in the database
    // Example: Create a new order
    const newItem = await db.insert(orders).values({
      id: `order_${Date.now()}`, // Generate an ID (use your own method)
      userId: validData.userId,
      buyerId: validData.userId, // In this example, the buyer is the same as the user
      type: "credit", // or "debit" depending on your use case
      amount: validData.amount,
      description: validData.description,
      status: "PENDING", // Default status
      // Add more fields as needed
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    return NextResponse.json({
      message: "Resource created successfully",
      item: newItem[0],
    });
  } catch (error) {
    console.error("[ADMIN_COLLECTION_POST]", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

// ===== DETAIL ENDPOINT EXAMPLE =====
// Place this in a [id]/route.ts file

/**
 * GET handler for detail endpoints
 * Retrieves a specific resource by ID
 */
/*
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    const { id } = await params;

    // Get resource details
    const item = await db.query.orders.findFirst({
      where: eq(orders.id, id),
      with: {
        user: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
            avatarUrl: true,
            createdAt: true,
          },
        },
      },
    });

    if (!item) {
      return NextResponse.json(
        { error: "Resource not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      item, // Replace with your actual data key (e.g., order, user, etc.)
    });
  } catch (error) {
    console.error("[ADMIN_DETAIL_GET]", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
*/

/**
 * PUT handler for updating resources
 * Includes request validation with zod
 */
/*
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    const { id } = await params;

    // Parse and validate the request body
    const body = await request.json();
    const schema = z.object({
      // Define your schema based on the fields you allow to be updated
      status: z.enum(["PENDING", "SUCCESS", "FAILED", "REFUND"]).optional(),
      amount: z.number().positive().optional(),
      description: z.string().min(1).optional(),
      // Add more fields as needed
    });

    const validationResult = schema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const validData = validationResult.data;

    // Check if resource exists
    const existingItem = await db.query.orders.findFirst({
      where: eq(orders.id, id),
    });

    if (!existingItem) {
      return NextResponse.json(
        { error: "Resource not found" },
        { status: 404 }
      );
    }

    // Update the resource
    await db
      .update(orders)
      .set({
        ...validData,
        updatedAt: new Date(),
      })
      .where(eq(orders.id, id));

    // Get the updated resource
    const updatedItem = await db.query.orders.findFirst({
      where: eq(orders.id, id),
      with: {
        user: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "Resource updated successfully",
      item: updatedItem,
    });
  } catch (error) {
    console.error("[ADMIN_DETAIL_PUT]", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
*/

/**
 * DELETE handler for removing resources
 */
/*
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    const { id } = await params;

    // Check if resource exists
    const existingItem = await db.query.orders.findFirst({
      where: eq(orders.id, id),
    });

    if (!existingItem) {
      return NextResponse.json(
        { error: "Resource not found" },
        { status: 404 }
      );
    }

    // Delete the resource
    await db
      .delete(orders)
      .where(eq(orders.id, id));

    return NextResponse.json({
      message: "Resource deleted successfully",
    });
  } catch (error) {
    console.error("[ADMIN_DETAIL_DELETE]", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
*/
