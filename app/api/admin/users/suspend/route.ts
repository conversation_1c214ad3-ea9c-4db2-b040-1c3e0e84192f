import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { adminAuth } from "@/lib/admin/auth";
import { createLogger } from "@/lib/draw/logger";
import { z } from "zod";

const logger = createLogger('api-admin-users-suspend');

// Validation schema for user suspension request
const suspendUserSchema = z.object({
  userId: z.string().min(1, "用户ID不能为空"),
});

/**
 * POST handler to suspend a user
 * Sets the user's extra.isSuspended field to true
 */
export async function POST(request: Request) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = suspendUserSchema.safeParse(body);

    if (!validationResult.success) {
      const validationError = new Error('Validation error');
      logger.error('Validation error', validationError, { details: validationResult.error.format() });
      return NextResponse.json(
        { error: "无效的请求数据", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { userId } = validationResult.data;

    // Get the user
    const user = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!user) {
      return NextResponse.json(
        { error: "用户不存在" },
        { status: 404 }
      );
    }

    // Update the user's extra.isSuspended field
    const extra = { ...(user.extra as Record<string, any>), isSuspended: true };

    await db.update(users)
      .set({
        extra,
        updatedAt: new Date()
      })
      .where(eq(users.clerkId, userId));

    logger.info(`User suspended: ${userId}`);

    return NextResponse.json({
      success: true,
      message: "用户已冻结"
    });
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('Error suspending user', errorObj);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler to activate a user
 * Sets the user's extra.isSuspended field to false
 */
export async function DELETE(request: Request) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    // Parse URL to get userId from query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json(
        { error: "用户ID不能为空" },
        { status: 400 }
      );
    }

    // Get the user
    const user = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!user) {
      return NextResponse.json(
        { error: "用户不存在" },
        { status: 404 }
      );
    }

    // Update the user's extra.isSuspended field
    const extra = { ...(user.extra as Record<string, any>), isSuspended: false };

    await db.update(users)
      .set({
        extra,
        updatedAt: new Date()
      })
      .where(eq(users.clerkId, userId));

    logger.info(`User activated: ${userId}`);

    return NextResponse.json({
      success: true,
      message: "用户已激活"
    });
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('Error activating user', errorObj);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
}
