import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, wallets } from "@/lib/db/schema";
import { and, eq, like, desc, asc, sql } from "drizzle-orm";
import { adminAuth } from "@/lib/admin/auth";

export async function GET(request: Request) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);

    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;

    // Sorting parameters
    const orderBy = searchParams.get("orderBy") || "createdAt";
    const order = searchParams.get("order") || "desc";

    // Filter parameters
    const email = searchParams.get("email");
    const userId = searchParams.get("userId");

    // Build query conditions
    const conditions = [];

    if (email) {
      conditions.push(like(users.email, `%${email}%`));
    }

    if (userId) {
      conditions.push(like(users.clerkId, `%${userId}%`));
    }

    // Determine sort column
    let sortColumn;
    switch (orderBy) {
      case "email":
        sortColumn = users.email;
        break;
      case "username":
        sortColumn = users.username;
        break;
      case "updatedAt":
        sortColumn = users.updatedAt;
        break;
      case "createdAt":
      default:
        sortColumn = users.createdAt;
        break;
    }

    // Determine sort direction
    const sortDirection = order === "asc" ? asc : desc;

    // Execute query with pagination
    const usersList = await db.query.users.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
      orderBy: [sortDirection(sortColumn)],
      limit,
      offset,
    });

    // Fetch wallets for these users
    const userIds = usersList.map(user => user.clerkId);
    const userWallets = userIds.length > 0 ? await db.query.wallets.findMany({
      where: userIds.length === 1
        ? eq(wallets.userId, userIds[0])
        : sql`${wallets.userId} IN (${sql.join(userIds, sql`, `)})`
    }) : [];

    // Create a map of userId to wallet
    const walletMap = new Map();
    userWallets.forEach(wallet => {
      walletMap.set(wallet.userId, wallet);
    });

    // Combine users with their wallets
    const usersWithWallets = usersList.map(user => ({
      ...user,
      wallet: walletMap.get(user.clerkId) || null
    }));

    // Get total count for pagination
    const countResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(users)
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const totalCount = countResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      users: usersWithWallets,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    console.error("[ADMIN_USERS_GET]", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
