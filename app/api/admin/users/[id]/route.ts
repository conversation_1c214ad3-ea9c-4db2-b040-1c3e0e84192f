import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, wallets, orders, histories, shares, invitations, invitationUsages } from "@/lib/db/schema";
import { eq, desc, count } from "drizzle-orm";
import { adminAuth } from "@/lib/admin/auth";

export async function GET(
  request: Request, // Not used in this example
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    const { id } = await params;

    // Get user details
    const user = await db.query.users.findFirst({
      where: eq(users.clerkId, id),
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Get user wallet
    const wallet = await db.query.wallets.findFirst({
      where: eq(wallets.userId, id),
    });

    // Get user statistics
    const ordersCount = await db
      .select({ count: count() })
      .from(orders)
      .where(eq(orders.userId, id));

    const historiesCount = await db
      .select({ count: count() })
      .from(histories)
      .where(eq(histories.userId, id));

    const sharesCount = await db
      .select({ count: count() })
      .from(shares)
      .where(eq(shares.userId, id));

    // Get invitation statistics
    const sentInvitationsCount = await db
      .select({ count: count() })
      .from(invitations)
      .where(eq(invitations.referrerId, id));

    const receivedInvitationsCount = await db
      .select({ count: count() })
      .from(invitationUsages)
      .where(eq(invitationUsages.refereeId, id));

    // Get recent orders
    const recentOrders = await db.query.orders.findMany({
      where: eq(orders.userId, id),
      orderBy: [desc(orders.createdAt)],
      limit: 5,
    });

    // Get recent histories
    const recentHistories = await db.query.histories.findMany({
      where: eq(histories.userId, id),
      orderBy: [desc(histories.createdAt)],
      limit: 5,
      with: {
        user: true,
      },
    });

    // Get recent shares
    const recentShares = await db.query.shares.findMany({
      where: eq(shares.userId, id),
      orderBy: [desc(shares.sharedAt)],
      limit: 5,
      with: {
        user: true,
        history: true,
      },
    });

    return NextResponse.json({
      user,
      wallet,
      stats: {
        ordersCount: ordersCount[0]?.count || 0,
        historiesCount: historiesCount[0]?.count || 0,
        sharesCount: sharesCount[0]?.count || 0,
        sentInvitationsCount: sentInvitationsCount[0]?.count || 0,
        receivedInvitationsCount: receivedInvitationsCount[0]?.count || 0,
      },
      recentActivity: {
        orders: recentOrders,
        histories: recentHistories,
        shares: recentShares,
      },
    });
  } catch (error) {
    console.error("[ADMIN_USER_DETAIL_GET]", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
