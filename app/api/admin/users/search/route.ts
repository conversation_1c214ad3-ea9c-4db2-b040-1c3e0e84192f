import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, wallets } from "@/lib/db/schema";
import { and, eq, like, or } from "drizzle-orm";
import { adminAuth } from "@/lib/admin/auth";

export async function GET(request: Request) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("query") || "";
    const limit = parseInt(searchParams.get("limit") || "5");

    if (!query || query.length < 2) {
      return NextResponse.json({
        users: [],
      });
    }

    // Search for users by email, username, or clerkId
    const usersList = await db.query.users.findMany({
      where: or(
        like(users.email, `%${query}%`),
        like(users.username, `%${query}%`),
        like(users.clerkId, `%${query}%`)
      ),
      limit,
    });

    // Get wallets for these users
    const userIds = usersList.map(user => user.clerkId);
    const userWallets = userIds.length > 0 
      ? await db.query.wallets.findMany({
          where: userIds.length === 1
            ? eq(wallets.userId, userIds[0])
            : or(...userIds.map(id => eq(wallets.userId, id)))
        })
      : [];

    // Create a map of userId to wallet
    const walletMap = new Map();
    userWallets.forEach(wallet => {
      walletMap.set(wallet.userId, wallet);
    });

    // Combine users with their wallets
    const usersWithWallets = usersList.map(user => ({
      ...user,
      wallet: walletMap.get(user.clerkId) || { permanentPoints: 0 }
    }));

    return NextResponse.json({
      users: usersWithWallets,
    });
  } catch (error) {
    console.error("[ADMIN_USERS_SEARCH]", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
