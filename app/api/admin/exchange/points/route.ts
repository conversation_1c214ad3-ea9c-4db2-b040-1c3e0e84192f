import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { wallets, orders, histories } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { adminAuth } from "@/lib/admin/auth";
import { z } from "zod";
import { nanoid } from "nanoid";

// Define a schema for validating the request body
const exchangePointsSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  points: z.number().positive("Points must be a positive number"),
  type: z.enum(["refund", "gift", "award", "affiliate", "other"], {
    errorMap: () => ({ message: "Type must be one of: refund, gift, award, affiliate, other" }),
  }),
  sendBy: z.string().default("system"),
  historyId: z.string().optional(),
  orderId: z.string().optional(),
  description: z.string().optional(),
  note: z.string().optional(),
});

export async function POST(request: Request) {
  try {
    // Check if user is authenticated and is an admin
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse; // Return error response if not admin
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = exchangePointsSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const {
      userId,
      points,
      type,
      sendBy,
      historyId,
      orderId: relatedOrderId, // Rename to avoid conflict with generated orderId
      description = `${type} points: ${points}`,
      note
    } = validationResult.data;

    // Start a transaction to ensure atomicity
    const result = await db.transaction(async (tx) => {
      // Check if user exists and get their wallet
      const userWallet = await tx.query.wallets.findFirst({
        where: eq(wallets.userId, userId),
      });

      if (!userWallet) {
        throw new Error("User wallet not found");
      }

      // Calculate new balance
      const oldBalance = userWallet.permanentPoints;
      const newBalance = oldBalance + points;

      // Update wallet
      await tx.update(wallets)
        .set({
          permanentPoints: newBalance,
          updatedAt: new Date(),
        })
        .where(eq(wallets.userId, userId));

      // Create order record
      const newOrderId = nanoid();
      const timestamp = new Date();
      const newOrder = await tx.insert(orders).values({
        id: newOrderId,
        userId,
        buyerId: sendBy,
        type: "credit",
        amount: points,
        description,
        status: "SUCCESS",
        extra: {
          exchangeType: type,
          ...(historyId && { historyId }),
          ...(relatedOrderId && { relatedOrderId }), // Use the renamed variable
          ...(note && { note }),
          pointsExchange: {
            oldBalance,
            newBalance,
            timestamp: timestamp.toISOString(),
          }
        },
        createdAt: timestamp,
        updatedAt: timestamp,
      }).returning();

      // Create transaction record for the new order
      const transactionRecord = {
        id: newOrderId,
        type: "credit",
        amount: points,
        description,
        exchangeType: type,
        timestamp: timestamp.toISOString(),
        userId,
        ...(note && { note }),
      };

      // If historyId is provided, update the history record with transaction info
      if (historyId) {
        const history = await tx.query.histories.findFirst({
          where: eq(histories.id, historyId),
        });

        if (history) {
          const historyExtra = history.extra as Record<string, any>;
          const transactions = historyExtra.transactions || [];

          // Add the new transaction to the transactions array
          transactions.push(transactionRecord);

          // Update the history record
          await tx.update(histories)
            .set({
              extra: {
                ...historyExtra,
                transactions,
              },
              updatedAt: timestamp,
            })
            .where(eq(histories.id, historyId));
        }
      }

      // If relatedOrderId is provided, update the related order record with transaction info
      if (relatedOrderId) {
        const relatedOrder = await tx.query.orders.findFirst({
          where: eq(orders.id, relatedOrderId),
        });

        if (relatedOrder) {
          const orderExtra = relatedOrder.extra as Record<string, any>;
          const transactions = orderExtra.transactions || [];

          // Add the new transaction to the transactions array
          transactions.push(transactionRecord);

          // Update the order record
          await tx.update(orders)
            .set({
              extra: {
                ...orderExtra,
                transactions,
              },
              updatedAt: timestamp,
            })
            .where(eq(orders.id, relatedOrderId));
        }
      }

      // Get the updated wallet
      const updatedWallet = await tx.query.wallets.findFirst({
        where: eq(wallets.userId, userId),
      });

      return {
        order: newOrder[0],
        wallet: updatedWallet,
      };
    });

    return NextResponse.json({
      success: true,
      order: result.order,
      wallet: result.wallet,
    });
  } catch (error) {
    console.error("[EXCHANGE_POINTS_ERROR]", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal Server Error" },
      { status: 500 }
    );
  }
}
