import { NextResponse } from "next/server";
import { adminAuth } from "@/lib/admin/auth";
import { db } from "@/lib/db";
import { invitationUsages, invitations, users } from "@/lib/db/schema";
import { eq, desc, asc, count, and, or } from "drizzle-orm";
import { createLogger } from "@/lib/draw/logger";

const logger = createLogger('api-admin-invitation-usages');

export async function GET(req: Request) {
  try {
    // 检查管理员权限
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse;
    }

    // 解析查询参数
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;
    const orderBy = searchParams.get("orderBy") || "registeredAt";
    const order = searchParams.get("order") || "desc";
    const status = searchParams.get("status") || "";
    const invitationId = searchParams.get("invitationId") || "";
    const refereeId = searchParams.get("refereeId") || "";

    // 构建查询条件
    let whereConditions = [];

    if (status) {
      whereConditions.push(eq(invitationUsages.status, status as any));
    }

    if (invitationId) {
      whereConditions.push(eq(invitationUsages.invitationId, invitationId));
    }

    if (refereeId) {
      whereConditions.push(eq(invitationUsages.refereeId, refereeId));
    }

    // 查询邀请使用记录
    const orderDirection = order === "asc" ? asc : desc;
    let orderByColumn;

    // 确定排序字段
    if (orderBy === "status") {
      orderByColumn = invitationUsages.status;
    } else {
      // 默认按注册时间排序
      orderByColumn = invitationUsages.registeredAt;
    }

    const usages = await db.query.invitationUsages.findMany({
      where: whereConditions.length > 0 ? and(...whereConditions) : undefined,
      with: {
        invitation: true,
        referee: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
            avatarUrl: true,
          },
        },
        operator: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
          },
        },
      },
      orderBy: [orderDirection(orderByColumn)],
      limit: limit,
      offset: offset,
    });

    // 查询已在上面执行

    // 获取总数
    const countResult = await db
      .select({ count: count() })
      .from(invitationUsages)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined);

    const total = countResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      usages,
      pagination: {
        page,
        limit,
        totalCount: total,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error: any) {
    logger.error("获取邀请使用记录失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
