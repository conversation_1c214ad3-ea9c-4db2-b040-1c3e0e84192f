import { NextResponse } from "next/server";
import { adminAuth, isAdmin } from "@/lib/admin/auth";
import { redeemInvitationPointsBatch } from "@/lib/invitation/points";
import { redeemInvitationCashBatch } from "@/lib/invitation/cash";
import { createLogger } from "@/lib/draw/logger";
import { z } from "zod";

const logger = createLogger('api-admin-invitation-batch-redeem');

// 批量兑换请求验证
const batchRedeemSchema = z.object({
  usage_ids: z.array(z.string()).min(1).max(50),
  redeem_type: z.enum(["points", "cash"]),
  note: z.string().optional(),
});

export async function POST(req: Request) {
  try {
    // 检查管理员权限
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse;
    }

    const body = await req.json();

    // 验证请求数据
    const validationResult = batchRedeemSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { usage_ids, redeem_type, note } = validationResult.data;

    // 获取管理员ID
    const adminResult = await isAdmin();

    if (!adminResult.userId) {
      return NextResponse.json(
        { error: "Admin ID not found" },
        { status: 500 }
      );
    }

    // 兑换奖励
    let result;
    if (redeem_type === "points") {
      // 使用积分批量兑换函数
      result = await redeemInvitationPointsBatch(usage_ids, adminResult.userId);
    } else {
      // 管理员可以批量兑换现金奖励
      result = await redeemInvitationCashBatch(usage_ids, adminResult.userId, note);
    }

    return NextResponse.json(result);
  } catch (error: any) {
    logger.error("批量兑换邀请奖励失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
