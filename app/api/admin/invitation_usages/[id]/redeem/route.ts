import { NextResponse } from "next/server";
import { adminAuth, isAdmin } from "@/lib/admin/auth";
import { redeemInvitationPoints } from "@/lib/invitation/points";
import { redeemInvitationCash } from "@/lib/invitation/cash";
import { createLogger } from "@/lib/draw/logger";
import { z } from "zod";

const logger = createLogger('api-admin-invitation-redeem');

// 兑换请求验证
const redeemSchema = z.object({
  redeem_type: z.enum(["points", "cash"]),
  note: z.string().optional(),
});

export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    const authResponse = await adminAuth();
    if (authResponse) {
      return authResponse;
    }

    const { id } = await params;
    if (!id) {
      return NextResponse.json(
        { error: "Missing usage ID" },
        { status: 400 }
      );
    }

    const body = await req.json();

    // 验证请求数据
    const validationResult = redeemSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { redeem_type, note } = validationResult.data;

    // 获取管理员ID
    const adminResult = await isAdmin();

    if (!adminResult.userId) {
      return NextResponse.json(
        { error: "Admin ID not found" },
        { status: 500 }
      );
    }

    // 兑换奖励
    let result;
    if (redeem_type === "points") {
      // 使用积分兑换函数
      result = await redeemInvitationPoints(id, adminResult.userId);
    } else {
      // 管理员可以兑换现金奖励
      result = await redeemInvitationCash(id, adminResult.userId, note);
    }

    return NextResponse.json(result);
  } catch (error: any) {
    logger.error("兑换邀请奖励失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
