import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { shares } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userShares = await db.query.shares.findMany({
      where: eq(shares.userId, userId),
      orderBy: (shares, { desc }) => [desc(shares.createdAt)],
    });

    return NextResponse.json({ shares: userShares });
  } catch (error) {
    console.error("[SHARES_USER_GET]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
