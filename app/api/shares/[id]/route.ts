import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { shares } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { createLogger } from "@/lib/draw/logger";

const logger = createLogger('api-shares');

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { id } = await params;

    const share = await db.query.shares.findFirst({
      where: eq(shares.id, id),
    });

    if (!share) {
      return new NextResponse("Share not found", { status: 404 });
    }

    if (share.userId !== userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    return NextResponse.json(share);
  } catch (error) {
    logger.error("Error fetching share", error instanceof Error ? error : new Error(String(error)), { id: params.then(p => p.id) });
    return new NextResponse("Internal Error", { status: 500 });
  }
}

export async function PATCH(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { isPublic, allowFork } = body;

    // Verify the share exists and belongs to the user
    const { id } = await params;
    const share = await db.query.shares.findFirst({
      where: eq(shares.id, id),
    });

    if (!share) {
      return new NextResponse("Share not found", { status: 404 });
    }

    if (share.userId !== userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Update share
    const [updatedShare] = await db
      .update(shares)
      .set({
        isPublic,
        allowFork,
        updatedAt: new Date(),
      })
      .where(eq(shares.id, id))
      .returning();

    return NextResponse.json(updatedShare);
  } catch (error) {
    logger.error("Error updating share", error instanceof Error ? error : new Error(String(error)), { id: params.then(p => p.id) });
    return new NextResponse("Internal Error", { status: 500 });
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Verify the share exists and belongs to the user
    const { id } = await params;
    const share = await db.query.shares.findFirst({
      where: eq(shares.id, id),
    });

    if (!share) {
      return new NextResponse("Share not found", { status: 404 });
    }

    if (share.userId !== userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Delete the share
    await db.delete(shares).where(eq(shares.id, id));

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    logger.error("Error deleting share", error instanceof Error ? error : new Error(String(error)), { id: params.then(p => p.id) });
    return new NextResponse("Internal Error", { status: 500 });
  }
}
