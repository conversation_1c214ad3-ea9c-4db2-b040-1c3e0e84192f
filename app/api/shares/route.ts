import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { shares, histories } from "@/lib/db/schema";
import { eq, desc, and, asc } from "drizzle-orm";
import { nanoid } from "nanoid";

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { historyId, isPublic = true, allowFork = true } = body;

    if (!historyId) {
      return new NextResponse("History ID is required", { status: 400 });
    }

    // Verify the history exists and belongs to the user
    const history = await db.query.histories.findFirst({
      where: eq(histories.id, historyId),
    });

    if (!history) {
      return new NextResponse("History not found", { status: 404 });
    }

    if (history.userId !== userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if ((history.extra as { isWebp?: boolean })?.isWebp || !history.resultUrl) {
      return new NextResponse("Should not share this image. Cannot share WebP or empty images", { status: 400 });
    }

    // Check if share already exists for this history
    const existingShare = await db.query.shares.findFirst({
      where: eq(shares.historyId, historyId),
    });

    if (existingShare) {
      return new NextResponse("Share already exists for this history", { status: 400 });
    }

    // Create new share
    const shareId = nanoid();
    const [newShare] = await db
      .insert(shares)
      .values({
        id: nanoid(),
        shareId,
        historyId,
        userId,
        imageUrl: history.resultUrl || "",
        viewCount: 0,
        likeCount: 0,
        forkCount: 0,
        forkEarnings: 0,
        isPublic,
        allowFork,
        model: (history.extra as { model?: string })?.model,
        styleId: (history.extra as { style?: string })?.style,
        customPrompt: history.prompt,
        originalImages: (history.extra as { originalImages?: string[] })?.originalImages || [],
      })
      .returning();

    return NextResponse.json(newShare);
  } catch (error) {
    console.error("[SHARES_POST]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}

export async function GET(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const orderBy = searchParams.get("orderBy") || "createdAt";
    const order = searchParams.get("order") || "desc";

    // 构建排序条件
    const orderDirection = order === "asc" ? asc : desc;
    const orderByColumn = orderBy === "createdAt" ? shares.createdAt : shares.updatedAt;

    const userShares = await db.query.shares.findMany({
      where: eq(shares.userId, userId),
      orderBy: [orderDirection(orderByColumn)],
    });

    return NextResponse.json(userShares);
  } catch (error) {
    console.error("[SHARES_GET]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
