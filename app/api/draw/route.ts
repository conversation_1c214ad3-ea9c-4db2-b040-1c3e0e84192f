import {
  Message,
} from "ai";
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";

import { drawModels } from "@/constants/draw/models";
import { getStylePrompt, getStylePara<PERSON>, DRAW_STYLES } from "@/constants/draw";
import {
  MAX_PENDING_REQUESTS,
  MAX_PENDING_REQUESTS_VIP,
} from "@/constants/draw/limits";

import { db } from "@/lib/db";
import { wallets } from "@/lib/db/schema";
import { checkUserPaid, checkUserStatus } from "@/lib/db/user";

import { checkKeywordBlocklist } from "@/lib/blocklist";
import { getModelCreditCost } from "@/lib/ai/models";

import { checkUserPoints } from "@/lib/draw/points";
import { DrawError, DrawErrorType } from "@/lib/draw/error-handler";
import { createLogger } from "@/lib/draw/logger";
import {
  createHistory,
  updateHistory,
  getUserPendingDraws,
} from "@/lib/draw/history";
import { processDrawRequest } from "@/lib/draw/image-requests";

type Attachment = {
  name: string;
  contentType: string;
  url: string;
};

interface ExtendedMessage extends Message {
  experimental_attachments?: Attachment[];
}

export async function POST(req: Request) {
  const session = await auth();
  const userId = session.userId;

  if (!userId) {
    return new Response("未授权", { status: 401 });
  }

  // 检查用户状态
  const { isSuspended } = await checkUserStatus(userId);
  if (isSuspended) {
    return new Response("未授权", { status: 403 });
  }

  const logger = createLogger(userId);
  logger.info("Draw request received");

  try {
    const formData = await req.formData();
    const customPrompt = formData.get("prompt")?.toString() || "";
    const styleId = formData.get("style")?.toString();
    const modelId = formData.get("model")?.toString();

    // Get all image files
    const imageFiles: File[] = [];
    let index = 0;
    while (true) {
      const imageFile = formData.get(`image${index}`) as File | null;
      if (!imageFile) break;
      imageFiles.push(imageFile);
      index++;
    }

    logger.debug("Request parameters", {
      customPrompt,
      styleId,
      modelId,
      imageCount: imageFiles.length,
    });

    // 验证必填参数
    if (!styleId || !modelId) {
      throw DrawError.validation("风格和模型是必填项");
    }

    // 验证提示词或图片至少有一个
    if (!customPrompt && imageFiles.length === 0) {
      throw DrawError.validation("提示词或图片至少需要提供一个");
    }

    // 检查模型是否存在
    const requestedModel = drawModels.find((model) => model.id === modelId);
    if (!requestedModel) {
      throw DrawError.validation("无效的模型");
    }

    // 获取样式名称
    const style = DRAW_STYLES[styleId as keyof typeof DRAW_STYLES];
    if (!style) {
      throw DrawError.validation("无效的风格");
    }

    // 检查积分
    const creditCost = getModelCreditCost(modelId);

    // 检查用户是否有足够的积分
    const hasEnoughPoints = await checkUserPoints({
      userId,
      points: creditCost,
    });

    if (!hasEnoughPoints) {
      throw DrawError.insufficientPoints("积分不足", {
        required: creditCost,
      });
    }

    // 检查用户是否有过多的待处理请求
    const pendingDraws = await getUserPendingDraws(userId);
    // 使用 console.log 确保调试信息能够显示
    console.log(
      `[${new Date().toISOString()}] [DRAW_PENDING_DEBUG] 待处理请求详情:`,
      {
        count: pendingDraws.count,
        totalPointsUsed: pendingDraws.totalPointsUsed,
        pendingHistories: pendingDraws.pendingHistories.map((h) => ({
          id: h.id,
          createdAt: h.createdAt,
        })),
      }
    );

    // 检查用户是否是付费用户，并限制待处理请求数量
    const { isPaid } = await checkUserPaid(userId);
    const maxPendingRequests = isPaid
      ? MAX_PENDING_REQUESTS_VIP
      : MAX_PENDING_REQUESTS;
    // 使用 console.log 确保调试信息能够显示
    console.log(
      `[${new Date().toISOString()}] [DRAW_PAID_DEBUG] 用户付费状态:`,
      {
        userId,
        isPaid,
        maxPendingRequests,
      }
    );

    // 检查提示词是否包含非法关键词
    if (!isPaid && customPrompt) {
      const hasIllegalKeyword = await checkKeywordBlocklist(customPrompt);
      if (hasIllegalKeyword) {
        throw DrawError.validation("提示词中包含非法关键词");
      }
    }

    // 关键调试信息：检查是否超过限制
    console.log(
      `[${new Date().toISOString()}] [DRAW_LIMIT_CHECK] 检查是否超过限制: ${
        pendingDraws.count
      } >= ${maxPendingRequests} = ${pendingDraws.count >= maxPendingRequests}`
    );

    if (pendingDraws.count >= maxPendingRequests) {
      throw DrawError.validation(
        isPaid
          ? `付费用户最多同时拥有 ${maxPendingRequests} 个待处理请求。`
          : `免费用户最多同时拥有 ${maxPendingRequests} 个待处理请求。请等待当前请求完成后再试。`
      );
    }

    // 检查所有待处理请求的积分总和是否超过用户当前积分
    const totalPendingPoints = pendingDraws.totalPointsUsed + creditCost;
    const userWallet = await db.query.wallets.findFirst({
      where: eq(wallets.userId, userId),
    });

    if (userWallet && totalPendingPoints > userWallet.permanentPoints) {
      throw DrawError.insufficientPoints(
        "待处理请求过多。所需总积分超过可用余额。",
        {
          required: totalPendingPoints,
          available: userWallet.permanentPoints,
        }
      );
    }

    // 构建提示词消息
    const stylePrompt = getStylePrompt(styleId as keyof typeof DRAW_STYLES);
    const finalPrompt = customPrompt
      ? `${stylePrompt}. ${customPrompt}`
      : stylePrompt;

    const styleParams = getStyleParams(styleId as keyof typeof DRAW_STYLES);

    const promptMessages: ExtendedMessage[] = [
      {
        id: "draw-prompt",
        role: "user",
        // TODO： moderation:low 后续要放到接口参数中
        content: `${finalPrompt}.<moderation:low>`,
        ...styleParams,
      },
    ];

    // 添加图片附件
    if (imageFiles.length > 0) {
      promptMessages[0].experimental_attachments = await Promise.all(
        imageFiles.map(async (imageFile) => {
          const imageArrayBuffer = await imageFile.arrayBuffer();
          const base64Image = Buffer.from(imageArrayBuffer).toString("base64");
          const dataUrl = `data:${imageFile.type};base64,${base64Image}`;

          return {
            name: imageFile.name,
            contentType: imageFile.type,
            url: dataUrl,
          };
        })
      );
    }

    // 创建历史记录
    const history = await createHistory({
      userId,
      prompt: customPrompt,
      style: styleId,
      model: modelId,
      pointsUsed: creditCost,
      description: {
        model: modelId,
        style: styleId,
        imageCount: imageFiles.length,
        modelName: requestedModel.name,
        styleName: style.name,
        timestamp: new Date().toISOString(),
        customPrompt: customPrompt || undefined,
        ...(imageFiles.length > 0 && {
          originalImages: imageFiles.map((imageFile) => ({
            type: imageFile.type,
            size: imageFile.size,
            name: imageFile.name,
          })),
        }),
      },
      ...(imageFiles.length > 0 && {
        originalImages: imageFiles.map((imageFile) => ({
          type: imageFile.type,
          size: imageFile.size,
          name: imageFile.name,
        })),
      }),
    });

    logger.info("History created", { historyId: history.id });

    // 立即返回历史记录ID
    const response = NextResponse.json({
      historyId: history.id,
      message: "绘图请求已成功提交",
      status: "PENDING",
    });

    // 异步处理绘图请求
    processDrawRequest({
      userId,
      historyId: history.id,
      promptMessages: [
        {
          id: "draw-prompt",
          role: "user",
          content: finalPrompt,
          imageFiles,
          ...(imageFiles.length > 0 && {
            experimental_attachments: await Promise.all(
              imageFiles.map(async (imageFile) => {
                const imageArrayBuffer = await imageFile.arrayBuffer();
                const base64Image =
                  Buffer.from(imageArrayBuffer).toString("base64");
                const dataUrl = `data:${imageFile.type};base64,${base64Image}`;

                return {
                  name: imageFile.name,
                  contentType: imageFile.type,
                  url: dataUrl,
                };
              })
            ),
          }),
        },
      ],
      modelId,
      model: requestedModel,
      creditCost,
      logger,
    });

    return response;
  } catch (error) {
    const isDrawError = error instanceof DrawError;
    const status = isDrawError
      ? error.type === DrawErrorType.VALIDATION
        ? 400
        : error.type === DrawErrorType.INSUFFICIENT_POINTS
        ? 403
        : 500
      : 500;

    logger.error("[DRAW_ERROR]", error as Error, {
      status,
      isDrawError,
    });

    // 如果已经创建了历史记录，但处理过程中出错，更新历史记录状态
    if (typeof history !== "undefined" && history) {
      try {
        const errorMessage = isDrawError ? error.message : "服务器内部错误";
        // 使用类型断言来解决类型问题
        const historyId = (history as any).id;

        await updateHistory({
          historyId,
          status: false,
          drawStatus: "FAILED",
          error: errorMessage,
          drawResult: errorMessage,
        });

        logger.info("[DRAW_ERROR_HISTORY_UPDATED]", {
          historyId,
          errorMessage,
        });
      } catch (updateError) {
        logger.error(
          "[DRAW_ERROR_HISTORY_UPDATE_FAILED]",
          updateError as Error,
          {
            historyId: (history as any).id,
          }
        );
      }
    }

    return new Response(isDrawError ? error.message : "服务器内部错误", {
      status,
    });
  }
}
