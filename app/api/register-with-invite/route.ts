import { NextResponse } from "next/server";
import { createInvitationUsage, validateInviteCode } from "@/lib/invitation";
import { createLogger } from "@/lib/draw/logger";
import { z } from "zod";

const logger = createLogger('api-register-with-invite');

// 注册请求验证
const registerSchema = z.object({
  invite_code: z.string().min(3).max(20),
  user_id: z.string().min(1),
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    
    // 验证请求数据
    const validationResult = registerSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { invite_code, user_id } = validationResult.data;
    
    // 验证邀请码
    const { valid, message } = await validateInviteCode(invite_code);
    if (!valid) {
      return NextResponse.json(
        { error: message },
        { status: 400 }
      );
    }
    
    // 创建邀请使用记录
    const usage = await createInvitationUsage(invite_code, user_id);
    
    return NextResponse.json({
      success: true,
      message: "邀请关联成功",
      usage,
    });
  } catch (error: any) {
    logger.error("注册关联邀请失败", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
