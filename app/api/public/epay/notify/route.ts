import { NextResponse } from 'next/server';
import { PaymentNotificationHandler } from '@/lib/epay/handler';
import { db } from '@/lib/db';
import { orders } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { rechargeUserPoints } from '@/lib/wallet/recharge';
import { updateInvitationUsageAfterRecharge } from '@/lib/invitation';

interface OrderExtra {
  points?: number; // 积分数量
  paymentData?: Record<string, any>;
  price?: number; // 价格（单位：元）
}

interface NotifyResponse {
  code: number;
  msg: string;
  data?: {
    orderId: string;
    points?: number;
    userId?: string;
    status: string;
    walletId?: string;
  };
}

const handler = new PaymentNotificationHandler({
  apiUrl: process.env.PAY_API_URL!,
  pid: process.env.PAY_PID!,
  publicKey: process.env.PAY_PUBLIC_KEY!,
  privateKey: process.env.PAY_MERCHANT_PRIVATE_KEY!,
});

export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const data = Object.fromEntries(url.searchParams.entries());

    // Log the incoming notification data for debugging
    console.log('[NOTIFY_DEBUG] Incoming notification data:', JSON.stringify(data));

    // Always check if out_trade_no exists first
    if (!data.out_trade_no) {
      console.error('[NOTIFY_ERROR] Missing out_trade_no');
      return NextResponse.json({
        code: 400,
        msg: 'Missing out_trade_no'
      });
    }

    // Extract order ID from out_trade_no (format: ORDER_xxx)
    const orderId = data.out_trade_no.replace('ORDER_', '');
    console.log('[NOTIFY_DEBUG] Extracted orderId:', orderId);

    // Start a transaction for order status update
    const result = await db.transaction(async (tx) => {
      // Check if order exists and is not already processed
      const order = await tx.query.orders.findFirst({
        where: and(
          eq(orders.id, orderId),
          eq(orders.status, 'PENDING') // Only process PENDING orders
        )
      });

      if (!order) {
        console.log('[NOTIFY_INFO] Order not found or already processed:', orderId);
        return {
          success: true,
          data: {
            orderId,
            status: 'ALREADY_PROCESSED'
          }
        };
      }

      // Try to verify signature if sign_type is RSA
      let signatureValid = true;
      if (data.sign_type === 'RSA' && data.sign) {
        try {
          signatureValid = await handler.validateSignature(data);
          console.log('[NOTIFY_DEBUG] Signature validation result:', signatureValid);
        } catch (signError) {
          console.error('[NOTIFY_ERROR] Signature validation error:', signError);
          signatureValid = false;
        }
      }

      if (!signatureValid) {
        return {
          success: false,
          error: 'Invalid signature',
          data: {
            orderId,
            status: 'SIGNATURE_INVALID'
          }
        };
      }

      // Update order status
      const orderExtra = order.extra as OrderExtra;
      const updateResult = await tx
        .update(orders)
        .set({
          status: 'SUCCESS',
          paidAt: new Date(),
          tradeNo: data.trade_no,
          extra: {
            ...orderExtra,
            paymentData: data,
          },
        })
        .where(and(
          eq(orders.id, orderId),
          eq(orders.status, 'PENDING') // Ensure we only update PENDING orders
        ));

      // Check if order was actually updated
      if (!updateResult.rowCount || updateResult.rowCount === 0) {
        console.log('[NOTIFY_INFO] Order was already processed by another request');
        return {
          success: true,
          data: {
            orderId,
            status: 'ALREADY_PROCESSED_RACE'
          }
        };
      }

      // If order has points, process the recharge
      const points = orderExtra.points;
      if (points) {
        try {
          const rechargeResult = await rechargeUserPoints({
            userId: order.userId,
            points,
            metadata: { orderId }
          });

          // 更新邀请使用记录（如果有）
          try {
            // 直接使用元作为单位
            const priceInYuan = orderExtra.price || 0; // 单位：元
            const invitationUsage = await updateInvitationUsageAfterRecharge(
              order.userId,
              points,
              priceInYuan // 传入的金额单位为元
            );

            console.log('[INVITATION] Updated invitation usage after recharge:',
              invitationUsage ? invitationUsage.id : 'No invitation found');
          } catch (inviteError) {
            console.error('[INVITATION_ERROR] Failed to update invitation usage:', inviteError);
            // 不影响主流程，继续处理
          }

          return {
            success: true,
            data: {
              orderId,
              status: 'POINTS_CREDITED',
              userId: order.userId,
              points,
              walletId: rechargeResult.walletId
            }
          };
        } catch (rechargeError) {
          console.error('[NOTIFY_ERROR] Failed to recharge points:', rechargeError);
          return {
            success: false,
            error: 'Failed to recharge points',
            data: {
              orderId,
              status: 'RECHARGE_FAILED',
              userId: order.userId,
              points
            }
          };
        }
      }

      return {
        success: true,
        data: {
          orderId,
          status: 'SUCCESS_NO_POINTS',
          userId: order.userId
        }
      };
    });

    // Return JSON response with detailed information
    const response: NotifyResponse = {
      code: result.success ? 0 : 400,
      msg: result.success ? 'success' : (result.error || 'Unknown error'),
      data: result.data
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('[NOTIFY_ERROR]', error);
    return NextResponse.json({
      code: 500,
      msg: 'Internal server error',
      data: { status: 'ERROR' }
    });
  }
}
