import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { histories } from "@/lib/db/schema";
import { and, not, inArray, gte } from "drizzle-orm";
import { drawModels } from "@/constants/draw/models";
import { createLogger } from "@/lib/draw/logger";

// 定义查询结果的类型，只包含我们需要的字段
type HistoryStatusData = {
  status: boolean;
  drawStatus: string;
  extra: { model?: string; [key: string]: any };
};

const logger = createLogger('api-public-status');

export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    // 获取指定小时数之前的时间
    const previousHours = process.env.NEXT_PUBLIC_DEBUG === "on" ? 160 : 12;

    const hoursAgo = new Date();
    hoursAgo.setHours(hoursAgo.getHours() - previousHours);

    // 获取所有非禁用的模型ID
    const enabledModelIds = drawModels
      .filter(model => !model.disabled)
      .map(model => model.id);

    // 只查询必要的字段以节省数据库带宽
    const completedHistories = await db.query.histories.findMany({
      where: and(
        gte(histories.updatedAt, hoursAgo),
        not(inArray(histories.drawStatus, ['PENDING', 'PROCESSING'])),
      ),
      columns: {
        status: true,
        drawStatus: true,
        extra: true,
      },
    }) as HistoryStatusData[];

    // 按模型分组并计算成功率，使用紧凑的数组格式节省带宽
    const modelStats = enabledModelIds.map(modelId => {
      // 过滤出当前模型的历史记录
      const modelHistories = completedHistories.filter(
        history => history.extra?.model === modelId
      );

      // 计算成功率
      const total = modelHistories.length;
      const successful = modelHistories.filter(
        history => history.status && history.drawStatus === 'SUCCESS'
      ).length;

      // 如果没有数据，默认为 -1，表示无数据
      const successRate = total > 0 ? Math.round((successful / total) * 100) : -1;

      // 记录日志，方便调试
      // logger.debug(`Model ${modelId} stats:`, { total, successful, successRate });

      // 返回紧凑格式：[modelId, successRate]
      return [modelId, successRate];
    });

    // 返回紧凑的响应格式，节省带宽
    return NextResponse.json(modelStats);
  } catch (error) {
    logger.error("Error fetching model status", error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
