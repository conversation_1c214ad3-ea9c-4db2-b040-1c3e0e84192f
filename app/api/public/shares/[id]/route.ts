import { NextResponse } from "next/server";
import { getPublicShareById } from "@/lib/sharing/queries";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;

  try {
    const share = await getPublicShareById(id);

    if (!share) {
      return NextResponse.json(
        { data: null, message: "分享不存在", status: 404 }
      );
    }

    if (!share.isPublic) {
      return NextResponse.json(
        { data: null, message: "分享未公开", status: 403 }
      );
    }

    const { user, ...shareData } = share;
    const filteredShareData = share.allowFork
      ? shareData
      : { ...shareData, customPrompt: undefined, originalImages: undefined };
    return NextResponse.json({
      data: {
        ...filteredShareData,
        author: {
          id: user.clerkId,
          name: user.username,
          avatar: user.avatarUrl,
        }
      },
      message: "Success"
    });
  } catch (error) {
    console.error("[PUBLIC_SHARE_GET]", error);
    return NextResponse.json(
      { data: null, message: "Internal server error" },
      { status: 500 }
    );
  }
}
