import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { shares } from "@/lib/db/schema";
import { eq, and, desc, asc, sql } from "drizzle-orm";
import { getTableColumns } from "drizzle-orm";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const model = searchParams.get("model");
    const style = searchParams.get("style");
    const userId = searchParams.get("userId");
    const orderBy = searchParams.get("orderBy") || "sharedAt";
    const order = searchParams.get("order") || "desc";
    const isRandom = searchParams.get("isRandom") === "true";

    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "20", 10);
    const offset = (page - 1) * limit;

    // 构建查询条件
    const conditions = [eq(shares.isPublic, true)];

    if (model && model !== "all") {
      conditions.push(eq(shares.model, model));
    }

    if (style && style !== "all") {
      conditions.push(eq(shares.styleId, style));
    }

    if (userId) {
      conditions.push(eq(shares.userId, userId));
    }

    // 获取所有列，然后排除 customPrompt 和 originalImages
    const { customPrompt, originalImages, ...rest } = getTableColumns(shares);

    // 构建查询
    let publicShares;

    // 如果需要随机排序
    if (isRandom) {
      publicShares = await db
        .select(rest)
        .from(shares)
        .where(and(...conditions))
        .orderBy(sql`RANDOM()`)
        .limit(limit);
    } else {
      const orderDirection = order === "asc" ? asc : desc;
      const orderByColumn = orderBy === "sharedAt" ? shares.sharedAt : shares.createdAt;
      publicShares = await db
        .select(rest)
        .from(shares)
        .where(and(...conditions))
        .orderBy(orderDirection(orderByColumn))
        .limit(limit)
        .offset(offset);
    }

    return NextResponse.json(publicShares);
  } catch (error) {
    console.error("[PUBLIC_SHARES_GET] Error:", error);
    return new NextResponse(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}
