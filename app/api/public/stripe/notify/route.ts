import { NextResponse } from 'next/server';
import Stripe from 'stripe';
import { db } from '@/lib/db';
import { orders } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { rechargeUserPoints } from '@/lib/wallet/recharge';
import { updateInvitationUsageAfterRecharge } from '@/lib/invitation';

import { stripeEnabled } from '@/constants/payment';

// Initialize Stripe client
const stripe = stripeEnabled ? new Stripe(process.env.STRIPE_SECRET_KEY!) : null;
const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

interface OrderExtra {
  points?: number; // 积分数量
  paymentData?: Record<string, any>;
  price?: number; // 价格（单位：元）
}

export async function POST(request: Request) {
  try {
    if (!stripe) {
      return new NextResponse('Stripe is not enabled', { status: 500 });
    }

    // 获取请求体和签名
    const payload = await request.text();
    const signature = request.headers.get('stripe-signature') as string;

    // 记录原始请求信息（用于调试）
    console.log('[STRIPE_WEBHOOK] Received webhook payload:', payload);
    console.log('[STRIPE_WEBHOOK] Signature:', signature);

    // 验证 webhook 签名
    let event;
    try {
      if (endpointSecret) {
        event = stripe.webhooks.constructEvent(payload, signature, endpointSecret);
      } else {
        // 如果没有设置 webhook 密钥，直接解析 payload
        event = JSON.parse(payload);
        console.log('[STRIPE_WEBHOOK] No webhook secret configured, skipping signature verification');
      }
    } catch (err) {
      console.error('[STRIPE_WEBHOOK] Webhook signature verification failed:', err);
      return new NextResponse('Webhook signature verification failed', { status: 400 });
    }

    console.log('[STRIPE_WEBHOOK] Event type:', event.type);
    console.log('[STRIPE_WEBHOOK] Event ID:', event.id);
    console.log('[STRIPE_WEBHOOK] Event created:', new Date(event.created * 1000).toISOString());
    console.log('[STRIPE_WEBHOOK] Event data:', JSON.stringify(event.data, null, 2));

    // 处理不同类型的支付成功事件
    if (event.type === 'checkout.session.completed' || event.type === 'checkout.session.async_payment_succeeded') {
      const session = event.data.object as Stripe.Checkout.Session;
      const orderId = session.metadata?.orderId;

      console.log(`[STRIPE_WEBHOOK] Processing ${event.type} event`);
      console.log('[STRIPE_WEBHOOK] Session ID:', session.id);
      console.log('[STRIPE_WEBHOOK] Session metadata:', session.metadata);
      console.log('[STRIPE_WEBHOOK] Payment status:', session.payment_status);

      if (!orderId) {
        console.error('[STRIPE_WEBHOOK] Missing orderId in session metadata');
        return new NextResponse('Missing orderId', { status: 400 });
      }

      console.log('[STRIPE_WEBHOOK] Processing payment for order:', orderId);

      try {
        const order = await db.query.orders.findFirst({
          where: eq(orders.id, orderId),
        });

        if (!order) {
          console.log('[STRIPE_WEBHOOK] Order not found:', orderId);
          return NextResponse.json({
            code: 400,
            msg: 'Order not found',
            data: {
              orderId,
              status: 'ORDER_NOT_FOUND'
            }
          });
        }

        if (order.status === 'SUCCESS') {
          console.log('[STRIPE_WEBHOOK] Order already processed:', orderId);
          return NextResponse.json({
            code: 0,
            msg: 'success',
            data: {
              orderId,
              status: 'ALREADY_PROCESSED',
              userId: order.userId
            }
          });
        }

        // 更新订单状态
        const orderExtra = order.extra as OrderExtra;
        await db.update(orders)
          .set({
            status: 'SUCCESS',
            paidAt: new Date(),
            tradeNo: session.id,
            extra: {
              ...orderExtra,
              paymentData: {
                ...orderExtra.paymentData,
                type: event.type,
                id: session.id,
                paymentIntent: session.payment_intent as string,
                paymentStatus: session.payment_status,
                customer: session.customer || undefined,
                customerDetails: session.customer_details || undefined,
                amount: session.amount_total || undefined,
                currency: session.currency || undefined,
                processedAt: new Date().toISOString(),
              },
            },
          })
          .where(eq(orders.id, orderId));

        console.log('[STRIPE_WEBHOOK] Order status updated to SUCCESS:', orderId);

        // 只有当支付状态为 'paid' 时才处理积分充值
        if (session.payment_status === 'paid') {
          const points = orderExtra.points;
          if (points) {
            try {
              console.log('[STRIPE_WEBHOOK] Recharging points for user:', order.userId, 'points:', points);

              const rechargeResult = await rechargeUserPoints({
                userId: order.userId,
                points,
                metadata: {
                  orderId,
                  paymentMethod: 'stripe',
                  sessionId: session.id,
                  paymentType: event.type
                }
              });

              console.log('[STRIPE_WEBHOOK] Points recharged successfully:', rechargeResult);

              // 更新邀请使用记录（如果有）
              try {
                const priceInYuan = orderExtra.price || 0;
                await updateInvitationUsageAfterRecharge(
                  order.userId,
                  points,
                  priceInYuan
                );

                console.log('[STRIPE_WEBHOOK] Invitation usage updated successfully');
              } catch (inviteError) {
                console.error('[STRIPE_WEBHOOK] Failed to update invitation usage:', inviteError);
                // 不影响主流程
              }

              return NextResponse.json({
                code: 0,
                msg: 'success',
                data: {
                  orderId,
                  status: 'POINTS_CREDITED',
                  userId: order.userId,
                  points,
                  walletId: rechargeResult.walletId,
                }
              });
            } catch (rechargeError) {
              console.error('[STRIPE_WEBHOOK] Failed to recharge points:', rechargeError);
              return NextResponse.json({
                code: 400,
                msg: 'Failed to recharge points',
                data: {
                  orderId,
                  status: 'RECHARGE_FAILED',
                  userId: order.userId,
                  points,
                }
              });
            }
          }
        } else {
          console.log(`[STRIPE_WEBHOOK] Payment status is '${session.payment_status}', not processing points recharge yet`);
        }

        return NextResponse.json({
          code: 0,
          msg: 'success',
          data: {
            orderId,
            status: 'STATUS_UPDATED',
            userId: order.userId
          }
        });
      } catch (error) {
        console.error('[STRIPE_WEBHOOK] Error processing checkout session:', error);
        return NextResponse.json({
          code: 500,
          msg: 'Internal server error',
          data: {
            orderId,
            status: 'ERROR'
          }
        });
      }
    }
    // 处理 payment_intent.succeeded 事件 - 只记录日志，不处理积分
    else if (event.type === 'payment_intent.succeeded') {
      const paymentIntent = event.data.object as Stripe.PaymentIntent;

      console.log('[STRIPE_WEBHOOK] Received payment_intent.succeeded event');
      console.log('[STRIPE_WEBHOOK] Payment Intent ID:', paymentIntent.id);
      console.log('[STRIPE_WEBHOOK] Payment Intent metadata:', paymentIntent.metadata);
      console.log('[STRIPE_WEBHOOK] Payment Intent amount:', paymentIntent.amount);
      console.log('[STRIPE_WEBHOOK] Payment Intent currency:', paymentIntent.currency);
      console.log('[STRIPE_WEBHOOK] Payment Intent status:', paymentIntent.status);

      // 从 metadata 中获取订单 ID
      let orderId = paymentIntent.metadata?.orderId;

      // 如果 metadata 中没有 orderId，尝试通过 payment_intent ID 查找相关的 Checkout Session
      if (!orderId) {
        console.log('[STRIPE_WEBHOOK] Missing orderId in payment intent metadata, attempting to find related Checkout Session');

        try {
          // 尝试通过 payment_intent ID 查找相关的 Checkout Session
          const sessions = await stripe.checkout.sessions.list({
            payment_intent: paymentIntent.id,
            expand: ['data.metadata']
          });

          console.log('[STRIPE_WEBHOOK] Found sessions:', sessions.data.length);

          if (sessions.data.length > 0) {
            const session = sessions.data[0];
            if (session.metadata?.orderId) {
              orderId = session.metadata.orderId;
              console.log('[STRIPE_WEBHOOK] Found orderId from session:', orderId);
            } else {
              console.log('[STRIPE_WEBHOOK] Session found but no orderId in metadata');
            }
          }
        } catch (sessionError) {
          console.error('[STRIPE_WEBHOOK] Error finding Checkout Session:', sessionError);
        }
      }

      // 如果找到了订单 ID，记录信息但不处理积分
      if (orderId) {
        console.log('[STRIPE_WEBHOOK] Found order:', orderId, 'but not processing points (handled by checkout.session events)');

        // 可以选择更新订单状态，但不处理积分
        try {
          const order = await db.query.orders.findFirst({
            where: eq(orders.id, orderId),
          });

          if (order && order.status !== 'SUCCESS') {
            console.log('[STRIPE_WEBHOOK] Updating order status only (no points processing):', orderId);

            const orderExtra = order.extra as OrderExtra;
            await db.update(orders)
              .set({
                status: 'SUCCESS',
                paidAt: new Date(),
                tradeNo: paymentIntent.id,
                extra: {
                  ...orderExtra,
                  paymentData: {
                    ...orderExtra.paymentData,
                    type: 'payment_intent',
                    id: paymentIntent.id,
                    paymentStatus: paymentIntent.status,
                    amount: paymentIntent.amount,
                    currency: paymentIntent.currency,
                    paymentMethod: paymentIntent.payment_method as string,
                    latestCharge: paymentIntent.latest_charge as string,
                    processedAt: new Date().toISOString(),
                  },
                },
              })
              .where(eq(orders.id, orderId));
          }
        } catch (error) {
          console.error('[STRIPE_WEBHOOK] Error updating order status:', error);
        }
      } else {
        console.log('[STRIPE_WEBHOOK] No order found for payment intent:', paymentIntent.id);
      }

      // 返回成功响应，但不处理积分
      return NextResponse.json({
        code: 0,
        msg: 'success',
        data: {
          status: 'PAYMENT_INTENT_LOGGED',
          paymentIntentId: paymentIntent.id,
          orderId: orderId || 'unknown'
        }
      });
    }

    // 对于其他事件类型，提供更详细的日志并返回成功
    if (event.type === 'charge.updated') {
      // 这是一个常见的事件，Stripe 在支付处理过程中会发送多次
      console.log('[STRIPE_WEBHOOK] Received charge.updated event - this is normal and can be safely ignored');
      const charge = event.data.object as Stripe.Charge;
      console.log('[STRIPE_WEBHOOK] Charge ID:', charge.id);
      console.log('[STRIPE_WEBHOOK] Charge status:', charge.status);
      console.log('[STRIPE_WEBHOOK] Payment intent:', charge.payment_intent);
    } else {
      // 其他未处理的事件类型
      console.log('[STRIPE_WEBHOOK] Received unhandled event type:', event.type);
      console.log('[STRIPE_WEBHOOK] Event data:', JSON.stringify(event.data.object, null, 2).substring(0, 500) + '...');
    }

    // 对所有未专门处理的事件类型都返回成功
    return new NextResponse('Received', { status: 200 });
  } catch (error) {
    console.error('[STRIPE_WEBHOOK] Error:', error);
    console.error('[STRIPE_WEBHOOK] Error stack:', (error as Error).stack);
    return new NextResponse('Internal server error', { status: 500 });
  }
}
