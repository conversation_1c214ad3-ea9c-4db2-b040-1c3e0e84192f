import { NextResponse } from "next/server";
import { fixStuckBackupExecutions } from "@/lib/backup/async-image-backup";
import { createLogger } from "@/lib/draw/logger";

import { TIMEOUT_SECONDS } from "@/constants/system";

const logger = createLogger('api-public-backup-fix');

export async function GET(req: Request) {
  try {
    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const secret = searchParams.get('secret');
    const maxAgeMinutes = searchParams.get("maxAgeMinutes")
      ? parseInt(searchParams.get("maxAgeMinutes") || TIMEOUT_SECONDS.toString(), 10)
      : 30;
    const authHeader = req.headers.get('Authorization');

    // Check for CRON_SECRET if provided in environment
    if (process.env.CRON_SECRET &&
        authHeader !== `Bearer ${process.env.CRON_SECRET}` &&
        secret !== process.env.CRON_SECRET) {
      logger.warn('Unauthorized access attempt', { ip: req.headers.get('x-forwarded-for') });
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Fix stuck backup executions
    logger.info('Checking for stuck backup executions', { maxAgeMinutes });
    const fixedCount = await fixStuckBackupExecutions(maxAgeMinutes);

    return NextResponse.json({
      message: `Fixed ${fixedCount} stuck backup executions`,
      fixedCount,
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Unknown error');
    logger.error('Error fixing stuck backup executions', err, { details: error });
    return NextResponse.json(
      { error: "Internal server error", message: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
