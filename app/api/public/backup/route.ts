import { NextResponse } from "next/server";
import { createPreviousDayBackupExecution, createHourlyBackupExecution } from "@/lib/backup/async-image-backup";
import { createLogger } from "@/lib/draw/logger";

const logger = createLogger('api-public-backup');

export async function GET(req: Request) {
  try {
    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const secret = searchParams.get('secret');
    const batchSize = searchParams.get('batchSize') ? parseInt(searchParams.get('batchSize') || '10', 10) : 10;
    const skipBackupCheck = searchParams.get('skipBackupCheck') === 'true';
    const type = searchParams.get('type') || 'daily'; // Default to daily if not specified
    const authHeader = req.headers.get('Authorization');

    // Check for CRON_SECRET if provided in environment
    if (process.env.CRON_SECRET &&
        authHeader !== `Bearer ${process.env.CRON_SECRET}` &&
        secret !== process.env.CRON_SECRET) {
      logger.warn('Unauthorized access attempt', { ip: req.headers.get('x-forwarded-for') });
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    let executionId: string | null;
    let backupType = type;

    // Create backup execution based on type
    if (type === 'hourly') {
      logger.info('Creating hourly backup execution', { batchSize, skipBackupCheck });
      executionId = await createHourlyBackupExecution(batchSize, skipBackupCheck);

      if (executionId === null) {
        // A successful execution already exists for this time range
        return NextResponse.json({
          message: "Hourly backup skipped - a successful execution already exists for the last 80 minutes",
          skipped: true,
        });
      }
    } else {
      // Default to daily backup
      logger.info('Creating daily backup execution', { batchSize, skipBackupCheck });
      executionId = await createPreviousDayBackupExecution(batchSize, skipBackupCheck);

      if (executionId === null) {
        // A successful execution already exists for this date range
        return NextResponse.json({
          message: "Daily backup skipped - a successful execution already exists for yesterday",
          skipped: true,
        });
      }
    }

    return NextResponse.json({
      message: `${backupType === 'hourly' ? 'Hourly' : 'Daily'} backup execution created`,
      executionId,
      type: backupType,
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Unknown error');
    logger.error('Error creating backup execution', err, { details: error });
    return NextResponse.json(
      { error: "Internal server error", message: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
