import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { orders } from "@/lib/db/schema";
import { and, eq, lt } from "drizzle-orm";
import { fixStuckBackupExecutions, fixPendingBackupExecutions } from "@/lib/backup/async-image-backup";
import { createLogger } from "@/lib/draw/logger";
import { clearAllUsersPendingDraws } from "@/lib/draw/history";
import { deleteTmpObjectsOlderThan } from "@/lib/storage/r2-client";

import { TIMEOUT_SECONDS } from "@/constants/system";

const logger = createLogger('api-public-clear');

export async function GET(request: Request) {
  try {
    // Verify CRON_SECRET from Authorization header or query parameter
    const { searchParams } = new URL(request.url);
    const secret = searchParams.get('secret');
    const authHeader = request.headers.get('Authorization');

    if (process.env.CRON_SECRET &&
        authHeader !== `Bearer ${process.env.CRON_SECRET}` &&
        secret !== process.env.CRON_SECRET) {
      logger.warn('Unauthorized access attempt', { ip: request.headers.get('x-forwarded-for') });
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Calculate timestamp for 5 minutes ago
    const fiveMinutesAgo = new Date(Date.now() - TIMEOUT_SECONDS * 1000);

    // Update all PENDING orders older than 5 minutes to FAILED
    const result = await db
      .update(orders)
      .set({ status: "FAILED" })
      .where(
        and(
          eq(orders.status, "PENDING"),
          lt(orders.createdAt, fiveMinutesAgo)
        )
      );

    // Get parameters for backup executions fix
    const maxRunningAgeMinutes = searchParams.get('maxRunningAgeMinutes') ? parseInt(searchParams.get('maxRunningAgeMinutes') || '30', 10) : 30;
    const maxPendingAgeMinutes = searchParams.get('maxPendingAgeMinutes') ? parseInt(searchParams.get('maxPendingAgeMinutes') || '60', 10) : 60;

    // Fix stuck backup executions (RUNNING state)
    logger.info('Checking for stuck backup executions', { maxRunningAgeMinutes });
    const stuckFixedCount = await fixStuckBackupExecutions(maxRunningAgeMinutes);

    // Fix pending backup executions (PENDING state)
    logger.info('Checking for pending backup executions', { maxPendingAgeMinutes });
    const pendingFixedCount = await fixPendingBackupExecutions(maxPendingAgeMinutes);

    // Clear stale pending draw requests
    logger.info('Clearing stale pending draw requests');
    const clearedDrawsCount = await clearAllUsersPendingDraws();

    // Clear tmp directory files older than 30 minutes
    const tmpCleanupMinutes = searchParams.get('tmpCleanupMinutes') ? parseInt(searchParams.get('tmpCleanupMinutes') || '30', 10) : 30;
    logger.info('Clearing tmp directory files', { tmpCleanupMinutes });
    const deletedTmpFilesCount = await deleteTmpObjectsOlderThan(tmpCleanupMinutes);

    return NextResponse.json({
      success: true,
      message: "Maintenance tasks completed successfully",
      details: {
        clearedOrders: "Cleared pending orders older than 5 minutes",
        fixedStuckExecutions: `Fixed ${stuckFixedCount} stuck backup executions`,
        fixedPendingExecutions: `Fixed ${pendingFixedCount} pending backup executions`,
        clearedDraws: `Cleared ${clearedDrawsCount} stale pending draw requests`,
        deletedTmpFiles: `Deleted ${deletedTmpFilesCount} tmp files older than ${tmpCleanupMinutes} minutes`
      }
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Unknown error');
    logger.error('Error in maintenance tasks', err, { details: error });
    return NextResponse.json(
      { error: "Internal server error", message: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
