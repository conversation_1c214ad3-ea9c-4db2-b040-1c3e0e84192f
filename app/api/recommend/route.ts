import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { shares, histories, users } from "@/lib/db/schema";
import { eq, and, desc, asc, sql, ne } from "drizzle-orm";
import { getTableColumns } from "drizzle-orm";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const styleId = searchParams.get("styleId");
    const styleLimit = parseInt(searchParams.get("styleLimit") || "8", 10);
    const totalLimit = parseInt(searchParams.get("totalLimit") || "8", 10);
    const isRandom = searchParams.get("isRandom") === "true";

    // 获取所有列，然后排除 customPrompt 和 originalImages
    const { customPrompt, originalImages, ...shareColumns } = getTableColumns(shares);

    // 构建查询条件
    const styleConditions = [eq(shares.isPublic, true)];
    if (styleId) {
      styleConditions.push(eq(shares.styleId, styleId));
    }

    // 获取指定风格的分享
    let styleShares;
    if (isRandom) {
      styleShares = await db
        .select(shareColumns)
        .from(shares)
        .where(and(...styleConditions))
        .orderBy(sql`RANDOM()`)
        .limit(styleLimit);
    } else {
      styleShares = await db
        .select(shareColumns)
        .from(shares)
        .where(and(...styleConditions))
        .orderBy(desc(shares.sharedAt))
        .limit(styleLimit);
    }

    // 如果指定了风格但获取的分享不足，则获取其他风格的分享补充
    let combinedShares = [...styleShares];

    // 如果需要随机排序，对当前结果进行随机排序
    if (isRandom && combinedShares.length > 1) {
      // 使用 Fisher-Yates 洗牌算法对结果进行随机排序
      for (let i = combinedShares.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [combinedShares[i], combinedShares[j]] = [combinedShares[j], combinedShares[i]];
      }
    }

    if (styleId && styleShares.length < totalLimit) {
      const otherConditions = [
        eq(shares.isPublic, true),
        ne(shares.styleId, styleId)
      ];

      let otherShares;
      if (isRandom) {
        otherShares = await db
          .select(shareColumns)
          .from(shares)
          .where(and(...otherConditions))
          .orderBy(sql`RANDOM()`)
          .limit(totalLimit - styleShares.length);
      } else {
        otherShares = await db
          .select(shareColumns)
          .from(shares)
          .where(and(...otherConditions))
          .orderBy(desc(shares.sharedAt))
          .limit(totalLimit - styleShares.length);
      }

      // 合并结果
      combinedShares = [...styleShares, ...otherShares];

      // 如果需要随机排序，对合并后的结果再次随机排序
      if (isRandom && combinedShares.length > 0) {
        // 使用 Fisher-Yates 洗牌算法对合并后的结果进行随机排序
        for (let i = combinedShares.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [combinedShares[i], combinedShares[j]] = [combinedShares[j], combinedShares[i]];
        }
      }
    }

    // 为每个分享获取历史记录信息
    const sharesWithHistory = await Promise.all(
      combinedShares.map(async (share) => {
        const historyResult = await db.query.histories.findFirst({
          where: eq(histories.id, share.historyId),
          columns: {
            id: true,
            status: true,
            resultUrl: true,
            prompt: true,
            pointsUsed: true,
            createdAt: true,
            parameters: true,
            extra: true
          }
        });

        let processedHistory = null;
        if (historyResult) {
          // 创建一个新的对象，而不是修改原始对象
          const historyExtra = historyResult.extra as Record<string, any> || {};

          processedHistory = {
            ...historyResult,
            prompt: share.allowFork ? historyResult.prompt : "",
            extra: {
              ...historyExtra,
              // 隐藏积分消费记录
              pointsConsumption: {},
              // 如果分享不允许 Fork，则隐藏原始图片和自定义提示词
              originalImages: share.allowFork ? historyExtra.originalImages || [] : [],
              customPrompt: share.allowFork ? historyExtra.customPrompt || "" : "",
            }
          };
        }

        return {
          ...share,
          history: processedHistory,
        };
      })
    );

    return NextResponse.json(sharesWithHistory);
  } catch (error) {
    console.error("[RECOMMEND_GET] Error:", error);
    return new NextResponse(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}
