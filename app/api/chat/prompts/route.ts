import {
  createDataStreamResponse,
  smoothStream,
  streamText,
} from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

import { DRAW_STYLES } from '@/constants/draw';
import { chatModels } from '@/constants/chat/models';

import { myProvider } from '@/lib/ai/models';
import { getPromptTemplateByType, PromptType } from '@/lib/chat/prompt-templates';
import { checkUserPoints, deductPoints } from '@/lib/draw/points';

export const runtime = 'edge';

export async function POST(req: NextRequest) {
  try {
    // 验证用户身份
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    // 获取请求数据
    const {
      model,
      styleId,
      customPrompt,
      requirements,
      type,
    } = await req.json();

    // 验证必要参数
    if (!model || !styleId || !type) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      );
    }

    // 验证模型是否存在
    const modelConfig = chatModels.find(m => m.id === model);
    if (!modelConfig) {
      return NextResponse.json(
        { error: '无效的模型' },
        { status: 400 }
      );
    }

    // 验证风格是否存在
    const styleInfo = DRAW_STYLES[styleId as keyof typeof DRAW_STYLES];
    if (!styleInfo) {
      return NextResponse.json(
        { error: '无效的风格' },
        { status: 400 }
      );
    }

    // 检查用户是否有足够的积分
    const pointsRequired = modelConfig.points;
    const hasEnoughPoints = await checkUserPoints({
      userId,
      points: pointsRequired,
    });

    if (!hasEnoughPoints) {
      return NextResponse.json(
        { error: '积分不足，无法使用该模型生成提示词' },
        { status: 403 }
      );
    }

    // 获取提示词模板
    const systemPrompt = getPromptTemplateByType(type as PromptType, {
      styleId: styleId as keyof typeof DRAW_STYLES,
      styleInfo,
      customPrompt: customPrompt || '',
      requirements: requirements || '',
    });

    // 使用 createDataStreamResponse 创建流式响应
    return createDataStreamResponse({
      execute: async (dataStream) => {
        // 扣除用户积分
        try {
          await deductPoints({
            userId,
            points: pointsRequired,
            description: `使用 ${modelConfig.name} 生成提示词`,
            model: model,
          });

          console.log(`[PROMPT_POINTS_DEDUCTED] 成功扣除积分: ${pointsRequired}，用户ID: ${userId}`);
        } catch (error) {
          console.error('[PROMPT_POINTS_ERROR] 扣除积分失败:', error);
          throw new Error('扣除积分失败，请重试');
        }

        const result = streamText({
          model: myProvider.languageModel(model),
          system: systemPrompt,
          messages: [{ role: 'user', content: '请根据我的要求生成提示词' }],
          maxTokens: modelConfig.maxTokens || 4000,
          temperature: 0.7,
          topP: 0.9,
          experimental_transform: smoothStream({ chunking: "word" }),
          onFinish: async () => {
            // 处理完成事件（如果需要）
            console.log('[PROMPT_GENERATION_COMPLETED] 生成提示词完成');
          },
          experimental_telemetry: {
            isEnabled: true,
            functionId: "stream-text",
          },
        });

        result.mergeIntoDataStream(dataStream, {
          sendReasoning: false,
        });
      },
      onError: (error) => {
        console.error("生成提示词时出错:", error);
        return "生成提示词失败，请重试";
      },
    });
  } catch (error) {
    console.error('生成提示词时出错:', error);
    return NextResponse.json(
      { error: '生成提示词失败' },
      { status: 500 }
    );
  }
}
