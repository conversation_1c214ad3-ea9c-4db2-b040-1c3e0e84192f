"use client";

import { useEffect, use } from "react";
import { useRouter } from "next/navigation";

export default function InvitePage({
  params,
}: {
  params: Promise<{ inviteCode: string }>;
}) {
  const resolvedParams = use(params);
  const { inviteCode } = resolvedParams;
  const router = useRouter();

  useEffect(() => {
    console.log(`邀请码: ${inviteCode}`);

    // 存储邀请码到 localStorage
    if (inviteCode) {
      localStorage.setItem("inviteCode", inviteCode);
      console.log(`邀请码 ${inviteCode} 已保存到 localStorage`);
    }

    // 立即跳转到注册页面
    router.push("/register");
  }, [inviteCode, router]);

  // 简洁的加载界面，用户几乎看不到这个页面，因为会立即跳转
  return (
    <div className="flex justify-center items-center h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p className="text-gray-600">正在跳转到注册页面...</p>
      </div>
    </div>
  );
}
