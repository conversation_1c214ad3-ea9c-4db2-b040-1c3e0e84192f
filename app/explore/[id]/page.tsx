"use client";

import { useEffect, useState, use } from "react";
import { Navbar } from "@/components/global/navbar";
import { Footer } from "@/components/global/footer";
import { ShareDetail } from "@/components/explore/share-detail";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent } from "@/components/ui/card";

export default function ShareDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = use(params);
  const [loading, setLoading] = useState(true);
  const { id } = resolvedParams;

  useEffect(() => {
    setLoading(false);
  }, [id]);

  if (loading) {
    return (
      <>
        <main className="max-w-[75rem] w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid gap-4 pb-4">
            <Navbar />
            <div className="py-4 px-2 md:px-4 relative">
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <Skeleton className="h-8 w-1/4" />
                    <Skeleton className="h-24 w-24 rounded-md" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-1/2" />
                      <Skeleton className="h-4 w-1/3" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
        <Footer />
      </>
    );
  }

  return (
    <>
      <main className="max-w-[75rem] w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid gap-4 pb-4">
          <Navbar />
          <div className="py-4 px-2 md:px-4 relative">
            <ShareDetail id={id} />
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
