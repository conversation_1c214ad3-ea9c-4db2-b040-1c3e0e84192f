import { Suspense } from "react";
import { Metada<PERSON> } from "next";
import { Navbar } from "@/components/global/navbar";
import { Footer } from "@/components/global/footer";
import { Explore } from "@/components/explore";
import { getPageMetadata } from "@/constants";

export const metadata: Metadata = getPageMetadata("探索", "探索AI创作的精美图片，获取灵感");

export default function ExplorePage() {
  return (
    <>
      <main className="max-w-[75rem] w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid gap-4 pb-4">
          <Navbar />
          <div className="py-4 px-2 md:px-4 relative">
            <Suspense fallback={<div>Loading...</div>}>
              <Explore />
            </Suspense>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
