const fs = require('fs');
const path = require('path');

// 读取配置文件
function loadConfig() {
  const defaultConfig = {
    PM2_INSTANCES: 1,
    PM2_MAX_MEMORY: '1G',
    PM2_NODE_ARGS: '--max-old-space-size=2048',
    PM2_PORT: 3000,
    PM2_ENV: 'production',
    APP_NAME: 'image-render',
    RESTART_DELAY: 4000,
    MAX_RESTARTS: 10,
    MIN_UPTIME: '10s',
    ENABLE_MONITORING: true,
    ENV_FILE: '.env.local'
  };

  let config = { ...defaultConfig };

  // 尝试读取 .conf.local 文件
  const confLocalPath = path.join(__dirname, '.conf.local');
  if (fs.existsSync(confLocalPath)) {
    try {
      const confContent = fs.readFileSync(confLocalPath, 'utf8');
      const confLines = confContent.split('\n');

      for (const line of confLines) {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            const value = valueParts.join('=').trim();
            config[key.trim()] = value;
          }
        }
      }
    } catch (error) {
      console.warn('警告: 无法读取 .conf.local 文件:', error.message);
    }
  }

  // 尝试读取 .conf 文件作为备用
  const confPath = path.join(__dirname, '.conf');
  if (fs.existsSync(confPath)) {
    try {
      const confContent = fs.readFileSync(confPath, 'utf8');
      const confLines = confContent.split('\n');

      for (const line of confLines) {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            const value = valueParts.join('=').trim();
            // 只有在 .conf.local 中没有设置时才使用 .conf 的值
            if (config[key.trim()] === defaultConfig[key.trim()]) {
              config[key.trim()] = value;
            }
          }
        }
      }
    } catch (error) {
      console.warn('警告: 无法读取 .conf 文件:', error.message);
    }
  }

  return config;
}

const config = loadConfig();

module.exports = {
  apps: [
    {
      name: config.APP_NAME,
      script: 'npm',
      args: 'start',
      instances: config.PM2_INSTANCES,
      exec_mode: 'cluster',
      node_args: config.PM2_NODE_ARGS,
      env: {
        NODE_ENV: config.PM2_ENV,
        PORT: parseInt(config.PM2_PORT),
        HOST: '0.0.0.0'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: parseInt(config.PM2_PORT),
        HOST: '0.0.0.0'
      },

      // 稳定性配置
      max_restarts: parseInt(config.MAX_RESTARTS),
      min_uptime: config.MIN_UPTIME,
      max_memory_restart: config.PM2_MAX_MEMORY,
      restart_delay: parseInt(config.RESTART_DELAY),

      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,

      // 监控配置
      pmx: config.ENABLE_MONITORING === 'true',

      // 自动重启条件
      watch: false,
      ignore_watch: ['node_modules', 'logs'],

      // 其他配置
      autorestart: true,
      max_memory_restart: config.PM2_MAX_MEMORY,

      // 进程管理
      wait_ready: true,

      // 错误处理
      crash_cleanup: true,
      force: true,

      // 环境变量
      env_file: config.ENV_FILE
    }
  ]
};
