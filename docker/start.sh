#!/bin/sh

# Docker container startup script
# This script replaces runtime placeholders, runs database migrations, and starts the Next.js server

set -e

echo "Starting container..."
echo "Node version: $(node --version)"
echo "NPM version: $(npm --version)"

# Replace runtime placeholders in built files
echo "Replacing runtime environment variables..."

# Create a function to safely replace placeholders
replace_placeholder() {
    local placeholder="$1"
    local value="$2"
    
    if [ -n "$value" ]; then
        echo "Replacing $placeholder with runtime value"
        # Replace in all JavaScript files in .next directory
        find .next -name "*.js" -type f -exec sed -i "s|$placeholder|$value|g" {} + 2>/dev/null || true
    else
        echo "Warning: $placeholder not provided, keeping placeholder"
    fi
}

# Replace key placeholders with runtime values
replace_placeholder "pk_test_dummydummydummydummydummydummydummydummydummydummy" "$NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"
replace_placeholder "http://localhost:3000" "$NEXT_PUBLIC_APP_URL"

echo "Runtime variable replacement completed."

# Check if database connection is available before running migrations
if [ -n "$DATABASE_URL" ]; then
    echo "Running database migrations..."
    npm run db:update
    echo "Database migrations completed."
else
    echo "Warning: DATABASE_URL not set, skipping database migrations."
fi

# Start the Next.js server
echo "Starting Next.js server..."
exec node server.js