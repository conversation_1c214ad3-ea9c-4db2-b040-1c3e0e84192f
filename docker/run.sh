#!/bin/bash

# Docker container runner script
# This script reads configuration from .conf and .env.local files and runs the container

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONF_FILE="$SCRIPT_DIR/.conf"
ENV_FILE="$SCRIPT_DIR/.env.local"

# Check if configuration files exist
if [ ! -f "$CONF_FILE" ]; then
    echo "Error: .conf file not found at $CONF_FILE"
    exit 1
fi

if [ ! -f "$ENV_FILE" ]; then
    echo "Error: .env.local file not found at $ENV_FILE"
    echo "Please create $ENV_FILE based on .env.example"
    exit 1
fi

# Read configuration from .conf file
source "$CONF_FILE"

# Validate required configuration
if [ -z "$CONTAINER_NAME" ]; then
    echo "Error: CONTAINER_NAME not set in .conf"
    exit 1
fi

if [ -z "$IMAGE_NAME" ]; then
    echo "Error: IMAGE_NAME not set in .conf"
    exit 1
fi

if [ -z "$PORT_MAPPING" ]; then
    echo "Error: PORT_MAPPING not set in .conf"
    exit 1
fi

echo "Starting Docker container with configuration:"
echo "  Container name: $CONTAINER_NAME"
echo "  Image: $IMAGE_NAME"
echo "  Port mapping: $PORT_MAPPING"
echo "  Environment file: $ENV_FILE"

# Check if image exists locally
if ! docker image inspect "$IMAGE_NAME" >/dev/null 2>&1; then
    echo "⚠️  Image not found locally: $IMAGE_NAME"
    echo "   Run ./update.sh first to pull the latest image, or build it locally:"
    echo "   docker build -t $IMAGE_NAME ."
    exit 1
fi

echo "Using image: $IMAGE_NAME"

# Stop and remove existing container if it exists
if docker ps -a --format 'table {{.Names}}' | grep -q "^$CONTAINER_NAME$"; then
    echo "Stopping existing container: $CONTAINER_NAME"
    docker stop "$CONTAINER_NAME" || true
    echo "Removing existing container: $CONTAINER_NAME"
    docker rm "$CONTAINER_NAME" || true
fi

# Build docker run command
DOCKER_CMD="docker run -d"
DOCKER_CMD="$DOCKER_CMD --name $CONTAINER_NAME"
DOCKER_CMD="$DOCKER_CMD -p $PORT_MAPPING"
DOCKER_CMD="$DOCKER_CMD --env-file $ENV_FILE"

# Add optional configurations
if [ -n "$NETWORK_MODE" ]; then
    DOCKER_CMD="$DOCKER_CMD --network $NETWORK_MODE"
fi

if [ -n "$RESTART_POLICY" ]; then
    DOCKER_CMD="$DOCKER_CMD --restart $RESTART_POLICY"
fi

if [ -n "$DOCKER_OPTS" ]; then
    DOCKER_CMD="$DOCKER_CMD $DOCKER_OPTS"
fi

DOCKER_CMD="$DOCKER_CMD $IMAGE_NAME"

# Run the container
echo "Running command: $DOCKER_CMD"
eval "$DOCKER_CMD"

echo "Container $CONTAINER_NAME started successfully!"
echo "You can view logs with: docker logs -f $CONTAINER_NAME"
echo "You can stop the container with: docker stop $CONTAINER_NAME"