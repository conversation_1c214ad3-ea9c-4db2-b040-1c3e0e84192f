# Docker Configuration

This directory contains Docker-related configuration files for the Next.js application.

## Files

- `.conf` - Docker container configuration (ports, name, etc.)
- `start.sh` - Production container startup script that builds, runs migrations, and starts the server
- `dev-start.sh` - Development container startup script that runs migrations and starts dev server
- `run.sh` - Container runner script that reads configuration and manages container lifecycle
- `update.sh` - Update script that pulls the latest image (does not start container)

## Setup

### 1. Create Environment File

Create `docker/.env.local` based on the root `.env.example`:

```bash
# Copy the example file
cp .env.example docker/.env.local

# Edit with your actual values
nano docker/.env.local
```

**Important:** Do not use quotes around values in `.env.local`:
```bash
# ❌ Wrong (with quotes)
DATABASE_URL="******************************"

# ✅ Correct (without quotes)  
DATABASE_URL=******************************
```

### 2. Configure Docker Settings

Edit `docker/.conf` if needed to customize:
- Container name
- Port mapping
- Image name
- Network settings
- Restart policy

## Usage

### Building the Docker Image

```bash
# Build the image locally
docker build -t image-render .

# Build with specific tag
docker build -t image-render:latest .
```

### Using GitHub Container Registry Images

If using images from GHCR, you need to login first:

```bash
# Login to GitHub Container Registry
docker login ghcr.io -u YOUR_GITHUB_USERNAME
# Enter your GitHub Personal Access Token when prompted

# The run.sh script will automatically pull the image
```

**Creating a GitHub Personal Access Token:**
1. Go to GitHub Settings → Developer settings → Personal access tokens
2. Generate a new token with `read:packages` permission
3. Use this token as the password when logging in to GHCR

### Running the Container

```bash
# Use the automated runner script (recommended)
cd docker && ./run.sh

# Manual docker run (alternative)
docker run -p 3000:3000 \
  --env-file docker/.env.local \
  --name image-render \
  image-render

# Development mode
docker run -p 3000:3000 \
  --env-file docker/.env.local \
  --name image-render-dev \
  image-render /app/dev-start.sh
```

### Environment Variables

Create `docker/.env.local` with all environment variables from `.env.example`:

- `DATABASE_URL` - PostgreSQL connection string
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` - Clerk publishable key
- `CLERK_SECRET_KEY` - Clerk secret key
- `NEXT_PUBLIC_APP_URL` - Application URL
- All other API keys and configuration

## Container Management

```bash
# Update to latest image (pull only)
./update.sh

# Run container (stops existing container first)
./run.sh

# Update and run in one go
./update.sh && ./run.sh

# View logs
docker logs -f image-render

# Stop container
docker stop image-render

# Remove container
docker rm image-render

# Execute commands in running container
docker exec -it image-render sh
```

## Build Process

1. **Build-time compilation** - Application is built during Docker image creation using placeholder values
2. **Runtime variable replacement** - `NEXT_PUBLIC_` variables are replaced in built files at container startup
3. **Runtime database migrations** - Database migrations are run at container startup
4. **Standalone mode** - Optimized for production deployment
5. **Security** - The container runs as a non-root user

## How It Works

### Build Time:
- Uses `.env.example` (from project root) as build-time environment variables
- Clerk gets `pk_test_0000000000000000000000000000000000000000` (valid format)
- App URL gets `http://localhost:3000` (placeholder)
- Application builds successfully without real credentials

### Runtime:
- Startup script replaces placeholders with real environment variables
- `pk_test_0000...` → your real `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`
- `http://localhost:3000` → your real `NEXT_PUBLIC_APP_URL`
- Database migrations run with real credentials

## Advantages

- **Build-time validation passes** - Uses valid placeholder values for Clerk and other services
- **Flexible deployment** - Same image works with different environment configurations
- **Secure** - No real credentials embedded in the image
- **Fast startup** - Only text replacement, no rebuilding required