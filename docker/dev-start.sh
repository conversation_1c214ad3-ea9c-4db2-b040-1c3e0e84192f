#!/bin/sh

# Development container startup script
# This script runs the development server without building

set -e

echo "Starting development container..."
echo "Node version: $(node --version)"
echo "NPM version: $(npm --version)"

# Check if database connection is available before running migrations
if [ -n "$DATABASE_URL" ]; then
    echo "Running database migrations..."
    npm run db:update
    echo "Database migrations completed."
else
    echo "Warning: DATABASE_URL not set, skipping database migrations."
fi

# Start the development server
echo "Starting Next.js development server..."
exec npm run dev