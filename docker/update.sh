#!/bin/bash

# Docker container update script
# This script pulls the latest image and restarts the container

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONF_FILE="$SCRIPT_DIR/.conf"

# Check if configuration file exists
if [ ! -f "$CONF_FILE" ]; then
    echo "Error: .conf file not found at $CONF_FILE"
    exit 1
fi

# Read configuration from .conf file
source "$CONF_FILE"

# Validate required configuration
if [ -z "$CONTAINER_NAME" ]; then
    echo "Error: CONTAINER_NAME not set in .conf"
    exit 1
fi

if [ -z "$IMAGE_NAME" ]; then
    echo "Error: IMAGE_NAME not set in .conf"
    exit 1
fi

echo "Updating Docker image: $IMAGE_NAME"
echo ""

# Get current local image digest (if exists)
echo "Checking for image updates..."
LOCAL_DIGEST=""
if docker image inspect "$IMAGE_NAME" >/dev/null 2>&1; then
    LOCAL_DIGEST=$(docker image inspect "$IMAGE_NAME" --format='{{index .RepoDigests 0}}' 2>/dev/null || echo "")
    echo "Local image digest: ${LOCAL_DIGEST:-"unknown"}"
fi

# Check remote manifest and pull if needed
echo "Checking for latest image: $IMAGE_NAME"

# Get remote manifest digest to compare
echo "Checking remote image digest..."
REMOTE_DIGEST=""
if [[ "$IMAGE_NAME" == ghcr.io/* ]]; then
    # For GHCR images, get remote digest
    REMOTE_DIGEST=$(docker manifest inspect "$IMAGE_NAME" 2>/dev/null | grep -o '"digest":"sha256:[^"]*' | cut -d'"' -f4 | head -n1 || echo "")
    if [ -z "$REMOTE_DIGEST" ]; then
        echo "Unable to check remote manifest, trying direct pull..."
        if ! docker pull "$IMAGE_NAME"; then
            echo ""
            echo "⚠️  Unable to pull image from GitHub Container Registry."
            echo "   Make sure you are logged in:"
            echo "   docker login ghcr.io -u YOUR_GITHUB_USERNAME"
            echo ""
            exit 1
        fi
    else
        echo "Remote image digest: $REMOTE_DIGEST"
        
        # Compare digests
        if [ -n "$LOCAL_DIGEST" ] && [[ "$LOCAL_DIGEST" == *"$REMOTE_DIGEST"* ]]; then
            echo "✅ Image is already up to date (no changes)"
        else
            echo "New version detected, pulling..."
            if ! docker pull "$IMAGE_NAME"; then
                echo ""
                echo "⚠️  Unable to pull image from GitHub Container Registry."
                echo "   Make sure you are logged in:"
                echo "   docker login ghcr.io -u YOUR_GITHUB_USERNAME"
                echo ""
                exit 1
            fi
        fi
    fi
else
    # For other registries, try direct pull
    if ! docker pull "$IMAGE_NAME"; then
        echo "⚠️  Unable to pull image: $IMAGE_NAME"
        echo "   The image might be local-only or from an inaccessible registry."
        exit 1
    fi
fi

# Get new image digest and compare
NEW_DIGEST=$(docker image inspect "$IMAGE_NAME" --format='{{index .RepoDigests 0}}' 2>/dev/null || echo "")
echo "New image digest: ${NEW_DIGEST:-"unknown"}"

if [ -n "$LOCAL_DIGEST" ] && [ "$LOCAL_DIGEST" = "$NEW_DIGEST" ]; then
    echo "✅ Image is already up to date (no changes)"
else
    echo "✅ Image updated successfully (new version pulled)"
fi

echo "✅ Image update completed: $IMAGE_NAME"
echo ""
echo "🎉 Image update completed successfully!"
echo "Run './run.sh' to start the container with the latest image."