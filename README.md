# Vibany Next - Image AI Render

## 运行应用程序

```bash
git clone https://github.com/vibany-shipping/vibany-next
cd vibany-next
npm install
```

要在本地运行应用程序，您需要：

1. 在 [https://clerk.com](https://clerk.com) 注册一个 Clerk 账户。
2. 前往 [Clerk 控制面板](https://dashboard.clerk.com) 并创建一个应用程序。
3. 按照 [示例 `.env` 文件](./.env.example) 中所示设置所需的环境变量。
4. 如果需要，在侧边栏中进入"Organization Settings"并启用组织功能。
5. 使用 `npm install` 安装所需依赖项。
6. 使用 `npm run dev` 启动开发服务器。

## 环境变量配置

在根目录中创建一个 `.env.local` 文件，包含以下变量：

## PM2 配置文件说明

项目支持使用 `.conf.local` 文件进行 PM2 和构建相关的配置。

### 配置文件使用方法

1. **复制示例配置文件**：
   ```bash
   cp .conf.example .conf.local
   ```

2. **编辑配置文件**：
   ```bash
   nano .conf.local
   ```

3. **配置项说明**：
   - `PM2_INSTANCES`: PM2 进程实例数（默认：max，使用所有CPU核心）
   - `PM2_MAX_MEMORY`: 内存限制，超过此值自动重启（默认：1G）
   - `PM2_NODE_ARGS`: Node.js 启动参数（默认：--max-old-space-size=2048）
   - `PM2_PORT`: 应用监听端口（默认：3000）
   - `PM2_ENV`: 运行环境（默认：production）
   - `APP_NAME`: 应用名称（默认：image-render）
   - `ENV_FILE`: 环境变量文件路径（默认：.env.local）

4. **配置文件优先级**：
   - `.conf.local` 文件优先级最高（本地配置，不会被提交到版本控制）
   - `.conf` 文件作为备用（可提交到版本控制作为团队共享配置）
   - 如果都不存在，使用内置默认配置

5. **注意事项**：
   - `.conf` 和 `.conf.local` 文件已添加到 `.gitignore`，不会被提交到版本控制
   - 配置文件使用 `KEY=VALUE` 格式，支持注释行（以 `#` 开头）
   - 数值类型的配置项会自动转换为适当的类型

### 基本配置
```
# 调试模式
NEXT_PUBLIC_DEBUG=

# 应用URL
NEXT_PUBLIC_APP_URL=https://yourdomain.com
```

### Vercel Cron 配置
```
# Vercel Cron 任务密钥（用于保护定时任务API）
CRON_SECRET=your_cron_secret_key

# Cron 任务超时时间（秒）
TIMEOUT_SECONDS=790
```

### 数据库配置
```
# Neon PostgreSQL 数据库连接URL
DATABASE_URL=**************************************
```

### Clerk 身份验证
```
# Clerk 公钥和密钥
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_your_publishable_key
CLERK_SECRET_KEY=sk_your_secret_key

# 登录和注册后的重定向URL
CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/draw
CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/draw
```

### 支付系统配置

* 支付宝/微信支付服务注册：https://s.zhaikr.com/epay
* Stripe 支付注册：https://stripe.com

```
# 支付宝/微信支付API配置
PAY_API_URL=https://api.dulupay.com
PAY_PID=your_payment_id
PAY_PUBLIC_KEY=your_payment_public_key
PAY_MERCHANT_PRIVATE_KEY=your_merchant_private_key

# Stripe支付配置
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key  # 测试环境使用sk_test_，生产环境使用sk_live_
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret  # Stripe webhook签名密钥
```

### AI 模型配置

AI 模型的配置包括 Tuzi、XAI 和 OpenAI 的 API 配置。

* TUZI 注册地址：https://s.zhaikr.com/tuzi
* XAI 注册地址：https://xai.com
* OpenAI 注册地址：https://openai.com

```
# Tuzi API配置
TUZI_API_URL=https://api.tu-zi.com/v1
TUZI_API_KEY=sk_your_tuzi_api_key
TUZI_MODEL_IMAGE=gpt-4o-image-vip
TUZI_MODEL_IMAGE_VIP=gpt-4o-image-vip
TUZI_MODEL_IMAGE_SMALL=gpt-4o-image

# XAI API配置
XAI_API_URL=https://api.xai.com/v1
XAI_API_KEY=xai-your_xai_api_key
XAI_API_MODEL_IMAGE=grok-2-image-latest

# OpenAI API配置
OPENAI_API_URL=https://api.openai.com/v1
OPENAI_API_KEY=sk_your_openai_api_key
OPENAI_MODEL_IMAGE=gpt-image-1
OPENAI_MODEL_IMAGE_SMALL=dall-e-3
```

### Cloudflare R2 存储配置

Cloudflare R2 存储注册

* 地址：https://dash.cloudflare.com
* 路径：R2 对象存储

```
# Cloudflare R2 存储配置
R2_ACCOUNT_ID=your_account_id
R2_ACCESS_KEY_ID=your_access_key_id
R2_SECRET_ACCESS_KEY=your_secret_access_key
R2_BUCKET_NAME=your_bucket_name
R2_PUBLIC_URL_PREFIX=https://your-bucket-name.r2.dev
```

## Vercel 配置

Vercel 注册地址：https://vercel.com

本项目使用 Vercel 进行部署，需要特别注意以下配置：

1. **Vercel Pro 计划**：由于项目使用了 Vercel 的高级功能（如长时间运行的函数和 Cron Jobs），需要升级到 Vercel Pro 计划
2. **环境变量设置**：在 Vercel 项目设置中的 "Environment Variables" 部分添加所有 `.env.local` 中的环境变量

### vercel.json 配置说明

项目根目录下的 `vercel.json` 文件包含以下重要配置：

```json
{
  "functions": {
    "app/api/**/*": {
      "maxDuration": 800
    }
  },
  "crons": [
    {
      "path": "/api/public/clear",
      "schedule": "*/5 * * * *"
    },
    {
      "path": "/api/public/backup?type=hourly",
      "schedule": "0 * * * *"
    },
    {
      "path": "/api/public/backup/fix",
      "schedule": "15,45 * * * *"
    }
  ]
}
```

#### 函数执行时间

- `maxDuration: 800`：设置 API 路由的最大执行时间为 800 秒（约 13 分钟）
- **注意**：此配置需要 Vercel Pro 或 Enterprise 计划才能生效，免费计划的函数执行时间上限为 60 秒

#### 定时任务 (Cron Jobs)

项目配置了以下定时任务：
- 每 5 分钟执行一次清理任务 (`/api/public/clear`)
- 每小时执行一次小时级备份 (`/api/public/backup?type=hourly`)
- 每小时的第 15 分钟和第 45 分钟执行修复任务 (`/api/public/backup/fix`)

**重要提示**：Vercel 的 Cron Jobs 功能需要 Pro 或 Enterprise 计划才能使用。

### Vercel 手动配置事项

在 Vercel 部署时，需要手动配置以下内容：

1. **环境变量设置**：
   - 在 Vercel 项目设置中的 "Environment Variables" 部分添加所有 `.env.local` 中的环境变量
   - 确保敏感信息（如 API 密钥、数据库连接字符串）正确设置且受到保护

2. **项目设置**：
   - **构建命令**：确保使用 `npm run build` 作为构建命令
   - **输出目录**：默认为 `.next`，通常不需要修改
   - **Node.js 版本**：建议使用 Node.js 18.x 或更高版本

3. **域名配置**：
   - 在 Vercel 项目的 "Domains" 部分添加您的自定义域名
   - 按照 Vercel 提供的说明配置 DNS 记录

4. **付费计划升级**：
   - 由于项目使用了 Vercel 的高级功能（如长时间运行的函数和 Cron Jobs），需要升级到 Vercel Pro 计划
   - 当前 Vercel Pro 计划价格为 $20/月（可能会变动，请查看 Vercel 官方定价）

## 支付系统配置

本项目集成了支付系统，需要特别注意以下配置：

### 支付接口配置

1. **申请支付接口**：
   - 联系支付服务提供商申请商户账号
   - 获取 `PAY_PID`、`PAY_PUBLIC_KEY` 和 `PAY_MERCHANT_PRIVATE_KEY`

2. **支付宝/微信支付回调配置**：
   - 在支付服务提供商后台设置支付回调 URL：`https://yourdomain.com/api/public/epay/notify`
   - 确保回调 URL 可以被支付服务提供商的服务器访问

3. **Stripe 支付配置**：
   - 在 [Stripe Dashboard](https://dashboard.stripe.com/) 创建账户
   - 获取 API 密钥 (`STRIPE_SECRET_KEY`)
   - 配置 Webhook 端点：`https://yourdomain.com/api/public/stripe/notify`
   - 选择事件类型：`checkout.session.completed` 和 `payment_intent.succeeded`
   - 获取 Webhook 签名密钥 (`STRIPE_WEBHOOK_SECRET`)

4. **支付安全**：
   - 妥善保管所有支付相关的密钥，不要泄露给第三方
   - 定期检查支付记录，确保系统安全
   - 使用 HTTPS 确保支付过程的安全性

### 费用说明

1. **Vercel 相关费用**：
   - Vercel Pro 计划：$20/月（[套餐影响](https://vercel.com/docs/functions/configuring-functions/duration)）
   - 超出免费额度的带宽和构建分钟数将产生额外费用
   - 可以使用 opennext 方案部署到其他平台或 VPS 上（[Next Deployment 文档](https://nextjs.org/docs/app/getting-started/deploying)），节省成本（但会增加服务器维护的工作）

2. **数据库费用**：
   - Neon PostgreSQL 数据库：根据使用情况收费，基本计划约 $4/月起，早期可以直接使用免费套餐完全够用

3. **存储费用**：
   - Cloudflare R2 存储：前 10GB 免费，超出部分 $0.015/GB/月
   - 出站流量：前 100GB 免费，超出部分 $0.09/GB

4. **API 调用费用**：
   - 各 AI 模型 API 根据调用次数和使用量收费
   - 建议设置使用限额，避免意外超支

5. **Clerk 费用**：
   - 早期使用免费套餐即可

6. **支付系统费用**：
   - 支付宝/微信支付：https://s.zhaikr.com/epay
   - Stripe 支付：根据使用情况收费，大概 4% 上下


## 部署流程

### 本地开发到生产部署

1. **本地开发**：
   ```bash
   npm run dev
   ```

2. **构建项目**：
   ```bash
   npm run build
   ```

3. **部署到 Vercel**：
   - 使用 Vercel CLI 部署：
     ```bash
     npm i -g vercel
     vercel
     ```
   - 或通过 GitHub 集成自动部署（推荐）

### 数据库迁移

项目使用 Drizzle ORM 进行数据库管理（以下操作在生产构建时会自动完成）：

1. **生成迁移文件**：
   ```bash
   npm run db:generate
   ```

2. **应用迁移**：
   ```bash
   npm run db:migrate
   ```

**注意**：不要手动创建 `@drizzle/migrations/` 目录下的 SQL 文件，这些文件由 Drizzle ORM 自动生成。

## VPS 部署指南

除了使用 Vercel 部署外，您还可以选择直接在 VPS 上部署本应用。以下是详细的部署步骤：

### 1. 使用 prod.sh 脚本部署

项目根目录下的 `prod.sh` 脚本提供了一键部署功能：

1. **克隆代码库**：
   ```bash
   git clone https://github.com/vibany-shipping/vibany-next
   cd vibany-next
   ```

2. **配置环境变量**：
   - 创建 `.env.local` 文件并配置所有必要的环境变量（参考上文的环境变量配置部分）

3. **赋予脚本执行权限**：
   ```bash
   chmod +x prod.sh
   ```

4. **执行部署脚本**：
   ```bash
   ./prod.sh
   ```

   该脚本会自动执行以下操作：
   - 加载 `.env.local` 中的环境变量
   - 安装依赖项 (`npm i`)
   - 构建项目 (`npm run build`)
   - 启动应用 (`npm run start`)

### 2. 配置 Nginx 反向代理

默认情况下，Next.js 应用会在 3000 端口运行。您需要配置 Nginx 将域名请求转发到该端口：

1. **安装 Nginx**（如果尚未安装）：
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install nginx

   # CentOS/RHEL
   sudo yum install nginx
   ```

2. **创建 Nginx 配置文件**：
   ```bash
   sudo nano /etc/nginx/sites-available/vibany-next
   ```

3. **添加以下配置**：
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;  # 替换为您的域名

       location / {
           proxy_pass http://localhost:3000;  # Next.js 应用的地址和端口
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }

       # 增加上传文件大小限制（如需要）
       client_max_body_size 10M;
   }
   ```

4. **创建符号链接并测试配置**：
   ```bash
   sudo ln -s /etc/nginx/sites-available/vibany-next /etc/nginx/sites-enabled/
   sudo nginx -t
   ```

5. **重启 Nginx**：
   ```bash
   sudo systemctl restart nginx
   ```

6. **配置 SSL**（推荐）：
   ```bash
   sudo apt install certbot python3-certbot-nginx
   sudo certbot --nginx -d your-domain.com
   ```

### 3. 修改 Next.js 端口

如果需要更改 Next.js 应用的默认端口（3000），有以下几种方法：

1. **通过命令行参数**：
   ```bash
   next start -p 4000  # 使用 4000 端口
   ```

2. **修改 package.json**：
   ```json
   "scripts": {
     "start": "next start -p 4000"
   }
   ```

3. **使用环境变量**：
   ```bash
   # 在 .env.local 中添加
   PORT=4000

   # 或在启动命令前设置
   PORT=4000 npm run start
   ```

4. **在 prod.sh 中修改**：
   ```bash
   export PORT=4000
   export $(grep -v '^#' .env.local | xargs)

   npm i && npm run build && npm run start
   ```

### 4. 使用 PM2 进行部署和进程管理

PM2 是一个强大的 Node.js 应用进程管理器，可以帮助您保持应用持续运行。

#### 📋 完整 PM2 部署指南

> **详细操作文档**: [PM2 管理脚本使用指南](./pm2/README.md)

#### 🚀 快速开始

1. **安装 PM2**：
   ```bash
   npm install -g pm2
   ```

2. **一键部署**：
   ```bash
   # 智能部署（推荐）
   ./pm2/deploy.sh
   ```

3. **应用管理**：
   ```bash
   # 查看状态
   ./pm2/status.sh
   
   # 停止应用
   ./pm2/stop.sh
   ```

#### 🔧 脚本功能特性

| 脚本 | 功能 | 特性 |
|------|------|------|
| `deploy.sh` | 智能部署 | 自动检测应用状态，支持启动/重启 |
| `stop.sh` | 停止应用 | 完全停止并清理应用实例 |
| `status.sh` | 状态查看 | 显示详细状态和监控信息 |

**deploy.sh 脚本特性**：
- 🔍 自动检查并创建 `.conf.local` 配置文件
- 🔨 询问是否需要运行 `npm run build`
- 🚀 智能检测应用状态，自动选择启动或重启
- 🔒 端口冲突检测
- 💾 自动保存 PM2 配置

#### ⚙️ 配置文件管理

项目支持使用 `.conf.local` 文件进行自定义配置：

```bash
# 创建配置文件
cp .conf.example .conf.local
nano .conf.local
```

**主要配置项**：
- `APP_NAME`: 应用名称（默认：image-render）
- `PM2_PORT`: 应用端口（默认：3000）
- `ENV_FILE`: 环境变量文件路径（默认：.env.local）
- `PM2_ENV`: 运行环境（默认：production）
- `PM2_INSTANCES`: 进程实例数（默认：1）
- `PM2_MAX_MEMORY`: 内存限制（默认：1G）

#### 🛠️ 常用 PM2 命令

```bash
# 基本管理
pm2 list                        # 查看所有应用
pm2 logs image-render           # 查看应用日志
pm2 restart image-render        # 重启应用
pm2 monit                       # 实时监控

# 进程管理
pm2 scale image-render 4        # 调整进程数量
pm2 reload image-render         # 零停机重载

# 开机自启
pm2 startup                     # 设置开机自启
pm2 save                        # 保存当前配置
```

#### 📖 更多信息

- [PM2 脚本详细使用指南](./pm2/README.md)
- [故障排除和性能优化](./pm2/README.md#🔍-故障排除)
- [最佳实践建议](./pm2/README.md#📝-最佳实践)

### 5. 部署注意事项

1. **系统要求**：
   - Node.js 18.x 或更高版本
   - 至少 1GB RAM（推荐 2GB 或更多）
   - 足够的磁盘空间用于应用和依赖项

2. **安全考虑**：
   - 使用非 root 用户运行应用
   - 配置防火墙只开放必要端口
   - 定期更新系统和依赖项
   - 使用 HTTPS 加密所有流量

3. **性能优化**：
   - 考虑使用 CDN 分发静态资源
   - 配置适当的缓存策略
   - 监控应用性能并根据需要调整资源分配

4. **备份策略**：
   - 定期备份数据库和环境配置
   - 配置日志轮转以避免磁盘空间耗尽

## 常见问题与故障排除

### Vercel 部署问题

1. **函数执行超时**：
   - 检查是否已升级到 Vercel Pro 计划
   - 确认 `vercel.json` 中的 `maxDuration` 设置正确
   - 优化代码减少执行时间

2. **环境变量问题**：
   - 确保所有必需的环境变量都已在 Vercel 项目设置中配置
   - 检查环境变量名称是否正确（注意大小写）
   - 重新部署项目以应用环境变量更改

3. **Cron 任务未执行**：
   - 确认已升级到 Vercel Pro 计划
   - 检查 Cron 表达式是否正确
   - 在 Vercel 控制台中查看 Functions 日志以排查问题

### 支付系统问题

1. **支付宝/微信支付回调失败**：
   - 确认回调 URL 配置正确
   - 检查支付服务提供商的 IP 是否被允许访问
   - 查看服务器日志以获取详细错误信息

2. **Stripe Webhook 通知失败**：
   - 确认 Webhook 端点配置正确
   - 检查 `STRIPE_WEBHOOK_SECRET` 是否正确配置
   - 查看 Stripe Dashboard 中的 Webhook 日志
   - 使用 Stripe CLI 进行本地测试：`stripe listen --forward-to localhost:3000/api/public/stripe/notify`

3. **订单状态不同步**：
   - 检查数据库连接是否稳定
   - 确认支付回调处理逻辑正确
   - 使用管理员工具手动同步订单状态
   - 对于 Stripe 支付，可以在 Stripe Dashboard 中查看支付状态

### API 集成问题

1. **AI 模型调用失败**：
   - 验证 API 密钥是否有效
   - 检查 API 调用参数是否正确
   - 确认 API 服务提供商的服务状态

2. **存储问题**：
   - 验证 Cloudflare R2 配置是否正确
   - 检查存储桶权限设置
   - 确认文件上传路径和访问 URL 格式正确
