import { useState, useEffect } from 'react';

export type OrderStatus = 'PENDING' | 'SUCCESS' | 'FAILED' | 'REFUND';

interface OrderStatusResponse {
    status: OrderStatus;
    orderId: string;
    amount: number;
    createdAt: string;
}

interface UseOrderPollingProps {
    orderId: string | null;
    onSuccess?: () => Promise<void> | void;
    onFailed?: () => void;
}

interface OrderPollingState {
    status: OrderStatus | null;
    error: string | null;
    isPolling: boolean;
}

export function useOrderPolling({ orderId, onSuccess, onFailed }: UseOrderPollingProps) {
    const [state, setState] = useState<OrderPollingState>({
        status: null,
        error: null,
        isPolling: false
    });

    useEffect(() => {
        if (!orderId) return;

        let mounted = true;
        let timeoutId: NodeJS.Timeout;

        const pollOrder = async () => {
            try {
                const response = await fetch(`/api/orders/${orderId}/status`);
                if (!response.ok) {
                    throw new Error("查询订单状态失败");
                }

                const result = await response.json();
                if (result.code !== 0) {
                    throw new Error(result.msg || "查询订单状态失败");
                }

                if (!mounted) return true;

                const data: OrderStatusResponse = result.data;
                setState(prev => ({ ...prev, status: data.status }));

                if (data.status === "SUCCESS") {
                    await onSuccess?.();
                    return true; // Stop polling
                }

                if (data.status === "FAILED" || data.status === "REFUND") {
                    onFailed?.();
                    return true; // Stop polling
                }

                return false; // Continue polling
            } catch (error) {
                if (!mounted) return true;

                setState(prev => ({
                    ...prev,
                    error: error instanceof Error ? error.message : "查询订单状态失败"
                }));
                return true; // Stop polling on error
            }
        };

        const startPolling = async () => {
            if (!mounted) return;

            setState(prev => ({ ...prev, isPolling: true }));

            const shouldStop = await pollOrder();
            if (shouldStop || !mounted) {
                setState(prev => ({ ...prev, isPolling: false }));
                return;
            }

            timeoutId = setTimeout(startPolling, 3000);
        };

        // Start polling immediately
        startPolling();

        // Cleanup function
        return () => {
            mounted = false;
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
            setState(prev => ({ ...prev, isPolling: false }));
        };
    }, [orderId, onSuccess, onFailed]);

    return state;
}
