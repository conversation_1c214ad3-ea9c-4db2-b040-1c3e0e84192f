import { useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useProfileStore } from '@/store/profile/index';

export function useProfile() {
  const { isLoaded: isAuthLoaded, isSignedIn } = useAuth();
  const store = useProfileStore();

  useEffect(() => {
    if (isAuthLoaded && isSignedIn) {
      store.fetch();
    }
  }, [isAuthLoaded, isSignedIn, store.fetch]);

  return store;
}
