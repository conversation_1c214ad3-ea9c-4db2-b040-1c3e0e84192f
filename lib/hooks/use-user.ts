import { create } from 'zustand';
import { useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useProfileStore } from '@/store/profile';

interface UserState {
  user: {
    id: string;
    email: string;
    username: string;
    avatarUrl?: string;
  } | null;
  wallet: {
    permanentPoints: number;
  } | null;
  recentTransactions: Array<{
    id: string;
    type: 'credit' | 'debit';
    amount: number;
    description: string;
    createdAt: string;
  }>;
  recentHistories: Array<{
    id: string;
    status: boolean;
    prompt: string;
    pointsUsed: number;
    createdAt: string;
    resultUrl?: string;
  }>;
  isLoading: boolean;
  isNewUser: boolean;
  error: string | null;
  fetchUser: () => Promise<void>;
}

export const useUserStore = create<UserState>((set) => ({
  user: null,
  wallet: null,
  recentTransactions: [],
  recentHistories: [],
  isLoading: true,
  isNewUser: false,
  error: null,
  fetchUser: async () => {
    try {
      set({ isLoading: true, error: null });

      // 使用 ProfileStore 的 refreshUserInfo 方法获取用户数据
      const data = await useProfileStore.getState().refreshUserInfo();

      // 设置 UserStore 的状态
      set({
        user: data.user,
        wallet: data.wallet,
        recentTransactions: data.recentTransactions,
        recentHistories: data.recentHistories,
        isNewUser: data.isNewUser,
        isLoading: false,
      });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'An error occurred',
        isLoading: false,
      });
    }
  },
}));

export function useUser() {
  const { isSignedIn, isLoaded } = useAuth();
  const store = useUserStore();

  useEffect(() => {
    if (isLoaded && isSignedIn) {
      store.fetchUser();
    }
  }, [isLoaded, isSignedIn]);

  return store;
}
