import { checkEmailBlocklist, isDisposableEmail } from '../index';
import { db } from '@/lib/db';
import { blocklists } from '@/lib/db/schema';
import { and, eq } from 'drizzle-orm';
import { BLOCKLIST_TYPES } from '@/constants/blocklist';

// Mock the database and fs modules
jest.mock('@/lib/db', () => ({
  query: {
    blocklists: {
      findMany: jest.fn(),
    },
  },
}));

jest.mock('fs', () => ({
  readFileSync: jest.fn(() => 'example.com\nspam-domain.com\ndisposable.com'),
}));

describe('Blocklist Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('checkEmailBlocklist', () => {
    it('should return true for disposable email domains', async () => {
      const result = await checkEmailBlocklist('<EMAIL>');
      expect(result).toBe(true);
    });

    it('should return false for valid email domains', async () => {
      // Mock database to return empty rules
      (db.query.blocklists.findMany as jest.Mock).mockResolvedValue([]);
      
      const result = await checkEmailBlocklist('<EMAIL>');
      expect(result).toBe(false);
    });

    it('should return true for emails matching database rules', async () => {
      // Mock database to return a rule that matches
      (db.query.blocklists.findMany as jest.Mock).mockResolvedValue([
        {
          id: '1',
          type: BLOCKLIST_TYPES.EMAIL,
          pattern: '.*@badpattern\\.com',
          enabled: true,
        },
      ]);
      
      const result = await checkEmailBlocklist('<EMAIL>');
      expect(result).toBe(true);
    });
  });
});
