import { db } from '@/lib/db';
import { blocklists } from '@/lib/db/schema';
import { and, eq, count } from 'drizzle-orm';
import { BLOCKLIST_TYPES } from '@/constants/blocklist';
import { nanoid } from 'nanoid';
import { createLogger } from '@/lib/draw/logger';
import fs from 'fs';
import path from 'path';

const logger = createLogger('blocklist');

// 缓存域名列表，避免重复读取文件
let disposableEmailDomains: Set<string> | null = null;

/**
 * 读取一次性邮箱黑名单文件
 * @returns 一次性邮箱域名集合
 */
function getDisposableEmailDomains(): Set<string> {
  if (disposableEmailDomains) {
    return disposableEmailDomains;
  }

  try {
    const filePath = path.join(process.cwd(), 'lib/blocklist/disposable_email_blocklist.conf');
    const content = fs.readFileSync(filePath, 'utf-8');
    const domains = content.split('\n').filter(line => line.trim() !== '');
    disposableEmailDomains = new Set(domains);
    logger.info(`[BLOCKLIST] Loaded ${disposableEmailDomains.size} disposable email domains from file`);
    return disposableEmailDomains;
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[BLOCKLIST_ERROR] Failed to load disposable email domains file', errorObj);
    return new Set();
  }
}

/**
 * 检查邮箱域名是否在一次性邮箱黑名单中
 * @param email 用户邮箱
 * @returns 是否在一次性邮箱黑名单中
 */
export function isDisposableEmail(email: string): boolean {
  try {
    const domain = email.split('@')[1]?.toLowerCase();
    if (!domain) {
      return false;
    }

    const domains = getDisposableEmailDomains();
    return domains.has(domain);
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[BLOCKLIST_ERROR] Error checking disposable email', errorObj);
    return false;
  }
}

/**
 * 检查邮箱是否匹配黑名单规则
 * @param email 用户邮箱
 * @returns 是否在黑名单中
 */
export async function checkEmailBlocklist(email: string): Promise<boolean> {
  try {
    // 首先检查是否在一次性邮箱黑名单文件中
    if (isDisposableEmail(email)) {
      logger.info(`[BLOCKLIST] Email ${email} matched disposable email blocklist file`);
      return true;
    }

    // 获取所有启用的邮箱黑名单规则
    const rules = await db.query.blocklists.findMany({
      where: and(
        eq(blocklists.type, BLOCKLIST_TYPES.EMAIL),
        eq(blocklists.enabled, true)
      ),
    });

    if (rules.length === 0) {
      return false;
    }

    // 检查邮箱是否匹配任何规则
    for (const rule of rules) {
      try {
        const regex = new RegExp(rule.pattern, 'i'); // 不区分大小写
        if (regex.test(email)) {
          logger.info(`[BLOCKLIST] Email ${email} matched blocklist rule: ${rule.pattern}`);
          return true;
        }
      } catch (error) {
        const errorObj = error instanceof Error ? error : new Error(String(error));
        logger.error(`[BLOCKLIST_ERROR] Invalid regex pattern: ${rule.pattern}`, errorObj);
        // 如果正则表达式无效，跳过该规则
        continue;
      }
    }

    return false;
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[BLOCKLIST_ERROR] Error checking email blocklist', errorObj);
    // 如果发生错误，默认不阻止用户
    return false;
  }
}

/**
 * 检查文本是否匹配关键词黑名单规则
 * @param text 要检查的文本
 * @returns 是否匹配黑名单规则
 */
export async function checkKeywordBlocklist(text: string): Promise<boolean> {
  try {
    // 获取所有启用的关键词黑名单规则
    const rules = await db.query.blocklists.findMany({
      where: and(
        eq(blocklists.type, BLOCKLIST_TYPES.KEYWORD),
        eq(blocklists.enabled, true)
      ),
    });

    if (rules.length === 0) {
      return false;
    }

    // 检查文本是否匹配任何规则
    for (const rule of rules) {
      try {
        const regex = new RegExp(rule.pattern, 'i'); // 不区分大小写
        if (regex.test(text)) {
          logger.info(`[BLOCKLIST] Text matched keyword blocklist rule: ${rule.pattern}`);
          return true;
        }
      } catch (error) {
        const errorObj = error instanceof Error ? error : new Error(String(error));
        logger.error(`[BLOCKLIST_ERROR] Invalid regex pattern: ${rule.pattern}`, errorObj);
        // 如果正则表达式无效，跳过该规则
        continue;
      }
    }

    return false;
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[BLOCKLIST_ERROR] Error checking keyword blocklist', errorObj);
    // 如果发生错误，默认不阻止
    return false;
  }
}

/**
 * 创建新的黑名单规则
 */
export async function createBlocklistRule({
  type,
  pattern,
  description,
  enabled = true,
  createdBy,
}: {
  type: string;
  pattern: string;
  description?: string;
  enabled?: boolean;
  createdBy: string;
}) {
  try {
    // 验证正则表达式是否有效
    try {
      new RegExp(pattern);
    } catch (error) {
      throw new Error(`无效的正则表达式: ${pattern}`);
    }

    const id = nanoid();
    const result = await db.insert(blocklists).values({
      id,
      type,
      pattern,
      description,
      enabled,
      createdBy,
      extra: {},
    }).returning();

    return result[0];
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[BLOCKLIST_ERROR] Error creating blocklist rule', errorObj);
    throw errorObj;
  }
}

/**
 * 更新黑名单规则
 */
export async function updateBlocklistRule({
  id,
  pattern,
  description,
  enabled,
}: {
  id: string;
  pattern?: string;
  description?: string;
  enabled?: boolean;
}) {
  try {
    // 如果提供了新的正则表达式，验证其有效性
    if (pattern) {
      try {
        new RegExp(pattern);
      } catch (error) {
        throw new Error(`无效的正则表达式: ${pattern}`);
      }
    }

    const updateData: any = {
      updatedAt: new Date(),
    };

    if (pattern !== undefined) updateData.pattern = pattern;
    if (description !== undefined) updateData.description = description;
    if (enabled !== undefined) updateData.enabled = enabled;

    const result = await db.update(blocklists)
      .set(updateData)
      .where(eq(blocklists.id, id))
      .returning();

    return result[0];
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[BLOCKLIST_ERROR] Error updating blocklist rule', errorObj);
    throw errorObj;
  }
}

/**
 * 删除黑名单规则
 */
export async function deleteBlocklistRule(id: string) {
  try {
    await db.delete(blocklists).where(eq(blocklists.id, id));
    return { success: true };
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[BLOCKLIST_ERROR] Error deleting blocklist rule', errorObj);
    throw errorObj;
  }
}

/**
 * 获取黑名单规则列表
 */
export async function getBlocklistRules({
  type,
  enabled,
  page = 1,
  limit = 10,
}: {
  type?: string;
  enabled?: boolean;
  page?: number;
  limit?: number;
}) {
  try {
    const offset = (page - 1) * limit;

    // 构建查询条件
    const whereConditions = [];

    if (type) {
      whereConditions.push(eq(blocklists.type, type));
    }

    if (enabled !== undefined) {
      whereConditions.push(eq(blocklists.enabled, enabled));
    }

    // 构建查询
    const query = whereConditions.length > 0
      ? and(...whereConditions)
      : undefined;

    // 获取总数
    const countResult = await db
      .select({ count: count() })
      .from(blocklists)
      .where(query);

    const total = Number(countResult[0].count);

    // 获取分页数据
    const rules = await db.query.blocklists.findMany({
      where: query,
      limit,
      offset,
      orderBy: (blocklists, { desc }) => [desc(blocklists.createdAt)],
      with: {
        creator: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
          },
        },
      },
    });

    return {
      rules,
      total,
      page,
      limit,
    };
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[BLOCKLIST_ERROR] Error getting blocklist rules', errorObj);
    throw errorObj;
  }
}

/**
 * 获取单个黑名单规则
 */
export async function getBlocklistRule(id: string) {
  try {
    const rule = await db.query.blocklists.findFirst({
      where: eq(blocklists.id, id),
      with: {
        creator: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
          },
        },
      },
    });

    return rule;
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error('[BLOCKLIST_ERROR] Error getting blocklist rule', errorObj);
    throw errorObj;
  }
}
