/**
 * Stripe utility functions
 */

/**
 * Generate a URL to the Stripe dashboard for a session
 * @param sessionId The Stripe session ID
 * @returns URL to the Stripe dashboard
 */
export function getStripeSessionUrl(sessionId: string): string {
  // Determine if we're in test mode based on the session ID prefix
  const isTestMode = sessionId.startsWith('cs_test_');
  const baseUrl = isTestMode 
    ? 'https://dashboard.stripe.com/test' 
    : 'https://dashboard.stripe.com';
  
  return `${baseUrl}/checkout/sessions/${sessionId}`;
}

/**
 * Generate a URL to the Stripe dashboard for a payment intent
 * @param paymentIntentId The Stripe payment intent ID
 * @returns URL to the Stripe dashboard
 */
export function getStripePaymentIntentUrl(paymentIntentId: string): string {
  // Determine if we're in test mode based on the payment intent ID prefix
  const isTestMode = paymentIntentId.startsWith('pi_test_');
  const baseUrl = isTestMode 
    ? 'https://dashboard.stripe.com/test' 
    : 'https://dashboard.stripe.com';
  
  return `${baseUrl}/payments/${paymentIntentId}`;
}

/**
 * Generate a Stripe dashboard URL based on the transaction ID
 * This function handles different types of Stripe IDs
 * @param transactionId The Stripe transaction ID (session ID, payment intent ID, etc.)
 * @returns URL to the appropriate Stripe dashboard page
 */
export function getStripeDashboardUrl(transactionId: string): string {
  if (!transactionId) return '';
  
  if (transactionId.startsWith('cs_')) {
    return getStripeSessionUrl(transactionId);
  } else if (transactionId.startsWith('pi_')) {
    return getStripePaymentIntentUrl(transactionId);
  } else {
    // Default to session URL if we can't determine the type
    return getStripeSessionUrl(transactionId);
  }
}
