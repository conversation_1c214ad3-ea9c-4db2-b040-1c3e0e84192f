import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import { createOpenA<PERSON> } from "@ai-sdk/openai";
import { createXai } from '@ai-sdk/xai';
import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from "ai";
import { drawModels } from "@/constants/draw/models";


// OPENAI COMPLETIONS GROUP
const OPENAI_COMPLETIONS_MODEL_SMALL =
  process.env.OPENAI_COMPLETIONS_MODEL_SMALL || "doubao-seed-1-6-flash-250615";
const OPENAI_COMPLETIONS_MODEL_LARGE =
  process.env.OPENAI_COMPLETIONS_MODEL_LARGE || "doubao-seed-1-6-250615";
const OPENAI_COMPLETIONS_MODEL_REASONING =
  process.env.OPENAI_COMPLETIONS_MODEL_REASONING ||
  "doubao-seed-1-6-thinking-250615";
const OPENAI_COMPLETIONS_MODEL_FUNCTION = process.env.OPENAI_COMPLETIONS_MODEL_FUNCTION || "doubao-seed-1-6-250615";

// XAI GROUP
const XAI_API_MODEL_IMAGE = process.env.XAI_API_MODEL_IMAGE || "grok-2";

// TUZI DEFAULT GROUP
const TUZI_MODEL_IMAGE_SMALL = process.env.TUZI_MODEL_IMAGE_SMALL || "gpt-4o-image";
const TUZI_MODEL_IMAGE = process.env.TUZI_MODEL_IMAGE || "gpt-4o-image-vip";
const TUZI_MODEL_IMAGE_VIP =
  process.env.TUZI_MODEL_IMAGE_VIP || "gpt-4o-image-vip";

// TUZI FLUX GROUP
const TUZI_MODEL_FLUX_PRO =
  process.env.TUZI_MODEL_FLUX_PRO || "flux-kontext-pro";
const TUZI_MODEL_FLUX_MAX =
  process.env.TUZI_MODEL_FLUX_MAX || "flux-kontext-max";

// TUZI OPENAI GROUP
const TUZI_OPENAI_API_URL =
  process.env.TUZI_OPENAI_API_URL || "https://api.openai.com/v1";
const TUZI_OPENAI_API_KEY =
  process.env.TUZI_OPENAI_API_KEY || process.env.TUZI_API_KEY || "";
const TUZI_OPENAI_MODEL_IMAGE =
  process.env.TUZI_OPENAI_MODEL_IMAGE || "gpt-image-1";

// OPENAI GROUP
const OPENAI_API_URL = process.env.OPENAI_API_URL || "https://api.openai.com/v1";
const OPENAI_API_KEY = process.env.OPENAI_API_KEY || "";
const OPENAI_MODEL_IMAGE = process.env.OPENAI_MODEL_IMAGE || "gpt-image-1";

// 获取模型的积分成本
export function getModelCreditCost(model: string): number {
  const modelConfig = drawModels.find(m => m.id === model);
  return modelConfig?.points || 100; // 默认使用100积分
}

const chatProvider = createOpenAICompatible({
  name: "chat",
  apiKey: process.env.OPENAI_COMPLETIONS_API_KEY || "",
  baseURL: process.env.OPENAI_COMPLETIONS_BASE_URL || "",
});

const xaiProvider = createXai({
  apiKey: process.env.XAI_API_KEY || "",
});

const tuziProvider = createOpenAICompatible({
  name: "tuzi",
  apiKey: process.env.TUZI_API_KEY || "",
  baseURL: process.env.TUZI_API_URL || "",
});

const tuziOaiProvider = createOpenAI({
  name: "tuzi-openai",
  apiKey: TUZI_OPENAI_API_KEY,
  baseURL: TUZI_OPENAI_API_URL,
});

const openaiProvider = createOpenAI({
  name: "openai",
  apiKey: OPENAI_API_KEY || "",
  baseURL: OPENAI_API_URL || "",
  compatibility: "strict",
});

export const myProvider = customProvider({
  languageModels: {
    // LLM Draw
    "draw-model-small": tuziProvider(TUZI_MODEL_IMAGE_SMALL) as any,
    "draw-model": tuziProvider(TUZI_MODEL_IMAGE) as any,
    "draw-model-vip": tuziProvider(TUZI_MODEL_IMAGE_VIP) as any,

    // Chat
    "chat-model-small": chatProvider(OPENAI_COMPLETIONS_MODEL_SMALL) as any,
    "chat-model-large": chatProvider(OPENAI_COMPLETIONS_MODEL_LARGE) as any,
    "chat-model-function": chatProvider(
      OPENAI_COMPLETIONS_MODEL_FUNCTION
    ) as any,
    "chat-model-reasoning": wrapLanguageModel({
      model: chatProvider(OPENAI_COMPLETIONS_MODEL_REASONING) as any,
      middleware: extractReasoningMiddleware({ tagName: "think" }),
    }),
    "title-model": chatProvider(OPENAI_COMPLETIONS_MODEL_SMALL) as any,
    "artifact-model": chatProvider(OPENAI_COMPLETIONS_MODEL_LARGE) as any,
  },
  imageModels: {
    "draw-model-raw-small": tuziOaiProvider.image(TUZI_OPENAI_MODEL_IMAGE),
    "draw-model-raw-medium": tuziOaiProvider.image(TUZI_OPENAI_MODEL_IMAGE),
    "draw-model-raw": tuziOaiProvider.image(TUZI_OPENAI_MODEL_IMAGE),
    "draw-model-raw-openai": openaiProvider.image(OPENAI_MODEL_IMAGE),
    "draw-model-grok-2": xaiProvider.image(XAI_API_MODEL_IMAGE),
    "draw-model-flux-pro": tuziOaiProvider.image(TUZI_MODEL_FLUX_PRO),
    "draw-model-flux-max": tuziOaiProvider.image(TUZI_MODEL_FLUX_MAX),
  },
});
