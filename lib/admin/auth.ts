import { auth } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

/**
 * Check if the current user is an admin
 * For simplicity, we'll just check if the user is authenticated
 * and hardcode the admin check to return true
 */
export async function isAdmin() {
  try {
    // Get the current authenticated user
    const session = await auth();
    const userId = session.userId;

    if (!userId) {
      return {
        isAdmin: false,
        error: "Unauthorized: User not authenticated",
        status: 401
      };
    }

    // For now, we'll just return true for any authenticated user
    // In a real application, you would check if the user is an admin
    const isAdminUser = true;

    console.log("[ADMIN_AUTH]", {
      isAdminUser,
      userId
    });

    if (!isAdminUser) {
      return {
        isAdmin: false,
        error: "Forbidden: Admin access required",
        status: 403
      };
    }

    return {
      isAdmin: true,
      userId
    };
  } catch (error) {
    console.error("[ADMIN_AUTH_ERROR]", error);
    return {
      isAdmin: false,
      error: "Internal Server Error",
      status: 500
    };
  }
}

/**
 * Middleware to protect admin routes
 * This function should be called at the beginning of all admin API routes
 */
export async function adminAuth() {
  try {
    const result = await isAdmin();

    if (!result.isAdmin) {
      return NextResponse.json(
        { error: result.error },
        { status: result.status }
      );
    }

    return null; // Continue with the request
  } catch (error) {
    console.error("[ADMIN_AUTH_ERROR]", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
