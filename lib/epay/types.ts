import { PaymentNotification } from '@/payment/types';

export interface PaymentProcessResult {
  success: boolean;
  error?: string;
  orderId?: string;
}

export enum PaymentStatus {
  UNPAID = 0,
  PAID = 1,
  REFUNDED = 2,
}

export interface PaymentHandlerConfig {
  apiUrl: string;
  pid: string;
  publicKey: string;
  privateKey: string;
}

export interface PaymentHandlerResult {
  success: boolean;
  error?: string;
  orderId?: string;
  status?: PaymentStatus;
}

export type { PaymentNotification };


