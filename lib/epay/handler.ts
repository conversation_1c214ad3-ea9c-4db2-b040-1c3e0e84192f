import { db } from '@/lib/db';
import { orders, wallets } from '@/lib/db/schema';
import { eq, sql } from 'drizzle-orm';
import { PaymentClient, PaymentClientInterface } from '@/payment/client';
import { nanoid } from 'nanoid';
import {
  PaymentNotification,
  PaymentProcessResult,
  PaymentStatus,
  PaymentHandlerConfig,
  PaymentHandlerResult
} from './types';

export class PaymentNotificationHandler {
  private client: PaymentClientInterface;

  constructor(config: PaymentHandlerConfig) {
    this.client = new PaymentClient(config);
  }

  private convertToStringRecord(data: Record<string, string | FormDataEntryValue>): Record<string, string> {
    return Object.entries(data).reduce((acc, [key, value]) => {
      acc[key] = value.toString();
      return acc;
    }, {} as Record<string, string>);
  }

  private validateRequiredFields(data: Record<string, string | FormDataEntryValue>): data is Record<string, string> {
    const requiredFields = [
      'pid',
      'trade_no',
      'out_trade_no',
      'type',
      'name',
      'money',
      'trade_status',
      'sign_type',
      'sign'
    ];

    return requiredFields.every(field =>
      typeof data[field] === 'string' && data[field] !== ''
    );
  }

  async validateSignature(data: Record<string, string | FormDataEntryValue>): Promise<boolean> {
    try {
      console.log('[SIGNATURE_DEBUG] Raw data:', JSON.stringify(data));

      const stringData = this.convertToStringRecord(data);
      console.log('[SIGNATURE_DEBUG] String data:', JSON.stringify(stringData));

      if (!this.validateRequiredFields(stringData)) {
        console.error('[VALIDATION_ERROR] Missing required fields');
        return false;
      }

      // Extract signature and data to verify
      const { sign, ...params } = stringData;
      console.log('[SIGNATURE_DEBUG] Sign type:', stringData.sign_type);
      console.log('[SIGNATURE_DEBUG] Sign value:', sign);

      return this.client.verifySignature(params, sign);
    } catch (error) {
      console.error('[SIGNATURE_VALIDATION_ERROR]', error);
      return false;
    }
  }

  async verifyOrder(data: Record<string, string | FormDataEntryValue>): Promise<PaymentProcessResult> {
    try {
      const stringData = this.convertToStringRecord(data);
      if (!this.validateRequiredFields(stringData)) {
        return {
          success: false,
          error: 'Missing required fields'
        };
      }

      // 使用类型断言，因为我们已经验证了所有必需的字段
      const notification = stringData as unknown as PaymentNotification;

      // Extract order ID from out_trade_no (format: ORDER_xxx)
      const orderId = notification.out_trade_no.replace('ORDER_', '');

      // Query order from database
      const order = await db.query.orders.findFirst({
        where: eq(orders.id, orderId)
      });

      if (!order) {
        return {
          success: false,
          error: 'Order not found',
          orderId
        };
      }

      // Verify payment amount
      const extra = order.extra as Record<string, any>;
      const orderAmount = extra.price; // 订单价格（单位：元）
      const paidAmount = parseFloat(notification.money); // 支付金额（单位：元）

      if (paidAmount !== orderAmount) {
        return {
          success: false,
          error: 'Payment amount mismatch',
          orderId
        };
      }

      // Check if order is already processed
      if (order.status === 'SUCCESS') {
        return {
          success: false,
          error: 'Order already processed',
          orderId
        };
      }

      return {
        success: true,
        orderId
      };
    } catch (error) {
      console.error('[ORDER_VERIFICATION_ERROR]', error);
      return {
        success: false,
        error: 'Order verification failed'
      };
    }
  }

  async processPayment(orderId: string, status: PaymentStatus): Promise<PaymentProcessResult> {
    try {
      // Start transaction
      return await db.transaction(async (tx) => {
        // Get order
        const order = await tx.query.orders.findFirst({
          where: eq(orders.id, orderId)
        });

        if (!order) {
          throw new Error('Order not found');
        }

        const extra = order.extra as Record<string, unknown>;

        // Update order status
        await tx
          .update(orders)
          .set({
            status: status === PaymentStatus.PAID ? 'SUCCESS' : 'FAILED',
            paidAt: status === PaymentStatus.PAID ? new Date() : undefined,
            extra: {
              ...extra,
              updatedAt: new Date().toISOString()
            }
          })
          .where(eq(orders.id, orderId));

        // If payment successful, update user's wallet
        if (status === PaymentStatus.PAID) {
          const points = extra?.points as number;

          if (!points) {
            throw new Error('Invalid points in order');
          }

          // Update wallet balance using onConflictDoUpdate
          await tx
            .insert(wallets)
            .values({
              id: nanoid(),
              userId: order.userId,
              permanentPoints: points,
              extra: {
                transactions: [{
                  id: nanoid(),
                  points,
                  orderId,
                  type: 'credit',
                  description: `充值${points}积分`,
                  createdAt: new Date().toISOString()
                }]
              }
            })
            .onConflictDoUpdate({
              target: [wallets.userId],
              set: {
                permanentPoints: sql`${wallets.permanentPoints} + ${points}`,
                extra: sql`jsonb_set(
                  ${wallets.extra},
                  '{transactions}',
                  coalesce(${wallets.extra}->'transactions', '[]'::jsonb) ||
                  jsonb_build_object(
                    'id', ${nanoid()}::text,
                    'points', ${points}::integer,
                    'orderId', ${orderId}::text,
                    'type', 'credit'::text,
                    'description', ${`充值${points}积分`}::text,
                    'createdAt', ${new Date().toISOString()}::text
                  )
                )`,
                updatedAt: new Date()
              }
            });
        }

        return {
          success: true,
          orderId
        };
      });
    } catch (error) {
      console.error('[PAYMENT_PROCESSING_ERROR]', error);
      return {
        success: false,
        error: 'Payment processing failed',
        orderId
      };
    }
  }

  private mapTradeStatusToPaymentStatus(tradeStatus: string): PaymentStatus {
    switch (tradeStatus) {
      case 'TRADE_SUCCESS':
        return PaymentStatus.PAID;
      case 'TRADE_REFUND':
        return PaymentStatus.REFUNDED;
      default:
        return PaymentStatus.UNPAID;
    }
  }

  async handlePaymentNotification(data: Record<string, string>): Promise<PaymentHandlerResult> {
    try {
      // 1. 验证签名
      const isValid = await this.validateSignature(data);
      if (!isValid) {
        return {
          success: false,
          error: 'Invalid signature'
        };
      }

      // 2. 验证订单
      const verifyResult = await this.verifyOrder(data);
      if (!verifyResult.success) {
        return {
          success: false,
          error: verifyResult.error,
          orderId: verifyResult.orderId
        };
      }

      // 3. 处理支付结果
      const paymentStatus = this.mapTradeStatusToPaymentStatus(data.trade_status);
      const processResult = await this.processPayment(
        verifyResult.orderId!,
        paymentStatus
      );

      if (!processResult.success) {
        return {
          success: false,
          error: processResult.error,
          orderId: processResult.orderId
        };
      }

      return {
        success: true,
        orderId: processResult.orderId,
        status: paymentStatus
      };
    } catch (error) {
      console.error('[PAYMENT_NOTIFICATION_ERROR]', error);
      return {
        success: false,
        error: 'System error'
      };
    }
  }

  /**
   * Simple notification handler that doesn't require signature verification
   * This is based on the PHP example implementation
   */
  async handleSimpleNotification(data: Record<string, string>): Promise<PaymentHandlerResult> {
    try {
      console.log('[SIMPLE_NOTIFY_DEBUG] Processing notification:', JSON.stringify(data));

      // Skip signature validation, just check if required fields are present
      if (!data.out_trade_no || !data.trade_no || !data.trade_status) {
        console.error('[SIMPLE_NOTIFY_ERROR] Missing required fields');
        return {
          success: false,
          error: 'Missing required fields'
        };
      }

      // Extract order ID from out_trade_no (format: ORDER_xxx)
      const orderId = data.out_trade_no.replace('ORDER_', '');

      // Check if payment was successful
      if (data.trade_status !== 'TRADE_SUCCESS') {
        console.log('[SIMPLE_NOTIFY_INFO] Payment not successful, status:', data.trade_status);
        return {
          success: false,
          error: 'Payment not successful',
          orderId
        };
      }

      // Query order from database
      const order = await db.query.orders.findFirst({
        where: eq(orders.id, orderId)
      });

      if (!order) {
        console.error('[SIMPLE_NOTIFY_ERROR] Order not found:', orderId);
        return {
          success: false,
          error: 'Order not found',
          orderId
        };
      }

      // Verify payment amount if present
      if (data.money) {
        const extra = order.extra as Record<string, any>;
        const orderAmount = extra.price; // 订单价格（单位：元）
        const paidAmount = parseFloat(data.money); // 支付金额（单位：元）

        if (paidAmount !== orderAmount) {
          console.error(
            '[SIMPLE_NOTIFY_ERROR] Payment amount mismatch:',
            `expected ${orderAmount}, got ${paidAmount}`
          );
          return {
            success: false,
            error: 'Payment amount mismatch',
            orderId
          };
        }
      }

      // Check if order is already processed
      if (order.status === 'SUCCESS') {
        console.log('[SIMPLE_NOTIFY_INFO] Order already processed:', orderId);
        return {
          success: true, // Return success even if already processed
          orderId,
          status: PaymentStatus.PAID
        };
      }

      // Process the payment
      const processResult = await this.processPayment(
        orderId,
        PaymentStatus.PAID // Directly use PAID status since we already checked trade_status
      );

      if (!processResult.success) {
        return {
          success: false,
          error: processResult.error,
          orderId: processResult.orderId
        };
      }

      return {
        success: true,
        orderId: processResult.orderId,
        status: PaymentStatus.PAID
      };
    } catch (error) {
      console.error('[SIMPLE_NOTIFY_ERROR]', error);
      return {
        success: false,
        error: 'System error'
      };
    }
  }
}
