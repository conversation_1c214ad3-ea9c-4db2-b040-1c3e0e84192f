import { db } from "@/lib/db";
import { shares, users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function getPublicShares() {
  return await db.query.shares.findMany({
    where: eq(shares.isPublic, true),
    orderBy: (shares, { desc }) => [desc(shares.createdAt)],
  });
}

export async function getPublicShareById(shareId: string) {
  return await db.query.shares.findFirst({
    where: eq(shares.shareId, shareId),
    with: {
      user: {
        columns: {
          clerkId: true,
          username: true,
          avatarUrl: true,
        },
      },
    },
  });
}
