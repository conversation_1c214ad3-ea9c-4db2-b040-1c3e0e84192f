import { useToast } from "@/lib/hooks/use-toast";

export async function copyToClipboard(text: string, successMessage = "已复制到剪贴板", errorMessage = "复制失败，请重试") {
  const { toast } = useToast();

  try {
    await navigator.clipboard.writeText(text);
    toast({
      title: "成功",
      description: successMessage,
      className: "bg-green-500 text-white border-green-600",
    });
    return true;
  } catch (error) {
    console.error("Error copying to clipboard:", error);
    toast({
      variant: "destructive",
      title: "错误",
      description: errorMessage,
    });
    return false;
  }
}
