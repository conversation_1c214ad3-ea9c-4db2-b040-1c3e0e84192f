/**
 * 图片压缩工具
 * 使用 jSquash 库将图片压缩为 WebP 格式
 */

import { encode } from '@jsquash/webp';

/**
 * 将 File 对象转换为 Data URL
 * @param file 图片文件
 * @returns Promise<string> Data URL
 */
export const fileToDataUrl = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

/**
 * 将 Data URL 转换为 Image 对象
 * @param dataUrl Data URL
 * @returns Promise<HTMLImageElement> Image 对象
 */
export const dataUrlToImage = (dataUrl: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = dataUrl;
  });
};

/**
 * 使用 Canvas 压缩图片
 * @param image 图片对象
 * @param quality 压缩质量 (0-1)
 * @param type 输出类型 (默认为 'image/webp')
 * @returns Promise<string> 压缩后的 Data URL
 */
export const compressWithCanvas = (
  image: HTMLImageElement,
  quality: number = 0.8,
  type: string = 'image/webp'
): string => {
  const canvas = document.createElement('canvas');
  canvas.width = image.width;
  canvas.height = image.height;

  const ctx = canvas.getContext('2d');
  if (!ctx) {
    throw new Error('无法创建 Canvas 上下文');
  }

  // 绘制图片到 Canvas
  ctx.drawImage(image, 0, 0);

  // 返回压缩后的 Data URL
  return canvas.toDataURL(type, quality);
};

/**
 * 将 Data URL 转换为 File 对象
 * @param dataUrl Data URL
 * @param filename 文件名
 * @param type MIME 类型
 * @returns File 对象
 */
export const dataUrlToFile = (
  dataUrl: string,
  filename: string,
  type: string = 'image/webp'
): File => {
  // 从 Data URL 中提取 base64 数据
  const arr = dataUrl.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || type;
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  // 创建 File 对象
  return new File([u8arr], filename, { type: mime });
};

/**
 * 压缩单个图片文件
 * @param file 原始图片文件
 * @param quality 压缩质量 (0-100)
 * @returns Promise<File> 压缩后的文件
 */
export const compressImage = async (
  file: File,
  quality: number = 80
): Promise<File> => {
  try {
    // 创建图片对象
    const image = new Image();
    const blobUrl = URL.createObjectURL(file);

    // 等待图片加载
    await new Promise((resolve) => {
      image.onload = resolve;
      image.src = blobUrl;
    });

    // 创建 canvas 并绘制图片
    const canvas = document.createElement('canvas');
    canvas.width = image.width;
    canvas.height = image.height;
    const ctx = canvas.getContext('2d');
    ctx?.drawImage(image, 0, 0);

    // 释放 Blob URL
    URL.revokeObjectURL(blobUrl);

    // 从 canvas 获取图像数据
    const imageData = ctx?.getImageData(0, 0, canvas.width, canvas.height);

    if (!imageData) {
      throw new Error('无法获取图像数据');
    }

    // 使用 jSquash 压缩为 WebP
    const compressedData = await encode(imageData, {
      quality
    });

    // 获取新文件名 (替换扩展名为 .webp)
    const filename = file.name.replace(/\.[^.]+$/, '.webp');

    // 创建新的 File 对象
    const compressedFile = new File(
      [compressedData],
      filename,
      { type: 'image/webp' }
    );

    return compressedFile;
  } catch (error) {
    console.error('图片压缩失败:', error);
    throw error;
  }
};

/**
 * 批量压缩多个图片
 * @param files 图片文件数组
 * @param quality 压缩质量 (0-1)
 * @returns Promise<File[]> 压缩后的文件数组
 */
export const compressImages = async (
  files: File[],
  quality: number = 80
): Promise<File[]> => {
  const compressPromises = files.map(file => compressImage(file, quality));
  return Promise.all(compressPromises);
};

/**
 * 计算文件大小 (MB)
 * @param file 文件
 * @returns 文件大小 (MB)
 */
export const getFileSizeMB = (file: File): number => {
  return file.size / (1024 * 1024);
};

/**
 * 计算多个文件的总大小 (MB)
 * @param files 文件数组
 * @returns 总大小 (MB)
 */
export const getTotalSizeMB = (files: File[]): number => {
  const totalBytes = files.reduce((sum, file) => sum + file.size, 0);
  return totalBytes / (1024 * 1024);
};
