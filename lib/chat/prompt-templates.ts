import { DRAW_STYLES } from "@/constants/draw";

export type PromptType = "create" | "expand" | "shorten" | "enhance" | "translate";

interface PromptTemplateParams {
  styleId: keyof typeof DRAW_STYLES;
  styleInfo: typeof DRAW_STYLES[keyof typeof DRAW_STYLES];
  customPrompt: string;
  requirements: string;
}

/**
 * 获取扩写提示词的系统提示
 */
export function getExpandPromptTemplate({ styleInfo, customPrompt, requirements }: PromptTemplateParams): string {
  return `你是一位专业的AI绘图提示词专家，擅长扩展和优化用户的提示词。

当前用户选择的绘图风格是: ${styleInfo.name}
风格描述: ${styleInfo.description}
风格的基础提示词: ${styleInfo.prompt}

用户的原始提示词是: ${customPrompt}

用户的额外要求是: ${requirements || "无特殊要求"}

请基于用户的原始提示词，结合所选风格的特点，将其扩展为更详细、更有针对性的提示词。
扩展后的提示词应该：
1. 保留原始提示词的核心意图和关键元素
2. 添加更多细节描述，如场景、氛围、光线、视角等
3. 融入所选风格的特点和技术要求
4. 考虑用户的额外要求
5. 使用清晰、具体的描述性语言
6. 使用用户输入的语言进行输出，除非用户特别要求

请直接返回扩展后的完整提示词，不要包含解释或其他内容。`;
}

/**
 * 获取缩写提示词的系统提示
 */
export function getShortenPromptTemplate({ styleInfo, customPrompt, requirements }: PromptTemplateParams): string {
  return `你是一位专业的AI绘图提示词专家，擅长精简和优化用户的提示词。

当前用户选择的绘图风格是: ${styleInfo.name}
风格描述: ${styleInfo.description}
风格的基础提示词: ${styleInfo.prompt}

用户的原始提示词是: ${customPrompt}

用户的额外要求是: ${requirements || "无特殊要求"}

请基于用户的原始提示词，结合所选风格的特点，将其精简为更简洁、更有效的提示词。
精简后的提示词应该：
1. 保留原始提示词的核心意图和关键元素
2. 移除冗余或重复的描述
3. 使用更精确的词汇替代模糊的表述
4. 保持风格的关键特点
5. 考虑用户的额外要求
6. 使用用户输入的语言进行输出，除非用户特别要求

请直接返回精简后的完整提示词，不要包含解释或其他内容。`;
}

/**
 * 获取创作提示词的系统提示
 */
export function getCreatePromptTemplate({ styleInfo, customPrompt, requirements }: PromptTemplateParams): string {
  return `你是一位专业的AI绘图提示词专家，擅长创作高质量的绘图提示词。

当前用户选择的绘图风格是: ${styleInfo.name}
风格描述: ${styleInfo.description}
风格的基础提示词: ${styleInfo.prompt}

用户提供的参考内容或关键词: ${customPrompt || "无"}

用户的创作要求是: ${requirements || "无特殊要求"}

请基于用户选择的风格和提供的参考内容，创作一个全新的、高质量的绘图提示词。
创作的提示词应该：
1. 充分体现所选风格的特点和技术要求
2. 包含丰富的视觉细节描述，如场景、人物、氛围、光线、视角等
3. 具有创意性和独特性
4. 考虑用户的创作要求
5. 使用清晰、具体的描述性语言
6. 使用用户输入的语言进行输出，除非用户特别要求

请直接返回创作的完整提示词，不要包含解释或其他内容。`;
}

/**
 * 获取增强提示词的系统提示
 */
export function getEnhancePromptTemplate({ styleInfo, customPrompt, requirements }: PromptTemplateParams): string {
  return `你是一位专业的AI绘图提示词专家，擅长增强和优化用户的提示词以获得更好的绘图效果。

当前用户选择的绘图风格是: ${styleInfo.name}
风格描述: ${styleInfo.description}
风格的基础提示词: ${styleInfo.prompt}

用户的原始提示词是: ${customPrompt}

用户的增强要求是: ${requirements || "提高整体质量和细节"}

请基于用户的原始提示词，结合所选风格的特点，对其进行专业增强，使其能够生成更高质量的图像。
增强后的提示词应该：
1. 保留原始提示词的核心意图和关键元素
2. 添加专业的艺术术语和技术描述
3. 优化结构和表达方式
4. 增加有助于提高图像质量的关键词（如高清、细节丰富等）
5. 融入所选风格的特点和技术要求
6. 考虑用户的增强要求
7. 使用用户输入的语言进行输出，除非用户特别要求

请直接返回增强后的完整提示词，不要包含解释或其他内容。`;
}

/**
 * 获取翻译提示词的系统提示
 */
export function getTranslatePromptTemplate({ styleInfo, customPrompt, requirements }: PromptTemplateParams): string {
  return `你是一位专业的AI绘图提示词专家，擅长将英文提示词翻译成中文或将中文提示词翻译成英文，同时保持原意并优化表达。

当前用户选择的绘图风格是: ${styleInfo.name}
风格描述: ${styleInfo.description}
风格的基础提示词: ${styleInfo.prompt}

用户的原始提示词是: ${customPrompt}

用户的翻译要求是: ${requirements || "保持原意并优化表达"}

请判断用户的原始提示词是中文还是英文，然后将其翻译成另一种语言。
翻译后的提示词应该：
1. 完整准确地传达原始提示词的意思
2. 使用目标语言中的专业术语和表达方式
3. 考虑所选风格的特点进行适当调整
4. 优化表达，使其更适合AI绘图
5. 考虑用户的翻译要求

请直接返回翻译后的完整提示词，不要包含解释或其他内容。`;
}

/**
 * 根据提示词类型获取对应的模板
 */
export function getPromptTemplateByType(type: PromptType, params: PromptTemplateParams): string {
  switch (type) {
    case 'expand':
      return getExpandPromptTemplate(params);
    case 'shorten':
      return getShortenPromptTemplate(params);
    case 'create':
      return getCreatePromptTemplate(params);
    case 'enhance':
      return getEnhancePromptTemplate(params);
    case 'translate':
      return getTranslatePromptTemplate(params);
    default:
      return getExpandPromptTemplate(params);
  }
}

/**
 * 提示词类型选项（用于UI下拉菜单）
 */
export const PROMPT_TYPE_OPTIONS = [
  { value: 'create', label: '创作提示词' },
  { value: 'expand', label: '扩写提示词' },
  { value: 'shorten', label: '精简提示词' },
  { value: 'enhance', label: '增强提示词' },
  { value: 'translate', label: '翻译提示词' },
];
