/**
 * 解析AI流式响应数据
 * 处理格式如：
 * f:{"messageId":"msg-21X0B4FlfZpXz5nkqE2hzOOp"}
 * 0:"实际内容..."
 * e:{"finishReason":"stop","usage":{"promptTokens":null,"completionTokens":null},"isContinued":false}
 */
export function parseStreamData(chunk: string): string {
  // 如果是空字符串，直接返回
  if (!chunk || chunk.trim() === '') {
    return '';
  }

  try {
    // 分割行
    const lines = chunk.split('\n').filter(line => line.trim() !== '');
    
    // 查找内容行（以数字开头的行）
    for (const line of lines) {
      // 匹配内容行，格式为 0:"内容"
      const contentMatch = line.match(/^\d+:"(.+)"$/);
      if (contentMatch && contentMatch[1]) {
        return contentMatch[1];
      }
    }

    // 如果没有找到内容行，检查是否是JSON格式的行
    for (const line of lines) {
      // 跳过以f:, e:, d:开头的行
      if (line.startsWith('f:') || line.startsWith('e:') || line.startsWith('d:')) {
        continue;
      }
      
      // 尝试解析JSON
      try {
        const jsonStart = line.indexOf('{');
        if (jsonStart !== -1) {
          const jsonStr = line.substring(jsonStart);
          const data = JSON.parse(jsonStr);
          if (data.content) {
            return data.content;
          }
        }
      } catch (e) {
        // JSON解析失败，忽略
      }
    }
  } catch (error) {
    console.error('解析流数据时出错:', error);
  }

  // 如果无法解析，返回原始数据
  return chunk;
}

/**
 * 处理完整的流式响应
 * @param chunks 接收到的所有数据块
 * @returns 处理后的完整内容
 */
export function processStreamResponse(chunks: string[]): string {
  // 合并所有数据块
  const fullResponse = chunks.join('');
  
  // 分割行
  const lines = fullResponse.split('\n').filter(line => line.trim() !== '');
  
  // 提取所有内容行
  let content = '';
  for (const line of lines) {
    // 匹配内容行，格式为 0:"内容"
    const contentMatch = line.match(/^\d+:"(.+)"$/);
    if (contentMatch && contentMatch[1]) {
      content += contentMatch[1];
    }
  }
  
  return content || fullResponse;
}
