export type PaymentType = "alipay" | "wxpay";
export type PayMethodType = "web" | "jump" | "jsapi" | "app" | "scan" | "applet";
export type DeviceType = "pc" | "mobile" | "qq" | "wechat" | "alipay";
export type PayResponseType = "jump" | "html" | "qrcode" | "urlscheme" | "jsapi" | "app" | "scan" | "wxplugin" | "wxapp";

export interface PaymentConfig {
  apiUrl: string;
  pid: string;
  publicKey: string;
  privateKey: string;
}

export interface PaymentRequest {
  type: PaymentType;
  outTradeNo: string;
  /** 支付金额（单位：元） */
  amount: number;
  name: string;
  notifyUrl: string;
  returnUrl: string;
  device?: DeviceType;
  method?: PayMethodType;
  param?: string;
}

export interface CreatePaymentRequest {
  type: string;
  out_trade_no: string;
  /** 支付金额（单位：元） */
  amount: number;
  name: string;
  notify_url: string;
  return_url: string;
  clientip?: string;
}

export interface CreatePaymentResponse {
  code: number;
  msg: string;
  trade_no: string;
  pay_type: PayResponseType;
  pay_info: string;
  timestamp: string;
  sign: string;
  sign_type: string;
}

export interface PaymentResponse {
  code: number;
  msg: string;
  trade_no: string;
  qrcode?: string;
  code_url?: string;
  qrCodeUrl: string;
}

export interface PaymentNotification {
  pid: string;
  trade_no: string;
  out_trade_no: string;
  api_trade_no?: string;
  type: string;
  name: string;
  money: string; // 支付金额（单位：元，字符串格式）
  trade_status: string;
  addtime: string;
  endtime?: string;
  param?: string;
  buyer?: string;
  timestamp: string;
  sign: string;
  sign_type: string;
}

export interface OrderStatus {
  status: "pending" | "success" | "failed" | "refund";
  orderId: string;
  extra?: {
    price: number; // 价格（单位：元）
    [key: string]: any;
  };
  createdAt: string;
}

export interface PaymentClientInterface {
  verifySignature(params: Record<string, string>, sign: string): boolean;
  createPaymentRequest(request: CreatePaymentRequest): Promise<PaymentResponse>;
}
