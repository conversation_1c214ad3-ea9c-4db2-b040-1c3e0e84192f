import { createHash, create<PERSON><PERSON><PERSON><PERSON><PERSON>, createVerify, createSign } from 'crypto';
import type {
  PaymentConfig,
  PaymentClientInterface,
  CreatePaymentRequest,
  PaymentResponse,
} from './types';

export type { PaymentConfig, PaymentClientInterface } from './types';

export class PaymentClient implements PaymentClientInterface {
  private config: PaymentConfig;

  constructor(config: PaymentConfig) {
    this.config = config;
  }

  private generateSignature(params: Record<string, string | number>): string {
    // Sort parameters alphabetically
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((acc, key) => {
        if (key !== 'sign' && key !== 'sign_type' && params[key] !== undefined && params[key] !== '') {
          acc[key] = params[key];
        }
        return acc;
      }, {} as Record<string, string | number>);

    // Create string to sign
    const stringToSign = Object.entries(sortedParams)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');

    // Generate signature using merchant private key
    const signature = createHash('md5')
      .update(stringToSign + this.config.privateKey)
      .digest('hex');

    return signature;
  }

  private generateRSASignature(params: Record<string, string | number>): string {
    // Sort parameters alphabetically
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((acc, key) => {
        if (key !== 'sign' && key !== 'sign_type' && params[key] !== undefined && params[key] !== '') {
          acc[key] = params[key];
        }
        return acc;
      }, {} as Record<string, string | number>);

    // Create string to sign
    const stringToSign = Object.entries(sortedParams)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');

    try {
      // Use SHA256WithRSA algorithm for signing
      const sign = createSign('SHA256'); // This is SHA256WithRSA in Node.js crypto
      sign.update(stringToSign);

      // Prepare private key in PEM format - direct concatenation
      const privateKeyPEM = `-----BEGIN PRIVATE KEY-----\n${this.config.privateKey}\n-----END PRIVATE KEY-----`;

      // Sign using SHA256WithRSA
      const signature = sign.sign(privateKeyPEM, 'base64');

      return signature;
    } catch (error) {
      console.error('[GENERATE_RSA_SIGNATURE_ERROR]', error);
      console.error('Sign content:', stringToSign);
      throw error;
    }
  }

  public verifySignature(params: Record<string, string>, sign: string): boolean {
    try {
      // Check if sign_type is RSA
      if (params.sign_type === 'RSA') {
        return this.verifyRSASignature(params, sign);
      }

      // Fallback to MD5 signature verification
      const calculatedSignature = this.generateSignature(params);
      return calculatedSignature === sign;
    } catch (error) {
      console.error('[VERIFY_SIGNATURE_ERROR]', error);
      return false;
    }
  }

  private verifyRSASignature(params: Record<string, string>, sign: string): boolean {
    // Sort parameters alphabetically and build string to verify
    let signContent = '';
    try {
      // Build the signature content string
      signContent = Object.keys(params)
        .sort()
        .reduce((str, key) => {
          if (key !== 'sign' && key !== 'sign_type' && params[key] !== undefined && params[key] !== '') {
            return str ? `${str}&${key}=${params[key]}` : `${key}=${params[key]}`;
          }
          return str;
        }, '');

      // Properly format the public key
      // The key might be in a format that Node.js crypto can't directly use
      let formattedPublicKey = this.config.publicKey;

      // Clean the key first (remove any potential whitespace or line breaks)
      formattedPublicKey = formattedPublicKey.replace(/\s/g, '');

      // Properly format with headers and proper line breaks every 64 characters
      formattedPublicKey = '-----BEGIN PUBLIC KEY-----\n' +
          formattedPublicKey.match(/.{1,64}/g)?.join('\n') +
          '\n-----END PUBLIC KEY-----';

      console.log('[VERIFY_DEBUG] Formatted public key:', formattedPublicKey);

      try {
        // Create verify object using SHA256WithRSA algorithm
        const verify = createVerify('SHA256'); // This is SHA256WithRSA in Node.js crypto
        verify.update(signContent);

        // Decode the URI-encoded sign parameter
        const decodedSign = decodeURIComponent(sign);

        // Verify signature using SHA256WithRSA
        return verify.verify(
          formattedPublicKey,
          Buffer.from(decodedSign, 'base64')
        );
      } catch (innerError) {
        // Try alternative key format if the first attempt fails
        console.error('[VERIFY_ERROR] First attempt failed, trying alternative format:', innerError);

        // Try with RSA key format instead
        formattedPublicKey = '-----BEGIN RSA PUBLIC KEY-----\n' +
            formattedPublicKey.match(/.{1,64}/g)?.join('\n') +
            '\n-----END RSA PUBLIC KEY-----';

        console.log('[VERIFY_DEBUG] Alternative formatted public key:', formattedPublicKey);

        const verify = createVerify('SHA256');
        verify.update(signContent);
        return verify.verify(
          formattedPublicKey,
          Buffer.from(decodeURIComponent(sign), 'base64')
        );
      }
    } catch (error) {
      console.error('[VERIFY_RSA_SIGNATURE_ERROR]', error);
      console.error('Sign content:', signContent);
      console.error('Public key:', this.config.publicKey);
      console.error('Signature:', sign);
      return false;
    }
  }

  public async createPaymentRequest(request: CreatePaymentRequest): Promise<PaymentResponse> {
    try {
      const params = {
        pid: this.config.pid,
        type: request.type,
        method: 'web', // 使用通用网页支付方式
        device: 'pc', // 设备类型为PC
        out_trade_no: request.out_trade_no,
        notify_url: request.notify_url,
        return_url: request.return_url,
        name: request.name,
        money: request.amount.toString(), // 金额（单位：元），转换为字符串
        clientip: request.clientip || '127.0.0.1', // 客户端IP，必填参数
        sign_type: 'RSA', // Use RSA signature
        timestamp: Math.floor(Date.now() / 1000).toString(), // 10位时间戳，单位秒
      };

      // Generate RSA signature
      const signature = this.generateRSASignature(params);

      // Add signature to params
      const requestParams = {
        ...params,
        sign: signature,
      };

      // Log request parameters for debugging
      console.log('[PAYMENT_REQUEST_DEBUG] Request URL:', `${this.config.apiUrl}/api/pay/create`);
      console.log('[PAYMENT_REQUEST_DEBUG] Request params:', JSON.stringify(requestParams, null, 2));

      // Create URL-encoded form data
      const formData = new URLSearchParams(requestParams as Record<string, string>);

      // Send request to payment gateway
      const response = await fetch(`${this.config.apiUrl}/api/pay/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0',
        },
        body: formData,
      });

      // Log response headers for debugging
      console.log('[PAYMENT_REQUEST_DEBUG] Response status:', response.status);
      console.log('[PAYMENT_REQUEST_DEBUG] Response headers:', Object.fromEntries(response.headers.entries()));

      // Get response text
      const text = await response.text();
      console.log('[PAYMENT_REQUEST_DEBUG] Response text:', text);

      // Parse response as JSON
      try {
        const data = JSON.parse(text);

        if (data.code !== 0) { // API文档中成功状态码为0
          throw new Error(`Payment request failed: ${data.msg}`);
        }

        // 根据不同的支付类型处理返回值
        let qrCodeUrl = '';
        if (data.pay_type === 'qrcode') {
          // 直接返回二维码链接
          qrCodeUrl = data.pay_info;
        } else if (data.pay_type === 'jump') {
          // 跳转URL
          qrCodeUrl = data.pay_info;
        } else if (data.pay_type === 'jsapi' || data.pay_type === 'app' ||
                  data.pay_type === 'wxplugin' || data.pay_type === 'wxapp') {
          // 对于特殊支付类型，直接返回对应的支付信息
          qrCodeUrl = data.pay_info;
        } else {
          // 默认情况
          qrCodeUrl = data.pay_info;
        }

        return {
          code: 1, // 我们内部使用1表示成功
          msg: 'success',
          trade_no: data.trade_no,
          qrcode: qrCodeUrl,
          code_url: qrCodeUrl,
          qrCodeUrl: qrCodeUrl,
        };
      } catch (e) {
        console.error('[PAYMENT_REQUEST_ERROR] Failed to parse JSON response:', e);

        // 检查是否是重定向响应
        if (text.includes('window.location.replace')) {
          const redirectMatch = text.match(/window\.location\.replace\(['"]([^'"]+)['"]\)/);
          if (redirectMatch) {
            const redirectUrl = redirectMatch[1];
            console.log('[PAYMENT_REQUEST_DEBUG] Found redirect URL:', redirectUrl);

            // 构建完整URL
            const fullRedirectUrl = redirectUrl.startsWith('/')
              ? new URL(redirectUrl, this.config.apiUrl).toString()
              : redirectUrl;

            return {
              code: 1,
              msg: 'success',
              trade_no: request.out_trade_no,
              qrcode: fullRedirectUrl,
              code_url: fullRedirectUrl,
              qrCodeUrl: fullRedirectUrl,
            };
          }
        }

        throw new Error(`Could not process payment response: ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}`);
      }
    } catch (error) {
      console.error('[CREATE_PAYMENT_ERROR]', error);
      throw error;
    }
  }
}
