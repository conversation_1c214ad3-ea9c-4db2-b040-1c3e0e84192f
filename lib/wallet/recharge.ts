import { db } from '@/lib/db';
import { wallets } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { nanoid } from 'nanoid';

interface WalletTransaction {
  id: string;
  points: number;
  type: 'credit' | 'debit';
  description: string;
  createdAt: string;
  [key: string]: any;
}

interface WalletExtra {
  transactions?: WalletTransaction[];
  [key: string]: any;
}

export interface RechargePoints {
  userId: string;
  points: number;
  description?: string;
  metadata?: Record<string, any>;
}

export async function rechargeUserPoints(params: RechargePoints) {
  const { userId, points, description = `充值${points}积分`, metadata = {} } = params;
  const walletId = nanoid();

  // 查询用户钱包
  const userWallet = await db.query.wallets.findFirst({
    where: eq(wallets.userId, userId),
  });

  const transaction: WalletTransaction = {
    id: walletId,
    points,
    type: 'credit',
    description,
    createdAt: new Date().toISOString(),
    ...metadata
  };

  if (!userWallet) {
    // 创建新钱包
    const extra: WalletExtra = {
      transactions: [transaction]
    };

    const newWallet = await db
      .insert(wallets)
      .values({
        id: walletId,
        userId,
        permanentPoints: points,
        extra
      })
      .returning();

    return {
      success: true,
      walletId,
      currentPoints: points,
    };
  }

  // 更新现有钱包
  const currentPoints = userWallet.permanentPoints || 0;
  const newPoints = currentPoints + points;
  const currentExtra = userWallet.extra as WalletExtra;
  const transactions = currentExtra?.transactions || [];

  const updatedExtra: WalletExtra = {
    ...currentExtra,
    transactions: [...transactions, transaction]
  };

  const updatedWallet = await db
    .update(wallets)
    .set({
      permanentPoints: newPoints,
      extra: updatedExtra
    })
    .where(eq(wallets.userId, userId))
    .returning();

  return {
    success: true,
    walletId,
    currentPoints: newPoints,
  };
}
