import { db } from '@/lib/db';
import { invitations, invitationUsages, InviteType, InvitationUsageStatus, wallets } from '@/lib/db/schema';
import { eq, and, or, desc, count, sql, inArray, SQL } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import { createLogger } from '@/lib/draw/logger';

const logger = createLogger('invitation-service');

/**
 * 创建邀请码
 */
export async function createInvitation({
  referrerId,
  inviteCode = nanoid(8),
  inviteType,
  refRatio,
  channel,
  maxUses = 0,
  expiresAt,
}: {
  referrerId: string;
  inviteCode?: string;
  inviteType: InviteType;
  refRatio: number;
  channel?: string;
  maxUses?: number;
  expiresAt?: Date;
}) {
  try {
    // 检查邀请码是否已存在
    const existingInvitation = await db.query.invitations.findFirst({
      where: eq(invitations.inviteCode, inviteCode),
    });

    if (existingInvitation) {
      throw new Error('邀请码已存在');
    }

    // 创建新邀请码
    const id = nanoid();
    const [newInvitation] = await db
      .insert(invitations)
      .values({
        id,
        inviteCode,
        referrerId,
        inviteType,
        refRatio: String(refRatio), // Convert number to string for numeric field
        channel,
        maxUses,
        expiresAt,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return newInvitation;
  } catch (error: any) {
    logger.error('创建邀请码失败', error);
    throw error;
  }
}

/**
 * 获取用户的邀请码列表
 */
export async function getUserInvitations(userId: string, limit = 50, offset = 0) {
  try {
    // 获取邀请码列表
    const userInvitations = await db.query.invitations.findMany({
      where: eq(invitations.referrerId, userId),
      orderBy: [desc(invitations.createdAt)],
      limit,
      offset,
    });

    // 获取每个邀请码的使用统计
    const invitationsWithStats = await Promise.all(
      userInvitations.map(async (invitation) => {
        const usageCount = await db
          .select({ count: count() })
          .from(invitationUsages)
          .where(eq(invitationUsages.invitationId, invitation.id));

        const readyCount = await db
          .select({ count: count() })
          .from(invitationUsages)
          .where(
            and(
              eq(invitationUsages.invitationId, invitation.id),
              eq(invitationUsages.status, 'ready')
            )
          );

        const completedCount = await db
          .select({ count: count() })
          .from(invitationUsages)
          .where(
            and(
              eq(invitationUsages.invitationId, invitation.id),
              eq(invitationUsages.status, 'completed')
            )
          );

        return {
          ...invitation,
          usageCount: usageCount[0]?.count || 0,
          readyCount: readyCount[0]?.count || 0,
          completedCount: completedCount[0]?.count || 0,
        };
      })
    );

    // 获取总数
    const totalCount = await db
      .select({ count: count() })
      .from(invitations)
      .where(eq(invitations.referrerId, userId));

    return {
      invitations: invitationsWithStats,
      total: totalCount[0]?.count || 0,
      limit,
      offset,
    };
  } catch (error: any) {
    logger.error('获取邀请码列表失败', error);
    throw error;
  }
}

/**
 * 验证邀请码有效性
 */
export async function validateInviteCode(inviteCode: string) {
  try {
    const invitation = await db.query.invitations.findFirst({
      where: eq(invitations.inviteCode, inviteCode),
    });

    if (!invitation) {
      return { valid: false, message: '邀请码不存在' };
    }

    // 检查是否过期
    if (invitation.expiresAt && invitation.expiresAt < new Date()) {
      return { valid: false, message: '邀请码已过期' };
    }

    // 检查使用次数是否达到上限
    if (invitation.maxUses > 0) {
      const usageCount = await db
        .select({ count: count() })
        .from(invitationUsages)
        .where(eq(invitationUsages.invitationId, invitation.id));

      if ((usageCount[0]?.count || 0) >= invitation.maxUses) {
        return { valid: false, message: '邀请码已达到使用上限' };
      }
    }

    return { valid: true, invitation };
  } catch (error: any) {
    logger.error('验证邀请码失败', error);
    throw error;
  }
}

/**
 * 创建邀请使用记录
 */
export async function createInvitationUsage(inviteCode: string, refereeId: string) {
  try {
    // 验证邀请码
    const { valid, invitation, message } = await validateInviteCode(inviteCode);
    if (!valid || !invitation) {
      throw new Error(message || '邀请码无效');
    }

    // 检查是否已经使用过该邀请码
    const existingUsage = await db.query.invitationUsages.findFirst({
      where: and(
        eq(invitationUsages.invitationId, invitation.id),
        eq(invitationUsages.refereeId, refereeId)
      ),
    });

    if (existingUsage) {
      return existingUsage;
    }

    // 创建使用记录
    const id = nanoid();
    const [newUsage] = await db
      .insert(invitationUsages)
      .values({
        id,
        invitationId: invitation.id,
        refereeId,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return newUsage;
  } catch (error: any) {
    logger.error('创建邀请使用记录失败', error);
    throw error;
  }
}

/**
 * 获取邀请使用记录
 */
export async function getInvitationUsages(
  userId: string,
  params: {
    invitationId?: string;
    status?: InvitationUsageStatus;
    limit?: number;
    offset?: number;
  } = {}
) {
  try {
    const { invitationId, status, limit = 50, offset = 0 } = params;

    // 查询用户创建的邀请码
    const userInvitations = await db.query.invitations.findMany({
      where: eq(invitations.referrerId, userId),
      columns: { id: true },
    });

    const invitationIds = userInvitations.map(inv => inv.id);
    if (invitationIds.length === 0) {
      return {
        usages: [],
        total: 0,
        limit,
        offset,
      };
    }

    // 使用 Drizzle 的查询构建器
    let query = db.query.invitationUsages;

    // 执行查询
    const usages = await query.findMany({
      where: (usage) => {
        // 初始条件：邀请码ID在用户的邀请码列表中
        let condition = inArray(usage.invitationId, invitationIds);

        // 如果指定了特定邀请码ID
        if (invitationId) {
          condition = and(condition, eq(usage.invitationId, invitationId)) as SQL<unknown>;
        }

        // 如果指定了状态
        if (status) {
          condition = and(condition, eq(usage.status, status)) as SQL<unknown>;
        }

        return condition;
      },
      orderBy: [desc(invitationUsages.createdAt)],
      limit,
      offset,
      with: {
        invitation: true,
        referee: {
          columns: {
            clerkId: true,
            username: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
    });

    // 获取总数
    const totalCount = await db
      .select({ count: count() })
      .from(invitationUsages)
      .where(inArray(invitationUsages.invitationId, invitationIds))
      .execute();

    return {
      usages,
      total: totalCount[0]?.count || 0,
      limit,
      offset,
    };
  } catch (error: any) {
    logger.error('获取邀请使用记录失败', error);
    throw error;
  }
}

/**
 * 更新邀请使用记录状态（首次充值后）
 * @param refereeId 被邀请人的用户ID
 * @param rechargeAmount 充值金额（单位：元）
 */
export async function updateInvitationUsageAfterRecharge(
  refereeId: string,
  points: number,
  rechargeAmount: number
) {
  try {
    console.log(`[INVITATION_RECHARGE_DEBUG] 开始处理邀请奖励，用户ID: ${refereeId}`);
    console.log(`[INVITATION_RECHARGE_DEBUG] 原始充值金额: ${rechargeAmount}元 (数据类型: ${typeof rechargeAmount})`);
    console.log(`[INVITATION_RECHARGE_DEBUG] 充值积分: ${points}积分`);

    // 记录日志
    logger.info(`处理邀请奖励，用户ID: ${refereeId}, 充值金额: ${Math.floor(rechargeAmount)}元`); // 输入金额单位：元


    // 查找用户的邀请记录
    const usage = await db.query.invitationUsages.findFirst({
      where: and(
        eq(invitationUsages.refereeId, refereeId),
        eq(invitationUsages.status, 'pending')
      ),
      with: {
        invitation: true,
      },
    });

    if (!usage || !usage.invitation) {
      logger.info(`没有找到用户 ${refereeId} 的待处理邀请记录`);
      console.log(`[INVITATION_RECHARGE_DEBUG] 没有找到用户 ${refereeId} 的待处理邀请记录`);
      return null;
    }

    console.log(`[INVITATION_RECHARGE_DEBUG] 找到邀请记录: ${usage.id}`);
    console.log(`[INVITATION_RECHARGE_DEBUG] 邀请码: ${usage.invitation.inviteCode}, 奖励类型: ${usage.invitation.inviteType}, 奖励比例: ${usage.invitation.refRatio}`);

    // 将元转换为分进行存储（1元 = 100分）
    const amountInCents = Math.floor(rechargeAmount * 100); // amountInCents单位：分
    console.log(`[INVITATION_RECHARGE_DEBUG] 充值金额转换为分: ${rechargeAmount}元 => ${amountInCents}分`);

    // 计算奖励（积分奖励基于积分数量，现金奖励基于元的金额）
    const refRatio = Number(usage.invitation.refRatio);
    console.log(`[INVITATION_RECHARGE_DEBUG] 奖励比例转换为数字: ${usage.invitation.refRatio} => ${refRatio}`);

    const pointsAwarded = usage.invitation.inviteType === 'cash'
      ? 0
      : Math.floor(points * refRatio); // 积分按用户实际获得的积分数量计算，结果为积分值

    const cashAwarded = usage.invitation.inviteType === 'points'
      ? 0
      : Math.floor(rechargeAmount * refRatio * 100); // 现金奖励计算基于元，但存储为分（1元 = 100分）

    console.log(`[INVITATION_RECHARGE_DEBUG] 奖励计算过程:`);
    console.log(`[INVITATION_RECHARGE_DEBUG] - 积分: ${points} 积分 * ${refRatio} = ${pointsAwarded} 积分 (基于用户实际获得的积分数量)`);
    console.log(`[INVITATION_RECHARGE_DEBUG] - 现金: ${rechargeAmount} 元 * ${refRatio} * 100 = ${cashAwarded} 分 (${cashAwarded/100} 元)`);

    logger.info(`邀请奖励计算结果 - 积分: ${pointsAwarded}积分, 现金: ${cashAwarded/100}元`); // 现金从分转换为元进行显示

    // 更新记录
    const [updatedUsage] = await db
      .update(invitationUsages)
      .set({
        firstRechargeAt: new Date(),
        rechargeAmount: amountInCents, // 存储为分（1元 = 100分）
        pointsAwarded,
        cashAwarded,
        status: 'ready',
        updatedAt: new Date(),
      })
      .where(eq(invitationUsages.id, usage.id))
      .returning();

    console.log(`[INVITATION_RECHARGE_DEBUG] 更新成功，新状态: ${updatedUsage.status}`);
    console.log(`[INVITATION_RECHARGE_DEBUG] 存储的充值金额: ${updatedUsage.rechargeAmount || 0}分 (${(updatedUsage.rechargeAmount || 0)/100}元)`);
    console.log(`[INVITATION_RECHARGE_DEBUG] 存储的积分奖励: ${updatedUsage.pointsAwarded || 0}积分`);
    console.log(`[INVITATION_RECHARGE_DEBUG] 存储的现金奖励: ${updatedUsage.cashAwarded || 0}分 (${(updatedUsage.cashAwarded || 0)/100}元)`);

    return updatedUsage;
  } catch (error: any) {
    logger.error('更新邀请使用记录失败', error);
    throw error;
  }
}

/**
 * 兑换邀请奖励（旧版本，已弃用）
 * @deprecated 请使用 lib/invitation/points.ts 中的 redeemInvitationPoints 函数
 */
export async function redeemInvitationReward(
  usageId: string,
  operatorId: string,
  redeemType: 'points' | 'cash',
  note?: string
) {
  try {
    // 查找邀请记录
    const usage = await db.query.invitationUsages.findFirst({
      where: eq(invitationUsages.id, usageId),
      with: {
        invitation: true,
      },
    });

    if (!usage || !usage.invitation) {
      throw new Error('邀请记录不存在');
    }

    if (usage.status !== 'ready') {
      throw new Error('该记录不可兑换');
    }

    // 检查操作权限
    const isAdmin = true; // 这里应该调用实际的管理员检查逻辑
    const isReferrer = usage.invitation.referrerId === operatorId;

    if (!isReferrer && !isAdmin) {
      throw new Error('无权操作此记录');
    }

    // 检查兑换类型
    if (redeemType === 'cash' && !isAdmin) {
      throw new Error('只有管理员可以兑换现金奖励');
    }

    if (redeemType === 'points' && (usage.pointsAwarded === null || usage.pointsAwarded <= 0)) {
      throw new Error('无积分可兑换');
    }

    if (redeemType === 'cash' && (usage.cashAwarded === null || usage.cashAwarded <= 0)) {
      throw new Error('无现金可兑换');
    }

    // 开始事务
    return await db.transaction(async (tx) => {
      // 更新邀请记录状态
      const [updatedUsage] = await tx
        .update(invitationUsages)
        .set({
          status: 'completed',
          redeemedAt: new Date(),
          operatorId,
          updatedAt: new Date(),
          extra: {
            ...(usage.extra || {}),
            redeemNote: note,
            redeemType,
          },
        })
        .where(eq(invitationUsages.id, usage.id))
        .returning();

      // 如果是积分奖励，更新钱包
      if (redeemType === 'points' && usage.pointsAwarded !== null && usage.pointsAwarded > 0) {
        // 获取邀请人钱包
        const wallet = await tx.query.wallets.findFirst({
          where: eq(wallets.userId, usage.invitation.referrerId),
        });

        if (!wallet) {
          throw new Error('邀请人钱包不存在');
        }

        // 更新钱包积分
        await tx
          .update(wallets)
          .set({
            permanentPoints: wallet.permanentPoints + (usage.pointsAwarded || 0),
            updatedAt: new Date(),
          })
          .where(eq(wallets.id, wallet.id));

        // 返回更新后的钱包信息
        const updatedWallet = await tx.query.wallets.findFirst({
          where: eq(wallets.userId, usage.invitation.referrerId),
        });

        return {
          success: true,
          usage: updatedUsage,
          wallet: updatedWallet,
        };
      }

      // 如果是现金奖励，这里可以添加相关逻辑

      return {
        success: true,
        usage: updatedUsage,
      };
    });
  } catch (error: any) {
    logger.error('兑换邀请奖励失败', error);
    throw error;
  }
}

/**
 * 获取邀请统计信息
 */
export async function getInvitationStats(userId: string, includePendingRewards = false) {
  try {
    // 获取用户创建的邀请码
    const userInvitations = await db.query.invitations.findMany({
      where: eq(invitations.referrerId, userId),
      columns: { id: true },
    });

    const invitationIds = userInvitations.map(inv => inv.id);

    if (invitationIds.length === 0) {
      return {
        totalInvitations: 0,
        pendingCount: 0,
        readyCount: 0,
        completedCount: 0,
        totalPointsAwarded: 0,
        totalCashAwarded: 0,
      };
    }

    // 获取总邀请数
    const totalUsages = await db
      .select({ count: count() })
      .from(invitationUsages)
      .where(sql`invitation_usages.invitation_id IN ${invitationIds}`);

    // 获取待充值数
    const pendingCount = await db
      .select({ count: count() })
      .from(invitationUsages)
      .where(
        and(
          sql`invitation_usages.invitation_id IN ${invitationIds}`,
          eq(invitationUsages.status, 'pending')
        )
      );

    // 获取待兑换数
    const readyCount = await db
      .select({ count: count() })
      .from(invitationUsages)
      .where(
        and(
          sql`invitation_usages.invitation_id IN ${invitationIds}`,
          eq(invitationUsages.status, 'ready')
        )
      );

    // 获取已兑换数
    const completedCount = await db
      .select({ count: count() })
      .from(invitationUsages)
      .where(
        and(
          sql`invitation_usages.invitation_id IN ${invitationIds}`,
          eq(invitationUsages.status, 'completed')
        )
      );

    // 获取总积分奖励（只计算选择兑换积分的记录）
    const totalPointsAwarded = await db
      .select({ sum: sql<number>`SUM(points_awarded)` })
      .from(invitationUsages)
      .where(
        and(
          sql`invitation_usages.invitation_id IN ${invitationIds}`,
          eq(invitationUsages.status, 'completed'),
          or(
            sql`invitation_usages.extra->>'redeemType' = 'points'`,
            and(
              sql`invitation_usages.extra->>'redeemType' IS NULL`,
              sql`invitation_usages.points_awarded > 0`
            )
          )
        )
      );

    // 获取总现金奖励（只计算选择兑换现金的记录）
    const totalCashAwarded = await db
      .select({ sum: sql<number>`SUM(cash_awarded)` })
      .from(invitationUsages)
      .where(
        and(
          sql`invitation_usages.invitation_id IN ${invitationIds}`,
          eq(invitationUsages.status, 'completed'),
          or(
            sql`invitation_usages.extra->>'redeemType' = 'cash'`,
            and(
              sql`invitation_usages.extra->>'redeemType' IS NULL`,
              sql`invitation_usages.cash_awarded > 0`
            )
          )
        )
      );

    // 获取待领取的积分奖励
    const pendingPointsAwarded = await db
      .select({ sum: sql<number>`SUM(points_awarded)` })
      .from(invitationUsages)
      .where(
        and(
          sql`invitation_usages.invitation_id IN ${invitationIds}`,
          eq(invitationUsages.status, 'ready')
        )
      );

    // 获取待领取的现金奖励
    const pendingCashAwarded = await db
      .select({ sum: sql<number>`SUM(cash_awarded)` })
      .from(invitationUsages)
      .where(
        and(
          sql`invitation_usages.invitation_id IN ${invitationIds}`,
          eq(invitationUsages.status, 'ready')
        )
      );

    const result = {
      totalInvitations: totalUsages[0]?.count || 0,
      pendingCount: pendingCount[0]?.count || 0,
      readyCount: readyCount[0]?.count || 0,
      completedCount: completedCount[0]?.count || 0,
      totalPointsAwarded: totalPointsAwarded[0]?.sum || 0,
      totalCashAwarded: totalCashAwarded[0]?.sum || 0,
    };

    // 如果需要包含待领取的奖励信息
    if (includePendingRewards) {
      return {
        ...result,
        pendingPointsAwarded: pendingPointsAwarded[0]?.sum || 0,
        pendingCashAwarded: pendingCashAwarded[0]?.sum || 0,
      };
    }

    return result;
  } catch (error: any) {
    logger.error('获取邀请统计信息失败', error);
    throw error;
  }
}

// 导出现金奖励兑换函数
export { redeemInvitationCash } from './cash';
