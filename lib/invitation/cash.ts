import { db } from '@/lib/db';
import { invitationUsages, InvitationUsage } from '@/lib/db/schema';
import { eq, inArray } from 'drizzle-orm';
import { createLogger } from '@/lib/draw/logger';

const logger = createLogger('invitation-cash');

/**
 * 兑换邀请现金奖励
 * 注意：此函数仅更新邀请使用记录状态，不执行实际的现金转账操作
 *
 * @param usageId 邀请使用记录ID
 * @param operatorId 操作人ID（管理员）
 * @param note 可选的备注信息
 * @returns 兑换结果
 */
export async function redeemInvitationCash(
  usageId: string,
  operatorId: string,
  note?: string
) {
  try {
    return await _redeemInvitationCash(usageId, operatorId, note);
  } catch (error: any) {
    logger.error('兑换邀请现金奖励失败', error);
    throw error;
  }
}

/**
 * 内部函数：兑换邀请现金奖励
 * 注意：此函数仅更新邀请使用记录状态，不执行实际的现金转账操作
 *
 * @param usageId 邀请使用记录ID
 * @param operatorId 操作人ID（管理员）
 * @param note 可选的备注信息
 * @returns 兑换结果
 */
async function _redeemInvitationCash(
  usageId: string,
  operatorId: string,
  note?: string
) {
  try {
    console.log(`[INVITATION_CASH_DEBUG] 开始兑换现金奖励，记录ID: ${usageId}, 操作人: ${operatorId}`);
    if (note) {
      console.log(`[INVITATION_CASH_DEBUG] 备注信息: ${note}`);
    }

    // 查找邀请记录
    const usage = await db.query.invitationUsages.findFirst({
      where: eq(invitationUsages.id, usageId),
      with: {
        invitation: true,
        referee: {
          columns: {
            clerkId: true,
            username: true,
          }
        }
      },
    });

    if (!usage || !usage.invitation) {
      console.log(`[INVITATION_CASH_DEBUG] 邀请记录不存在，记录ID: ${usageId}`);
      throw new Error('邀请记录不存在');
    }

    console.log(`[INVITATION_CASH_DEBUG] 找到邀请记录: ${usage.id}`);
    console.log(`[INVITATION_CASH_DEBUG] 当前状态: ${usage.status}, 邀请人: ${usage.invitation.referrerId}, 被邀请人: ${usage.refereeId}`);
    console.log(`[INVITATION_CASH_DEBUG] 积分奖励: ${usage.pointsAwarded || 0}, 现金奖励: ${usage.cashAwarded || 0}分 (${(usage.cashAwarded || 0)/100}元)`);

    if (usage.status !== 'ready') {
      console.log(`[INVITATION_CASH_DEBUG] 记录状态不是 'ready'，当前状态: ${usage.status}`);
      throw new Error('该记录不可兑换');
    }

    // 检查是否有现金可兑换
    if (usage.cashAwarded === null || usage.cashAwarded <= 0) {
      console.log(`[INVITATION_CASH_DEBUG] 无现金可兑换，当前现金奖励: ${usage.cashAwarded}`);
      throw new Error('无现金可兑换');
    }

    console.log(`[INVITATION_CASH_DEBUG] 验证通过，准备兑换 ${usage.cashAwarded} 分现金 (${usage.cashAwarded/100} 元)`);

    // 开始事务
    return await db.transaction(async (tx) => {
      console.log(`[INVITATION_CASH_DEBUG] 开始数据库事务`);

      // 再次检查记录状态（防止并发操作）
      const currentUsage = await tx.query.invitationUsages.findFirst({
        where: eq(invitationUsages.id, usageId),
      });

      if (!currentUsage) {
        console.log(`[INVITATION_CASH_DEBUG] 事务中找不到记录，ID: ${usageId}`);
        throw new Error('该记录已被处理或不可兑换');
      }

      if (currentUsage.status !== 'ready') {
        console.log(`[INVITATION_CASH_DEBUG] 记录状态已变更，当前状态: ${currentUsage.status}`);
        throw new Error('该记录已被处理或不可兑换');
      }

      console.log(`[INVITATION_CASH_DEBUG] 事务中再次检查记录状态成功，状态仍为: ${currentUsage.status}`);

      // 更新邀请记录状态
      console.log(`[INVITATION_CASH_DEBUG] 准备更新邀请记录状态为 'completed'`);

      const timestamp = new Date();
      const [updatedUsage] = await tx
        .update(invitationUsages)
        .set({
          status: 'completed',
          redeemedAt: timestamp,
          operatorId,
          updatedAt: timestamp,
          extra: {
            ...(usage.extra || {}),
            redeemType: 'cash',
            redeemNote: note,
            redeemTimestamp: timestamp.toISOString(),
            // 未来可能需要添加转账记录ID或其他相关信息
          },
        })
        .where(eq(invitationUsages.id, usage.id))
        .returning();

      console.log(`[INVITATION_CASH_DEBUG] 邀请记录状态更新成功，新状态: ${updatedUsage.status}`);
      console.log(`[INVITATION_CASH_DEBUG] 兑换金额: ${usage.cashAwarded || 0}分 (${(usage.cashAwarded || 0)/100}元)`);

      // 注意：此处不执行实际的现金转账操作
      console.log(`[INVITATION_CASH_DEBUG] 注意: 此处仅更新记录状态，不执行实际的现金转账操作`);
      console.log(`[INVITATION_CASH_DEBUG] 事务完成，兑换成功`);

      // TODO: 未来可能需要集成支付系统或其他现金转账机制

      return {
        success: true,
        usage: updatedUsage,
        message: '现金奖励兑换状态已更新，请手动处理实际的现金转账',
      };
    });
  } catch (error: any) {
    throw error;
  }
}

/**
 * 批量兑换邀请现金奖励
 * 在单个事务中处理多个邀请记录的现金兑换
 * 注意：此函数仅更新邀请使用记录状态，不执行实际的现金转账操作
 *
 * @param usageIds 邀请使用记录ID数组
 * @param operatorId 操作人ID（管理员）
 * @param note 可选的备注信息
 * @returns 批量兑换结果
 */
export async function redeemInvitationCashBatch(
  usageIds: string[],
  operatorId: string,
  note?: string
) {
  try {
    console.log(`[INVITATION_CASH_BATCH_DEBUG] 开始批量兑换现金奖励，记录数量: ${usageIds.length}, 操作人: ${operatorId}`);
    console.log(`[INVITATION_CASH_BATCH_DEBUG] 记录IDs: ${usageIds.join(', ')}`);
    if (note) {
      console.log(`[INVITATION_CASH_BATCH_DEBUG] 备注信息: ${note}`);
    }

    if (!usageIds.length) {
      throw new Error('未提供有效的记录ID');
    }

    // 查询所有邀请记录
    const usages = await db.query.invitationUsages.findMany({
      where: inArray(invitationUsages.id, usageIds),
      with: {
        invitation: true,
        referee: {
          columns: {
            clerkId: true,
            username: true,
          }
        }
      },
    });

    console.log(`[INVITATION_CASH_BATCH_DEBUG] 找到 ${usages.length} 条记录，开始验证`);

    // 验证前检查
    if (usages.length === 0) {
      throw new Error('未找到有效的邀请记录');
    }

    // 验证每条记录
    const validUsages: Array<InvitationUsage & { invitation: any; referee: any }> = [];
    const invalidUsages: Array<{ id: string; error: string }> = [];

    for (const usage of usages) {
      // 检查记录状态
      if (!usage.invitation) {
        invalidUsages.push({ id: usage.id, error: '邀请记录不完整' });
        continue;
      }

      if (usage.status !== 'ready') {
        invalidUsages.push({ id: usage.id, error: `记录状态不是 'ready'，当前状态: ${usage.status}` });
        continue;
      }

      // 检查是否有现金可兑换
      if (usage.cashAwarded === null || usage.cashAwarded <= 0) {
        invalidUsages.push({ id: usage.id, error: '无现金可兑换' });
        continue;
      }

      validUsages.push(usage);
    }

    console.log(`[INVITATION_CASH_BATCH_DEBUG] 验证完成，有效记录: ${validUsages.length}, 无效记录: ${invalidUsages.length}`);

    if (validUsages.length === 0) {
      return {
        success: false,
        results: {
          successful: [],
          failed: invalidUsages
        },
        message: '没有有效的记录可以兑换'
      };
    }

    // 开始事务处理
    const result = await db.transaction(async (tx) => {
      console.log(`[INVITATION_CASH_BATCH_DEBUG] 开始数据库事务`);

      const successfulRedemptions: Array<{ id: string; cashAwarded: number; usage: any }> = [];
      const failedRedemptions = [...invalidUsages];
      let totalCashAwarded = 0;

      // 再次检查记录状态（防止并发操作）
      const validUsageIds = validUsages.map(u => u.id);
      const currentUsages = await tx.query.invitationUsages.findMany({
        where: inArray(invitationUsages.id, validUsageIds),
      });

      // 检查状态
      const finalValidUsages: Array<InvitationUsage & { invitation: any; referee: any }> = [];
      for (const usage of validUsages) {
        const currentUsage = currentUsages.find(u => u.id === usage.id);
        if (!currentUsage) {
          failedRedemptions.push({ id: usage.id, error: '记录不存在或已被删除' });
          continue;
        }

        if (currentUsage.status !== 'ready') {
          failedRedemptions.push({ id: usage.id, error: `记录状态已变更，当前状态: ${currentUsage.status}` });
          continue;
        }

        finalValidUsages.push(usage);
      }

      if (finalValidUsages.length === 0) {
        console.log(`[INVITATION_CASH_BATCH_DEBUG] 没有有效记录可处理`);
        return {
          success: false,
          results: {
            successful: [],
            failed: failedRedemptions
          },
          message: '没有有效的记录可以兑换'
        };
      }

      // 更新所有有效记录的状态
      const timestamp = new Date();

      for (const usage of finalValidUsages) {
        try {
          const cashAwarded = usage.cashAwarded || 0;

          // 更新邀请记录状态
          const [updatedUsage] = await tx
            .update(invitationUsages)
            .set({
              status: 'completed',
              redeemedAt: timestamp,
              operatorId,
              updatedAt: timestamp,
              extra: {
                ...(usage.extra || {}),
                redeemType: 'cash',
                redeemNote: note,
                redeemTimestamp: timestamp.toISOString(),
                batchRedemption: true,
              },
            })
            .where(eq(invitationUsages.id, usage.id))
            .returning();

          // 添加到成功列表
          successfulRedemptions.push({
            id: usage.id,
            cashAwarded,
            usage: updatedUsage
          });

          totalCashAwarded += cashAwarded;
        } catch (error) {
          console.error(`[INVITATION_CASH_BATCH_DEBUG] 处理记录 ${usage.id} 时出错:`, error);
          failedRedemptions.push({ id: usage.id, error: '处理过程中出错' });
        }
      }

      console.log(`[INVITATION_CASH_BATCH_DEBUG] 批量兑换完成，成功: ${successfulRedemptions.length}, 失败: ${failedRedemptions.length}`);
      console.log(`[INVITATION_CASH_BATCH_DEBUG] 总兑换现金: ${totalCashAwarded}分 (${totalCashAwarded/100}元)`);

      return {
        success: successfulRedemptions.length > 0,
        results: {
          successful: successfulRedemptions,
          failed: failedRedemptions
        },
        totalCashAwarded,
        message: '现金奖励兑换状态已更新，请手动处理实际的现金转账'
      };
    });

    return result;
  } catch (error: any) {
    logger.error('批量兑换邀请现金奖励失败', error);
    throw error;
  }
}
