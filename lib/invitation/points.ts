import { db } from '@/lib/db';
import { wallets, orders, invitationUsages } from '@/lib/db/schema';
import { eq, inArray } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import { createLogger } from '@/lib/draw/logger';

const logger = createLogger('invitation-points');

/**
 * 兑换邀请积分奖励
 * 本质是系统奖励积分给邀请人，理由是邀请奖励
 *
 * @param usageId 邀请使用记录ID
 * @param operatorId 操作人ID（邀请人）
 * @returns 兑换结果
 */
export async function redeemInvitationPoints(
  usageId: string,
  operatorId: string,
) {
  try {
    return await _redeemInvitationPoints(usageId, operatorId);
  } catch (error: any) {
    logger.error('兑换邀请积分奖励失败', error);
    throw error;
  }
}

/**
 * 内部函数：兑换邀请积分奖励
 * 本质是系统奖励积分给邀请人，理由是邀请奖励
 *
 * @param usageId 邀请使用记录ID
 * @param operatorId 操作人ID（邀请人）
 * @returns 兑换结果
 */
async function _redeemInvitationPoints(
  usageId: string,
  operatorId: string,
) {
  try {
    console.log(`[INVITATION_POINTS_DEBUG] 开始兑换积分奖励，记录ID: ${usageId}, 操作人: ${operatorId}`);

    // 查找邀请记录
    const usage = await db.query.invitationUsages.findFirst({
      where: eq(invitationUsages.id, usageId),
      with: {
        invitation: true,
        referee: {
          columns: {
            clerkId: true,
            username: true,
          }
        }
      },
    });

    if (!usage || !usage.invitation) {
      console.log(`[INVITATION_POINTS_DEBUG] 邀请记录不存在，记录ID: ${usageId}`);
      throw new Error('邀请记录不存在');
    }

    console.log(`[INVITATION_POINTS_DEBUG] 找到邀请记录: ${usage.id}`);
    console.log(`[INVITATION_POINTS_DEBUG] 当前状态: ${usage.status}, 邀请人: ${usage.invitation.referrerId}, 被邀请人: ${usage.refereeId}`);
    console.log(`[INVITATION_POINTS_DEBUG] 积分奖励: ${usage.pointsAwarded || 0}, 现金奖励: ${usage.cashAwarded || 0}分`);

    if (usage.status !== 'ready') {
      console.log(`[INVITATION_POINTS_DEBUG] 记录状态不是 'ready'，当前状态: ${usage.status}`);
      throw new Error('该记录不可兑换');
    }

    // 检查操作权限（必须是邀请人本人）
    const isReferrer = usage.invitation.referrerId === operatorId;
    console.log(`[INVITATION_POINTS_DEBUG] 操作权限检查 - 邀请人: ${usage.invitation.referrerId}, 操作人: ${operatorId}, 是否为邀请人: ${isReferrer}`);

    if (!isReferrer) {
      console.log(`[INVITATION_POINTS_DEBUG] 操作人不是邀请人，无权操作`);
      throw new Error('无权操作此记录');
    }

    // 检查是否有积分可兑换
    if (usage.pointsAwarded === null || usage.pointsAwarded <= 0) {
      console.log(`[INVITATION_POINTS_DEBUG] 无积分可兑换，当前积分: ${usage.pointsAwarded}`);
      throw new Error('无积分可兑换');
    }

    console.log(`[INVITATION_POINTS_DEBUG] 验证通过，准备兑换 ${usage.pointsAwarded} 积分`);

    // 开始事务
    return await db.transaction(async (tx) => {
      console.log(`[INVITATION_POINTS_DEBUG] 开始数据库事务`);

      // 再次检查记录状态（防止并发操作）
      const currentUsage = await tx.query.invitationUsages.findFirst({
        where: eq(invitationUsages.id, usageId),
      });

      if (!currentUsage) {
        console.log(`[INVITATION_POINTS_DEBUG] 事务中找不到记录，ID: ${usageId}`);
        throw new Error('该记录已被处理或不可兑换');
      }

      if (currentUsage.status !== 'ready') {
        console.log(`[INVITATION_POINTS_DEBUG] 记录状态已变更，当前状态: ${currentUsage.status}`);
        throw new Error('该记录已被处理或不可兑换');
      }

      console.log(`[INVITATION_POINTS_DEBUG] 事务中再次检查记录状态成功，状态仍为: ${currentUsage.status}`);

      // 获取邀请人钱包
      const wallet = await tx.query.wallets.findFirst({
        where: eq(wallets.userId, usage.invitation.referrerId),
      });

      if (!wallet) {
        console.log(`[INVITATION_POINTS_DEBUG] 邀请人钱包不存在，邀请人 ID: ${usage.invitation.referrerId}`);
        throw new Error('邀请人钱包不存在');
      }

      console.log(`[INVITATION_POINTS_DEBUG] 找到邀请人钱包，当前积分: ${wallet.permanentPoints}`);

      // 计算新余额
      const oldBalance = wallet.permanentPoints;
      const newBalance = oldBalance + (usage.pointsAwarded || 0);
      const pointsAwarded = usage.pointsAwarded || 0;

      console.log(`[INVITATION_POINTS_DEBUG] 积分计算: ${oldBalance} + ${pointsAwarded} = ${newBalance}`);

      // 更新钱包积分
      await tx
        .update(wallets)
        .set({
          permanentPoints: newBalance,
          updatedAt: new Date(),
        })
        .where(eq(wallets.id, wallet.id));

      console.log(`[INVITATION_POINTS_DEBUG] 钱包积分更新成功，新余额: ${newBalance}`);

      // 创建订单记录
      const newOrderId = nanoid();
      const timestamp = new Date();
      const description = `邀请奖励: ${pointsAwarded}积分`;

      console.log(`[INVITATION_POINTS_DEBUG] 准备创建订单记录，订单ID: ${newOrderId}`);

      const [newOrder] = await tx.insert(orders).values({
        id: newOrderId,
        userId: usage.invitation.referrerId,
        buyerId: "system",
        type: "credit",
        amount: pointsAwarded,
        description,
        status: "SUCCESS",
        extra: {
          exchangeType: "affiliate",
          invitationUsageId: usage.id,
          refereeId: usage.refereeId,
          rechargeAmount: usage.rechargeAmount,
          pointsExchange: {
            oldBalance,
            newBalance,
            timestamp: timestamp.toISOString(),
          }
        },
        createdAt: timestamp,
        updatedAt: timestamp,
      }).returning();

      console.log(`[INVITATION_POINTS_DEBUG] 订单记录创建成功，订单ID: ${newOrder.id}`);

      // 更新邀请记录状态
      console.log(`[INVITATION_POINTS_DEBUG] 准备更新邀请记录状态为 'completed'`);

      const [updatedUsage] = await tx
        .update(invitationUsages)
        .set({
          status: 'completed',
          redeemedAt: timestamp,
          operatorId,
          updatedAt: timestamp,
          extra: {
            ...(usage.extra || {}),
            redeemType: 'points',
            orderId: newOrderId,
          },
        })
        .where(eq(invitationUsages.id, usage.id))
        .returning();

      console.log(`[INVITATION_POINTS_DEBUG] 邀请记录状态更新成功，新状态: ${updatedUsage.status}`);

      // 返回更新后的钱包信息和订单信息
      const updatedWallet = await tx.query.wallets.findFirst({
        where: eq(wallets.userId, usage.invitation.referrerId),
      });

      console.log(`[INVITATION_POINTS_DEBUG] 兑换完成，钱包新余额: ${updatedWallet?.permanentPoints || 0}`);
      console.log(`[INVITATION_POINTS_DEBUG] 事务完成，兑换成功`);

      return {
        success: true,
        usage: updatedUsage,
        wallet: updatedWallet,
        order: newOrder,
      };
    });
  } catch (error: any) {
    throw error;
  }
}

/**
 * 批量兑换邀请积分奖励
 * 在单个事务中处理多个邀请记录的积分兑换
 *
 * @param usageIds 邀请使用记录ID数组
 * @param operatorId 操作人ID（邀请人或管理员）
 * @returns 批量兑换结果
 */
export async function redeemInvitationPointsBatch(
  usageIds: string[],
  operatorId: string,
) {
  try {
    console.log(`[INVITATION_POINTS_BATCH_DEBUG] 开始批量兑换积分奖励，记录数量: ${usageIds.length}, 操作人: ${operatorId}`);
    console.log(`[INVITATION_POINTS_BATCH_DEBUG] 记录IDs: ${usageIds.join(', ')}`);

    if (!usageIds.length) {
      throw new Error('未提供有效的记录ID');
    }

    // 查询所有邀请记录
    const usages = await db.query.invitationUsages.findMany({
      where: inArray(invitationUsages.id, usageIds),
      with: {
        invitation: true,
        referee: {
          columns: {
            clerkId: true,
            username: true,
          }
        }
      },
    });

    console.log(`[INVITATION_POINTS_BATCH_DEBUG] 找到 ${usages.length} 条记录，开始验证`);

    // 验证前检查
    if (usages.length === 0) {
      throw new Error('未找到有效的邀请记录');
    }

    // 按邀请人分组记录
    const usagesByReferrer: Record<string, typeof usages> = {};
    const invalidUsages: Array<{ id: string; error: string }> = [];

    // 验证每条记录
    for (const usage of usages) {
      // 检查记录状态
      if (!usage.invitation) {
        invalidUsages.push({ id: usage.id, error: '邀请记录不完整' });
        continue;
      }

      if (usage.status !== 'ready') {
        invalidUsages.push({ id: usage.id, error: `记录状态不是 'ready'，当前状态: ${usage.status}` });
        continue;
      }

      // 检查操作权限（必须是邀请人本人）
      const isReferrer = usage.invitation.referrerId === operatorId;
      if (!isReferrer) {
        invalidUsages.push({ id: usage.id, error: '无权操作此记录' });
        continue;
      }

      // 检查是否有积分可兑换
      if (usage.pointsAwarded === null || usage.pointsAwarded <= 0) {
        invalidUsages.push({ id: usage.id, error: '无积分可兑换' });
        continue;
      }

      // 按邀请人分组
      const referrerId = usage.invitation.referrerId;
      if (!usagesByReferrer[referrerId]) {
        usagesByReferrer[referrerId] = [];
      }
      usagesByReferrer[referrerId].push(usage);
    }

    console.log(`[INVITATION_POINTS_BATCH_DEBUG] 验证完成，有效记录: ${usages.length - invalidUsages.length}, 无效记录: ${invalidUsages.length}`);
    console.log(`[INVITATION_POINTS_BATCH_DEBUG] 邀请人分组数量: ${Object.keys(usagesByReferrer).length}`);

    if (Object.keys(usagesByReferrer).length === 0) {
      return {
        success: false,
        results: {
          successful: [],
          failed: invalidUsages
        },
        message: '没有有效的记录可以兑换'
      };
    }

    // 开始事务处理
    const result = await db.transaction(async (tx) => {
      console.log(`[INVITATION_POINTS_BATCH_DEBUG] 开始数据库事务`);

      const successfulRedemptions: Array<{ id: string; pointsAwarded: number; order: any }> = [];
      const failedRedemptions = [...invalidUsages];
      let totalPointsAwarded = 0;
      let updatedWallet = null;

      // 按邀请人处理记录
      for (const [referrerId, referrerUsages] of Object.entries(usagesByReferrer)) {
        console.log(`[INVITATION_POINTS_BATCH_DEBUG] 处理邀请人 ${referrerId} 的 ${referrerUsages.length} 条记录`);

        // 再次检查记录状态（防止并发操作）
        const usageIds = referrerUsages.map(u => u.id);
        const currentUsages = await tx.query.invitationUsages.findMany({
          where: inArray(invitationUsages.id, usageIds),
        });

        // 检查状态
        const validUsages = [];
        for (const usage of referrerUsages) {
          const currentUsage = currentUsages.find(u => u.id === usage.id);
          if (!currentUsage) {
            failedRedemptions.push({ id: usage.id, error: '记录不存在或已被删除' });
            continue;
          }

          if (currentUsage.status !== 'ready') {
            failedRedemptions.push({ id: usage.id, error: `记录状态已变更，当前状态: ${currentUsage.status}` });
            continue;
          }

          validUsages.push(usage);
        }

        if (validUsages.length === 0) {
          console.log(`[INVITATION_POINTS_BATCH_DEBUG] 邀请人 ${referrerId} 没有有效记录可处理`);
          continue;
        }

        // 获取邀请人钱包
        const wallet = await tx.query.wallets.findFirst({
          where: eq(wallets.userId, referrerId),
        });

        if (!wallet) {
          console.log(`[INVITATION_POINTS_BATCH_DEBUG] 邀请人钱包不存在，邀请人 ID: ${referrerId}`);
          for (const usage of validUsages) {
            failedRedemptions.push({ id: usage.id, error: '邀请人钱包不存在' });
          }
          continue;
        }

        // 计算总积分
        const oldBalance = wallet.permanentPoints;
        let pointsToAdd = 0;
        for (const usage of validUsages) {
          pointsToAdd += (usage.pointsAwarded || 0);
        }
        const newBalance = oldBalance + pointsToAdd;

        console.log(`[INVITATION_POINTS_BATCH_DEBUG] 积分计算: ${oldBalance} + ${pointsToAdd} = ${newBalance}`);

        // 更新钱包积分
        await tx
          .update(wallets)
          .set({
            permanentPoints: newBalance,
            updatedAt: new Date(),
          })
          .where(eq(wallets.id, wallet.id));

        console.log(`[INVITATION_POINTS_BATCH_DEBUG] 钱包积分更新成功，新余额: ${newBalance}`);

        // 为每条记录创建订单并更新状态
        const timestamp = new Date();
        for (const usage of validUsages) {
          try {
            const pointsAwarded = usage.pointsAwarded || 0;
            const newOrderId = nanoid();
            const description = `邀请奖励: ${pointsAwarded}积分`;

            // 创建订单记录
            const [newOrder] = await tx.insert(orders).values({
              id: newOrderId,
              userId: referrerId,
              buyerId: "system",
              type: "credit",
              amount: pointsAwarded,
              description,
              status: "SUCCESS",
              extra: {
                exchangeType: "affiliate",
                invitationUsageId: usage.id,
                refereeId: usage.refereeId,
                rechargeAmount: usage.rechargeAmount,
                pointsExchange: {
                  oldBalance,
                  newBalance,
                  timestamp: timestamp.toISOString(),
                }
              },
              createdAt: timestamp,
              updatedAt: timestamp,
            }).returning();

            // 更新邀请记录状态
            await tx
              .update(invitationUsages)
              .set({
                status: 'completed',
                redeemedAt: timestamp,
                operatorId,
                updatedAt: timestamp,
                extra: {
                  ...(usage.extra || {}),
                  redeemType: 'points',
                  orderId: newOrderId,
                  batchRedemption: true,
                },
              })
              .where(eq(invitationUsages.id, usage.id))
              .returning();

            // 添加到成功列表
            successfulRedemptions.push({
              id: usage.id,
              pointsAwarded,
              order: newOrder
            });

            totalPointsAwarded += pointsAwarded;
          } catch (error) {
            console.error(`[INVITATION_POINTS_BATCH_DEBUG] 处理记录 ${usage.id} 时出错:`, error);
            failedRedemptions.push({ id: usage.id, error: '处理过程中出错' });
          }
        }

        // 获取更新后的钱包
        updatedWallet = await tx.query.wallets.findFirst({
          where: eq(wallets.userId, referrerId),
        });
      }

      console.log(`[INVITATION_POINTS_BATCH_DEBUG] 批量兑换完成，成功: ${successfulRedemptions.length}, 失败: ${failedRedemptions.length}`);
      console.log(`[INVITATION_POINTS_BATCH_DEBUG] 总兑换积分: ${totalPointsAwarded}`);

      return {
        success: successfulRedemptions.length > 0,
        results: {
          successful: successfulRedemptions,
          failed: failedRedemptions
        },
        wallet: updatedWallet,
        totalPointsAwarded
      };
    });

    return result;
  } catch (error: any) {
    logger.error('批量兑换邀请积分奖励失败', error);
    throw error;
  }
}
