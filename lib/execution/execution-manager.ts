import { db } from '@/lib/db';
import { executions, type ExecutionType, type ExecutionStatus } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import { createLogger } from '@/lib/draw/logger';

const logger = createLogger('execution-manager');

export interface ExecutionParams {
  [key: string]: any;
}

export interface ExecutionSummary {
  [key: string]: any;
}

/**
 * Create a new execution record
 * @param type Execution type
 * @param params Execution parameters
 * @returns Execution ID
 */
export async function createExecution(
  type: ExecutionType,
  params: ExecutionParams
): Promise<string> {
  const executionId = `exec_${nanoid(10)}`;

  await db.insert(executions).values({
    id: executionId,
    type,
    status: 'PENDING',
    params,
    summary: {},
    logs: '',
    createdAt: new Date(),
    updatedAt: new Date(),
  });

  logger.info(`Created execution ${executionId} of type ${type}`, { executionId, type, params });

  return executionId;
}

/**
 * Start an execution
 * @param executionId Execution ID
 * @returns Success status
 */
export async function startExecution(executionId: string): Promise<boolean> {
  try {
    await db
      .update(executions)
      .set({
        status: 'RUNNING',
        startedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(executions.id, executionId));

    logger.info(`Started execution ${executionId}`, { executionId });
    return true;
  } catch (error) {
    const err = error instanceof Error ? error : new Error(`Failed to start execution ${executionId}`);
    logger.error(`Error starting execution ${executionId}`, err, { details: error });
    return false;
  }
}

/**
 * Update execution summary
 * @param executionId Execution ID
 * @param summary Execution summary
 * @returns Success status
 */
export async function updateExecutionSummary(
  executionId: string,
  summary: ExecutionSummary
): Promise<boolean> {
  try {
    await db
      .update(executions)
      .set({
        summary,
        updatedAt: new Date(),
      })
      .where(eq(executions.id, executionId));

    logger.info(`Updated execution ${executionId} summary`, { executionId, summary });
    return true;
  } catch (error) {
    const err = error instanceof Error ? error : new Error(`Failed to update execution ${executionId} summary`);
    logger.error(`Error updating execution ${executionId} summary`, err, { details: error });
    return false;
  }
}

/**
 * Append to execution logs
 * @param executionId Execution ID
 * @param logEntry Log entry to append
 * @returns Success status
 */
export async function appendExecutionLog(
  executionId: string,
  logEntry: string
): Promise<boolean> {
  try {
    // Get current logs
    const execution = await db.query.executions.findFirst({
      where: eq(executions.id, executionId),
      columns: { logs: true },
    });

    if (!execution) {
      logger.error(`Execution ${executionId} not found`, new Error(`Execution ${executionId} not found`));
      return false;
    }

    // Append new log entry with timestamp
    const timestamp = new Date().toISOString();
    const newLogEntry = `[${timestamp}] ${logEntry}\n`;
    const updatedLogs = execution.logs + newLogEntry;

    // Update logs
    await db
      .update(executions)
      .set({
        logs: updatedLogs,
        updatedAt: new Date(),
      })
      .where(eq(executions.id, executionId));

    return true;
  } catch (error) {
    const err = error instanceof Error ? error : new Error(`Failed to append to execution ${executionId} logs`);
    logger.error(`Error appending to execution ${executionId} logs`, err, { details: error });
    return false;
  }
}

/**
 * Complete an execution
 * @param executionId Execution ID
 * @param status Completion status (SUCCESS or FAILED)
 * @param summary Final execution summary
 * @returns Success status
 */
export async function completeExecution(
  executionId: string,
  status: 'SUCCESS' | 'FAILED',
  summary: ExecutionSummary
): Promise<boolean> {
  try {
    await db
      .update(executions)
      .set({
        status,
        summary,
        completedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(executions.id, executionId));

    logger.info(`Completed execution ${executionId} with status ${status}`, { executionId, status, summary });
    return true;
  } catch (error) {
    const err = error instanceof Error ? error : new Error(`Failed to complete execution ${executionId}`);
    logger.error(`Error completing execution ${executionId}`, err, { details: error });
    return false;
  }
}

/**
 * Get execution by ID
 * @param executionId Execution ID
 * @returns Execution or null if not found
 */
export async function getExecution(executionId: string) {
  return db.query.executions.findFirst({
    where: eq(executions.id, executionId),
  });
}

/**
 * List executions with pagination
 * @param options List options
 * @returns Executions and pagination info
 */
export async function listExecutions({
  type,
  status,
  page = 1,
  limit = 10,
}: {
  type?: ExecutionType;
  status?: ExecutionStatus;
  page?: number;
  limit?: number;
}) {
  // Build where conditions
  const whereConditions = [];
  if (type) {
    whereConditions.push(eq(executions.type, type));
  }
  if (status) {
    whereConditions.push(eq(executions.status, status));
  }

  // Calculate offset
  const offset = (page - 1) * limit;

  // Get total count
  const totalCountResult = await db
    .select({ count: sql`count(*)` })
    .from(executions)
    .where(whereConditions.length > 0 ? and(...whereConditions) : undefined);
  const totalCount = Number(totalCountResult[0]?.count || 0);

  // Get executions
  const executionsList = await db.query.executions.findMany({
    where: whereConditions.length > 0 ? and(...whereConditions) : undefined,
    orderBy: [desc(executions.createdAt)],
    limit,
    offset,
  });

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    executions: executionsList,
    pagination: {
      page,
      limit,
      totalCount,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
}

/**
 * Check if a successful execution exists for the given date range
 * @param type Execution type
 * @param startDate Start date
 * @param endDate End date
 * @returns True if a successful execution exists, false otherwise
 */
export async function checkSuccessfulExecutionExists(
  type: ExecutionType,
  startDate: Date,
  endDate: Date
): Promise<boolean> {
  try {
    // Find executions of the given type with SUCCESS status
    const existingExecutions = await db.query.executions.findMany({
      where: and(
        eq(executions.type, type),
        eq(executions.status, 'SUCCESS')
      ),
    });

    // Check if any of the executions have the same date range
    for (const execution of existingExecutions) {
      const params = execution.params as any;
      if (params.startDate && params.endDate) {
        const execStartDate = new Date(params.startDate);
        const execEndDate = new Date(params.endDate);

        // Check if the date ranges match
        if (
          execStartDate.getTime() === startDate.getTime() &&
          execEndDate.getTime() === endDate.getTime()
        ) {
          logger.info(`Found existing successful execution for date range ${startDate.toISOString()} to ${endDate.toISOString()}`, {
            executionId: execution.id,
            type,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          });
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Unknown error');
    logger.error(`Error checking for existing executions`, err, { details: error });
    return false; // Assume no existing execution in case of error
  }
}

// Import SQL functions
import { sql } from 'drizzle-orm';
import { and, desc } from 'drizzle-orm';
