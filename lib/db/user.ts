import { db, generateId } from './';
import { users, wallets, orders, histories, User, UserExtra } from './schema';
import { eq, and } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import { DEFAULT_NEW_USER_POINTS, BLOCKLISTED_USER_POINTS } from '@/constants/blocklist';
import { checkEmailBlocklist } from '@/lib/blocklist';
import { createLogger } from '@/lib/draw/logger';

const logger = createLogger('user');

export type ClerkUser = {
  id: string;
  emailAddresses: { emailAddress: string }[];
  username: string;
  imageUrl: string;
};

import { createInvitationUsage } from '@/lib/invitation';

export async function syncUser(clerkUserId: string, userData: {
  email: string;
  username: string;
  avatarUrl?: string;
  inviteCode?: string;
}) {
  // 检查用户是否存在
  const existingUser = await db.query.users.findFirst({
    where: eq(users.clerkId, clerkUserId),
  });

  if (!existingUser) {
    // 检查用户邮箱是否在黑名单中
    const isBlocklisted = await checkEmailBlocklist(userData.email);
    const initialPoints = isBlocklisted
      ? BLOCKLISTED_USER_POINTS
      : DEFAULT_NEW_USER_POINTS;

    logger.info(
      `[USER_REGISTER] User ${clerkUserId} with email ${userData.email} is ${
        isBlocklisted ? "blocklisted" : "not blocklisted"
      }`
    );

    // 创建新用户
    await db.insert(users).values({
      clerkId: clerkUserId,
      email: userData.email,
      username: userData.username,
      avatarUrl: userData.avatarUrl,
      extra: {
        isBlocklisted: isBlocklisted,
        blocklisted_at: isBlocklisted ? new Date().toISOString() : null,
      },
    });

    // 初始化用户钱包
    const walletId = nanoid();
    await db.insert(wallets).values({
      id: walletId,
      userId: clerkUserId,
      permanentPoints: initialPoints, // 根据黑名单状态设置初始积分
    });

    // 记录赠送操作
    await db.insert(orders).values({
      id: nanoid(),
      userId: clerkUserId,
      buyerId: "system",
      type: "credit",
      amount: initialPoints,
      status: "SUCCESS",
      description: isBlocklisted ? "新用户注册受限赠送" : "新用户注册赠送",
      extra: {
        tradeStatus: "success",
        isBlocklisted: isBlocklisted,
        blocklisted_at: isBlocklisted ? new Date().toISOString() : null,
        blocklist_reason: isBlocklisted ? "邮箱匹配黑名单规则" : null,
        standard_points: DEFAULT_NEW_USER_POINTS,
        reduced_points: BLOCKLISTED_USER_POINTS,
      },
    });

    // 如果有邀请码，创建邀请使用记录
    if (userData.inviteCode) {
      try {
        console.log(
          `[INVITATION_DEBUG] 开始处理邀请码 ${userData.inviteCode} 用户 ${clerkUserId}`
        );
        console.log(`[INVITATION_DEBUG] 邀请码来源: ${userData.inviteCode}`);

        const usage = await createInvitationUsage(
          userData.inviteCode,
          clerkUserId
        );

        console.log(
          `[INVITATION] 用户 ${clerkUserId} 成功注册并关联邀请码 ${userData.inviteCode}`
        );
        console.log(
          `[INVITATION_DEBUG] 创建的邀请使用记录:`,
          JSON.stringify(usage, null, 2)
        );
      } catch (error) {
        console.error(
          `[INVITATION_ERROR] 处理邀请码失败，用户 ${clerkUserId}，邀请码 ${userData.inviteCode}:`,
          error
        );
        // 不阻止用户注册流程，即使邀请码处理失败
      }
    }

    return {
      isNewUser: true,
      permanentPoints: initialPoints,
      isBlocklisted: isBlocklisted,
    };
  }

  // 如果用户已存在，返回用户信息
  const userWallet = await db.query.wallets.findFirst({
    where: eq(wallets.userId, clerkUserId),
  });

  return {
    isNewUser: false,
    permanentPoints: userWallet?.permanentPoints || 0,
  };
}

export async function getUserWallet(clerkUserId: string) {
  const userWallet = await db.query.wallets.findFirst({
    where: eq(wallets.userId, clerkUserId),
  });

  return userWallet || { permanentPoints: 0 };
}

export async function createHistory({
  userId,
  status,
  resultUrl,
  prompt,
  description,
  pointsUsed,
  parameters,
}: {
  userId: string;
  status: boolean;
  resultUrl?: string;
  prompt: string;
  description?: string;
  pointsUsed: number;
  parameters: Record<string, any>;
}) {
  const historyId = generateId('history');

  await db.insert(histories).values({
    id: historyId,
    userId,
    status,
    resultUrl,
    prompt,
    description,
    pointsUsed,
    parameters,
  });

  return historyId;
}

export async function updateWalletPoints(userId: string, amount: number, type: 'credit' | 'debit', description: string, buyerId: string = 'system') {
  // Start a transaction
  return await db.transaction(async (tx) => {
    // Get current wallet
    const wallet = await tx.query.wallets.findFirst({
      where: eq(wallets.userId, userId),
    });

    if (!wallet) {
      throw new Error('Wallet not found');
    }

    // Calculate new balance
    const newBalance = type === 'credit'
      ? wallet.permanentPoints + amount
      : wallet.permanentPoints - amount;

    if (type === 'debit' && newBalance < 0) {
      throw new Error('Insufficient points');
    }

    // Update wallet
    await tx.update(wallets)
      .set({
        permanentPoints: newBalance,
        updatedAt: new Date(),
      })
      .where(eq(wallets.userId, userId));

    // Record transaction
    const orderId = generateId('order');
    await tx.insert(orders).values({
      id: orderId,
      userId,
      buyerId,
      type,
      amount,
      description,
    });

    return {
      newBalance,
      orderId,
    };
  });
}

export async function getWalletBalance(userId: string): Promise<number> {
  const wallet = await getUserWallet(userId);
  if (!wallet) {
    throw new Error('Wallet not found');
  }
  return wallet.permanentPoints;
}

export async function getUserTransactions(userId: string, limit: number = 1) {
  return await db.query.orders.findMany({
    where: and(
      eq(orders.userId, userId),
      eq(orders.status, "SUCCESS")
    ),
    orderBy: (orders, { desc }) => [desc(orders.createdAt)],
    limit,
  });
}

export async function getUserHistories(userId: string, limit: number = 10, includeShare: boolean = false) {
  return await db.query.histories.findMany({
    where: and(
      eq(histories.userId, userId),
      eq(histories.archived, false)
    ),
    orderBy: (histories, { desc }) => [desc(histories.createdAt)],
    limit,
    with: includeShare ? {
      share: {
        columns: {
          id: true,
          shareId: true,
          isPublic: true,
          allowFork: true,
          viewCount: true,
          likeCount: true,
          forkCount: true,
        }
      }
    } : undefined,
  });
}

export async function checkUserPaid(userId: string): Promise<{ isPaid: boolean }> {
  const paidOrder = await db.query.orders.findFirst({
    where: and(
      eq(orders.userId, userId),
      eq(orders.buyerId, userId),
      eq(orders.status, "SUCCESS"),
      eq(orders.type, "credit")
    ),
  });

  return {
    isPaid: !!paidOrder,
  };
}

export async function checkUserStatus(userId: string): Promise<{ isSuspended: boolean }> {
  const user = await db.query.users.findFirst({
    where: eq(users.clerkId, userId),
  }) as User | undefined;

  return {
    isSuspended: user?.extra?.isSuspended || false,
  };
}
