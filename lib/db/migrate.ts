import { drizzle } from 'drizzle-orm/neon-serverless';
import { migrate } from 'drizzle-orm/neon-serverless/migrator';
import { neon, neonConfig } from '@neondatabase/serverless';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config({ path: '.env.local' });

// 配置 neon
neonConfig.fetchConnectionCache = true;

// 使用环境变量中的数据库连接字符串
const sql = neon(process.env.DATABASE_URL!);
// @ts-ignore - Types mismatch between Neon and Drizzle
const db = drizzle(sql);

async function main() {
  console.log('Running migrations...');

  try {
    // 执行迁移，指定迁移文件的路径
    await migrate(db, { migrationsFolder: './drizzle' });

    console.log('Migrations completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed!');
    console.error(error);
    process.exit(1);
  }
}

main();
