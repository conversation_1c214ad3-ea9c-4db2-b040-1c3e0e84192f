import { jsonb, pgTable, text, timestamp, integer, boolean, uniqueIndex, index, numeric } from "drizzle-orm/pg-core";
import { InferSelectModel, InferInsertModel, relations } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

export const users = pgTable("users", {
  clerkId: text("clerk_id").primaryKey(),
  email: text("email").notNull(),
  username: text("username").notNull(),
  avatarUrl: text("avatar_url"),
  extra: jsonb("extra").default({}).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const wallets = pgTable("wallets", {
  id: text("id").primaryKey(),
  userId: text("user_id").references(() => users.clerkId).notNull().unique(),
  permanentPoints: integer("permanent_points").default(0).notNull(),
  extra: jsonb("extra").default({}).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export type OrderStatus = "PENDING" | "SUCCESS" | "FAILED" | "REFUND";
export type PaymentMethod = "alipay" | "wxpay" | "stripe";

export const orders = pgTable("orders", {
  id: text("id").primaryKey(),
  userId: text("user_id").references(() => users.clerkId).notNull(),
  buyerId: text("buyer_id").notNull(), // can be "system" for system gifts
  type: text("type").notNull(), // "credit" or "debit"
  amount: integer("amount").notNull(),
  description: text("description").notNull(),
  status: text("status").notNull().$type<OrderStatus>().default("PENDING"),
  paymentMethod: text("payment_method").$type<PaymentMethod>(),
  outTradeNo: text("out_trade_no").unique(), // 商户订单号
  tradeNo: text("trade_no"), // 支付平台订单号
  qrCodeUrl: text("qr_code_url"), // 支付二维码URL
  paidAt: timestamp("paid_at"), // 支付完成时间
  refundedAt: timestamp("refunded_at"), // 退款时间
  extra: jsonb("extra").default({}).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Define table references first
const historyRef = () => histories.id;
const shareRef = () => shares.id;

export type BackupStatus = "PENDING" | "SUCCESS" | "FAILED" | "SKIPPED";
export type DrawStatus = "PENDING" | "PROCESSING" | "SUCCESS" | "FAILED";

export const histories = pgTable("histories", {
  id: text("id").primaryKey(),
  userId: text("user_id").references(() => users.clerkId).notNull(),
  status: boolean("status").notNull(),
  resultUrl: text("result_url"),
  prompt: text("prompt").notNull(),
  description: text("description"),
  pointsUsed: integer("points_used").notNull(),
  parameters: jsonb("parameters").default({}).notNull(),
  forkedFromShareId: text("forked_from_share_id").references(shareRef),
  backupStatus: text("backup_status").$type<BackupStatus>().default("PENDING"),
  lastBackupAt: timestamp("last_backup_at"),
  archived: boolean("archived").default(false).notNull(),
  drawStatus: text("draw_status").$type<DrawStatus>().default("SUCCESS"),
  drawResult: text("draw_result"),
  extra: jsonb("extra").default({}).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const historiesRelations = relations(histories, ({ one }) => ({
  share: one(shares, {
    fields: [histories.id],
    references: [shares.historyId],
  }),
  user: one(users, {
    fields: [histories.userId],
    references: [users.clerkId],
  }),
}));

export const shares = pgTable("shares", {
  id: text("id").primaryKey().$defaultFn(() => uuidv4()),
  shareId: text("share_id").notNull().unique().$defaultFn(() => uuidv4()),
  historyId: text("history_id").references(historyRef).notNull(),
  userId: text("user_id").references(() => users.clerkId).notNull(),
  isPublic: boolean("is_public").default(true).notNull(),
  allowFork: boolean("allow_fork").default(true).notNull(),
  forkTipPoints: integer("fork_tip_points").default(0).notNull(),
  imageUrl: text("image_url").notNull(),
  viewCount: integer("view_count").default(0).notNull(),
  likeCount: integer("like_count").default(0).notNull(),
  forkCount: integer("fork_count").default(0).notNull(),
  forkEarnings: integer("fork_earnings").default(0).notNull(),
  forkedFromId: text("forked_from_id").references(shareRef),
  model: text("model"),
  styleId: text("style_id"),
  originalImages: jsonb("original_images").default([]).notNull(),
  customPrompt: text("custom_prompt"),
  sharedAt: timestamp("shared_at").defaultNow().notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  extra: jsonb("extra").default({}).notNull(),
}, (table) => [
  index("shares_user_id_idx").on(table.userId),
  index("shares_history_id_idx").on(table.historyId),
  index("shares_is_public_idx").on(table.isPublic),
  index("shares_fork_count_idx").on(table.forkCount),
  index("shares_shared_at_idx").on(table.sharedAt),
]);

export const sharesRelations = relations(shares, ({ one, many }) => ({
  history: one(histories, {
    fields: [shares.historyId],
    references: [histories.id],
  }),
  user: one(users, {
    fields: [shares.userId],
    references: [users.clerkId],
  }),
  likes: many(likes),
}));

// User relations are defined after invitations table

export const ordersRelations = relations(orders, ({ one }) => ({
  user: one(users, {
    fields: [orders.userId],
    references: [users.clerkId],
  }),
}));

export const likes = pgTable("likes", {
  id: text("id").primaryKey(),
  userId: text("user_id").references(() => users.clerkId).notNull(),
  shareId: text("share_id").references(shareRef).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => [
  uniqueIndex("likes_user_id_share_id_unique").on(table.userId, table.shareId),
  index("likes_share_id_idx").on(table.shareId),
  index("likes_user_id_idx").on(table.userId),
]);

export const likesRelations = relations(likes, ({ one }) => ({
  user: one(users, {
    fields: [likes.userId],
    references: [users.clerkId],
  }),
  share: one(shares, {
    fields: [likes.shareId],
    references: [shares.id],
  }),
}));

export const forkTransactions = pgTable("fork_transactions", {
  id: text("id").primaryKey(),
  fromUserId: text("from_user_id").references(() => users.clerkId).notNull(),
  toUserId: text("to_user_id").references(() => users.clerkId).notNull(),
  shareId: text("share_id").references(shareRef).notNull(),
  historyId: text("history_id").references(historyRef).notNull(),
  tipPoints: integer("tip_points").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  extra: jsonb("extra").default({}).notNull(),
}, (table) => [
  index("fork_transactions_from_user_id_idx").on(table.fromUserId),
  index("fork_transactions_to_user_id_idx").on(table.toUserId),
  index("fork_transactions_share_id_idx").on(table.shareId),
]);

// Type exports
export type UserExtra = {
  isBlocklisted?: boolean;
  blocklisted_at?: string | null;
  isSuspended?: boolean;
  [key: string]: any;
};

export type HistoryExtra = {
  model?: string;
  style?: string;
  styleId?: string;
  originalImages?: string[];
  drawProgress?: string;
};

export type ExecutionStatus = "PENDING" | "RUNNING" | "SUCCESS" | "FAILED";
export type ExecutionType = "BACKUP_IMAGES" | "OTHER_FUTURE_OPERATIONS";

export const executions = pgTable("executions", {
  id: text("id").primaryKey(),
  type: text("type").notNull().$type<ExecutionType>(),
  status: text("status").notNull().$type<ExecutionStatus>().default("PENDING"),
  params: jsonb("params").default({}).notNull(),
  summary: jsonb("summary").default({}).notNull(),
  logs: text("logs").default(""),
  startedAt: timestamp("started_at"),
  completedAt: timestamp("completed_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => [
  index("executions_type_idx").on(table.type),
  index("executions_status_idx").on(table.status),
  index("executions_created_at_idx").on(table.createdAt),
]);

export type InviteType = "points" | "cash" | "both";
export type InvitationUsageStatus = "pending" | "ready" | "completed" | "void";

export const invitations = pgTable("invitations", {
  id: text("id").primaryKey(),
  inviteCode: text("invite_code").notNull().unique(),
  referrerId: text("referrer_id").references(() => users.clerkId).notNull(),
  inviteType: text("invite_type").notNull().$type<InviteType>(),
  refRatio: numeric("ref_ratio").notNull(),
  channel: text("channel"),
  maxUses: integer("max_uses").default(0).notNull(),
  expiresAt: timestamp("expires_at"),
  extra: jsonb("extra").default({}).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => [
  index("invitations_referrer_id_idx").on(table.referrerId),
  index("invitations_invite_code_idx").on(table.inviteCode),
]);

export const invitationUsages = pgTable("invitation_usages", {
  id: text("id").primaryKey(),
  invitationId: text("invitation_id").references(() => invitations.id).notNull(),
  refereeId: text("referee_id").references(() => users.clerkId).notNull(),
  registeredAt: timestamp("registered_at").defaultNow().notNull(),
  firstRechargeAt: timestamp("first_recharge_at"),
  rechargeAmount: integer("recharge_amount"), // 存储为分
  pointsAwarded: integer("points_awarded"),
  cashAwarded: integer("cash_awarded"),
  status: text("status").notNull().$type<InvitationUsageStatus>().default("pending"),
  redeemedAt: timestamp("redeemed_at"),
  operatorId: text("operator_id").references(() => users.clerkId),
  extra: jsonb("extra").default({}).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => [
  index("invitation_usages_invitation_id_idx").on(table.invitationId),
  index("invitation_usages_referee_id_idx").on(table.refereeId),
  index("invitation_usages_status_idx").on(table.status),
]);

export const invitationsRelations = relations(invitations, ({ one, many }) => ({
  referrer: one(users, {
    fields: [invitations.referrerId],
    references: [users.clerkId],
  }),
  usages: many(invitationUsages),
}));

export const invitationUsagesRelations = relations(invitationUsages, ({ one }) => ({
  invitation: one(invitations, {
    fields: [invitationUsages.invitationId],
    references: [invitations.id],
  }),
  referee: one(users, {
    fields: [invitationUsages.refereeId],
    references: [users.clerkId],
  }),
  operator: one(users, {
    fields: [invitationUsages.operatorId],
    references: [users.clerkId],
  }),
}));

// Update user relations to include invitations
export const usersRelations = relations(users, ({ many }) => ({
  shares: many(shares),
  orders: many(orders),
  histories: many(histories),
  likes: many(likes),
  sentInvitations: many(invitations, { relationName: "referrer" }),
  receivedInvitations: many(invitationUsages, { relationName: "referee" }),
}));

export type User = InferSelectModel<typeof users> & {
  extra: UserExtra;
};
export type NewUser = InferInsertModel<typeof users>;
export type History = InferSelectModel<typeof histories> & {
  extra: HistoryExtra;
};
export type NewHistory = InferInsertModel<typeof histories>;
export type Share = InferSelectModel<typeof shares>;
export type NewShare = InferInsertModel<typeof shares>;
export type Like = InferSelectModel<typeof likes>;
export type NewLike = InferInsertModel<typeof likes>;
export type ForkTransaction = InferSelectModel<typeof forkTransactions>;
export type NewForkTransaction = InferInsertModel<typeof forkTransactions>;
export type Execution = InferSelectModel<typeof executions>;
export type NewExecution = InferInsertModel<typeof executions>;
export type Invitation = InferSelectModel<typeof invitations>;
export type NewInvitation = InferInsertModel<typeof invitations>;
export type InvitationUsage = InferSelectModel<typeof invitationUsages>;
export type NewInvitationUsage = InferInsertModel<typeof invitationUsages>;

// 黑名单表
export const blocklists = pgTable("blocklists", {
  id: text("id").primaryKey(),
  type: text("type").notNull(),
  pattern: text("pattern").notNull(),
  enabled: boolean("enabled").default(true).notNull(),
  description: text("description"),
  createdBy: text("created_by").references(() => users.clerkId),
  extra: jsonb("extra").default({}).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => [
  index("blocklists_type_idx").on(table.type),
  index("blocklists_enabled_idx").on(table.enabled),
]);

export const blocklistsRelations = relations(blocklists, ({ one }) => ({
  creator: one(users, {
    fields: [blocklists.createdBy],
    references: [users.clerkId],
  }),
}));

export type Blocklist = InferSelectModel<typeof blocklists>;
export type NewBlocklist = InferInsertModel<typeof blocklists>;
