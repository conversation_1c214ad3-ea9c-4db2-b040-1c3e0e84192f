export enum DrawErrorType {
  VALIDATION = 'VALIDATION',
  INSUFFICIENT_POINTS = 'INSUFFICIENT_POINTS',
  UNAUTHORIZED = 'UNAUTHORIZED',
  INTERNAL = 'INTERNAL',
}

interface DrawErrorOptions {
  type: DrawErrorType;
  message: string;
  details?: Record<string, unknown>;
}

export class DrawError extends Error {
  readonly type: DrawErrorType;
  readonly details?: Record<string, unknown>;

  constructor({ type, message, details }: DrawErrorOptions) {
    super(message);
    this.type = type;
    this.details = details;
    this.name = 'DrawError';
  }

  static validation(message: string, details?: Record<string, unknown>): DrawError {
    return new DrawError({
      type: DrawErrorType.VALIDATION,
      message,
      details,
    });
  }

  static insufficientPoints(message: string, details?: Record<string, unknown>): DrawError {
    return new DrawError({
      type: DrawErrorType.INSUFFICIENT_POINTS,
      message,
      details,
    });
  }

  static unauthorized(message: string, details?: Record<string, unknown>): DrawError {
    return new DrawError({
      type: DrawErrorType.UNAUTHORIZED,
      message,
      details,
    });
  }

  static internal(message: string, details?: Record<string, unknown>): DrawError {
    return new DrawError({
      type: DrawErrorType.INTERNAL,
      message,
      details,
    });
  }
}

interface RetryOptions {
  maxAttempts?: number;
  delayMs?: number;
  backoffFactor?: number;
  shouldRetry?: (error: unknown) => boolean;
}

const defaultRetryOptions: Required<RetryOptions> = {
  maxAttempts: 3,
  delayMs: 1000,
  backoffFactor: 2,
  shouldRetry: (error) => {
    if (error instanceof DrawError) {
      // 不重试验证错误和积分不足错误
      return ![DrawErrorType.VALIDATION, DrawErrorType.INSUFFICIENT_POINTS].includes(error.type);
    }
    // 默认重试所有其他错误
    return true;
  },
};

/**
 * 带重试功能的函数包装器
 * @param fn 要执行的异步函数
 * @param options 重试选项
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {},
): Promise<T> {
  const {
    maxAttempts,
    delayMs,
    backoffFactor,
    shouldRetry,
  } = { ...defaultRetryOptions, ...options };

  let lastError: unknown;
  let attempt = 1;

  while (attempt <= maxAttempts) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;

      if (attempt === maxAttempts || !shouldRetry(error)) {
        break;
      }

      const delay = delayMs * Math.pow(backoffFactor, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
      attempt++;
    }
  }

  throw lastError;
}
