import { db } from '@/lib/db';
import { histories, orders, DrawStatus } from '@/lib/db/schema';
import { nanoid } from 'nanoid';
import { eq, and, sum, or, lt } from 'drizzle-orm';
import { createLogger } from '@/lib/draw/logger';
import { TIMEOUT_SECONDS } from '@/constants/system';

interface DrawHistoryExtra {
  style: string;
  model: string;
  originalImages?: {
    type: string;
    size: number;
    name?: string;
  }[] | null;
  error?: string;
  pointsConsumption?: {
    oldBalance: number;
    newBalance: number;
    timestamp: string;
  };
  originalUrl?: string;
  isWebp?: boolean;
  drawProgress?: string;
}

interface CreateDrawHistoryParams {
  userId: string;
  prompt: string;
  style: string;
  model: string;
  pointsUsed: number;
  originalImages?: {
    type: string;
    size: number;
  }[];
}

interface UpdateDrawHistoryParams {
  historyId: string;
  status: boolean;
  resultUrl?: string;
  error?: string;
}

interface CreateHistoryParams {
  userId: string;
  prompt: string;
  style: string;
  model: string;
  pointsUsed: number;
  description: {
    model: string;
    style: string;
    imageCount: number;
    modelName: string;
    styleName: string;
    timestamp: string;
    customPrompt?: string;
    originalImages?: {
      type: string;
      size: number;
      name: string;
    }[];
  };
  originalImages?: {
    type: string;
    size: number;
    name: string;
  }[];
}

interface UpdateHistoryParams {
  historyId: string;
  status: boolean;
  resultUrl?: string;
  originalUrl?: string;
  isWebp?: boolean;
  error?: string;
  pointsUsed?: number;
  pointsConsumption?: {
    oldBalance: number;
    newBalance: number;
  };
  drawStatus?: DrawStatus;
  drawResult?: string;
  drawProgress?: string;
}

/**
 * 创建绘图历史记录
 */
export async function createHistory(params: CreateHistoryParams) {
  console.log('[HISTORY_CREATE_START]', params);

  try {
    const historyId = nanoid();
    const history = await db.insert(histories)
      .values({
        id: historyId,
        userId: params.userId,
        prompt: params.prompt,
        pointsUsed: params.pointsUsed,
        status: false, // 初始状态为未完成
        drawStatus: 'PENDING', // 异步绘图时，初始状态为等待中
        description: JSON.stringify(params.description),
        extra: {
          style: params.style,
          model: params.model,
          ...(params.originalImages && {
            originalImages: params.originalImages
          })
        }
      })
      .returning();

    console.log('[HISTORY_CREATE_SUCCESS]', {
      historyId,
      userId: params.userId,
      model: params.model,
    });

    return history[0];
  } catch (error) {
    console.error('[HISTORY_CREATE_ERROR]', error, params);
    throw error;
  }
}

/**
 * 更新绘图历史记录
 */
export async function updateHistory(params: UpdateHistoryParams) {
  console.log('[HISTORY_UPDATE_START]', params);

  try {
    // 获取现有历史记录以合并extra数据
    const existingHistory = await db.query.histories.findFirst({
      where: eq(histories.id, params.historyId)
    });

    if (!existingHistory) {
      console.log('[HISTORY_UPDATE_ERROR]', 'History not found', params);
      throw new Error('History not found');
    }

    const currentExtra = existingHistory.extra as DrawHistoryExtra;
    const newExtra = {
      ...currentExtra,
      ...(params.error && { error: params.error }),
      ...(params.pointsConsumption && {
        pointsConsumption: {
          ...params.pointsConsumption,
          timestamp: new Date().toISOString(),
        },
      }),
      ...(params.originalUrl && { originalUrl: params.originalUrl }),
      ...(params.isWebp !== undefined && { isWebp: params.isWebp }),
      ...(params.pointsUsed !== undefined && {
        pointsUsed: params.status ? existingHistory.pointsUsed : params.pointsUsed,
      }),
      ...(params.drawProgress && { drawProgress: params.drawProgress }),
    };

    const updateSet = {
      status: params.status,
      resultUrl: params.resultUrl,
      updatedAt: new Date(),
      extra: newExtra,
      ...(params.drawStatus && { drawStatus: params.drawStatus }),
      ...(params.drawResult && { drawResult: params.drawResult }),
    };

    // 如果状态变为 true（成功）且没有明确设置 drawStatus，则设置为 SUCCESS
    if (params.status === true && !params.drawStatus) {
      updateSet.drawStatus = 'SUCCESS';
    }
    // 如果状态变为 false（失败）且没有明确设置 drawStatus，则设置为 FAILED
    else if (params.status === false && !params.drawStatus) {
      updateSet.drawStatus = 'FAILED';
    }

    console.log("[HISTORY_UPDATE_POCESSING]", updateSet);

    // 更新历史记录
    const updated = await db
      .update(histories)
      .set(updateSet)
      .where(eq(histories.id, params.historyId))
      .returning();

    console.log('[HISTORY_UPDATE_SUCCESS]', {
      historyId: params.historyId,
      status: params.status,
      hasError: !!params.error,
      hasPointsConsumption: !!params.pointsConsumption,
    });

    return updated[0];
  } catch (error) {
    console.error('[HISTORY_UPDATE_ERROR]', error, params);
    throw error;
  }
}

/**
 * 获取用户待处理的绘图请求数量和积分消耗
 * 在查询前会清理超过超时时间的待处理请求
 */
/**
 * 清理用户超过超时时间的待处理绘图请求
 */
export async function clearUserPendingDraws(userId: string) {
  const logger = createLogger('clear-user-pending-draws');
  logger.info(`开始清理用户 ${userId} 的过期待处理请求`);

  try {
    // 计算超时时间前的时间点
    const timeoutAgo = new Date(Date.now() - TIMEOUT_SECONDS * 1000);

    // 查找并更新该用户超时时间前的所有PENDING或PROCESSING状态的记录
    const result = await db.update(histories)
      .set({
        drawStatus: 'FAILED',
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(histories.userId, userId),
          or(
            eq(histories.drawStatus, 'PENDING'),
            eq(histories.drawStatus, 'PROCESSING')
          ),
          lt(histories.createdAt, timeoutAgo),
          eq(histories.archived, false)
        )
      )
      .returning({ id: histories.id });

    if (result.length > 0) {
      logger.info(`已清理用户 ${userId} 的 ${result.length} 个过期待处理请求`, {
        userId,
        clearedIds: result.map(item => item.id)
      });
    } else {
      logger.info(`用户 ${userId} 没有过期的待处理请求`);
    }

    return result.length;
  } catch (error) {
    logger.error(`清理用户 ${userId} 的过期待处理请求失败`, error instanceof Error ? error : new Error(String(error)));
    throw error;
  }
}

/**
 * 清理所有用户超过超时时间的待处理绘图请求
 */
export async function clearAllUsersPendingDraws() {
  const logger = createLogger('clear-all-users-pending-draws');
  logger.info('开始清理所有用户的过期待处理请求');

  try {
    // 计算超时时间前的时间点
    const timeoutAgo = new Date(Date.now() - TIMEOUT_SECONDS * 1000);

    // 查找并更新所有用户超时时间前的所有PENDING或PROCESSING状态的记录
    const result = await db.update(histories)
      .set({
        status: false,
        drawStatus: 'FAILED',
        updatedAt: new Date(),
      })
      .where(
        and(
          or(
            eq(histories.drawStatus, 'PENDING'),
            eq(histories.drawStatus, 'PROCESSING')
          ),
          lt(histories.createdAt, timeoutAgo),
          eq(histories.archived, false)
        )
      )
      .returning({ id: histories.id });

    logger.info(`已清理所有用户的 ${result.length} 个过期待处理请求`, {
      clearedCount: result.length,
      clearedIds: result.map(item => item.id)
    });

    return result.length;
  } catch (error) {
    logger.error('清理所有用户的过期待处理请求失败', error instanceof Error ? error : new Error(String(error)));
    throw error;
  }
}

export async function getUserPendingDraws(userId: string) {
  console.log(`[${new Date().toISOString()}] [GET_USER_PENDING_DRAWS_START] 开始查询用户待处理请求`, { userId });

  try {
    // 先清理该用户的过期待处理请求
    await clearUserPendingDraws(userId);
    // 查询用户所有 PENDING 或 PROCESSING 状态的绘图请求，并且未归档
    console.log(`[${new Date().toISOString()}] [GET_USER_PENDING_DRAWS_QUERY] 查询条件:`, {
      userId,
      drawStatus: ['PENDING', 'PROCESSING'],
      archived: false
    });

    const pendingHistories = await db.query.histories.findMany({
      where: and(
        eq(histories.userId, userId),
        or(
          eq(histories.drawStatus, 'PENDING'),
          eq(histories.drawStatus, 'PROCESSING')
        ),
        eq(histories.archived, false)
      ),
      columns: {
        id: true,
        pointsUsed: true,
        createdAt: true,
        drawStatus: true,
        prompt: true,
        extra: true,
        description: true
      },
      orderBy: (histories, { desc }) => [desc(histories.createdAt)]
    });

    // 打印查询结果详情
    console.log(`[${new Date().toISOString()}] [GET_USER_PENDING_DRAWS_RESULTS] 查询结果:`,
      pendingHistories.map(h => ({ id: h.id, drawStatus: h.drawStatus, createdAt: h.createdAt })));

    // 计算总积分消耗
    const totalPointsUsed = pendingHistories.reduce((total, history) => total + history.pointsUsed, 0);

    // 重要：输出实际的待处理请求数量
    console.log(`[${new Date().toISOString()}] [GET_USER_PENDING_DRAWS_COUNT] 用户 ${userId} 有 ${pendingHistories.length} 个待处理请求`);

    console.log(`[${new Date().toISOString()}] [GET_USER_PENDING_DRAWS_SUCCESS]`, {
      userId,
      count: pendingHistories.length,
      totalPointsUsed,
      pendingHistoriesIds: pendingHistories.map(h => h.id)
    });

    return {
      count: pendingHistories.length,
      totalPointsUsed,
      pendingHistories
    };
  } catch (error) {
    console.error('[GET_USER_PENDING_DRAWS_ERROR]', error, { userId });
    throw error;
  }
}
