import { randomUUID } from 'crypto';

interface LogContext {
  userId: string;
  [key: string]: unknown;
}

interface Logger {
  debug: (message: string, context?: Record<string, unknown>) => void;
  info: (message: string, context?: Record<string, unknown>) => void;
  warn: (message: string, context?: Record<string, unknown>) => void;
  error: (message: string, error: Error, context?: Record<string, unknown>) => void;
}

/**
 * 创建带用户上下文的日志记录器
 * @param userId 用户ID
 * @returns Logger实例
 */
export function createLogger(userId: string): Logger {
  const baseContext = { userId };

  const formatMessage = (
    level: string,
    message: string,
    error?: Error,
    context?: Record<string, unknown>,
  ) => {
    const timestamp = new Date().toISOString();
    const fullContext = {
      ...baseContext,
      ...context,
      ...(error && {
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
      }),
    };

    return {
      timestamp,
      level,
      message,
      context: fullContext,
    };
  };

  return {
    debug: (message: string, context?: Record<string, unknown>) => {
      console.debug(JSON.stringify(formatMessage('debug', message, undefined, context)));
    },

    info: (message: string, context?: Record<string, unknown>) => {
      console.info(JSON.stringify(formatMessage('info', message, undefined, context)));
    },

    warn: (message: string, context?: Record<string, unknown>) => {
      console.warn(JSON.stringify(formatMessage('warn', message, undefined, context)));
    },

    error: (message: string, error: Error, context?: Record<string, unknown>) => {
      console.error(JSON.stringify(formatMessage('error', message, error, context)));
    },
  };
}
