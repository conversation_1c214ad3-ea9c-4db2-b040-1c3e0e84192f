interface ImageValidationResult {
  isValid: boolean;
  error?: string;
  url?: string;
  originalUrl?: string;
  isWebp?: boolean;
}

/**
 * 验证WebP图片格式
 */
function validateWebPImage(url: string): boolean {
  // 直接通过URL判断是否为webp图片
  return url.toLowerCase().includes('.webp');

  // 以下是原有的图片内容检查逻辑，暂时不需要
  /*
  try {
    const response = await fetch(url);
    const arrayBuffer = await response.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);

    // 检查RIFF头部
    if (uint8Array[0] !== 0x52 || // 'R'
        uint8Array[1] !== 0x49 || // 'I'
        uint8Array[2] !== 0x46 || // 'F'
        uint8Array[3] !== 0x46) { // 'F'
      return false;
    }

    // 检查WEBP标识
    if (uint8Array[8] !== 0x57 || // 'W'
        uint8Array[9] !== 0x45 || // 'E'
        uint8Array[10] !== 0x42 || // 'B'
        uint8Array[11] !== 0x50) { // 'P'
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
  */
}

/**
 * 从响应文本中提取并验证图片URL
 * @param text 响应文本
 * @returns 验证结果
 */
export async function extractAndValidateImageUrl(text: string): Promise<ImageValidationResult> {
  try {
    // 查找所有Markdown格式的链接或图片URL
    const markdownLinkRegex = /(!?\[.*?\]\()([^\s)]+)(\))/gi;
    const imageUrlMatches = [...text.matchAll(markdownLinkRegex)].map(match => [match[2]]);

    // 如果没有找到任何链接
    if (imageUrlMatches.length === 0) {
      // 尝试匹配普通图片URL
      const urlRegex = /(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)(?:\?[^#]*)?(?:#.*)?)/gi;
      const plainUrlMatches = [...text.matchAll(urlRegex)];

      if (plainUrlMatches.length === 0) {
        return {
          isValid: false,
          error: 'No image URL found in response',
        };
      }

      // 获取最后一个普通图片URL
      const url = plainUrlMatches[plainUrlMatches.length - 1][0];
      return {
        isValid: true,
        url,
        originalUrl: url,
        isWebp: validateWebPImage(url),
      };
    }

    // 获取最后一个Markdown链接中的URL
    const url = imageUrlMatches[imageUrlMatches.length - 1][0];

    // 检查是否是图片URL
    const isImageUrl = /\.(jpg|jpeg|png|gif|webp)(?:\?|#|$)/i.test(url);

    // 如果不是图片URL，尝试匹配普通图片URL
    if (!isImageUrl) {
      const urlRegex = /(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)(?:\?[^#]*)?(?:#.*)?)/gi;
      const plainUrlMatches = [...text.matchAll(urlRegex)];

      if (plainUrlMatches.length > 0) {
        const plainUrl = plainUrlMatches[plainUrlMatches.length - 1][0];
        return {
          isValid: true,
          url: plainUrl,
          originalUrl: url, // 保存Markdown链接中的URL作为originalUrl
          isWebp: validateWebPImage(plainUrl),
        };
      }
    }

    return {
      isValid: true,
      url,
      originalUrl: url, // 当是图片URL时，url和originalUrl相同
      isWebp: validateWebPImage(url),
    };
  } catch (error) {
    return {
      isValid: false,
      error: `Failed to extract image URL: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}
