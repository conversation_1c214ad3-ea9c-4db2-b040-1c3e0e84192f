import { db } from '@/lib/db';
import { wallets } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

interface CheckPointsParams {
  userId: string;
  points: number;
}

interface DeductPointsParams {
  userId: string;
  points: number;
  description?: string;
  model?: string;
}

/**
 * 检查用户是否有足够的积分
 */
export async function checkUserPoints({ userId, points }: CheckPointsParams): Promise<boolean> {
  const wallet = await db.query.wallets.findFirst({
    where: eq(wallets.userId, userId),
  });

  if (!wallet) {
    return false;
  }

  return wallet.permanentPoints >= points;
}

/**
 * 扣除用户积分
 * 使用数据库原子操作确保并发安全
 * 返回旧余额和新余额，用于记录到历史记录中
 */
export async function deductPoints({ userId, points, description, model }: DeductPointsParams) {
  console.log('[POINTS_DEDUCT_START]', { userId, points });

  try {
    // 使用数据库事务和原子操作进行积分扣除
    const result = await db.transaction(async (tx) => {
      // 获取当前钱包状态
      const wallet = await tx.query.wallets.findFirst({
        where: eq(wallets.userId, userId),
      });

      if (!wallet) {
        console.log('[POINTS_DEDUCT_ERROR]', 'Wallet not found', { userId });
        throw new Error('Wallet not found');
      }

      console.log('[POINTS_DEDUCT_CURRENT]', {
        userId,
        currentPoints: wallet.permanentPoints,
        deductPoints: points,
      });

      if (points <= 0) {
        return {
          success: true,
          oldBalance: wallet.permanentPoints,
          newBalance: wallet.permanentPoints,
        };
      }

      // 计算新的积分余额（不能小于0）
      const newBalance = Math.max(0, wallet.permanentPoints - points);

      // 如果积分不足，抛出错误
      if (newBalance === 0 && wallet.permanentPoints < points) {
        console.log('[POINTS_DEDUCT_ERROR]', 'Insufficient points', {
          userId,
          required: points,
          current: wallet.permanentPoints,
        });
        throw new Error('Insufficient points');
      }

      // 更新钱包积分
      await tx.update(wallets)
        .set({
          permanentPoints: newBalance,
          updatedAt: new Date(),
        })
        .where(eq(wallets.userId, userId));

      console.log('[POINTS_DEDUCT_SUCCESS]', {
        userId,
        oldBalance: wallet.permanentPoints,
        newBalance,
        deducted: points,
        description,
        model,
      });

      return {
        success: true,
        oldBalance: wallet.permanentPoints,
        newBalance,
      };
    });

    return result;
  } catch (error) {
    console.error('[POINTS_DEDUCT_ERROR]', error, {
      userId,
      points,
      description,
      model,
    });
    throw error;
  }
}
