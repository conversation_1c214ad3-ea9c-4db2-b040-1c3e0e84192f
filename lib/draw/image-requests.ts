import {
  generateText,
  experimental_generateImage as generateImage,
} from "ai";
import { eq } from "drizzle-orm";

import { TIMEOUT_SECONDS } from "@/constants/system";

import { myProvider } from "@/lib/ai/models";
import { generateImageRaw, generateImageFlux } from "@/lib/ai/generate"

import { db } from "@/lib/db";
import { histories } from "@/lib/db/schema";

import { deductPoints } from "@/lib/draw/points";
import { updateHistory } from "@/lib/draw/history";
import { extractAndValidateImageUrl } from "@/lib/draw/image-validator";
import { processModelImage } from "@/lib/draw/image-processor";

/**
 * 异步处理绘图请求
 */
export async function processDrawRequest({
  userId,
  historyId,
  promptMessages,
  modelId,
  model,
  creditCost,
  logger,
}: {
  userId: string;
  historyId: string;
  promptMessages: any[];
  modelId: string;
  model: any;
  creditCost: number;
  logger: any;
}) {
  const TIMEOUT_MS = TIMEOUT_SECONDS * 1000;
  const startTime = Date.now();
  let timeoutId: NodeJS.Timeout | null = null;
  let isTimedOut = false;
  // 用于存储响应文本
  let accumulatedContent = "";

  // 记录调试日志（只打印到控制台）
  console.log(
    `[${new Date().toISOString()}] Starting draw request for historyId: ${historyId}`
  );

  try {
    // 设置超时处理
    timeoutId = setTimeout(async () => {
      isTimedOut = true;
      console.log(
        `[${new Date().toISOString()}] TIMEOUT: Draw request exceeded ${
          TIMEOUT_MS / 1000
        } seconds for historyId: ${historyId}`
      );

      try {
        await updateHistory({
          historyId,
          status: false,
          drawStatus: "FAILED",
          drawResult: `请求处理超时，超过${TIMEOUT_SECONDS}秒未完成`,
          error: `处理超时（超过${TIMEOUT_SECONDS}秒）`,
        });

        logger.error(
          "[DRAW_TIMEOUT]",
          new Error(`处理超时（超过${TIMEOUT_SECONDS}秒）`),
          {
            historyId,
            userId,
            timeoutMs: TIMEOUT_MS,
            processingTime: `${(Date.now() - startTime) / 1000}秒`,
          }
        );
      } catch (timeoutError) {
        console.error(
          `[${new Date().toISOString()}] Error updating history on timeout:`,
          timeoutError
        );
      }
    }, TIMEOUT_MS);

    // 更新历史记录状态为处理中
    console.log(
      `[${new Date().toISOString()}] Initializing draw process with model: ${modelId}, model type: ${
        model.type
      }`
    );

    await updateHistory({
      historyId,
      status: false,
      drawStatus: "PROCESSING",
      drawResult: "正在处理绘图请求...",
    });

    // 初始化 accumulatedContent
    accumulatedContent = "正在处理绘图请求...";

    // 记录开始日志
    logger.info("[DRAW_START]", {
      historyId,
      userId,
      modelId,
      promptLength: promptMessages[0].content.length,
      hasAttachments: !!promptMessages[0].experimental_attachments,
      temperature: promptMessages[0].temperature,
      top_p: promptMessages[0].top_p,
      frequency_penalty: promptMessages[0].frequency_penalty,
      presence_penalty: promptMessages[0].presence_penalty,
    });

    // 调用 AI 生成图片
    console.log(
      `[${new Date().toISOString()}] Starting generateText with model: ${modelId}`
    );

    // 使用 generateText 替代 streamText
    let result: any;
    try {
      if (model.type === "flux") {
        // 使用 generateImageFlux 处理 Flux 模型
        result = await generateImageFlux(promptMessages, modelId, userId);
      } else if (model.type === "image") {
        // 使用 generateImageRaw
        result = await generateImageRaw(promptMessages, modelId);
      } else if (model.type === "grok") {
        // 处理 grok 模型
        result = await generateImage({
          model: myProvider.imageModel(modelId),
          prompt: promptMessages[0].content,
          n: 1,
        });
      } else {
        result = await generateText({
          model: myProvider.languageModel(modelId),
          messages: promptMessages,
          maxTokens: 16000,
          temperature: promptMessages[0].temperature,
        });
      }
    } catch (modelError) {
      // 模型调用错误
      const errorMessage =
        modelError instanceof Error ? modelError.message : "模型调用失败";
      console.log(
        `[${new Date().toISOString()}] ERROR: Model call failed: ${errorMessage}`
      );

      // 更新历史记录状态
      await updateHistory({
        historyId,
        status: false,
        drawStatus: "FAILED",
        drawResult: `模型调用失败: ${errorMessage}`,
        error: errorMessage,
      });

      logger.error("[DRAW_MODEL_ERROR]", modelError as Error, {
        historyId,
        userId,
        processingTime: `${(Date.now() - startTime) / 1000}秒`,
      });

      return;
    }

    // 获取生成的文本
    let text;
    let finishReason;
    let usage;
    let imageObj;
    let imageB64;
    let contentType = "image/png";

    if (model.type === "flux") {
      // Flux 模型返回 URL 格式
      imageObj = result.data[0];
      const imageUrl = imageObj.url;

      console.log(`[DEBUG] Flux model returned image URL: ${imageUrl}`);
      text = imageUrl;
    } else if (model.type === "image") {
      imageObj = result.data[0];
      imageB64 = imageObj.b64_json;
      contentType = imageObj.image_type || "image/png";

      text = await processModelImage(imageB64, userId, modelId, contentType);
    } else if (model.type === "grok") {
      imageObj = result.images[0];
      imageB64 = imageObj.base64Data;
      contentType = imageObj.mimeType || "image/jpeg";

      text = await processModelImage(imageB64, userId, modelId, contentType);
    } else {
      text = result.text;
    }

    if (["flux", "image", "grok"].includes(model.type)) {
      // 上传到 R2 存储
      try {
        // 记录当前时间，用于设置 lastBackupAt
        const now = new Date();
        console.log(
          `[${new Date().toISOString()}] Setting backupStatus to SUCCESS and lastBackupAt to ${now.toISOString()}`
        );

        // 直接更新历史记录的备份状态
        try {
          await db
            .update(histories)
            .set({
              backupStatus: "SUCCESS",
              lastBackupAt: now,
            })
            .where(eq(histories.id, historyId));

          console.log(
            `[${new Date().toISOString()}] Successfully updated backup status for history ${historyId}`
          );
        } catch (updateError) {
          console.error(
            `[${new Date().toISOString()}] Error updating backup status for history ${historyId}:`,
            updateError
          );
        }
      } catch (uploadError) {
        console.error(
          `[${new Date().toISOString()}] Error uploading image to R2:`,
          uploadError
        );
        logger.error("[DRAW_R2_UPLOAD_ERROR]", uploadError as Error, {
          historyId,
          userId,
          processingTime: `${(Date.now() - startTime) / 1000}秒`,
        });

        // 如果上传失败，根据模型类型处理回退逻辑
        if (model.type === "flux") {
          // Flux 模型已经有 URL，直接使用原始 URL
          console.log(
            `[${new Date().toISOString()}] Flux model using original URL as fallback: ${text}`
          );
        } else if (imageB64) {
          // 其他模型尝试将base64数据转换为数据 URL
          text = `data:${contentType};base64,${imageB64}`;
          console.log(
            `[${new Date().toISOString()}] Created data URL as fallback, length: ${
              text.length
            }`
          );
        } else {
          // 如果没有base64Data，尝试将对象转换为字符串
          text = String(imageObj);
          console.log(
            `[${new Date().toISOString()}] Using string representation as fallback`
          );
        }
      }

      // 设置 finishReason 和 usage
      finishReason = "stop";
      usage = undefined;
    } else {
      finishReason = result.finishReason;
      usage = result.usage;
    }

    // 更新累积内容
    accumulatedContent = text;

    // 检查是否已超时
    if (isTimedOut) {
      console.log(
        `[${new Date().toISOString()}] Process already timed out for historyId: ${historyId}, skipping further processing`
      );
      // 确保状态已更新为失败
      try {
        await updateHistory({
          historyId,
          status: false,
          drawStatus: "FAILED",
          drawResult: `请求处理超时，超过${TIMEOUT_SECONDS}秒未完成`,
          error: `处理超时（超过${TIMEOUT_SECONDS}秒）`,
        });
      } catch (timeoutError) {
        console.error(
          `[${new Date().toISOString()}] Error updating history on timeout check:`,
          timeoutError
        );
      }
      return;
    }

    // 1. 提取并验证图片URL
    console.log(
      `[${new Date().toISOString()}] Analyzing AI response (${
        text.length
      } characters)...`
    );

    try {
      const { isValid, isWebp, error, url, originalUrl } =
        await extractAndValidateImageUrl(text);

      // 如果 finishReason 不是 'stop'，且 isValid 为 false，可能表示出错
      if (finishReason !== "stop" && !isValid) {
        console.log(
          `[${new Date().toISOString()}] WARNING: Unexpected finish reason: ${finishReason}`
        );

        // 如果不是正常结束，直接标记为失败
        await updateHistory({
          historyId,
          status: false,
          drawStatus: "FAILED",
          drawResult: accumulatedContent,
          error: `意外的结束原因: ${finishReason}`,
        });

        logger.error(
          "[DRAW_UNEXPECTED_FINISH]",
          new Error(`意外的结束原因: ${finishReason}`),
          {
            historyId,
            userId,
            finishReason,
            processingTime: `${(Date.now() - startTime) / 1000}秒`,
          }
        );

        return;
      }

      // 添加提取图片URL的日志
      console.log(
        `[${new Date().toISOString()}] Extracting image URL from response...`
      );

      // 检查是否已超时
      if (isTimedOut) {
        console.log(
          `[${new Date().toISOString()}] Process already timed out for historyId: ${historyId}, skipping further processing`
        );
        // 确保状态已更新为失败
        try {
          await updateHistory({
            historyId,
            status: false,
            drawStatus: "FAILED",
            drawResult: `请求处理超时，超过${TIMEOUT_SECONDS}秒未完成`,
            error: `处理超时（超过${TIMEOUT_SECONDS}秒）`,
          });
        } catch (timeoutError) {
          console.error(
            `[${new Date().toISOString()}] Error updating history on timeout check:`,
            timeoutError
          );
        }
        return;
      }

      if (url) {
        console.log(
          `[${new Date().toISOString()}] Successfully extracted image URL: ${url.substring(
            0,
            50
          )}...`
        );
        console.log(
          `[${new Date().toISOString()}] Image format: ${
            isWebp ? "WebP (free)" : "Non-WebP (charged)"
          }`
        );

        const pointsUsed = isWebp ? 0 : creditCost;

        // 2. 生成成功，扣除积分
        console.log(
          `[${new Date().toISOString()}] Deducting points: ${pointsUsed}`
        );
        const pointsResult = await deductPoints({
          userId,
          points: pointsUsed,
        });
        console.log(
          `[${new Date().toISOString()}] Points deducted: ${
            pointsResult.oldBalance
          } -> ${pointsResult.newBalance}`
        );

        // 3. 更新历史记录
        const updateitems = {
          historyId,
          status: true,
          resultUrl: url,
          originalUrl,
          isWebp,
          pointsUsed: isWebp ? pointsUsed : undefined,
          pointsConsumption: {
            oldBalance: pointsResult.oldBalance,
            newBalance: pointsResult.newBalance,
          },
        };

        // 更新历史记录，包括绘图状态
        console.log(
          `[${new Date().toISOString()}] Draw completed successfully!`
        );

        // 如果是图像模型并且我们已经上传到R2，已经直接更新了备份状态
        // 这里只需要正常更新历史记录
        await updateHistory({
          ...updateitems,
          drawStatus: "SUCCESS",
          drawResult: accumulatedContent,
        });

        logger.info("[DRAW_SUCCESS]", {
          historyId,
          pointsUsed,
          userId,
          imageUrl: url,
          originalUrl,
          usage,
          finishReason,
          processingTime: `${(Date.now() - startTime) / 1000}秒`,
        });
      } else {
        // 4. 生成失败，记录错误
        const errorMessage = error || "响应中没有图片URL";
        console.log(
          `[${new Date().toISOString()}] ERROR: Failed to extract image URL: ${errorMessage}`
        );
        console.log(`[${new Date().toISOString()}] Draw process failed.`);

        await updateHistory({
          historyId,
          status: false,
          pointsUsed: 0,
          error: errorMessage,
          drawStatus: "FAILED",
          drawResult: accumulatedContent,
        });

        logger.error("[DRAW_FAILED]", new Error(error || "响应中没有图片URL"), {
          historyId,
          userId,
          usage,
          finishReason,
          processingTime: `${(Date.now() - startTime) / 1000}秒`,
        });
      }
    } catch (error) {
      // 5. 处理错误
      const errorMessage =
        error instanceof Error ? error.message : "发生未知错误";
      console.log(
        `[${new Date().toISOString()}] ERROR: Exception during image extraction: ${errorMessage}`
      );
      console.log(
        `[${new Date().toISOString()}] Draw process failed with exception.`
      );

      logger.error("[DRAW_COMPLETION_ERROR]", error as Error, {
        historyId,
        userId,
        usage,
        finishReason,
        processingTime: `${(Date.now() - startTime) / 1000}秒`,
      });

      await updateHistory({
        historyId,
        status: false,
        error: errorMessage,
        drawStatus: "FAILED",
        drawResult: accumulatedContent || errorMessage,
      });
    }
  } catch (error) {
    // 处理所有其他错误
    const errorMessage =
      error instanceof Error ? error.message : "发生未知错误";
    console.log(
      `[${new Date().toISOString()}] CRITICAL ERROR: Exception in draw process: ${errorMessage}`
    );

    logger.error("[DRAW_PROCESS_ERROR]", error as Error, {
      historyId,
      userId,
      processingTime: `${(Date.now() - startTime) / 1000}秒`,
    });

    // 无论是否是超时导致的错误，都更新历史记录状态为失败，但保留原因
    await updateHistory({
      historyId,
      status: false,
      drawStatus: "FAILED",
      error: errorMessage,
      drawResult: isTimedOut
        ? `请求处理超时，超过${TIMEOUT_SECONDS}秒未完成`
        : accumulatedContent || errorMessage,
    });
  } finally {
    // 清除超时定时器
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // 记录总处理时间
    const totalProcessingTime = (Date.now() - startTime) / 1000;
    console.log(
      `[${new Date().toISOString()}] Draw request completed in ${totalProcessingTime} seconds for historyId: ${historyId}`
    );
  }
}
