import { format } from 'date-fns';
import { and, eq, gte, lte, not, sql } from 'drizzle-orm';

import { db } from '@/lib/db';
import { histories, shares, executions } from '@/lib/db/schema';
import {
  streamUploadFromUrl,
  extractFilenameFromUrl,
  uploadLogFile
} from '@/lib/storage/r2-client';
import { createLogger } from '@/lib/draw/logger';
import {
  createExecution,
  startExecution,
  updateExecutionSummary,
  appendExecutionLog,
  completeExecution,
  checkSuccessfulExecutionExists
} from '@/lib/execution/execution-manager';

import { TIMEOUT_SECONDS } from "@/constants/system";

// Create a logger for backup operations
const logger = createLogger('async-image-backup');

interface BackupResult {
  totalProcessed: number;
  succeeded: number;
  failed: number;
  skipped: number;
  errors: Array<{
    historyId: string;
    error: string;
  }>;
  processed: Array<{
    historyId: string;
    oldUrl: string;
    newUrl: string;
    shareId?: string;
    backupStatus?: string;
    lastBackupAt?: string;
  }>;
  logUrl?: string;
}

interface BackupOptions {
  startDate: Date;
  endDate: Date;
  dryRun?: boolean;
  batchSize?: number;
  skipBackupCheck?: boolean;
  timeoutSeconds?: number;
}

/**
 * Create a backup execution and start the backup process asynchronously
 * @param options Backup options
 * @returns Execution ID
 */
export async function createBackupExecution(options: BackupOptions): Promise<string> {
  // Create execution record
  const executionId = await createExecution('BACKUP_IMAGES', options);

  try {
    // At least start the execution and mark it as running before returning
    // This ensures the execution status is updated even if the process is terminated after API response
    await startExecution(executionId);
    await appendExecutionLog(executionId, `Starting backup process with options: ${JSON.stringify(options)}`);

    // Initialize result object
    const result: BackupResult = {
      totalProcessed: 0,
      succeeded: 0,
      failed: 0,
      skipped: 0,
      errors: [],
      processed: [],
    };

    // Update execution with initial summary
    await updateExecutionSummary(executionId, result);

    // Start backup process in the background
    // We don't await the full process to keep it asynchronous
    // But we've already updated the status to RUNNING
    setTimeout(() => {
      runBackupProcess(executionId, options).catch(error => {
        logger.error(`Unhandled error in backup process ${executionId}`,
          error instanceof Error ? error : new Error(String(error)),
          { executionId }
        );
      });
    }, 0);
  } catch (error) {
    logger.error(`Error starting backup execution ${executionId}`,
      error instanceof Error ? error : new Error(String(error)),
      { executionId }
    );
    // Don't rethrow, we still want to return the executionId
  }

  return executionId;
}

/**
 * Run the backup process asynchronously with timeout
 * @param executionId Execution ID
 * @param options Backup options
 */
async function runBackupProcess(executionId: string, options: BackupOptions): Promise<void> {
  // Get timeout from options or use default (TIMEOUT_SECONDS seconds)
  // If timeoutSeconds is 0, there will be no timeout
  const timeoutSeconds = options.timeoutSeconds ?? TIMEOUT_SECONDS;
  const timeoutMs =
    timeoutSeconds > 0 ? timeoutSeconds * 1000 : Number.MAX_SAFE_INTEGER;
  const startTime = Date.now();
  let timedOut = false;

  // Setup timeout handler if timeout is enabled
  const timeoutId =
    timeoutSeconds > 0
      ? setTimeout(() => {
          timedOut = true;
        }, timeoutMs)
      : null;
  try {
    // Get the execution to check its status
    const execution = await db.query.executions.findFirst({
      where: eq(executions.id, executionId),
    });

    // If execution doesn't exist or isn't in RUNNING state, something went wrong
    if (!execution) {
      logger.error(
        `Execution ${executionId} not found`,
        new Error(`Execution ${executionId} not found`)
      );
      return;
    }

    if (execution.status !== "RUNNING") {
      logger.warn(
        `Execution ${executionId} is not in RUNNING state, current state: ${execution.status}`
      );
      // If it's not running, we should mark it as running
      if (execution.status === "PENDING") {
        await startExecution(executionId);
        await appendExecutionLog(
          executionId,
          `Starting backup process with options: ${JSON.stringify(options)}`
        );
      } else {
        // If it's already completed or failed, we shouldn't continue
        logger.info(
          `Execution ${executionId} is already in ${execution.status} state, skipping`
        );
        return;
      }
    }

    // Get or initialize result object
    let result: BackupResult;
    if (execution.summary && Object.keys(execution.summary).length > 0) {
      result = execution.summary as BackupResult;
      await appendExecutionLog(
        executionId,
        `Resuming backup process with existing summary`
      );
    } else {
      result = {
        totalProcessed: 0,
        succeeded: 0,
        failed: 0,
        skipped: 0,
        errors: [],
        processed: [],
      };
      await appendExecutionLog(
        executionId,
        `Initializing backup process with new summary`
      );
      await updateExecutionSummary(executionId, result);
    }

    try {
      const {
        startDate,
        endDate,
        dryRun = false,
        batchSize = 10,
        skipBackupCheck = false,
        timeoutSeconds = TIMEOUT_SECONDS,
      } = options;

      await appendExecutionLog(
        executionId,
        `Backup parameters: startDate=${startDate.toISOString()}, endDate=${endDate.toISOString()}, dryRun=${dryRun}, batchSize=${batchSize}, skipBackupCheck=${skipBackupCheck}, timeoutSeconds=${timeoutSeconds}`
      );

      // Build the query conditions
      let whereConditions = [
        gte(histories.createdAt, startDate),
        lte(histories.createdAt, endDate),
        eq(histories.status, true), // Only process successful histories
        not(eq(histories.drawStatus, "FAILED")), // Skip items with drawStatus set to FAILED
      ];

      // Add backup status check if not skipping
      if (!skipBackupCheck) {
        // Skip both SUCCESS and FAILED backup statuses unless skipBackupCheck is true
        whereConditions.push(
          not(eq(histories.backupStatus, "SUCCESS")),
          not(eq(histories.backupStatus, "FAILED"))
        );
      }

      // First, get the total count of records that need processing
      const totalCountResult = await db
        .select({ count: sql`count(*)` })
        .from(histories)
        .where(and(...whereConditions));

      const totalCount = Number(totalCountResult[0]?.count || 0);
      await appendExecutionLog(
        executionId,
        `Found ${totalCount} total histories that need processing with batch size ${batchSize}`
      );

      // Calculate the number of batches needed
      const totalBatches = Math.ceil(totalCount / batchSize);
      await appendExecutionLog(
        executionId,
        `Will process in ${totalBatches} batches`
      );

      // Initialize result with total count
      result.totalProcessed = totalCount;
      await updateExecutionSummary(executionId, result);

      // If there are no records to process, mark as successful immediately
      if (totalCount === 0) {
        await appendExecutionLog(
          executionId,
          `No histories found that need processing. Marking execution as successful immediately.`
        );

        // Generate log file
        const logContent = generateLogContent(result, options);
        const timestamp = format(new Date(), "yyyy-MM-dd-HH-m-s");
        const logFilename = `backup-${timestamp}.log`;

        if (!dryRun) {
          const logUrl = await uploadLogFile(logContent, logFilename);
          result.logUrl = logUrl;
          await appendExecutionLog(
            executionId,
            `Uploaded log file to ${logUrl}`
          );
        }

        await appendExecutionLog(
          executionId,
          `Backup completed with no records to process: totalProcessed=${result.totalProcessed}`
        );

        // Clear the timeout if it was set
        if (timeoutId) clearTimeout(timeoutId);

        // Mark execution as successful
        await completeExecution(executionId, "SUCCESS", result);
        return;
      }

      // Process in batches of batchSize
      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        // Check if we're approaching the timeout
        if (Date.now() - startTime > timeoutMs - 15000) {
          // 15 seconds before timeout
          await appendExecutionLog(
            executionId,
            `Approaching timeout limit, stopping after batch ${batchIndex} of ${totalBatches}`
          );
          break;
        }

        // Query the next batch of histories
        const historiesToProcess = await db.query.histories.findMany({
          where: and(...whereConditions),
          with: {
            share: true,
          },
          limit: batchSize,
          offset: batchIndex * batchSize,
          orderBy: (histories, { asc }) => [asc(histories.createdAt)],
        });

        await appendExecutionLog(
          executionId,
          `Retrieved batch ${batchIndex + 1} of ${totalBatches} with ${
            historiesToProcess.length
          } histories`
        );

        // Process this batch concurrently
        const batchPromises = historiesToProcess.map(async (history) => {
          try {
            // Check if we're approaching the timeout
            if (Date.now() - startTime > timeoutMs - 5000) {
              // 5 seconds before timeout
              await appendExecutionLog(
                executionId,
                `Approaching timeout limit, skipping history ${history.id}`
              );
              result.skipped++;
              return {
                success: true,
                historyId: history.id,
                status: "TIMEOUT_SKIPPED",
              };
            }

            // Skip if no resultUrl
            if (!history.resultUrl) {
              await appendExecutionLog(
                executionId,
                `History ${history.id} has no resultUrl, skipping`
              );
              result.skipped++;

              // Update history backup status to SKIPPED
              if (!dryRun) {
                try {
                  await db
                    .update(histories)
                    .set({
                      backupStatus: "SKIPPED",
                      lastBackupAt: new Date(),
                      updatedAt: new Date(),
                    })
                    .where(eq(histories.id, history.id));
                } catch (updateError) {
                  const err =
                    updateError instanceof Error
                      ? updateError
                      : new Error(`Failed to update history ${history.id}`);
                  await appendExecutionLog(
                    executionId,
                    `Error updating backup status for history ${history.id}: ${err.message}`
                  );
                }
              }

              // Return early instead of continue
              return {
                success: true,
                historyId: history.id,
                status: "SKIPPED",
              };
            }

            // Get the URL to backup
            const historyExtra = history.extra as Record<string, any>;
            const urlToBackup = historyExtra.originalUrl || history.resultUrl;

            // Skip if already backed up (resultUrl starts with R2_PUBLIC_URL_PREFIX) and not skipping backup check
            if (
              !skipBackupCheck &&
              history.resultUrl.startsWith(
                process.env.R2_PUBLIC_URL_PREFIX || ""
              )
            ) {
              await appendExecutionLog(
                executionId,
                `History ${history.id} already backed up, skipping`
              );
              result.skipped++;

              // Update history backup status to SUCCESS if not already set
              if (!dryRun && history.backupStatus !== "SUCCESS") {
                try {
                  await db
                    .update(histories)
                    .set({
                      backupStatus: "SUCCESS",
                      lastBackupAt: new Date(),
                      updatedAt: new Date(),
                    })
                    .where(eq(histories.id, history.id));
                } catch (updateError) {
                  const err =
                    updateError instanceof Error
                      ? updateError
                      : new Error(`Failed to update history ${history.id}`);
                  await appendExecutionLog(
                    executionId,
                    `Error updating backup status for history ${history.id}: ${err.message}`
                  );
                }
              }

              // Return early instead of continue
              return {
                success: true,
                historyId: history.id,
                status: "ALREADY_BACKED_UP",
              };
            }

            // If skipBackupCheck is true and already backed up, log that we're re-backing up
            if (
              skipBackupCheck &&
              history.resultUrl.startsWith(
                process.env.R2_PUBLIC_URL_PREFIX || ""
              )
            ) {
              await appendExecutionLog(
                executionId,
                `History ${history.id} already backed up, but re-backing up due to skipBackupCheck=true`
              );
            }

            if (dryRun) {
              await appendExecutionLog(
                executionId,
                `[DRY RUN] Would backup ${history.id} from ${urlToBackup}`
              );
              result.succeeded++;
              result.processed.push({
                historyId: history.id,
                oldUrl: urlToBackup,
                newUrl: `[DRY RUN] Would be R2 URL`,
                shareId: history.share?.id,
                backupStatus: history.backupStatus || undefined,
                lastBackupAt: history.lastBackupAt?.toISOString(),
              });

              // Return early instead of continue
              return {
                success: true,
                historyId: history.id,
                status: "DRY_RUN",
              };
            }

            // Extract filename from URL
            const filename = extractFilenameFromUrl(urlToBackup);

            // Create R2 key (path)
            const r2Key = `${history.userId}/${history.id}/${filename}`;

            // Stream upload file to R2
            const r2Url = await streamUploadFromUrl(urlToBackup, r2Key);

            // If upload failed, mark as failed and return
            if (!r2Url) {
              await appendExecutionLog(
                executionId,
                `Failed to upload history ${history.id} from ${urlToBackup}`
              );

              // Update history backup status to FAILED
              await db
                .update(histories)
                .set({
                  backupStatus: "FAILED",
                  lastBackupAt: new Date(),
                  updatedAt: new Date(),
                })
                .where(eq(histories.id, history.id));

              result.failed++;
              result.errors.push({
                historyId: history.id,
                error: "Failed to upload image to R2",
              });

              return {
                success: false,
                historyId: history.id,
                error: "Failed to upload image to R2",
              };
            }

            // Update history
            const currentExtra = historyExtra || {};
            if (!currentExtra.originalUrl) {
              currentExtra.originalUrl = urlToBackup;
            }

            const now = new Date();

            await db
              .update(histories)
              .set({
                resultUrl: r2Url,
                extra: currentExtra,
                backupStatus: "SUCCESS",
                lastBackupAt: now,
                updatedAt: now,
              })
              .where(eq(histories.id, history.id));

            // Update share if exists
            if (history.share) {
              await db
                .update(shares)
                .set({
                  imageUrl: r2Url,
                  updatedAt: new Date(),
                })
                .where(eq(shares.id, history.share.id));
            }

            await appendExecutionLog(
              executionId,
              `Backed up history ${history.id} to ${r2Url}`
            );
            result.succeeded++;
            result.processed.push({
              historyId: history.id,
              oldUrl: urlToBackup,
              newUrl: r2Url,
              shareId: history.share?.id,
              backupStatus: "SUCCESS",
              lastBackupAt: now.toISOString(),
            });
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : String(error);
            await appendExecutionLog(
              executionId,
              `Error processing history ${history.id}: ${errorMessage}`
            );
            result.failed++;
            result.errors.push({
              historyId: history.id,
              error: errorMessage,
            });

            // Update history backup status to FAILED
            if (!dryRun) {
              try {
                await db
                  .update(histories)
                  .set({
                    backupStatus: "FAILED",
                    lastBackupAt: new Date(),
                    updatedAt: new Date(),
                  })
                  .where(eq(histories.id, history.id));
              } catch (updateError) {
                const err =
                  updateError instanceof Error
                    ? updateError
                    : new Error(`Failed to update history ${history.id}`);
                await appendExecutionLog(
                  executionId,
                  `Error updating backup status for history ${history.id}: ${err.message}`
                );
              }
            }

            // Return the result for this history
            return {
              success: false,
              historyId: history.id,
              error: errorMessage,
            };
          }

          // Return the result for this history
          return {
            success: true,
            historyId: history.id,
          };
        });

        // Wait for all promises in the batch to complete
        await Promise.all(batchPromises);
        await appendExecutionLog(
          executionId,
          `Completed batch ${batchIndex + 1} of ${totalBatches}`
        );

        // Update execution summary after each batch
        await updateExecutionSummary(executionId, result);
      } // End of batch processing loop

      // Generate log file
      const logContent = generateLogContent(result, options);
      const timestamp = format(new Date(), "yyyy-MM-dd-HH-m-s");
      const logFilename = `backup-${timestamp}.log`;

      if (!dryRun) {
        const logUrl = await uploadLogFile(logContent, logFilename);
        result.logUrl = logUrl;
        await appendExecutionLog(executionId, `Uploaded log file to ${logUrl}`);
      }

      await appendExecutionLog(
        executionId,
        `Backup completed: totalProcessed=${result.totalProcessed}, succeeded=${result.succeeded}, failed=${result.failed}, skipped=${result.skipped}`
      );

      // Clear the timeout if it was set
      if (timeoutId) clearTimeout(timeoutId);

      // Mark execution as successful
      await completeExecution(executionId, "SUCCESS", result);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      // Clear the timeout if it was set
      if (timeoutId) clearTimeout(timeoutId);

      if (timedOut) {
        await appendExecutionLog(
          executionId,
          `Backup process timed out after ${
            (Date.now() - startTime) / 1000
          } seconds`
        );

        // Mark as success with partial completion if we processed some records
        if (result.succeeded > 0) {
          await appendExecutionLog(
            executionId,
            `Marking as partial success: processed ${result.totalProcessed}, succeeded ${result.succeeded}, failed ${result.failed}, skipped ${result.skipped}`
          );
          await completeExecution(executionId, "SUCCESS", result);
        } else {
          // Mark as failed if we didn't process any records successfully
          result.errors.push({
            historyId: "system",
            error: `Timed out after ${(Date.now() - startTime) / 1000} seconds`,
          });
          await completeExecution(executionId, "FAILED", result);
        }
      } else {
        await appendExecutionLog(
          executionId,
          `Error during backup process: ${errorMessage}`
        );

        // Update result with error
        result.errors.push({
          historyId: "system",
          error: errorMessage,
        });

        // Mark execution as failed
        await completeExecution(executionId, "FAILED", result);
      }
    }
  } catch (error) {
    // This is a critical error in the execution framework itself
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      `Critical error in backup execution ${executionId}`,
      error instanceof Error ? error : new Error(errorMessage),
      { executionId }
    );

    // Clear the timeout if it was set
    if (timeoutId) clearTimeout(timeoutId);

    // Try to mark execution as failed
    try {
      await completeExecution(executionId, "FAILED", {
        totalProcessed: 0,
        succeeded: 0,
        failed: 1,
        skipped: 0,
        errors: [{ historyId: "system", error: errorMessage }],
        processed: [],
      });
    } catch (completeError) {
      logger.error(
        `Failed to mark execution ${executionId} as failed`,
        completeError instanceof Error
          ? completeError
          : new Error(String(completeError)),
        { executionId }
      );
    }
  }
}

/**
 * Generate log content from backup result
 * @param result Backup result
 * @param options Backup options
 * @returns Log content
 */
function generateLogContent(result: BackupResult, options: BackupOptions): string {
  const { startDate, endDate, dryRun, batchSize, skipBackupCheck, timeoutSeconds } = options;

  let content = `# Image Backup Log\n\n`;
  content += `Date Range: ${startDate.toISOString()} to ${endDate.toISOString()}\n`;
  content += `Dry Run: ${dryRun ? 'Yes' : 'No'}\n`;
  content += `Batch Size: ${batchSize}\n`;
  content += `Skip Backup Check: ${skipBackupCheck ? 'Yes' : 'No'}\n`;
  content += `Timeout Seconds: ${timeoutSeconds ?? TIMEOUT_SECONDS}${
    timeoutSeconds === 0 ? " (No timeout)" : ""
  }\n\n`;

  content += `## Summary\n\n`;
  content += `- Total Processed: ${result.totalProcessed}\n`;
  content += `- Succeeded: ${result.succeeded}\n`;
  content += `- Failed: ${result.failed}\n`;
  content += `- Skipped: ${result.skipped}\n\n`;

  if (result.errors.length > 0) {
    content += `## Errors\n\n`;
    result.errors.forEach((error, index) => {
      content += `### Error ${index + 1}\n`;
      content += `- History ID: ${error.historyId}\n`;
      content += `- Error: ${error.error}\n\n`;
    });
  }

  content += `## Processed Items\n\n`;
  result.processed.forEach((item, index) => {
    content += `### Item ${index + 1}\n`;
    content += `- History ID: ${item.historyId}\n`;
    content += `- Old URL: ${item.oldUrl}\n`;
    content += `- New URL: ${item.newUrl}\n`;
    if (item.shareId) {
      content += `- Share ID: ${item.shareId}\n`;
    }
    content += `\n`;
  });

  return content;
}

/**
 * Create a backup execution for the previous day
 * @param batchSize Number of images to process concurrently in each batch
 * @param skipBackupCheck Whether to skip checking if images are already backed up
 * @returns Execution ID or null if a successful execution already exists
 */
export async function createPreviousDayBackupExecution(
  batchSize = 10,
  skipBackupCheck = false,
  timeoutSeconds = TIMEOUT_SECONDS
): Promise<string | null> {
  const now = new Date();
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);

  // Set time to start of day
  yesterday.setHours(0, 0, 0, 0);

  // Set time to end of day
  const endOfYesterday = new Date(yesterday);
  endOfYesterday.setHours(23, 59, 59, 999);

  // Check if a successful execution already exists for this date range
  const existingExecution = await checkSuccessfulExecutionExists(
    "BACKUP_IMAGES",
    yesterday,
    endOfYesterday
  );

  if (existingExecution) {
    logger.info(
      `Skipping backup for ${yesterday.toISOString()} to ${endOfYesterday.toISOString()} as a successful execution already exists`
    );
    return null;
  }

  // No existing successful execution, create a new one
  return createBackupExecution({
    startDate: yesterday,
    endDate: endOfYesterday,
    batchSize,
    skipBackupCheck,
    timeoutSeconds,
  });
}

/**
 * Create a backup execution for the last 80 minutes
 * @param batchSize Number of images to process concurrently in each batch
 * @param skipBackupCheck Whether to skip checking if images are already backed up
 * @returns Execution ID or null if a successful execution already exists
 */
export async function createHourlyBackupExecution(
  batchSize = 10,
  skipBackupCheck = false,
  timeoutSeconds = TIMEOUT_SECONDS
): Promise<string | null> {
  const now = new Date();
  const startTime = new Date(now);
  startTime.setMinutes(startTime.getMinutes() - 80); // 80 minutes ago

  // Check if a successful execution already exists for this time range
  const existingExecution = await checkSuccessfulExecutionExists(
    "BACKUP_IMAGES",
    startTime,
    now
  );

  if (existingExecution) {
    logger.info(
      `Skipping hourly backup for ${startTime.toISOString()} to ${now.toISOString()} as a successful execution already exists`
    );
    return null;
  }

  // No existing successful execution, create a new one
  return createBackupExecution({
    startDate: startTime,
    endDate: now,
    batchSize,
    skipBackupCheck,
    timeoutSeconds,
  });
}

/**
 * Check for and fix stuck backup executions
 * This function finds executions that have been in RUNNING state for too long
 * and marks them as FAILED
 * @param maxAgeMinutes Maximum age in minutes for a running execution before it's considered stuck
 * @returns Number of fixed executions
 */
export async function fixStuckBackupExecutions(maxAgeMinutes = 30): Promise<number> {
  try {
    // Calculate the cutoff time
    const cutoffTime = new Date();
    cutoffTime.setMinutes(cutoffTime.getMinutes() - maxAgeMinutes);

    // Find stuck executions
    const stuckExecutions = await db.query.executions.findMany({
      where: and(
        eq(executions.type, 'BACKUP_IMAGES'),
        eq(executions.status, 'RUNNING'),
        lte(executions.updatedAt, cutoffTime)
      ),
    });

    logger.info(`Found ${stuckExecutions.length} stuck backup executions older than ${maxAgeMinutes} minutes`);

    // Fix each stuck execution
    let fixedCount = 0;
    for (const execution of stuckExecutions) {
      try {
        await completeExecution(
          execution.id,
          'FAILED',
          {
            ...((execution.summary as BackupResult) || {
              totalProcessed: 0,
              succeeded: 0,
              failed: 0,
              skipped: 0,
              errors: [],
              processed: [],
            }),
            errors: [
              ...((execution.summary as BackupResult)?.errors || []),
              {
                historyId: 'system',
                error: `Execution was stuck in RUNNING state for more than ${maxAgeMinutes} minutes and was automatically marked as FAILED`,
              },
            ],
          }
        );

        await appendExecutionLog(
          execution.id,
          `Execution was stuck in RUNNING state for more than ${maxAgeMinutes} minutes and was automatically marked as FAILED`
        );

        fixedCount++;
        logger.info(`Fixed stuck execution ${execution.id}`);
      } catch (error) {
        logger.error(
          `Failed to fix stuck execution ${execution.id}`,
          error instanceof Error ? error : new Error(String(error)),
          { executionId: execution.id }
        );
      }
    }

    return fixedCount;
  } catch (error) {
    logger.error(
      'Error fixing stuck backup executions',
      error instanceof Error ? error : new Error(String(error))
    );
    return 0;
  }
}

/**
 * Fix pending backup executions that are older than the specified time
 * This function finds executions that have been in PENDING state for too long
 * and marks them as FAILED
 * @param maxAgeMinutes Maximum age in minutes for a pending execution before it's considered abandoned
 * @returns Number of fixed executions
 */
export async function fixPendingBackupExecutions(maxAgeMinutes = 60): Promise<number> {
  try {
    // Calculate the cutoff time
    const cutoffTime = new Date();
    cutoffTime.setMinutes(cutoffTime.getMinutes() - maxAgeMinutes);

    // Find pending executions
    const pendingExecutions = await db.query.executions.findMany({
      where: and(
        eq(executions.type, 'BACKUP_IMAGES'),
        eq(executions.status, 'PENDING'),
        lte(executions.createdAt, cutoffTime)
      ),
    });

    logger.info(`Found ${pendingExecutions.length} pending backup executions older than ${maxAgeMinutes} minutes`);

    // Fix each pending execution
    let fixedCount = 0;
    for (const execution of pendingExecutions) {
      try {
        await completeExecution(
          execution.id,
          'FAILED',
          {
            ...((execution.summary as BackupResult) || {
              totalProcessed: 0,
              succeeded: 0,
              failed: 0,
              skipped: 0,
              errors: [],
              processed: [],
            }),
            errors: [
              ...((execution.summary as BackupResult)?.errors || []),
              {
                historyId: 'system',
                error: `Execution was stuck in PENDING state for more than ${maxAgeMinutes} minutes and was automatically marked as FAILED`,
              },
            ],
          }
        );

        await appendExecutionLog(
          execution.id,
          `Execution was stuck in PENDING state for more than ${maxAgeMinutes} minutes and was automatically marked as FAILED`
        );

        fixedCount++;
        logger.info(`Fixed pending execution ${execution.id}`);
      } catch (error) {
        logger.error(
          `Failed to fix pending execution ${execution.id}`,
          error instanceof Error ? error : new Error(String(error)),
          { executionId: execution.id }
        );
      }
    }

    return fixedCount;
  } catch (error) {
    logger.error(
      'Error fixing pending backup executions',
      error instanceof Error ? error : new Error(String(error))
    );
    return 0;
  }
}
