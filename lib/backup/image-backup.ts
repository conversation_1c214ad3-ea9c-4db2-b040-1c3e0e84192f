import { db } from '@/lib/db';
import { histories, shares } from '@/lib/db/schema';
import { and, eq, gte, lte, not } from 'drizzle-orm';
import {
  uploadFileFromUrl,
  extractFilenameFromUrl,
  uploadLogFile
} from '@/lib/storage/r2-client';
import { createLogger } from '@/lib/draw/logger';
import { format } from 'date-fns';

// Create a logger for backup operations
const logger = createLogger('image-backup');

interface BackupResult {
  totalProcessed: number;
  succeeded: number;
  failed: number;
  skipped: number;
  errors: Array<{
    historyId: string;
    error: string;
  }>;
  processed: Array<{
    historyId: string;
    oldUrl: string;
    newUrl: string;
    shareId?: string;
    backupStatus?: string;
    lastBackupAt?: string;
  }>;
  logUrl?: string;
}

interface BackupOptions {
  startDate: Date;
  endDate: Date;
  dryRun?: boolean;
  batchSize?: number;
  skipBackupCheck?: boolean;
}

/**
 * Backup images from histories to R2
 * @param options Backup options
 * @param options.startDate Start date for the backup range
 * @param options.endDate End date for the backup range
 * @param options.dryRun Whether to perform a dry run (no actual changes)
 * @param options.batchSize Number of images to process concurrently in each batch
 * @param options.skipBackupCheck Whether to skip checking if images are already backed up
 * @returns Backup result
 */
export async function backupImages(options: BackupOptions): Promise<BackupResult> {
  const { startDate, endDate, dryRun = false, batchSize = 10, skipBackupCheck = false } = options;

  logger.info('Starting image backup', { startDate, endDate, dryRun, batchSize, skipBackupCheck });

  const result: BackupResult = {
    totalProcessed: 0,
    succeeded: 0,
    failed: 0,
    skipped: 0,
    errors: [],
    processed: [],
  };

  try {
    // Build the query conditions
    let whereConditions = [
      gte(histories.createdAt, startDate),
      lte(histories.createdAt, endDate),
      eq(histories.status, true), // Only process successful histories
    ];

    // Add backup status check if not skipping
    if (!skipBackupCheck) {
      whereConditions.push(not(eq(histories.backupStatus, "SUCCESS")));
    }

    // Query histories within the date range
    const historiesToProcess = await db.query.histories.findMany({
      where: and(...whereConditions),
      with: {
        share: true,
      },
    });

    result.totalProcessed = historiesToProcess.length;
    logger.info(`Found ${historiesToProcess.length} histories to process with batch size ${batchSize}`);

    // Process histories in batches
    for (let i = 0; i < historiesToProcess.length; i += batchSize) {
      const batch = historiesToProcess.slice(i, i + batchSize);
      logger.info(`Processing batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(historiesToProcess.length / batchSize)} (${batch.length} items)`);

      // Process batch concurrently
      const batchPromises = batch.map(async (history) => {
        try {
          // Skip if no resultUrl
          if (!history.resultUrl) {
            logger.warn(`History ${history.id} has no resultUrl, skipping`);
            result.skipped++;

            // Update history backup status to SKIPPED
            if (!dryRun) {
              try {
                await db
                  .update(histories)
                  .set({
                    backupStatus: "SKIPPED",
                    lastBackupAt: new Date(),
                    updatedAt: new Date(),
                  })
                  .where(eq(histories.id, history.id));
              } catch (updateError) {
                const err = updateError instanceof Error ? updateError : new Error(`Failed to update history ${history.id}`);
                logger.error(`Error updating backup status for history ${history.id}`, err, { details: updateError });
              }
            }

            // Return early instead of continue
            return {
              success: true,
              historyId: history.id,
              status: 'SKIPPED'
            };
          }

          // Get the URL to backup
          const historyExtra = history.extra as Record<string, any>;
          const urlToBackup = historyExtra.originalUrl || history.resultUrl;

          // Skip if already backed up (resultUrl starts with R2_PUBLIC_URL_PREFIX)
          if (history.resultUrl.startsWith(process.env.R2_PUBLIC_URL_PREFIX || '')) {
            logger.info(`History ${history.id} already backed up, skipping`);
            result.skipped++;

            // Update history backup status to SUCCESS if not already set
            if (!dryRun && history.backupStatus !== "SUCCESS") {
              try {
                await db
                  .update(histories)
                  .set({
                    backupStatus: "SUCCESS",
                    lastBackupAt: new Date(),
                    updatedAt: new Date(),
                  })
                  .where(eq(histories.id, history.id));
              } catch (updateError) {
                const err = updateError instanceof Error ? updateError : new Error(`Failed to update history ${history.id}`);
                logger.error(`Error updating backup status for history ${history.id}`, err, { details: updateError });
              }
            }

            // Return early instead of continue
            return {
              success: true,
              historyId: history.id,
              status: 'ALREADY_BACKED_UP'
            };
          }

          if (dryRun) {
            logger.info(`[DRY RUN] Would backup ${history.id} from ${urlToBackup}`);
            result.succeeded++;
            result.processed.push({
              historyId: history.id,
              oldUrl: urlToBackup,
              newUrl: `[DRY RUN] Would be R2 URL`,
              shareId: history.share?.id,
              backupStatus: history.backupStatus || undefined,
              lastBackupAt: history.lastBackupAt?.toISOString(),
            });

            // Return early instead of continue
            return {
              success: true,
              historyId: history.id,
              status: 'DRY_RUN'
            };
          }

        // Extract filename from URL
        const filename = extractFilenameFromUrl(urlToBackup);

        // Create R2 key (path)
        const r2Key = `${history.userId}/${history.id}/${filename}`;

        // Upload file to R2
        const r2Url = await uploadFileFromUrl(urlToBackup, r2Key);

        // Update history
        const currentExtra = historyExtra || {};
        if (!currentExtra.originalUrl) {
          currentExtra.originalUrl = urlToBackup;
        }

        const now = new Date();

        await db
          .update(histories)
          .set({
            resultUrl: r2Url,
            extra: currentExtra,
            backupStatus: "SUCCESS",
            lastBackupAt: now,
            updatedAt: now,
          })
          .where(eq(histories.id, history.id));

        // Update share if exists
        if (history.share) {
          await db
            .update(shares)
            .set({
              imageUrl: r2Url,
              updatedAt: new Date(),
            })
            .where(eq(shares.id, history.share.id));
        }

        logger.info(`Backed up history ${history.id} to ${r2Url}`);
        result.succeeded++;
        result.processed.push({
          historyId: history.id,
          oldUrl: urlToBackup,
          newUrl: r2Url,
          shareId: history.share?.id,
          backupStatus: "SUCCESS",
          lastBackupAt: now.toISOString(),
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const err = error instanceof Error ? error : new Error(`Failed to process history ${history.id}`);
        logger.error(`Error processing history ${history.id}`, err, { details: error });
        result.failed++;
        result.errors.push({
          historyId: history.id,
          error: errorMessage,
        });

        // Update history backup status to FAILED
        if (!dryRun) {
          try {
            await db
              .update(histories)
              .set({
                backupStatus: "FAILED",
                lastBackupAt: new Date(),
                updatedAt: new Date(),
              })
              .where(eq(histories.id, history.id));
          } catch (updateError) {
            const err = updateError instanceof Error ? updateError : new Error(`Failed to update history ${history.id}`);
            logger.error(`Error updating backup status for history ${history.id}`, err, { details: updateError });
          }
        }

        // Return the result for this history
        return {
          success: false,
          historyId: history.id,
          error: errorMessage
        };
      }

      // Return the result for this history
      return {
        success: true,
        historyId: history.id
      };
    });

    // Wait for all promises in the batch to complete
    await Promise.all(batchPromises);
    logger.info(`Completed batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(historiesToProcess.length / batchSize)}`);
    }

    // Generate log file
    const logContent = generateLogContent(result, options);
    const timestamp = format(new Date(), 'yyyy-MM-dd-HH-m-s');
    const logFilename = `backup-${timestamp}.log`;

    if (!dryRun) {
      const logUrl = await uploadLogFile(logContent, logFilename);
      result.logUrl = logUrl;
    }

    logger.info('Backup completed', {
      totalProcessed: result.totalProcessed,
      succeeded: result.succeeded,
      failed: result.failed,
      skipped: result.skipped,
    });

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const err = error instanceof Error ? error : new Error('Unknown backup error');
    logger.error('Error during backup process', err, { details: error });
    throw new Error(`Backup failed: ${errorMessage}`);
  }
}

/**
 * Backup images from the previous day
 * @param batchSize Number of images to process concurrently in each batch
 * @param skipBackupCheck Whether to skip checking if images are already backed up
 * @returns Backup result
 */
export async function backupPreviousDay(batchSize = 10, skipBackupCheck = false): Promise<BackupResult> {
  const now = new Date();
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);

  // Set time to start of day
  yesterday.setHours(0, 0, 0, 0);

  // Set time to end of day
  const endOfYesterday = new Date(yesterday);
  endOfYesterday.setHours(23, 59, 59, 999);

  return backupImages({
    startDate: yesterday,
    endDate: endOfYesterday,
    batchSize,
    skipBackupCheck,
  });
}

/**
 * Generate log content from backup result
 * @param result Backup result
 * @param options Backup options
 * @returns Log content
 */
function generateLogContent(result: BackupResult, options: BackupOptions): string {
  const { startDate, endDate, dryRun, batchSize, skipBackupCheck } = options;

  let content = `# Image Backup Log\n\n`;
  content += `Date Range: ${startDate.toISOString()} to ${endDate.toISOString()}\n`;
  content += `Dry Run: ${dryRun ? 'Yes' : 'No'}\n`;
  content += `Batch Size: ${batchSize}\n`;
  content += `Skip Backup Check: ${skipBackupCheck ? 'Yes' : 'No'}\n\n`;

  content += `## Summary\n\n`;
  content += `- Total Processed: ${result.totalProcessed}\n`;
  content += `- Succeeded: ${result.succeeded}\n`;
  content += `- Failed: ${result.failed}\n`;
  content += `- Skipped: ${result.skipped}\n\n`;

  if (result.errors.length > 0) {
    content += `## Errors\n\n`;
    result.errors.forEach((error, index) => {
      content += `### Error ${index + 1}\n`;
      content += `- History ID: ${error.historyId}\n`;
      content += `- Error: ${error.error}\n\n`;
    });
  }

  content += `## Processed Items\n\n`;
  result.processed.forEach((item, index) => {
    content += `### Item ${index + 1}\n`;
    content += `- History ID: ${item.historyId}\n`;
    content += `- Old URL: ${item.oldUrl}\n`;
    content += `- New URL: ${item.newUrl}\n`;
    if (item.shareId) {
      content += `- Share ID: ${item.shareId}\n`;
    }
    content += `\n`;
  });

  return content;
}
