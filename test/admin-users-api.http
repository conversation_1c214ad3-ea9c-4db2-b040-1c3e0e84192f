### Test Admin Users API
# Note: You must be signed in and be a member of an organization with slug "root" to access this endpoint
GET http://localhost:3000/api/admin/users

### Test Admin Users API with pagination
# Note: You must be signed in and be a member of an organization with slug "root" to access this endpoint
GET http://localhost:3000/api/admin/users?page=1&limit=10

### Test Admin Users API with sorting
# Note: You must be signed in and be a member of an organization with slug "root" to access this endpoint
GET http://localhost:3000/api/admin/users?orderBy=createdAt&order=desc

### Test Admin Users API with filtering
# Note: You must be signed in and be a member of an organization with slug "root" to access this endpoint
GET http://localhost:3000/api/admin/users?email=test&username=user

### Test Admin Users API with all parameters
# Note: You must be signed in and be a member of an organization with slug "root" to access this endpoint
GET http://localhost:3000/api/admin/users?page=1&limit=10&orderBy=createdAt&order=desc&email=test&username=user
