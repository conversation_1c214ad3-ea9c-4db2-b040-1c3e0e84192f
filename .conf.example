# PM2 配置文件示例
# 复制此文件为 .conf.local 并填入实际配置

# ==== 基本配置 ====
# 应用名称 (确保每个应用使用不同的名称)
APP_NAME=vibany-next

# 应用端口 (确保每个应用使用不同的端口)
# 第一个应用可以使用 3000，第二个应用使用 3001，依此类推
PM2_PORT=3000

# 环境变量文件路径
ENV_FILE=.env.local

# 运行环境
PM2_ENV=production

# ==== PM2 进程配置 ====
# 进程实例数 (建议使用 1 避免端口冲突, max = 使用所有CPU核心, 或设置具体数字如 2)
PM2_INSTANCES=1

# 内存限制 (超过此值自动重启)
PM2_MAX_MEMORY=1G

# Node.js 启动参数
PM2_NODE_ARGS=--max-old-space-size=2048

# ==== 可选配置 ====
# 重启延迟 (毫秒)
RESTART_DELAY=4000

# 最大重启次数
MAX_RESTARTS=10

# 最小运行时间
MIN_UPTIME=10s

# 启用监控
ENABLE_MONITORING=true
