import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import type { NextRequest } from "next/server";

const publicRoutes = createRouteMatcher([
  "/",
  "/faq(.*)",
  "/contact(.*)",
  "/login(.*)",
  "/register(.*)",
  "/invite/(.*)",
  "/api/public(.*)",
]);

export default clerkMiddleware(async (auth, req: NextRequest) => {
  // For public routes, allow access without authentication
  if (publicRoutes(req)) return;

  // For all other routes, require authentication
  await auth.protect();
});

export const config = {
  matcher: [
    "/((?!.*\\..*|_next).*)",
    "/",
    "/(api|trpc)(.*)",
  ],
};
