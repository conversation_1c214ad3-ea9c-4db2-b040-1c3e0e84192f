# 本地备份操作指南

本文档详细说明如何在本地环境中执行图片备份操作，包括界面操作和命令行方法。

## 1. 环境准备

### 1.1 创建 .env.admin.local 文件

首先，需要创建一个 `.env.admin.local` 文件，该文件将包含连接到生产环境数据库和 Cloudflare R2 存储的配置，但使用开发版的 Clerk 认证。

1. 复制 `.env.example` 文件作为模板：

```bash
cp .env.example .env.admin.local
```

2. 编辑 `.env.admin.local` 文件，填入以下配置：

```
# 基本配置
NEXT_PUBLIC_DEBUG=true
NEXT_PUBLIC_APP_URL=http://localhost:3000

# 生产环境数据库连接
DATABASE_URL=<生产环境数据库URL>

# 开发版 Clerk 配置
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=<开发版Clerk公钥>
CLERK_SECRET_KEY=<开发版Clerk密钥>
CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/draw
CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/draw

# 生产环境 Cloudflare R2 配置
R2_ACCOUNT_ID=<Cloudflare账户ID>
R2_ACCESS_KEY_ID=<R2访问密钥ID>
R2_SECRET_ACCESS_KEY=<R2访问密钥>
R2_BUCKET_NAME=<R2存储桶名称>
R2_PUBLIC_URL_PREFIX=<R2公共URL前缀>

# 其他必要的配置项...
```

**重要提示**：
- 数据库和 Cloudflare R2 配置应使用生产环境的值
- Clerk 配置应使用开发版的值
- 确保有足够的权限访问生产数据库和 R2 存储

### 1.2 启动本地开发服务器

使用 `admin.sh` 脚本启动开发服务器，该脚本会自动加载 `.env.admin.local` 中的环境变量：

```bash
chmod +x admin.sh  # 确保脚本有执行权限
./admin.sh
```

服务器启动后，访问 http://localhost:3000/admin/backup 进入备份管理界面。

## 2. 通过界面执行备份

### 2.1 访问备份管理界面

1. 在浏览器中打开 http://localhost:3000/admin/backup
2. 使用管理员账户登录（必须具有管理员权限）

### 2.2 创建备份任务

在备份管理界面中：

1. 选择"创建备份任务"选项卡
2. 设置备份参数：
   - **日期范围**：选择要备份的时间范围
   - **仅模拟运行**：勾选此项将不会实际修改数据，仅模拟备份过程
   - **忽略备份状态检查**：勾选此项将重新备份所有图片，包括已备份的
   - **批处理大小**：设置每批并发处理的图片数量（默认为10）
   - **最大运行时间（秒）**：设置备份任务的最大运行时间，设置为0表示无限制（默认为270秒）
3. 点击"创建备份任务"按钮开始备份

### 2.3 查看备份结果

创建备份任务后，系统会自动跳转到备份执行详情页面，您可以：

1. 查看备份任务的基本信息：
   - 状态（等待中、运行中、成功、失败）
   - 创建时间、开始时间、完成时间
   - 备份参数（日期范围、批处理大小等）

2. 查看备份摘要：
   - 总处理数
   - 成功数
   - 失败数
   - 跳过数
   - 总用时和平均每张图片处理时间（对于已完成的任务）

3. 查看详细日志：
   - 备份过程的详细日志
   - 可下载完整日志文件

4. 查看详细信息：
   - 处理的每个历史记录的详细信息
   - 原始图片和备份后的图片预览
   - 历史记录ID和分享ID（如果有）

### 2.4 查看历史备份任务

在备份管理界面中：

1. 选择"备份历史记录"选项卡
2. 查看所有历史备份任务的列表
3. 点击任务ID或"查看详情"按钮查看特定任务的详细信息

## 3. 备份任务超时设置

从最新版本开始，备份系统支持配置最大运行时间：

- **默认值**：270秒（4分30秒），略低于Vercel的300秒函数执行限制
- **无限制**：设置为0表示无超时限制，任务将一直运行直到完成
- **自定义值**：可以设置1-3600秒之间的任意值

当备份任务接近超时时：
1. 系统会停止处理新的批次
2. 已开始处理的图片会尽可能完成
3. 如果有成功处理的图片，任务会被标记为部分成功
4. 如果没有成功处理的图片，任务会被标记为失败

## 4. 故障排除

### 4.1 常见问题

1. **无法连接到数据库**：
   - 检查 `.env.admin.local` 中的 `DATABASE_URL` 是否正确
   - 确保您的IP地址有权限访问生产数据库

2. **无法连接到R2存储**：
   - 检查 `.env.admin.local` 中的R2配置是否正确
   - 确保R2访问密钥具有足够的权限

3. **备份任务卡在"运行中"状态**：
   - 这可能是由于任务超时或执行环境问题导致
   - 可以使用"修复卡住的备份执行"功能（在API中可用）
   - 或者创建新的备份任务，设置更长的超时时间

4. **备份失败**：
   - 查看详细日志了解失败原因
   - 常见原因包括源图片URL不可访问、网络问题等
   - 对于特定图片的失败，可以使用"忽略备份状态检查"选项重新尝试

### 4.2 日志和监控

备份系统提供多种日志和监控方式：

1. **执行日志**：
   - 存储在数据库的 `executions` 表中
   - 可在执行详情页面查看和下载

2. **备份日志文件**：
   - 存储在R2存储桶的 `logs` 文件夹中
   - 命名格式为 `backup-yyyy-MM-dd-HH-m-s.log`
   - 包含处理的每个历史记录的详细信息

## 5. 安全注意事项

在本地执行备份操作时，请注意以下安全事项：

1. **环境变量保护**：
   - 不要将包含敏感信息的 `.env.admin.local` 文件提交到版本控制系统
   - 不要与未授权人员共享此文件

2. **数据库访问**：
   - 确保只有授权人员可以执行备份操作
   - 使用最小权限原则配置数据库访问

3. **R2存储访问**：
   - 使用专用的R2访问密钥，仅授予必要的权限
   - 定期轮换R2访问密钥

## 6. 参考资料

更多详细信息，请参考以下文档：

- [图片备份系统完整文档](./documents/image-backup-system.md)
- [备份系统设计文档](./documents/async-backup-design.md)
- [图片备份基础文档](./documents/image-backup.md)
- [备份系统README](./documents/backup-readme.md)
