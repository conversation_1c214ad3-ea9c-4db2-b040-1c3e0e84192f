import { create } from 'zustand';

export interface Profile {
  id: string;
  email: string;
  username: string;
  avatarUrl: string;
  isPaid: boolean;
}

export interface Wallet {
  id: string;
  permanentPoints: number;
}

export interface Transaction {
  id: string;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  createdAt: string;
}

interface ProfileState {
  profile: Profile | null;
  wallet: Wallet | null;
  transactions: Transaction[];
  isNewUser: boolean;
  isLoading: boolean;
  error: string | null;
  refreshUserInfo: () => Promise<any>;
  fetch: () => Promise<any>; // Kept for backward compatibility
}

// Create the store with a type annotation
export const useProfileStore = create<ProfileState>((set) => ({
  profile: null,
  wallet: null,
  transactions: [],
  isNewUser: false,
  isLoading: false,
  error: null,
  refreshUserInfo: async ({ historyLimit = 1, transactionLimit = 1, historyIncludeShare = false, inviteCode = '' } = {}) => {
    try {
      set({ isLoading: true, error: null });
      const timestamp = Date.now(); // Add timestamp to prevent caching

      // 从 localStorage 获取邀请码（如果没有提供）
      let finalInviteCode = inviteCode;
      if (!finalInviteCode && typeof window !== 'undefined') {
        finalInviteCode = localStorage.getItem('inviteCode') || '';
        console.log(`[INVITATION_DEBUG] 从 localStorage 获取邀请码: ${finalInviteCode}`);
        // 注意：不在这里清除邀请码，而是在获取用户信息后根据 isNewUser 判断
      }

      const res = await fetch(`/api/me?historyLimit=${historyLimit}&transactionLimit=${transactionLimit}&historyIncludeShare=${historyIncludeShare}&inviteCode=${encodeURIComponent(finalInviteCode)}&t=${timestamp}`);

      if (!res.ok) {
        throw new Error("Failed to fetch profile");
      }

      const data = await res.json();
      set({
        profile: {
          ...data.user,
          isPaid: data.user.isPaid ?? false,
        },
        wallet: data.wallet,
        transactions: data.recentTransactions,
        isNewUser: data.isNewUser,
        isLoading: false,
      });

      // 如果用户不是新用户，则清除 localStorage 中的邀请码
      if (!data.isNewUser && typeof window !== 'undefined' && localStorage.getItem('inviteCode')) {
        const codeToRemove = localStorage.getItem('inviteCode');
        console.log(`[INVITATION_DEBUG] 用户不是新用户，清除 localStorage 中的邀请码: ${codeToRemove}`);
        localStorage.removeItem('inviteCode');
      } else if (data.isNewUser && typeof window !== 'undefined' && localStorage.getItem('inviteCode')) {
        console.log(`[INVITATION_DEBUG] 用户是新用户，保留 localStorage 中的邀请码: ${localStorage.getItem('inviteCode')}`);
      }

      return data; // Return data for immediate access if needed
    } catch (error) {
      set({
        error:
          error instanceof Error ? error.message : "Failed to load profile",
        isLoading: false,
      });
      throw error; // Rethrow for error handling in components
    }
  },

  // Keep fetch as an alias for backward compatibility
  fetch: async (): Promise<any> => {
    const profileStore = useProfileStore.getState();
    return profileStore.refreshUserInfo();
  },
}));
