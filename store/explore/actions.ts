export interface Share {
  id: string;
  shareId: string;
  imageUrl: string;
  model: string;
  styleId: string;
  customPrompt: string;
  originalImages: string[];
  viewCount: number;
  likeCount: number;
  forkCount: number;
  author: {
    id: string;
    name: string;
    avatar: string | null;
  };
}

export function getShareUrl(shareId: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  return `${baseUrl}/explore/${shareId}`;
}

export async function getShare(id: string): Promise<Share | null> {
  try {
    const response = await fetch(`/api/public/shares/${id}`);
    if (!response.ok) return null;
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error("Failed to fetch share:", error);
    return null;
  }
}
