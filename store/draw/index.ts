import { create } from 'zustand'
import { DRAW_STYLES, DEFAULT_DRAW_STYLE } from "@/constants/draw";
import {
  DEFAULT_DRAW_MODEL,
  VIP_DEFAULT_DRAW_MODEL,
} from "@/constants/draw/models";
import { HistoryItem } from "@/components/settings/history/history";
import { useProfileStore } from "@/store/profile";

interface DrawState {
  // 基本绘图状态
  style: keyof typeof DRAW_STYLES
  model: string
  prompt: string
  images: File[]
  previewUrls: string[]
  output: string
  isLoading: boolean
  isModalOpen: boolean

  // 新图片通知状态
  newImageNotificationOpen: boolean
  latestGeneratedImageUrl: string | null
  previousShownHistoryId: string | null // 缓存上一次显示过的历史记录ID

  // 历史记录状态
  recentHistories: HistoryItem[]
  pendingHistories: HistoryItem[]
  historiesLoading: boolean
  pollingInterval: NodeJS.Timeout | null

  // 基本方法
  setStyle: (style: keyof typeof DRAW_STYLES) => void
  setModel: (model: string) => void
  setPrompt: (prompt: string) => void
  setImages: (images: File[]) => void
  setPreviewUrls: (urls: string[]) => void
  setOutput: (output: string) => void
  setIsLoading: (isLoading: boolean) => void
  setIsModalOpen: (isModalOpen: boolean) => void
  reset: () => void

  // 新图片通知方法
  showNewImageNotification: (imageUrl: string, historyId?: string) => void
  closeNewImageNotification: () => void

  // 历史记录缓存方法
  getPreviousShownHistoryId: () => string | null
  setPreviousShownHistoryId: (historyId: string) => void
  isHistoryAlreadyShown: (historyId: string) => boolean

  // 历史记录方法
  fetchCombinedHistories: () => Promise<void>
  startHistoryPolling: () => () => void
  stopHistoryPolling: () => void

  // 清理方法
  cleanup: () => void
}

// 比较历史记录是否有关键变化
const compareHistories = (oldHistories: HistoryItem[], newHistories: HistoryItem[]): boolean => {
  // 如果旧历史记录为空，但新历史记录不为空，则有变化
  if (oldHistories.length === 0 && newHistories.length > 0) {
    console.log('Old histories empty, new histories not empty');
    return true;
  }

  // 长度不同，肯定有变化
  if (oldHistories.length !== newHistories.length) {
    console.log('Histories length different');
    return true;
  }

  // 创建映射以便快速查找
  const oldMap = new Map(oldHistories.map(h => [h.id, h]));

  // 检查每个新历史记录
  for (const newHistory of newHistories) {
    const oldHistory = oldMap.get(newHistory.id);

    // 如果找不到对应的旧记录，说明有变化
    if (!oldHistory) {
      console.log('New history not found in old histories:', newHistory.id);
      return true;
    }

    // 检查关键字段
    if (
      oldHistory.status !== newHistory.status ||
      oldHistory.drawStatus !== newHistory.drawStatus ||
      JSON.stringify(oldHistory.share) !== JSON.stringify(newHistory.share)
    ) {
      console.log('History changed:', {
        id: newHistory.id,
        oldStatus: oldHistory.status,
        newStatus: newHistory.status,
        oldDrawStatus: oldHistory.drawStatus,
        newDrawStatus: newHistory.drawStatus
      });
      return true;
    }
  }

  // 没有检测到变化
  return false;
};

// 检查是否在浏览器环境中
const isBrowser = typeof window !== 'undefined';

// 安全地访问 localStorage
const getLocalStorageItem = (key: string): string | null => {
  if (isBrowser) {
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.error(`从 localStorage 读取 ${key} 失败:`, error);
      return null;
    }
  }
  return null;
};

const setLocalStorageItem = (key: string, value: string): void => {
  if (isBrowser) {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.error(`写入 localStorage ${key} 失败:`, error);
    }
  }
};

// 创建绘图状态存储
export const useDrawStore = create<DrawState>((set, get) => {
  // 初始化时获取 profile
  const { profile } = useProfileStore.getState();
  const model = profile?.isPaid ? VIP_DEFAULT_DRAW_MODEL : DEFAULT_DRAW_MODEL;

  // 订阅 profile 变化
  const unsubscribeProfile = useProfileStore.subscribe((state) => {
    // 当 profile 变化时，检查 isPaid 状态并更新 model
    const newProfile = state.profile;
    if (newProfile) {
      const currentModel = get().model;
      const shouldBeModel = newProfile.isPaid ? VIP_DEFAULT_DRAW_MODEL : DEFAULT_DRAW_MODEL;

      // 只有当模型需要变化时才更新
      if (currentModel !== shouldBeModel) {
        console.log('Profile changed, updating model:', {
          isPaid: newProfile.isPaid,
          oldModel: currentModel,
          newModel: shouldBeModel
        });
        set({ model: shouldBeModel });
      }
    }
  });

  return {
    // 基本绘图状态
    style: DEFAULT_DRAW_STYLE,
    model, // 默认使用非付费模型，后续会根据用户状态更新
    prompt: "",
    images: [],
    previewUrls: [],
    output: "",
    isLoading: false,
    isModalOpen: false,

    // 新图片通知状态
    newImageNotificationOpen: false,
    latestGeneratedImageUrl: null,
    // 从 localStorage 中读取上次显示的历史记录ID，如果有的话
    previousShownHistoryId: getLocalStorageItem('previousShownHistoryId'),

    // 历史记录状态
    recentHistories: [],
    pendingHistories: [],
    historiesLoading: false,
    pollingInterval: null,

    // 基本方法
    setStyle: (style) => set({ style }),
    setModel: (model) => set({ model }),
    setPrompt: (prompt) => set({ prompt }),
    setImages: (images) => set({ images }),
    setPreviewUrls: (previewUrls) => set({ previewUrls }),
    setOutput: (output) => set({ output }),
    setIsLoading: (isLoading) => set({ isLoading }),
    setIsModalOpen: (isModalOpen) => set({ isModalOpen }),
    reset: () => {
      // 获取当前的 profile 状态，确保重置时使用正确的模型
      const { profile } = useProfileStore.getState();
      const resetModel = profile?.isPaid ? VIP_DEFAULT_DRAW_MODEL : DEFAULT_DRAW_MODEL;

      set({
        style: "DEFAULT",
        model: resetModel, // 根据用户付费状态选择模型
        prompt: "",
        images: [],
        previewUrls: [],
        output: "",
        isLoading: false,
        isModalOpen: false,
        newImageNotificationOpen: false,
        latestGeneratedImageUrl: null,
        // 保留 localStorage 中的值
        previousShownHistoryId: getLocalStorageItem('previousShownHistoryId'),
      });
    },

    // 历史记录缓存方法
    getPreviousShownHistoryId: () => {
      // 优先使用 store 中的值，如果没有则从 localStorage 中读取
      const { previousShownHistoryId } = get();
      if (previousShownHistoryId) {
        return previousShownHistoryId;
      }
      return getLocalStorageItem('previousShownHistoryId');
    },

    setPreviousShownHistoryId: (historyId: string) => {
      // 同时更新 store 和 localStorage
      setLocalStorageItem('previousShownHistoryId', historyId);
      set({ previousShownHistoryId: historyId });
    },

    isHistoryAlreadyShown: (historyId: string) => {
      // 检查历史记录是否已经显示过
      if (!historyId) return false;

      const previousId = get().getPreviousShownHistoryId();
      return previousId === historyId;
    },

    // 新图片通知方法
    showNewImageNotification: (imageUrl: string, historyId?: string) => {
      // 如果提供了 historyId，则检查是否与上次显示的历史记录相同
      if (historyId && get().isHistoryAlreadyShown(historyId)) {
        console.log('跳过显示模态框，历史记录与上次相同:', historyId);
        return;
      }

      console.log('显示新图片通知，图片URL:', imageUrl, '历史记录ID:', historyId || '无');

      // 如果提供了 historyId，则更新上次显示的历史记录ID
      if (historyId) {
        get().setPreviousShownHistoryId(historyId);
      }

      // 显示新图片通知模态框
      set({
        newImageNotificationOpen: true,
        latestGeneratedImageUrl: imageUrl
      });
    },
    closeNewImageNotification: () => {
      // 关闭新图片通知模态框
      set({
        newImageNotificationOpen: false
      });
    },

    // 历史记录方法
    fetchCombinedHistories: async () => {
      set({ historiesLoading: true });
      try {
        console.log('Fetching combined histories...');
        const response = await fetch('/api/history/combined?includeShare=true&recentLimit=5&pendingLimit=10');
        if (response.ok) {
          const data = await response.json();
          console.log('Received combined histories:', {
            recentCount: (data.recentHistories || []).length,
            pendingCount: (data.pendingHistories || []).length
          });

          // 获取当前状态
          const currentRecentHistories = get().recentHistories;
          const currentPendingHistories = get().pendingHistories;

          // 比对数据，只有关键字段变化时才更新
          const recentHasChanged = compareHistories(currentRecentHistories, data.recentHistories || []);
          const pendingHasChanged = compareHistories(currentPendingHistories, data.pendingHistories || []);

          // 只更新变化的部分
          if (recentHasChanged) {
            console.log('Recent histories changed, updating state');

            // 检查是否有新的成功生成的图片
            const newHistories = data.recentHistories || [];
            if (newHistories.length > 0 && currentRecentHistories.length > 0) {
              // 找出新增的历史记录
              const newIds: string[] = newHistories.map((h: HistoryItem) => h.id);
              const oldIds: string[] = currentRecentHistories.map((h: HistoryItem) => h.id);

              // 找出新增的 ID
              const addedIds: string[] = newIds.filter(id => !oldIds.includes(id));

              if (addedIds.length > 0) {
                // 找出新增的历史记录中的第一个成功的图片
                const newSuccessfulHistories = newHistories
                  .filter((h: HistoryItem) => addedIds.includes(h.id) && h.status && h.drawStatus === 'SUCCESS');

                if (newSuccessfulHistories.length > 0) {
                  const latestHistory = newSuccessfulHistories[0];
                  if (latestHistory.resultUrl) {
                    // 显示新图片通知，传递历史记录ID
                    get().showNewImageNotification(latestHistory.resultUrl, latestHistory.id);
                  }
                }
              }
            }

            // 如果是第一次加载数据，并且有成功的图片，也显示通知
            if (currentRecentHistories.length === 0 && newHistories.length > 0) {
              const successfulHistories = newHistories.filter((h: HistoryItem) => h.status && h.drawStatus === 'SUCCESS');
              if (successfulHistories.length > 0) {
                const latestHistory = successfulHistories[0];
                if (latestHistory.resultUrl) {
                  // 显示新图片通知，传递历史记录ID
                  get().showNewImageNotification(latestHistory.resultUrl, latestHistory.id);
                }
              }
            }

            set({ recentHistories: data.recentHistories || [] });
          } else {
            console.log('Recent histories unchanged, skipping update');
          }

          if (pendingHasChanged) {
            console.log('Pending histories changed, updating state');
            set({ pendingHistories: data.pendingHistories || [] });
          } else {
            console.log('Pending histories unchanged, skipping update');
          }
        } else {
          console.error('Failed to fetch combined histories:', response.status);
        }
      } catch (error) {
        console.error("Error fetching combined histories:", error);
      } finally {
        set({ historiesLoading: false });
      }
    },

    startHistoryPolling: () => {
      console.log('Starting history polling');
      // 先清除可能存在的轮询
      get().stopHistoryPolling();

      // 立即获取一次数据
      get().fetchCombinedHistories();

      // 设置轮询
      const interval = setInterval(() => {
        console.log('Polling for histories...');
        get().fetchCombinedHistories();
      }, 5000); // 每5秒轮询一次

      // 保存 interval ID
      set({ pollingInterval: interval });

      // 返回清除函数
      return () => {
        console.log('Stopping history polling');
        clearInterval(interval);
        set({ pollingInterval: null });
      };
    },

    stopHistoryPolling: () => {
      const { pollingInterval } = get();
      if (pollingInterval) {
        clearInterval(pollingInterval);
        set({ pollingInterval: null });
      }
    },

    // 添加清理函数，用于在组件卸载时取消订阅
    cleanup: () => {
      // 取消对 profile 的订阅
      unsubscribeProfile();

      // 停止历史记录轮询
      get().stopHistoryPolling();
    },
  };
});
