import { create } from 'zustand';

export interface InvitationUsage {
  id: string;
  invitationId: string;
  invitation: {
    inviteCode: string;
    inviteType: "points" | "cash" | "both";
    refRatio: number;
  };
  referee: {
    clerkId: string;
    username: string;
    email: string;
    avatarUrl?: string;
  };
  registeredAt: string;
  firstRechargeAt?: string;
  rechargeAmount?: number;
  pointsAwarded?: number;
  cashAwarded?: number;
  status: "pending" | "ready" | "completed" | "void";
  redeemedAt?: string;
  operatorId?: string;
  operator?: {
    clerkId: string;
    username: string;
    email: string;
  };
  extra?: {
    redeemType?: "points" | "cash";
    redeemNote?: string;
    [key: string]: any;
  };
}

export interface InvitationStats {
  totalInvitations: number;
  pendingCount: number;
  readyCount: number;
  completedCount: number;
  totalPointsAwarded: number;
  totalCashAwarded: number;
  pendingPointsAwarded: number;
  pendingCashAwarded: number;
}

export interface InvitationDetail {
  invitation: {
    id: string;
    inviteCode: string;
    referrerId: string;
    inviteType: "points" | "cash" | "both";
    refRatio: number;
    channel?: string;
    maxUses: number;
    expiresAt?: string;
    createdAt: string;
    updatedAt: string;
  };
  referrer: {
    clerkId: string;
    email: string;
    username: string;
    avatarUrl?: string;
  };
  stats: {
    usageCount: number;
    pendingCount: number;
    readyCount: number;
    completedCount: number;
    voidCount: number;
  };
}

interface InvitationState {
  // 邀请详情
  invitationDetail: InvitationDetail | null;
  loadingDetail: boolean;

  // 邀请使用记录
  usages: InvitationUsage[];
  loadingUsages: boolean;
  usagesPagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
    orderBy?: string;
    order?: string;
  };
  usagesFilter: {
    status: string;
    invitationId?: string;
    refereeId?: string;
  };

  // 邀请统计
  stats: InvitationStats | null;
  loadingStats: boolean;

  // 选中的记录
  selectedUsageIds: string[];

  // 批量兑换状态
  batchRedeemLoading: boolean;

  // 操作方法
  setInvitationDetail: (detail: InvitationDetail | null) => void;
  setUsages: (usages: InvitationUsage[]) => void;
  setUsagesPagination: (pagination: any) => void;
  setUsagesFilter: (filter: any) => void;
  setStats: (stats: InvitationStats | null) => void;
  setSelectedUsageIds: (ids: string[]) => void;
  toggleUsageSelection: (id: string) => void;
  clearSelectedUsages: () => void;

  // 数据加载方法
  fetchInvitationDetail: (id: string) => Promise<void>;
  fetchInvitationUsages: (params?: any) => Promise<void>;
  fetchInvitationStats: (includePendingRewards?: boolean) => Promise<void>;

  // 批量兑换方法
  redeemBatchPoints: (usageIds: string[]) => Promise<any>;
  redeemBatchCash: (usageIds: string[], note?: string) => Promise<any>;

  // 刷新方法
  refreshAll: (id: string) => Promise<void>;
}

export const useInvitationStore = create<InvitationState>((set, get) => ({
  // 初始状态
  invitationDetail: null,
  loadingDetail: false,

  usages: [],
  loadingUsages: false,
  usagesPagination: {
    page: 1,
    limit: 10,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  },
  usagesFilter: {
    status: 'all',
  },

  stats: null,
  loadingStats: false,

  selectedUsageIds: [],
  batchRedeemLoading: false,

  // 设置方法
  setInvitationDetail: (detail) => set({ invitationDetail: detail }),
  setUsages: (usages) => set({ usages }),
  setUsagesPagination: (pagination) => set({ usagesPagination: pagination }),
  setUsagesFilter: (filter) => set({
    usagesFilter: { ...get().usagesFilter, ...filter },
    // 重置页码
    usagesPagination: { ...get().usagesPagination, page: 1 }
  }),
  setStats: (stats) => set({ stats }),
  setSelectedUsageIds: (ids) => set({ selectedUsageIds: ids }),
  toggleUsageSelection: (id) => {
    const { selectedUsageIds } = get();
    if (selectedUsageIds.includes(id)) {
      set({ selectedUsageIds: selectedUsageIds.filter(i => i !== id) });
    } else {
      set({ selectedUsageIds: [...selectedUsageIds, id] });
    }
  },
  clearSelectedUsages: () => set({ selectedUsageIds: [] }),

  // 数据加载方法
  fetchInvitationDetail: async (id) => {
    try {
      set({ loadingDetail: true });
      const response = await fetch(`/api/admin/invitations/${id}`);

      if (!response.ok) {
        throw new Error("Failed to fetch invitation details");
      }

      const data = await response.json();
      set({ invitationDetail: data });
      return data;
    } catch (error) {
      console.error("Error fetching invitation details:", error);
      throw error;
    } finally {
      set({ loadingDetail: false });
    }
  },

  fetchInvitationUsages: async (params = {}) => {
    try {
      set({ loadingUsages: true });
      const { usagesFilter, usagesPagination } = get();

      const queryParams = new URLSearchParams({
        page: params.page?.toString() || usagesPagination.page.toString(),
        limit: params.limit?.toString() || usagesPagination.limit.toString(),
        ...(params.orderBy && { orderBy: params.orderBy }),
        ...(params.order && { order: params.order }),
      });

      // 添加过滤条件
      const status = params.status || usagesFilter.status;
      if (status && status !== 'all') queryParams.append("status", status);

      const invitationId = params.invitationId || usagesFilter.invitationId;
      if (invitationId) queryParams.append("invitationId", invitationId);

      const refereeId = params.refereeId || usagesFilter.refereeId;
      if (refereeId) queryParams.append("refereeId", refereeId);

      // 确定API路径
      const apiPath = params.isAdmin ? '/api/admin/invitation_usages' : '/api/invitation_usages';
      const response = await fetch(`${apiPath}?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error("Failed to fetch invitation usages");
      }

      const data = await response.json();

      // 更新状态
      set({
        usages: data.usages || [],
        usagesPagination: data.pagination || get().usagesPagination,
        usagesFilter: {
          ...usagesFilter,
          ...(params.status && { status: params.status }),
          ...(params.invitationId && { invitationId: params.invitationId }),
          ...(params.refereeId && { refereeId: params.refereeId }),
        }
      });

      return data;
    } catch (error) {
      console.error("Error fetching invitation usages:", error);
      throw error;
    } finally {
      set({ loadingUsages: false });
    }
  },

  fetchInvitationStats: async (includePendingRewards = true) => {
    try {
      set({ loadingStats: true });
      const response = await fetch(`/api/invitations?includeStats=true&includePendingRewards=${includePendingRewards}&limit=1`);

      if (!response.ok) {
        throw new Error("Failed to fetch invitation stats");
      }

      const data = await response.json();
      set({ stats: data.stats });
      return data.stats;
    } catch (error) {
      console.error("Error fetching invitation stats:", error);
      throw error;
    } finally {
      set({ loadingStats: false });
    }
  },

  // 批量兑换方法
  redeemBatchPoints: async (usageIds) => {
    try {
      set({ batchRedeemLoading: true });
      const response = await fetch('/api/invitation_usages/redeem-batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          usage_ids: usageIds,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "批量兑换积分失败");
      }

      const data = await response.json();

      // 清除选中的记录
      set({ selectedUsageIds: [] });

      return data;
    } catch (error) {
      console.error("Error redeeming batch points:", error);
      throw error;
    } finally {
      set({ batchRedeemLoading: false });
    }
  },

  redeemBatchCash: async (usageIds, note) => {
    try {
      set({ batchRedeemLoading: true });
      const response = await fetch('/api/admin/invitation_usages/redeem-batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          usage_ids: usageIds,
          redeem_type: 'cash',
          note,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "批量兑换现金失败");
      }

      const data = await response.json();

      // 清除选中的记录
      set({ selectedUsageIds: [] });

      return data;
    } catch (error) {
      console.error("Error redeeming batch cash:", error);
      throw error;
    } finally {
      set({ batchRedeemLoading: false });
    }
  },

  // 刷新所有数据
  refreshAll: async (id) => {
    try {
      // 并行加载所有数据
      await Promise.all([
        get().fetchInvitationDetail(id),
        get().fetchInvitationUsages({ invitationId: id, isAdmin: true }),
        get().fetchInvitationStats(true),
      ]);
    } catch (error) {
      console.error("Error refreshing all data:", error);
      throw error;
    }
  },
}));
