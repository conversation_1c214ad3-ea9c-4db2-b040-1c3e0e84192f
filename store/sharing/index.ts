import { create } from "zustand";
import { Share, ShareSettings } from "@/types/share";

interface ShareStore {
  shares: Share[];
  loading: boolean;
  filter: { isPublic: boolean | null; allowFork: boolean | null };
  setShares: (shares: Share[]) => void;
  setLoading: (loading: boolean) => void;
  setFilter: (filter: { isPublic: boolean | null; allowFork: boolean | null }) => void;
  fetchShares: () => Promise<void>;
  updateShare: (id: string, settings: ShareSettings) => Promise<void>;
  createShare: (historyId: string, settings: ShareSettings) => Promise<Share>;
  deleteShare: (id: string) => Promise<void>;
}

export const useShareStore = create<ShareStore>((set) => ({
  shares: [],
  loading: true,
  filter: { isPublic: null, allowFork: null },
  setShares: (shares) => set({ shares }),
  setLoading: (loading) => set({ loading }),
  setFilter: (filter) => set({ filter }),
  fetchShares: async () => {
    try {
      set({ loading: true });
      const response = await fetch("/api/shares/user");
      if (!response.ok) throw new Error("Failed to fetch shares");
      const data = await response.json();
      set({ shares: data.shares });
    } catch (error) {
      console.error("Error fetching shares:", error);
    } finally {
      set({ loading: false });
    }
  },
  updateShare: async (id, settings) => {
    try {
      const response = await fetch(`/api/shares/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      });

      if (!response.ok) throw new Error("Failed to update share");

      const updatedShare = await response.json();
      set((state) => ({
        shares: state.shares.map((share) =>
          share.id === id ? updatedShare : share
        ),
      }));
    } catch (error) {
      console.error("Error updating share:", error);
      throw error;
    }
  },
  createShare: async (historyId, settings) => {
    try {
      const response = await fetch("/api/shares", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          historyId,
          ...settings,
        }),
      });

      if (!response.ok) throw new Error("Failed to create share");

      const newShare = await response.json();
      set((state) => ({
        shares: [...state.shares, newShare],
      }));
      return newShare;
    } catch (error) {
      console.error("Error creating share:", error);
      throw error;
    }
  },
  deleteShare: async (id) => {
    try {
      const response = await fetch(`/api/shares/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete share");

      // Remove the share from the store
      set((state) => ({
        shares: state.shares.filter((share) => share.id !== id),
      }));
    } catch (error) {
      console.error("Error deleting share:", error);
      throw error;
    }
  },
}));
