每次开发功能之前，请根据需求仔细思考与设计，在 /documents 目录中创建设计文档和 checklist，并逐步执行，每一个小的步骤完毕后，更新 checklist 状态

shadcn/ui install method: `npx shadcn@latest add accordion`

// 在 route handlers 中获取用户信息时，必须使用 @clerk/nextjs/server 中的 auth() 方法
// 错误示例：import { currentUser } from "@clerk/nextjs"
// 正确示例：import { auth } from "@clerk/nextjs/server"

// 所有需要生成 ID 的地方，默认使用 nanoid 方案
// 错误示例：import { v4 as uuidv4 } from "uuid"; const id = uuidv4();
// 正确示例：import { nanoid } from "nanoid"; const id = nanoid();

// toast 通知必须使用自定义的 useToast hook
// 错误示例：import { toast } from "sonner"
// 正确示例：import { useToast } from "@/lib/hooks/use-toast"

// 日期范围选择组件必须使用自定义的 DatePickerWithRange 组件
// 错误示例：import { DatePicker } from "react-day-picker"
// 正确示例：import { DatePickerWithRange } from "@/components/ui/date-picker-with-range"
