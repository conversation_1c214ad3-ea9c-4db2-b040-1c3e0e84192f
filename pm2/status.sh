#!/bin/bash

# PM2 状态查看脚本
# 用于查看 image-render 应用状态

set -e

PROJECT_DIR="$(dirname "$(dirname "$(realpath "$0")")")"
cd "$PROJECT_DIR"

echo "📊 image-render 应用状态"
echo "========================"

# 检查 PM2 是否已安装
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2 未安装"
    exit 1
fi

# 显示应用状态
pm2 status

echo ""
echo "🔍 应用详细信息:"
echo "------------------------"

# 检查应用是否在运行
if pm2 describe image-render > /dev/null 2>&1; then
    pm2 describe image-render
    
    echo ""
    echo "📈 实时监控："
    echo "  pm2 monit           - 打开实时监控面板"
    echo "  pm2 logs image-render - 查看应用日志"
    echo "  pm2 logs image-render --lines 50 - 查看最近50行日志"
    echo ""
    echo "🛠️  管理命令："
    echo "  ./pm2/restart.sh    - 重启应用"
    echo "  ./pm2/stop.sh       - 停止应用"
    echo "  pm2 reload image-render - 零停机重载"
    echo "  pm2 scale image-render 4 - 调整进程数量"
else
    echo "⚠️  应用未在运行中"
    echo ""
    echo "🚀 启动应用: ./pm2/start.sh"
fi