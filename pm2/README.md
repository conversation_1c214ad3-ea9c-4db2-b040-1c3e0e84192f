# PM2 部署管理脚本

本目录包含用于管理 image-render 应用的 PM2 部署脚本，提供了一套完整的应用生命周期管理工具。

## 📁 脚本文件说明

### 核心脚本

| 脚本文件 | 用途 | 描述 |
|---------|------|------|
| `deploy.sh` | 📈 智能部署 | 主要部署脚本，自动检测应用状态进行启动/重启 |
| `stop.sh` | 🛑 停止应用 | 停止并删除 PM2 应用实例 |
| `status.sh` | 📊 状态查看 | 查看应用详细状态和监控信息 |

### 兼容性脚本

| 脚本文件 | 用途 | 描述 |
|---------|------|------|
| `start.sh` | 🚀 启动应用 | 已弃用，自动重定向到 `deploy.sh` |
| `restart.sh` | 🔄 重启应用 | 已弃用，自动重定向到 `deploy.sh` |

## 🚀 快速开始

### 1. 首次部署

```bash
# 进入项目根目录
cd /path/to/image-render

# 运行部署脚本
./pm2/deploy.sh
```

部署脚本会自动：
- 🔍 检查并创建 `.conf.local` 配置文件
- 🔨 询问是否需要运行 `npm run build`
- 🚀 智能检测应用状态，自动选择启动或重启
- 💾 保存 PM2 配置

### 2. 应用管理

```bash
# 查看应用状态
./pm2/status.sh

# 停止应用
./pm2/stop.sh

# 重新部署（推荐）
./pm2/deploy.sh
```

## ⚙️ 配置文件

### 配置文件优先级

1. **`.conf.local`** - 本地配置文件（优先级最高，不会被提交到版本控制）
2. **`.conf`** - 团队共享配置文件
3. **内置默认配置** - 如果以上文件都不存在

### 配置文件创建

```bash
# 方式1：使用 deploy.sh 自动创建
./pm2/deploy.sh  # 脚本会询问是否创建配置文件

# 方式2：手动创建
cp .conf.example .conf.local
nano .conf.local
```

### 主要配置项

| 配置项 | 默认值 | 描述 |
|--------|--------|------|
| `APP_NAME` | `image-render` | 应用名称 |
| `PM2_PORT` | `3000` | 应用监听端口 |
| `ENV_FILE` | `.env.local` | 环境变量文件路径 |
| `PM2_ENV` | `production` | 运行环境 |
| `PM2_INSTANCES` | `1` | 进程实例数量 |
| `PM2_MAX_MEMORY` | `1G` | 内存限制 |
| `PM2_NODE_ARGS` | `--max-old-space-size=2048` | Node.js 启动参数 |

## 🔧 详细操作说明

### deploy.sh - 智能部署脚本

**功能特性:**
- 🔍 自动检测配置文件，询问是否创建
- 🔨 可选择性执行构建过程
- 🚀 智能检测应用状态（启动/重启）
- 🔒 端口冲突检测
- 💾 自动保存 PM2 配置

**使用示例:**

```bash
# 基本部署
./pm2/deploy.sh

# 部署过程中的交互选项
# 1. 是否创建 .conf.local 配置文件？[Y/n]
# 2. 是否需要运行 npm run build？[Y/n]
# 3. 端口冲突时是否继续部署？[y/N]
```

### stop.sh - 停止应用脚本

**功能特性:**
- 🛑 停止指定应用
- 🗑️ 删除 PM2 实例
- 🔍 检查并清理残留实例
- 💾 自动保存 PM2 配置

**使用示例:**

```bash
# 停止应用
./pm2/stop.sh

# 脚本会自动处理：
# - 停止主应用实例
# - 删除应用配置
# - 清理可能的残留实例
```

### status.sh - 状态查看脚本

**功能特性:**
- 📊 显示应用运行状态
- 📈 提供监控命令提示
- 🛠️ 显示常用管理命令

**使用示例:**

```bash
# 查看应用状态
./pm2/status.sh

# 显示信息包括：
# - PM2 进程列表
# - 应用详细信息
# - 监控命令提示
# - 管理命令列表
```

## 🛠️ 常用 PM2 命令

### 基本管理命令

```bash
# 查看所有应用
pm2 list

# 查看应用详细信息
pm2 describe image-render

# 查看实时日志
pm2 logs image-render

# 查看最近N行日志
pm2 logs image-render --lines 50

# 清空日志
pm2 flush image-render
```

### 进程管理命令

```bash
# 重启应用
pm2 restart image-render

# 零停机重载
pm2 reload image-render

# 停止应用
pm2 stop image-render

# 删除应用
pm2 delete image-render

# 调整进程数量
pm2 scale image-render 4
```

### 监控命令

```bash
# 实时监控面板
pm2 monit

# 查看资源使用情况
pm2 status

# 显示进程详情
pm2 show image-render
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -ti:3000
   
   # 修改配置文件中的端口
   echo "PM2_PORT=3001" >> .conf.local
   ```

2. **应用无法启动**
   ```bash
   # 检查日志
   pm2 logs image-render
   
   # 检查配置文件
   cat .conf.local
   
   # 检查环境变量
   cat .env.local
   ```

3. **进程意外退出**
   ```bash
   # 查看错误日志
   pm2 logs image-render --err
   
   # 重新部署
   ./pm2/deploy.sh
   ```

### 性能优化建议

1. **内存管理**
   ```bash
   # 调整内存限制
   echo "PM2_MAX_MEMORY=2G" >> .conf.local
   ```

2. **进程数量调整**
   ```bash
   # 根据 CPU 核心数调整
   echo "PM2_INSTANCES=max" >> .conf.local  # 使用所有核心
   echo "PM2_INSTANCES=4" >> .conf.local    # 固定4个进程
   ```

3. **Node.js 参数优化**
   ```bash
   # 调整 V8 内存限制
   echo "PM2_NODE_ARGS=--max-old-space-size=4096" >> .conf.local
   ```

## 🔗 相关文档

- [项目根目录 README](../README.md#deployment) - 完整部署指南
- [PM2 官方文档](https://pm2.keymetrics.io/docs/)
- [生态系统配置文件](../ecosystem.config.js)

## 📝 最佳实践

1. **使用 deploy.sh 进行部署**
   - 统一使用 `./pm2/deploy.sh` 进行部署，避免使用已弃用的脚本

2. **配置文件管理**
   - 使用 `.conf.local` 进行本地配置
   - 不要将敏感配置提交到版本控制

3. **定期监控**
   - 使用 `./pm2/status.sh` 定期检查应用状态
   - 设置适当的监控告警

4. **日志管理**
   - 定期清理日志文件避免磁盘空间不足
   - 使用 `pm2 logs --lines N` 查看特定行数的日志

5. **资源优化**
   - 根据服务器配置调整进程数量和内存限制
   - 监控应用性能并适时调整配置参数