#!/bin/bash

# PM2 停止脚本
# 用于停止 image-render 应用

set -e

PROJECT_DIR="$(dirname "$(dirname "$(realpath "$0")")")"
cd "$PROJECT_DIR"

# 从配置文件中读取应用名称
CONFIG_APP_NAME="image-render"
if [ -f ".conf.local" ]; then
    CONFIG_APP_NAME=$(grep "^APP_NAME=" .conf.local | cut -d'=' -f2 | tr -d ' ')
    if [ -z "$CONFIG_APP_NAME" ]; then
        CONFIG_APP_NAME="image-render"
    fi
fi

echo "🛑 停止 $CONFIG_APP_NAME 应用..."

# 检查 PM2 是否已安装
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2 未安装"
    exit 1
fi

# 检查应用是否在运行
if ! pm2 describe $CONFIG_APP_NAME > /dev/null 2>&1; then
    echo "⚠️  应用未在运行中"
    # 尝试停止所有可能的实例
    echo "🔍 检查是否有其他相关实例..."
    pm2 list | grep -E "(image-render|vibany)" && {
        echo "🛑 发现相关实例，正在停止..."
        pm2 stop all
        pm2 delete all
    } || echo "✅ 没有发现运行中的应用"
    exit 0
fi

# 停止应用
echo "🔄 停止应用..."
pm2 stop $CONFIG_APP_NAME

# 删除应用
echo "🗑️  删除应用..."
pm2 delete $CONFIG_APP_NAME

# 额外检查：确保所有相关实例都被停止
echo "🔍 检查是否还有其他实例..."
if pm2 list | grep -q "image-render\|vibany"; then
    echo "⚠️  发现残留实例，正在清理..."
    pm2 stop all
    pm2 delete all
fi

# 保存 PM2 配置
echo "💾 保存 PM2 配置..."
pm2 save

echo "✅ 应用已停止！"
echo ""
pm2 status

echo ""
echo "🚀 使用 ./pm2/start.sh 重新启动应用"