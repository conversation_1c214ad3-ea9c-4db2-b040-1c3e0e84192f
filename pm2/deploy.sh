#!/bin/bash

# PM2 部署脚本
# 用于启动/重启 image-render 应用

set -e

PROJECT_DIR="$(dirname "$(dirname "$(realpath "$0")")")"
cd "$PROJECT_DIR"

echo "🚀 部署 image-render 应用..."

# 检查 PM2 是否已安装
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2 未安装，请先安装 PM2:"
    echo "npm install -g pm2"
    exit 1
fi

# 检查并创建 .conf.local 文件
if [ ! -f ".conf.local" ]; then
    echo "⚠️  未找到 .conf.local 配置文件"
    if [ -f ".conf.example" ]; then
        echo "📄 发现 .conf.example 文件"
        read -p "是否要从 .conf.example 复制一个 .conf.local 文件? [Y/n]: " -r
        echo
        # 默认为 Y，只有明确输入 n 或 N 才拒绝
        if [[ ! $REPLY =~ ^[Nn]$ ]]; then
            cp .conf.example .conf.local
            echo "✅ 已复制 .conf.example 为 .conf.local"
            echo "💡 提示: 您可以编辑 .conf.local 文件来自定义配置"
            echo "编辑命令: nano .conf.local"
            echo ""
        fi
    else
        echo "⚠️  也未找到 .conf.example 文件，将使用默认配置"
    fi
fi

# 询问是否需要构建
echo "🔨 构建选项："
read -p "是否需要运行 npm run build? [Y/n]: " -r
echo
# 默认为 Y，只有明确输入 n 或 N 才跳过构建
if [[ ! $REPLY =~ ^[Nn]$ ]]; then
    echo "📦 正在构建项目..."
    # 使用 production 环境构建，与 Vercel 保持一致
    NODE_ENV=production npm run build
    if [ $? -eq 0 ]; then
        echo "✅ 构建成功"
    else
        echo "❌ 构建失败，请检查错误信息"
        exit 1
    fi
else
    echo "⏭️  跳过构建步骤"
fi

# 创建日志目录
mkdir -p logs

# 检查端口是否被占用
echo "🔍 检查端口冲突..."
# 从配置文件中读取端口号
CONFIG_PORT=3000
if [ -f ".conf.local" ]; then
    CONFIG_PORT=$(grep "^PM2_PORT=" .conf.local | cut -d'=' -f2 | tr -d ' ')
    if [ -z "$CONFIG_PORT" ]; then
        CONFIG_PORT=3000
    fi
fi

PORT_CHECK=$(lsof -ti:$CONFIG_PORT 2>/dev/null | wc -l)
if [ "$PORT_CHECK" -gt 0 ]; then
    echo "⚠️  端口 $CONFIG_PORT 已被占用"
    echo "请在 .conf.local 文件中设置不同的端口："
    echo "PM2_PORT=3001  # 或其他未被占用的端口"
    echo ""
    echo "当前占用端口 $CONFIG_PORT 的进程："
    lsof -ti:$CONFIG_PORT | head -5
    echo ""
    read -p "是否继续部署? (可能会因端口冲突而失败) [y/N]: " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 部署已取消"
        exit 1
    fi
fi

# 从配置文件中读取应用名称
CONFIG_APP_NAME="image-render"
if [ -f ".conf.local" ]; then
    CONFIG_APP_NAME=$(grep "^APP_NAME=" .conf.local | cut -d'=' -f2 | tr -d ' ')
    if [ -z "$CONFIG_APP_NAME" ]; then
        CONFIG_APP_NAME="image-render"
    fi
fi

# 检查应用是否已经在运行
if pm2 describe $CONFIG_APP_NAME > /dev/null 2>&1; then
    echo "🔄 应用已在运行，正在重启..."
    
    # 重启应用
    pm2 restart $CONFIG_APP_NAME
    
    # 保存 PM2 配置
    echo "💾 保存 PM2 配置..."
    pm2 save
    
    echo "✅ 应用重启成功！"
else
    echo "🆕 应用未运行，正在启动新实例..."
    
    # 启动应用
    pm2 start ecosystem.config.js
    
    # 保存 PM2 配置
    echo "💾 保存 PM2 配置..."
    pm2 save
    
    echo "✅ 应用启动成功！"
fi

# 显示状态
echo ""
pm2 status

echo ""
echo "📊 使用以下命令查看应用状态:"
echo "  pm2 status            - 查看所有应用状态"
echo "  pm2 logs image-render - 查看应用日志"
echo "  pm2 monit             - 实时监控"
echo ""
echo "🔧 使用以下脚本管理应用:"
echo "  ./pm2/deploy.sh       - 部署/重启应用"
echo "  ./pm2/stop.sh         - 停止应用"
echo "  ./pm2/status.sh       - 查看详细状态"
echo ""
echo "⚙️  配置文件:"
echo "  .conf.local           - 本地配置文件"
echo "  .conf.example         - 配置文件示例"
echo "  ecosystem.config.js   - PM2 配置文件"