export interface SampleImage {
  src: string
  alt: string
  width: number
  height: number
}

export const SAMPLE_IMAGES: SampleImage[] = [
  // {
  //   src: "/images/samples/1.jpeg",
  //   alt: "AI Generated Image",
  //   width: 330,
  //   height: 330,
  // },
  {
    src: "/images/samples/2.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/3.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/4.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/5.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/6.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/7.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/10.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/12.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/13.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/14.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/15.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/box.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/app-1.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/app-2.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/default-1.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/default-2.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/default-3.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/ghibli-1.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/mini.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  // {
  //   src: "/images/samples/photo_restore.jpeg",
  //   alt: "Photo Restoration Style AI Generated Image",
  //   width: 330,
  //   height: 330,
  // },
  // {
  //   src: "/images/samples/marble.jpeg",
  //   alt: "Marble Style AI Generated Image",
  //   width: 330,
  //   height: 330,
  // },
  // {
  //   src: "/images/samples/black_board.jpeg",
  //   alt: "Black Board Style AI Generated Image",
  //   width: 330,
  //   height: 330,
  // },
  // {
  //   src: "/images/samples/crazy_doodle.jpeg",
  //   alt: "Crazy Doodle Style AI Generated Image",
  //   width: 330,
  //   height: 330,
  // },
  {
    src: "/images/samples/marriage_photo.jpeg",
    alt: "Marriage Photo Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/clay.jpeg",
    alt: "Clay Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/voxel.jpeg",
    alt: "Voxel Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/renaissance.jpg",
    alt: "Renaissance Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/four_panel.jpg",
    alt: "Four Panel Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/app_icon.jpg",
    alt: "App Icon Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/app_icon-1.jpg",
    alt: "App Icon Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/toy_box.jpg",
    alt: "Toy Box Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/ghibli.jpeg",
    alt: "Ghibli Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/pixar.jpg",
    alt: "Pixar Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/mini_workspace.jpeg",
    alt: "Mini Workspace Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/heart_stone.jpeg",
    alt: "Heart Stone Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/micro_scene.jpeg",
    alt: "Micro Scene Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/plush.jpeg",
    alt: "Plush Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/mcdonalds.jpg",
    alt: "McDonalds Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/cushion.jpeg",
    alt: "Cushion Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/animal-crossing.jpeg",
    alt: "Animal Crossing Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/handmade.jpeg",
    alt: "Handmade Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/fengren.jpeg",
    alt: "Handmade Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/handmade-justin.jpeg",
    alt: "Handmade Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/figurine.jpeg",
    alt: "Figurine Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/figurine-op.jpg",
    alt: "Figurine Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/mc-1.jpeg",
    alt: "McDonalds Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/mc-2.jpeg",
    alt: "McDonalds Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/mc-3.jpeg",
    alt: "McDonalds Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/selfie.jpeg",
    alt: "Selfie Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/selfie-1.jpeg",
    alt: "Selfie Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/selfie-1.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/selfie-2.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/selfie-3.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/instax.jpeg",
    alt: "Instax Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/toy_box_elf.jpeg",
    alt: "Toy Box Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/enamel.jpg",
    alt: "Enamel Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/enamel-1.jpg",
    alt: "Enamel Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/enamel-2.jpg",
    alt: "Enamel Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/animal_transform.jpeg",
    alt: "Animal Transform Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/phone_screen.jpg",
    alt: "Phone Screen Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/phone_screen-1.jpg",
    alt: "Phone Screen Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/sock_puppet.jpeg",
    alt: "Sock Puppet Style AI Generated Image",
    width: 330,
    height: 330,
  },
];

export const IMAGE_POSITIONS = [
  { top: "5%", left: "0%", rotate: "-5deg" },
  { top: "10%", right: "5%", rotate: "8deg" },
  { top: "35%", left: "20%", rotate: "-3deg" },
  { bottom: "10%", left: "5%", rotate: "6deg" },
  { bottom: "25%", right: "15%", rotate: "-7deg" },
  { bottom: "5%", right: "30%", rotate: "4deg" },
]
