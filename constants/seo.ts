/**
 * SEO and site content constants
 * 集中管理网站SEO相关内容和文案
 */

// 网站基本信息
export const SITE = {
  name: "Image AI",
  slogan: "智能图片创作",
  fullTitle: "Image AI｜智能图片创作",
  description: "A professional AI-powered image creation and editing platform.",
  creator: "@interjc",
  logo: {
    path: "/logo.png",
    alt: "Site Logo",
    width: 24,
    height: 24,
  },
  ogImage: {
    path: "/og.png",
    alt: "Image AI｜智能图片创作",
    width: 1200,
    height: 630,
  },
};

// 导航菜单项
export const NAV_ITEMS = [
  {
    href: "/settings/history",
    label: "我的",
    icon: "🖼️",
    key: "history",
  },
  {
    href: "/draw",
    label: "创造",
    icon: "🎨",
    key: "create",
  },
  {
    href: "/explore",
    label: "探索",
    icon: "🌍",
    key: "explore",
  },
  {
    href: "/pricing",
    label: "价格",
    icon: "🚀",
    key: "pricing",
  },
  {
    href: "/contact",
    label: "联系",
    icon: "💻",
    key: "contact",
  },
  {
    href: "/faq",
    label: "问答",
    icon: "💬",
    key: "faq",
  },
];

// 首页文案
export const HOME_PAGE = {
  heroTitle: "Image AI",
  heroSlogan: "智能图片创作",
  ctaButton: "开始创造",
};

// 导航栏文案
export const NAVBAR = {
  signUpButton: "创建账户",
};

// 生成基础元数据
export const getBaseMetadata = () => {
  return {
    metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || ""),
    title: SITE.fullTitle,
    description: SITE.description,
    openGraph: {
      type: "website",
      url: process.env.NEXT_PUBLIC_APP_URL || "",
      title: SITE.fullTitle,
      description: SITE.description,
      images: [
        {
          url: SITE.ogImage.path,
          width: SITE.ogImage.width,
          height: SITE.ogImage.height,
          alt: SITE.ogImage.alt,
        }
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: SITE.fullTitle,
      description: SITE.description,
      images: [SITE.ogImage.path],
      creator: SITE.creator,
    },
  };
};

// 生成特定页面的元数据
export const getPageMetadata = (
  pageTitle: string,
  pageDescription?: string,
  pageImage?: string
) => {
  const baseMetadata = getBaseMetadata();

  return {
    ...baseMetadata,
    title: pageTitle ? `${pageTitle} | ${SITE.name}` : SITE.fullTitle,
    description: pageDescription || SITE.description,
    openGraph: {
      ...baseMetadata.openGraph,
      title: pageTitle ? `${pageTitle} | ${SITE.name}` : SITE.fullTitle,
      description: pageDescription || SITE.description,
      ...(pageImage && {
        images: [
          {
            url: pageImage,
            width: SITE.ogImage.width,
            height: SITE.ogImage.height,
            alt: pageTitle || SITE.ogImage.alt,
          }
        ],
      }),
    },
    twitter: {
      ...baseMetadata.twitter,
      title: pageTitle ? `${pageTitle} | ${SITE.name}` : SITE.fullTitle,
      description: pageDescription || SITE.description,
      ...(pageImage && { images: [pageImage] }),
    },
  };
};
