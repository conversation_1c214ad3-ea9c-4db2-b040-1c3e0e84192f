/**
 * 邀请系统常量配置
 */

// 默认邀请奖励类型
export const DEFAULT_INVITE_TYPE = "both" as const;

// 默认奖励比例（10%）
export const DEFAULT_REF_RATIO = 0.1;

// 默认最大使用次数（0表示不限制）
export const DEFAULT_MAX_USES = 0;

// 邀请码长度
export const INVITE_CODE_LENGTH = 8;

// 邀请码正则表达式（只允许小写字母和数字）
export const INVITE_CODE_REGEX = /^[a-z0-9]+$/;

// 邀请码验证错误信息
export const INVITE_CODE_ERROR_MESSAGE = "邀请码只能包含小写字母和数字";

// 邀请状态
export const INVITATION_STATUS = {
  PENDING: "pending", // 待充值
  READY: "ready",     // 待兑换
  COMPLETED: "completed", // 已兑换
  VOID: "void",       // 已作废
} as const;

// 邀请奖励类型
export const INVITE_TYPES = {
  POINTS: "points",   // 积分奖励
  CASH: "cash",       // 现金奖励
  BOTH: "both",       // 积分或现金
} as const;

// 邀请码激活状态
export const ACTIVATION_STATUS = {
  NOT_ACTIVATED: "not_activated", // 未激活
  ACTIVATED: "activated",         // 已激活
} as const;

/**
 * 验证邀请码是否有效
 * @param inviteCode 邀请码
 * @returns 是否有效
 */
export function validateInviteCode(inviteCode: string): boolean {
  return INVITE_CODE_REGEX.test(inviteCode);
}

/**
 * 生成邀请链接
 * @param inviteCode 邀请码
 * @returns 完整的邀请链接
 */
export function generateInviteLink(inviteCode: string): string {
  // 优先使用环境变量中的URL，如果没有则使用当前域名
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || (typeof window !== 'undefined' ? window.location.origin : '');

  return `${baseUrl}/invite/${inviteCode}`;
}
