// Draw styles and their prompts
export const DEFAULT_DRAW_STYLE = "DEFAULT";

export const DRAW_STYLES = {
  DEFAULT: {
    id: "default",
    name: "自定义",
    description: "基础绘图风格，没有预定义提示词",
    prompt: "draw image",
    params: {
      temperature: 0.5,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/default.jpeg",
  },
  GHIBLI: {
    id: "ghibli",
    name: "吉卜力",
    description: "宫崎骏动画风格，充满魔幻现实主义，温暖怀旧的氛围",
    prompt:
      "Please convert this image into an anime-style illustration inspired by the visual aesthetics of Studio Ghibli. Maintain the original composition and key elements, but reimagine them with Ghibli-style features. Aim for a whimsical and nostalgic atmosphere, capturing the magical realism often seen in Ghibli films like My Neighbor Totoro, Spirited Away, or Howl's Moving Castle.",
    params: {
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0.1,
    },
    image: "/images/samples/ghibli.jpeg",
  },
  PIXAR: {
    id: "pixar",
    name: "皮克斯",
    description: "皮克斯动画风格，充满创意和想象力，适合儿童和家庭",
    prompt: "Transform this image to a Pixar style, Pixar animation style",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/13.jpeg",
  },
  SELFIE: {
    id: "selfie",
    name: "随性自拍",
    description: "随性自拍，没有明确的主体或构图感",
    prompt:
      "请画一张极其平凡无奇的 iPhone 自拍照，没有明确的主体或构图感，就像是随手一拍的快照。照片略带运动模糊，阳光或店内灯光不均导致轻微曝光过度。角度尴尬、构图混乱，整体呈现出一种刻意的平庸感-就像是从口袋里拿手机时不小心拍到的一张自拍。要求人物样貌尽可能还原，参考值最大。人物或场景使用上传图片以及描述",
    params: {
      temperature: 0.5,
      top_p: 0.95,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/selfie.jpeg",
  },
  APP_ICON: {
    id: "app_icon",
    name: "APP 图标",
    description: "可爱玩具风格，3D 风格，高质感，适用于单人模式",
    prompt:
      "Create a 3D-style chibi app icon character with a cute, toy-like appearance. The character should have large expressive eyes, a small smile, and detailed, glossy features like a real physical toy. Use a soft, vibrant lighting style to give it a polished, high-quality finish. The character should slightly exceed the app icon's frame to enhance the 3D effect and playfulness. Style should feel collectible and adorable, like a miniature figure or Nendoroid.",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/app_icon.jpg",
  },
  SOCK_PUPPET: {
    id: "sock_puppet",
    name: "布偶",
    description: "人物转化为布偶风格",
    prompt: `A sock puppet version of the photo, with button eyes, stitched mouths, yarn hair, and playful improvisational textures. Background should resemble a DIY puppet theater set with cardboard props and curtain framing.`,
    params: {
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/sock_puppet.jpeg",
  },
  ENAMEL: {
    id: "enamel",
    name: "珐琅贴",
    description: "把图片或描述文字转化为珐琅贴风格",
    prompt:
      "Turn the subject in the attached image into a kawaii enamel pin. Use glossy metal outlines and vibrant enamel fill. No extra added features. Square mockup format. White background",
    params: {
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/enamel.jpg",
  },
  TOY_BOX: {
    id: "toy_box",
    name: "玩具盒",
    description: "将人物转化为可动人偶，包含配件和包装盒设计",
    prompt:
      "Create a toy of the person in the photo. Let it be an action figure. Next to the figure, there should be the toy's equipments . Also, on top of the box, write toy name and underneath it, and some special slogan. Visualize this in a realistic way.",
    params: {
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/toy_box.jpg",
  },
  MACDONALDS: {
    id: "macdonalds",
    name: "麦当劳玩具",
    description: "麦当劳儿童套餐赠送玩具风格",
    prompt:
      "Transform the uploaded image into a collectible toy figure that could be included as a gift with a McDonald's burger meal. Stylize the character as a small, plastic figurine placed on a simple display base. Include a realistic McDonald's burger and its branded packaging in the background to give the impression of a Happy Meal toy promotion. Use soft lighting and a clean background, keeping the colors vibrant and playful.",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/mcdonalds.jpg",
  },
  INSTAX: {
    id: "instax",
    name: "拍立得",
    description: "随性自拍，没有明确的主体或构图感",
    prompt:
      "将场景中的角色转化为3D Q版风格，放在一张拍立得照片上，相纸被一只手拿着，照片中的角色正从拍立得照片中走出，呈现出突破二维相片边框、进入二维现实空间的视觉效果",
    params: {
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0.1,
    },
    image: "/images/samples/instax.jpeg",
  },
  PHONE_SCREEN: {
    id: "phone_screen",
    name: "手机屏幕",
    description: "人物从手机屏幕里出来",
    prompt: `Trompe l'oeil illusion of A (subject) (attire) steps out of a large screen displaying xyz social media interface. The screen shows the username "@"(input content or guess from the picture) 1K likes, and 12- 20 comments, with floating emojis (heart-eyes, smiley) around it. Turn people in the uploaded picture or input content a pixar like 3D style. your preferred background`,
    params: {
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/phone_screen.jpg",
  },
  FIGURINE: {
    id: "figurine",
    name: "手办",
    description: "精美手办",
    prompt: "根据图片以及描述制作的精美手办",
    params: {
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/figurine.jpeg",
  },
  ANIMAL_TRANSFORM: {
    id: "animal_transform",
    name: "可爱动物",
    description: "将日常物品转化为可爱动物",
    prompt:
      "Transform this everyday object into an imaginary animal by merging its physical characteristics with the animal's anatomy. Keep certain shapes, textures, or mechanical elements of the object visible, naturally integrating them into the creature's design. The animal should look lively, expressive, and slightly cartoonish, with a playful and creative style. Use soft shadows, clean lines, and a vibrant yet harmonious color palette.",
    params: {
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/animal_transform.jpeg",
  },
  CUSHION: {
    id: "cushion",
    name: "柔软抱枕",
    description: "柔软、立体、毛茸茸的可爱物体，超现实风格，富有触感和现代感",
    prompt:
      "Create a high-resolution 3D render of given text and images designed as an inflatable, puffy object. The shape should appear soft, rounded, and air-filled — like a plush balloon or blow-up toy. Use a smooth, matte texture with subtle fabric creases and stitching to emphasize the inflatable look. The form should be slightly irregular and squishy, with gentle shadows and soft lighting that highlight volume and realism. Place it on a clean, minimal background (light gray or pale blue), and maintain a playful, sculptural aesthetic.",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/cushion.jpeg",
  },
  HANDMADE: {
    id: "handmade",
    name: "手工缝纫",
    description: "手工缝纫制作风格，手感柔软，质感细腻",
    prompt:
      "Create a highly detailed 3D rendering of this logo made from colorful felt fabric. The logo should have a soft, slightly fuzzy texture, with visible seams and hand-sewn details. Use bright or pastel colors and soft natural light against a black background. Emphasize the handmade, artisanal, and warm feel of the felt emoji. Center the felt fabric logo in the image without adding any elements.",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/fengren.jpeg",
  },
  ANIMAL_CROSSING: {
    id: "animal-crossing",
    name: "动森",
    description: "动物森友会系列游戏风格",
    prompt: "转换为动物森友会系列游戏风格",
    params: {
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/animal-crossing.jpeg",
  },
  MARRIAGE_PHOTO: {
    id: "marriage_photo",
    name: "结婚照",
    description: "上传至多两张照片后，生成一张结婚合影照",
    prompt:
      "我们要结婚了，帮我做一张正式一点、高雅的婚纱照，我要用来作为婚礼现场门口的主照片，脸部细节特征要跟原始照片保持一致的上妆后的效果",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/marriage_photo.jpeg",
  },
  PLUSH: {
    id: "plush",
    name: "可爱毛绒",
    description: "柔软、立体、毛茸茸的可爱物体，超现实风格，富有触感和现代感",
    prompt:
      "将图片转化为柔软、立体、毛茸茸的可爱物体。整体造型被浓密的毛发完全覆盖，毛发质感极其真实，带有柔和的阴影。物体居中悬浮于干净的浅灰色背景中，轻盈漂浮。整体风格超现实，富有触感和现代感，带来舒适和俏皮的视觉感受。采用摄影棚级灯光，高分辨率渲染。",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/plush.jpeg",
  },
  MICRO_SCENE: {
    id: "micro_scene",
    name: "微缩场景",
    description: "微缩场景风格，Q版3D游戏风",
    prompt:
      "绘图，高清画质，微型立体场景呈现，运用移轴摄影的技法，呈现出Q版场景，镜头聚焦在主要人物身上，根据上传图片或以下主题展开大胆想象：",
    params: {
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/micro_scene.jpeg",
  },
  MINI_WORKSPACE: {
    id: "mini_workspace",
    name: "微缩工作室",
    description: "微缩工作室风格，Q版3D游戏风",
    prompt:
      "这个其实是我的微缩工作室（尽可能立体化，整体是一个工作室），尽可能真实而富有科技感，Q版3D游戏风格，想象我在里面工作、生活、娱乐等的场景，根据我提供的图片或以下主题展开大胆想象：",
    params: {
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/mini_workspace.jpeg",
  },
  HEART_STONE: {
    id: "heart_stone",
    name: "炉石传说",
    description: "上传一张图片，转换为炉石传说风格卡片",
    prompt:
      "制作一张炉石传说那样的游戏卡片，人物要漫画化，卡片要有各种攻防数值和描述信息（可以适当根据提供的内容展开想象）。竖版设计的图片，要有手拿卡片的特写，卡片要显示全",
    params: {
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/heart_stone.jpeg",
  },
  FOUR_PANEL: {
    id: "four_panel",
    name: "四格漫画",
    description: "经典四格漫画布局，现代办公场景，卡通风格，幽默诙谐",
    prompt:
      '经典的"Out the Window" Meme 四格漫画竖版布局，标题在漫画顶部居中，背景为现代办公会议室场景，风格简洁、幽默、清晰易懂，所有人物均以卡通风格表现，适合用于网络传播。',
    params: {
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/four_panel.jpg",
  },
  PHOTO_RESTORE: {
    id: "photo_restore",
    name: "老照片修复",
    description: "修复老照片，上色并提高清晰度",
    prompt: "Restore this old photo, colorize it, and make it clearer.",
    params: {
      temperature: 0.5,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/photo_restore.jpeg",
  },
  VOXEL: {
    id: "voxel",
    name: "像素 Voxel",
    description: "将图像转换为3D像素风格",
    prompt: "Transform this image to 3D Voxel Style",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/voxel.jpeg",
  },
  CLAY: {
    id: "clay",
    name: "粘土风",
    description: "粘土质感，卡通风格，拟人化效果",
    prompt:
      "Transform this image to Remini Clay style, clay texture, cartoon, anthropomorphic",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/clay.jpeg",
  },
  MARBLE: {
    id: "marble",
    name: "大理石雕像",
    description: "超写实大理石雕塑效果，展现优雅的光泽和艺术工艺",
    prompt:
      "A photorealistic image of an ultra-detailed sculpture of the subject in image made of shining marble. The sculpture should display smooth and reflective marble surface, emphasizing its luster and artistic craftsmanship. The design is elegant, highlighting the beauty and depth of marble. The lighting in the image should enhance the sculpture's contours and textures, creating a visually stunning and mesmerizing effect",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/marble.jpeg",
  },
  CRAZY_DOODLE: {
    id: "crazy_doodle",
    name: "疯狂涂鸦",
    description: "手写中文批注和涂鸦风格，充满创意和趣味性",
    prompt:
      "生成图片，把它打印出来，然后用红墨水疯狂地加上手写中文批注、涂鸦、乱画，如果你想的话，还可以加点小剪贴画",
    params: {
      temperature: 0.8,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0.1,
    },
    image: "/images/samples/crazy_doodle.jpeg",
  },
  LINE_ART: {
    id: "line_art",
    name: "线条画",
    description: "彩色线条绘画风格",
    prompt: "Transform the image to a colorful line drawing.",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/line_art.jpeg",
  },
  RENAISSANCE: {
    id: "renaissance",
    name: "文艺复兴",
    description: "文艺复兴时期绘画风格",
    prompt: "Turn my photo into a Renaissance painting",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/renaissance.jpg",
  },
  BLACK_BOARD: {
    id: "black_board",
    name: "黑板画",
    description: "黑板画风格，需要提示词中包含黑板上的文案",
    prompt:
      "生成一张女讲师在黑板前讲课的照片，整体只需要黑板和女讲师就行，女老师年纪25岁左右，戴眼镜，身材高挑匀称，长相符合亚洲审美，需要写实风格，参考日本电影，黑板上写着：",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/black_board.jpeg",
  },
} as const;

// Helper function to get style prompt by id
export const getStylePrompt = (styleId: keyof typeof DRAW_STYLES) => {
  return DRAW_STYLES[styleId]?.prompt || '';
};

export const getStyleParams = (styleId: keyof typeof DRAW_STYLES) => {
  return DRAW_STYLES[styleId]?.params || {
    temperature: 0.5,
    top_p: 1,
    frequency_penalty: 0,
    presence_penalty: 0,
  };
};

export * from './models';
export * from './pricing';
