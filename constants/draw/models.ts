export const IMAGE_SIZE = (size: string) => {
  if (size === 'portrait') return '1024x1792';
  if (size === 'landscape') return '1792x1024';
  if (size === 'square') return '1024x1024';

  return '1024x1024'; // 默认为正方形
};

export interface DrawModel {
  id: string;
  type: string;
  name: string;
  description: string;
  paidOnly: boolean;
  disabled: boolean;
  points: number;
  maxImages: number;
}

export const DEFAULT_DRAW_MODEL = "draw-model-small";
export const VIP_DEFAULT_DRAW_MODEL = "draw-model-vip";

export const DEBUG_MODE = process.env.NEXT_PUBLIC_DEBUG === "on";

export const drawModels: Array<DrawModel> = [
  {
    id: "draw-model-small",
    type: "llm",
    name: "4o 基础版",
    description: "基于 gpt-4o 模型，试用版",
    paidOnly: false,
    disabled: false,
    points: 30,
    maxImages: 2,
  },
  {
    id: "draw-model",
    type: "llm",
    name: "4o 专业版",
    description: "基于 gpt-4o，与网页版一致",
    paidOnly: true,
    disabled: false,
    points: 50,
    maxImages: 2,
  },
  {
    id: "draw-model-vip",
    type: "llm",
    name: "4o 高级版",
    description: "基于 gpt-4o，高性能版",
    paidOnly: true,
    disabled: false,
    points: 70,
    maxImages: 4,
  },
  {
    id: "draw-model-raw-small",
    type: "image",
    name: "image-1 文字版",
    description: "基于 gpt-image-1，纯文字版",
    paidOnly: true,
    disabled: false,
    points: 100,
    maxImages: 0,
  },
  {
    id: "draw-model-raw-medium",
    type: "image",
    name: "image-1 全能版",
    description: "基于 gpt-image-1，图生图版",
    paidOnly: true,
    disabled: false,
    points: 150,
    maxImages: 5,
  },
  {
    id: "draw-model-raw",
    type: "image",
    name: "image-1 满血版",
    description: "基于 gpt-image-1，满血版",
    paidOnly: true,
    disabled: false,
    points: 180,
    maxImages: 10,
  },
  {
    id: "draw-model-raw-openai",
    type: "image",
    name: "image-1 直连版",
    description: "基于全新 gpt-image-1，直连官方",
    paidOnly: true,
    disabled: false,
    points: 200,
    maxImages: 10,
  },
  {
    id: "draw-model-flux-pro",
    type: "flux",
    name: "Kontext 基础版",
    description: "基于 flux-kontext-pro",
    paidOnly: true,
    disabled: false,
    points: 50,
    maxImages: 2,
  },
  {
    id: "draw-model-flux-max",
    type: "flux",
    name: "Kontext 高级版",
    description: "基于 flux-kontext-max",
    paidOnly: true,
    disabled: false,
    points: 70,
    maxImages: 4,
  },
  {
    id: "draw-model-grok-2",
    type: "grok",
    name: "推特版",
    description: "体验 Grok-2-Image，不可上传图片",
    paidOnly: true,
    disabled: DEBUG_MODE ? false : true,
    points: 100,
    maxImages: 0,
  },
];
