import { drawModels } from "./models";

export interface PricingTier {
  id: string;
  name: string;
  icon: React.ReactNode;
  price: number; // 价格（单位：元）
  description: string;
  features: string[];
  popular?: boolean;
  color: string;
  points?: number; // 积分数量
}

// 检查是否处于调试模式 - 使用 NEXT_PUBLIC_ 前缀确保服务端和客户端一致
const isDebug = process.env.NEXT_PUBLIC_DEBUG === 'on';

const getModelFeatureDescription = (modelIdx: number): string => {
  if (!drawModels[modelIdx]) return "";

  return `${drawModels[modelIdx].name}模型 ${drawModels[modelIdx].points} 积分/次`;
};

export const getModelFeatureDescriptionList = (): string[] => {
  return drawModels
    .filter((item) => !item.disabled && item.description !== "")
    .map((_, index) => getModelFeatureDescription(index));
};

export const getModelList = (): any[] => drawModels
    .filter((item) => !item.disabled);

export const PRICING_TIERS: PricingTier[] = [
  {
    id: "package-free",
    name: "免费试用",
    icon: "🎨",
    price: 0,
    points: 300,
    description: "适合入门体验",
    features: ["300 积分免费赠送", "立即开始使用", "无需付费"],
    color: "blue",
  },
  {
    id: "package-a",
    name: "新手套装",
    icon: "✨",
    price: isDebug ? 3 : 10, // 单位：元
    points: 1000, // 单位：积分
    description: isDebug ? "1元 = 1000积分 (调试模式)" : "10元 = 1000积分",
    features: ["增加 1000 积分", "解锁高级模型，体验更优质画质"],
    popular: false,
    color: "amber",
  },
  {
    id: "package-b",
    name: "专业套餐",
    icon: "✈️",
    price: isDebug ? 3.1 : 30, // 单位：元
    points: 3500, // 单位：积分
    description: "30 元 = 3500 积分",
    features: [
      "增加 3500 积分",
      "解锁高级模型，体验更优质画质",
      "八五折，性价比之选",
    ],
    popular: true,
    color: "indigo",
  },
  {
    id: "package-c",
    name: "土豪套餐",
    icon: "🌟",
    price: isDebug ? 3.2 : 100, // 单位：元
    points: 13000, // 单位：积分
    description: "100 元 = 13000 积分",
    features: [
      "增加 13000 积分",
      "解锁高级模型，体验更优质画质",
      "七六折，超值量贩装",
    ],
    popular: false,
    color: "purple",
  },
];
