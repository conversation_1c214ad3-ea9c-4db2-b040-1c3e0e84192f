export interface FooterLink {
  href: string;
  label: string;
  icon?: string;
  position?: "prifix" | "suffix";
  isExternal?: boolean;
}

export interface FooterSection {
  links: FooterLink[];
}

export const FOOTER_BRAND = {
  logo: '/logo.png',
  name: '<PERSON>',
  href: 'https://interjc.net',
  copyright: (year: number) => `© ${year}`,
};

export const FOOTER_LINKS = {
  terms: {
    href: '/terms',
    label: '相关协议',
  },
  pricing: {
    href: '/pricing',
    label: '剩余 {credits} 积分，点击购买',
  },
  invitation: {
    href: '/settings/invitation',
    label: '邀请奖励',
  },
};

export const FOOTER_EXTERNAL_LINKS: FooterLink[] = [
  {
    href: 'https://ship.vibany.com',
    label: '私有部署',
    icon: 'mdi:arrow-top-right-thin',
    position: "suffix",
    isExternal: true,
  },
  {
    href: 'https://justincourse.com/pricing',
    label: 'AI 课程',
    icon: 'mdi:arrow-top-right-thin',
    position: "suffix",
    isExternal: true,
  },
  {
    href: 'https://x.com/yokogaisha',
    label: '<PERSON>HD',
    icon: 'mdi:twitter',
    position: "prifix",
    isExternal: true,
  },
];
